/// <reference types="vitest" />
import react from "@vitejs/plugin-react";
import tsconfigPaths from "vite-tsconfig-paths";
import { coverageConfigDefaults, defineConfig } from "vitest/config";

export default defineConfig({
  plugins: [
    react(),
    tsconfigPaths(),
    // Custom plugin to handle SVG imports https://github.com/vitest-dev/vitest/discussions/1766#discussioncomment-3306812
    {
      name: "load-svg",
      enforce: "pre",
      transform(_, id) {
        if (id.endsWith(".svg")) {
          return "export default () => {}";
        }
      },
    },
  ],
  test: {
    include: ["**/*.test.ts?(x)"],
    coverage: {
      exclude: [
        "**/theme/*",
        "**/out/*",
        "**/types/*",
        "**/stories/*",
        "**/*.stories.tsx",
        "**/pages/*",
        ...coverageConfigDefaults.exclude,
      ],
    },
    globals: true,
    environment: "jsdom",
    setupFiles: "./vitest.setup.ts",
  },
});
