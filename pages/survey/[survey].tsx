import { useRouter } from "next/router";

import { ProtectedRoute } from "@Components/ProtectedRoute/ProtectedRoute";
import { WitchScreenContainer } from "@Features/witch/WitchScreenContainer";

const Witch = () => {
  const router = useRouter();

  const { survey } = router.query;

  return (
    <ProtectedRoute>
      <WitchScreenContainer witchId={survey as string} />
    </ProtectedRoute>
  );
};

export default Witch;
