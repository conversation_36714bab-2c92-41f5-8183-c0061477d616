import { useRouter } from "next/router";

import { ProtectedRoute } from "@Components/ProtectedRoute/ProtectedRoute";
import { WitchScreenContainer } from "@Features/witch/WitchScreenContainer";
import { Routes } from "@Types";

const Todo = () => {
  const router = useRouter();

  const { todo } = router.query;

  return (
    <ProtectedRoute path={Routes.TODO}>
      <WitchScreenContainer todoId={todo as string} />
    </ProtectedRoute>
  );
};

export default Todo;
