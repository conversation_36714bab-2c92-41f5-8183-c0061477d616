import { appStrings } from "@Assets/app_strings";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { CarePlanDetailScreenContainer } from "@Features/carePlanDetail/CarePlanDetailScreenContainer";
import { usePageTitle } from "@Hooks/pageTitleHook";

const CarePlanDetailPage = () => {
  usePageTitle(appStrings.pageTitles.carePlanDetail);

  return (
    <PostAuthContainer>
      <CarePlanDetailScreenContainer />
    </PostAuthContainer>
  );
};

export default CarePlanDetailPage;
