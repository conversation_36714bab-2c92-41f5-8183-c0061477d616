import { useRouter } from "next/router";

import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { CarePlanInterventionScreenContainer } from "@Features/carePlanIntervention/CarePlanInterventionScreenContainer";

const CarePlanIntervention = () => {
  const router = useRouter();
  const { intervention } = router.query;

  return (
    <PostAuthContainer>
      <CarePlanInterventionScreenContainer interventionId={intervention as string} />
    </PostAuthContainer>
  );
};

export default CarePlanIntervention;
