import { useEffect, useState } from "react";
import Router, { useRouter } from "next/router";

import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { Error404Screen } from "@Features/error404/Error404Screen";
import { handleUriNavigation } from "@Features/navigation/utils/navigation.util";
import { Routes } from "@Types";

const IndexPage = () => {
  const router = useRouter();
  const [loaded, setLoaded] = useState(false);
  const [isNotFound, setIsNotFound] = useState(false);

  useEffect(() => {
    const attemptToRoute = async () => {
      const { pathname, search } = window.location;
      const uri = `${pathname}${search}`;

      const route = handleUriNavigation(uri);

      if (pathname === "/") {
        setLoaded(true);
        router.replace(Routes.HOME);
        return;
      }

      try {
        await Router.replace(route ?? uri);
      } catch {
        setIsNotFound(true);
      }
    };

    attemptToRoute();
  }, [router]);

  if (isNotFound) {
    return <Error404Screen attemptedPath={window.location.pathname} />;
  }

  if (!loaded) {
    return null;
  }

  return <LoadingSpinner open overlayDrawer overlayHeader />;
};

export default IndexPage;
