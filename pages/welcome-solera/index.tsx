import { useSearchParams } from "next/navigation";

import { appStrings } from "@Assets/app_strings";
import { PreAuthContainer } from "@Components/PreAuthContainer/PreAuthContainer";
import { PartnerPreVerify } from "@Features/partnerPreVerify/PartnerPreVerify";
import { usePageTitle } from "@Hooks/pageTitleHook";

const WelcomeSolera = () => {
  const searchParams = useSearchParams();
  const lookupKey = searchParams.get("lookupKey") ?? "";

  usePageTitle(appStrings.pageTitles.partnerPreVerify);

  return (
    <PreAuthContainer>
      <PartnerPreVerify code={lookupKey} partner="Solera" />
    </PreAuthContainer>
  );
};

export default WelcomeSolera;
