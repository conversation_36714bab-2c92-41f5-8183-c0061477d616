import { appStrings } from "@Assets/app_strings";
import { PreAuthContainer } from "@Components/PreAuthContainer/PreAuthContainer";
import { ResetPasswordScreenContainer } from "@Features/resetPassword/ResetPasswordScreenContainer";
import { usePageTitle } from "@Hooks/pageTitleHook";

const ResetPassword = () => {
  usePageTitle(appStrings.pageTitles.passwordReset);

  return (
    <PreAuthContainer>
      <ResetPasswordScreenContainer />
    </PreAuthContainer>
  );
};

export default ResetPassword;
