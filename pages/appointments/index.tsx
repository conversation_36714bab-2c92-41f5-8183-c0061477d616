import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";

import { appStrings } from "@Assets/app_strings";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { AppointmentsScreenContainer } from "@Features/appointments/AppointmentsScreenContainer";
import { AppointmentsScreenContainer as AppointmentsScreenContainerNew } from "@Features/appointmentsNew/AppointmentsScreenContainer";
import { APPOINTMENT_API_FEATURE_FLAG } from "@Features/appointmentsNew/assets/constants";
import { usePageTitle } from "@Hooks/pageTitleHook";
import { useFeatureFlag } from "@Hooks/useFeatureFlag";

const Appointments = () => {
  const { isReady, treatment } = useFeatureFlag(APPOINTMENT_API_FEATURE_FLAG);
  const searchParams = useSearchParams();
  const [role, setRole] = useState<string | null>();

  useEffect(() => {
    const role = searchParams.get("role");

    setRole(role);
  }, [searchParams]);

  usePageTitle(appStrings.pageTitles.appointments);

  return (
    <PostAuthContainer>
      {isReady && treatment === "on" ? (
        <AppointmentsScreenContainerNew />
      ) : (
        <AppointmentsScreenContainer practitionerRole={role ?? ""} />
      )}
    </PostAuthContainer>
  );
};

export default Appointments;
