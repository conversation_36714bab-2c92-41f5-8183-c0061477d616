import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { PreAuthContainer } from "@Components/PreAuthContainer/PreAuthContainer";
import { AccessCodeScreenContainer } from "@Features/welcome/accessCode/AccessCodeScreenContainer";
import { usePageTitle } from "@Hooks/pageTitleHook";

const Register = () => {
  const router = useRouter();
  const code: string = router.query.code as string;

  usePageTitle(appStrings.pageTitles.accessCode);

  return (
    <PreAuthContainer>
      <AccessCodeScreenContainer accessCode={code} />
    </PreAuthContainer>
  );
};

export default Register;
