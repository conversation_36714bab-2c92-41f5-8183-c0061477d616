import { appStrings } from "@Assets/app_strings";
import { PreAuthContainer } from "@Components/PreAuthContainer/PreAuthContainer";
import { LoginScreenContainer } from "@Features/login/LoginScreenContainer";
import { usePageTitle } from "@Hooks/pageTitleHook";

const Login = () => {
  usePageTitle(appStrings.pageTitles.signIn);

  return (
    <PreAuthContainer>
      <LoginScreenContainer />
    </PreAuthContainer>
  );
};

export default Login;
