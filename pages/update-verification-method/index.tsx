import { appStrings } from "@Assets/app_strings";
import { PreAuthContainer } from "@Components/PreAuthContainer/PreAuthContainer";
import { UpdateVerificationMethod } from "@Features/updateVerificationMethod/UpdateVerificationMethod";
import { usePageTitle } from "@Hooks/pageTitleHook";

const UpdateVerificationMethodPage = () => {
  usePageTitle(appStrings.pageTitles.updateVerificationMethod);

  return (
    <PreAuthContainer>
      <UpdateVerificationMethod />
    </PreAuthContainer>
  );
};

export default UpdateVerificationMethodPage;
