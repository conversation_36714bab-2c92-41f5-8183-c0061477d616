import { useEffect } from "react";
import { useDispatch } from "react-redux";

import { appStrings } from "@Assets/app_strings";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { GutCheckDisplay } from "@Features/gutCheck/GutCheckDisplay";
import { NavigationStateSlice, NavOptions } from "@Features/navigation/store/navigationStateSlice";
import { usePageTitle } from "@Hooks/pageTitleHook";

const GutCheckPage = () => {
  usePageTitle(appStrings.pageTitles.gutCheck);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(NavigationStateSlice.actions.setActiveNavOption(NavOptions.GUT_CHECK));
  }, [dispatch]);

  return (
    <PostAuthContainer>
      <GutCheckDisplay />
    </PostAuthContainer>
  );
};

export default GutCheckPage;
