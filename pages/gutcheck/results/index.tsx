import { useEffect, useLayoutEffect } from "react";
import { useDispatch } from "react-redux";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { NavigationStateSlice, NavOptions } from "@Features/navigation/store/navigationStateSlice";
import { usePageTitle } from "@Hooks/pageTitleHook";
import { Routes } from "@Types";

const OrderResultsPage = () => {
  usePageTitle(appStrings.pageTitles.gutCheckResults);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(NavigationStateSlice.actions.setActiveNavOption(NavOptions.GUT_CHECK));
  }, [dispatch]);

  const router = useRouter();

  // If user gets to /gutcheck/results without an ID, redirect to gutcheck
  useLayoutEffect(() => {
    if (router.isReady) {
      router.push(Routes.GUT_CHECK_NEW);
    }
  }, [router]);

  return null;
};

export default OrderResultsPage;
