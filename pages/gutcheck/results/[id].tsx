import { useEffect } from "react";
import { useDispatch } from "react-redux";

import { appStrings } from "@Assets/app_strings";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { GutcheckResultContainer } from "@Features/gutCheck/components/results/GutcheckResultContainer";
import { NavigationStateSlice, NavOptions } from "@Features/navigation/store/navigationStateSlice";
import { usePageTitle } from "@Hooks/pageTitleHook";

const OrderResultsPage = () => {
  usePageTitle(appStrings.pageTitles.gutCheckResults);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(NavigationStateSlice.actions.setActiveNavOption(NavOptions.GUT_CHECK));
  }, [dispatch]);

  return (
    <PostAuthContainer>
      <GutcheckResultContainer />
    </PostAuthContainer>
  );
};

export default OrderResultsPage;
