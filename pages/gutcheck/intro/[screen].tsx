import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { GutCheckIntro } from "@Features/gutCheck/GutCheckIntro";
import { GUT_CHECK_INTRO_SCREENS, GutCheckIntroScreens } from "@Features/gutCheck/types/gutCheck.types";
import { NavigationStateSlice, NavOptions } from "@Features/navigation/store/navigationStateSlice";
import { usePageTitle } from "@Hooks/pageTitleHook";

const isValidScreen = (screen: string): screen is GutCheckIntroScreens => {
  return [
    GUT_CHECK_INTRO_SCREENS.MICROBIOME_ROLE,
    GUT_CHECK_INTRO_SCREENS.WHAT_AND_WHY,
    GUT_CHECK_INTRO_SCREENS.UNLOCK_BETTER_HEALTH,
  ].some((gutCheckScreen) => gutCheckScreen === screen);
};

const GutCheckPage = () => {
  usePageTitle(appStrings.pageTitles.gutCheck);

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(NavigationStateSlice.actions.setActiveNavOption(NavOptions.GUT_CHECK));
  }, [dispatch]);

  const router = useRouter();
  const { screen } = router.query;
  const validScreen =
    typeof screen === "string" && isValidScreen(screen) ? screen : GUT_CHECK_INTRO_SCREENS.MICROBIOME_ROLE;

  return (
    <PostAuthContainer>
      <GutCheckIntro screen={validScreen} />
    </PostAuthContainer>
  );
};

export default GutCheckPage;
