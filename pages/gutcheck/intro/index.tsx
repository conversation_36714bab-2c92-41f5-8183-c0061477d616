import { useEffect } from "react";
import { useRouter } from "next/router";

import { GUT_CHECK_INTRO_SCREENS } from "@Features/gutCheck/types/gutCheck.types";
import { Routes } from "@Types";

const IntroRedirectPage = () => {
  const router = useRouter();

  useEffect(() => {
    router.replace(`${Routes.GUT_CHECK_INTRO}/${GUT_CHECK_INTRO_SCREENS.MICROBIOME_ROLE}`);
  }, [router]);

  return null;
};

export default IntroRedirectPage;
