import { useRouter } from "next/router";

import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { OrderScreenContainer } from "@Features/order/OrderScreenContainer";

const OrderDetailPage = () => {
  const router = useRouter();
  const orderId: string = router.query.order as string;

  return (
    <PostAuthContainer>
      <OrderScreenContainer orderId={orderId} />
    </PostAuthContainer>
  );
};

export default OrderDetailPage;
