import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { GoalWelcomePage, ScreenType, OriginType } from "@Features/goalSetting/components/GoalWelcomePage";
import { usePageTitle } from "@Hooks/pageTitleHook";
import { Routes } from "@Types";

function isSupportedScreenType(screen: string): screen is ScreenType {
  return ["specificGoals", "achievableGoals", "getStarted"].includes(screen);
}

function isSupportedOriginType(origin: string): origin is OriginType {
  return ["home", "my-progress"].includes(origin);
}

const BACK_MAPPING = (origin: OriginType) =>
  ({
    home: { path: `${Routes.HOME}?addGoal=true`, screenName: "Home" },
    "my-progress": { path: `${Routes.PROGRESS}?addGoal=true`, screenName: "HistoryDayView" },
    achievableGoals: {
      path: `${Routes.GOAL_SETTING_WELCOME_SPECIFIC}?origin=${origin}`,
      screenName: "GoalSettingWelcome",
    },
    getStarted: {
      path: `${Routes.GOAL_SETTING_WELCOME_ACHIEVABLE}?origin=${origin}`,
      screenName: "GoalSettingWelcome",
    },
  }) as const;

const FORWARD_MAPPING = (origin: OriginType) =>
  ({
    specificGoals: {
      path: `${Routes.GOAL_SETTING_WELCOME_ACHIEVABLE}?origin=${origin}`,
      screenName: "GoalSettingWelcome",
    },
    achievableGoals: {
      path: `${Routes.GOAL_SETTING_WELCOME_START}?origin=${origin}`,
      screenName: "GoalSettingWelcome",
    },
  }) as const;

const getRoute = (isForward: boolean, screenName: ScreenType, origin: OriginType) => {
  if (isForward && screenName !== "getStarted") {
    return FORWARD_MAPPING(origin)[screenName];
  }

  return screenName !== "specificGoals" ? BACK_MAPPING(origin)[screenName] : undefined;
};

export default function WelcomeToGoalSetting() {
  usePageTitle(appStrings.pageTitles.goalSettingWelcome);
  const router = useRouter();

  const { screen, origin } = router.query;
  const allowedOrigin = !origin || Array.isArray(origin) || !isSupportedOriginType(origin) ? "home" : origin;
  const allowedScreen = !screen || Array.isArray(screen) || !isSupportedScreenType(screen) ? "specificGoals" : screen;
  const route = BACK_MAPPING(allowedOrigin)[allowedOrigin];
  const forwardRoute = getRoute(true, allowedScreen, allowedOrigin) ?? route;
  const backRoute = getRoute(false, allowedScreen, allowedOrigin) ?? route;

  return (
    <PostAuthContainer>
      <GoalWelcomePage
        screen={allowedScreen}
        originalRoute={route}
        backRoute={backRoute}
        forwardRoute={forwardRoute}
        origin={allowedOrigin}
      />
    </PostAuthContainer>
  );
}
