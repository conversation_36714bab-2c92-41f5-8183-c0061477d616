import { useEffect } from "react";
import { useSelector } from "react-redux";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { ProgressScreenContainer } from "@Features/progress/ProgressScreenContainer";
import { progressStateSelector } from "@Features/progress/store/progressStateSlice";
import { symptomLoggingStateSelector } from "@Features/symptomLogging/store/symptomLoggingStateSlice";
import { usePageTitle } from "@Hooks/pageTitleHook";
import { useOpenSymptomLoggingDrawer } from "@Hooks/useOpenSymptopLoggingDrawer";

const Progress = () => {
  const triggerSymptomLoggingDrawer = useOpenSymptomLoggingDrawer(false, 0);
  const isSymptomLoggingLoaded = useSelector(symptomLoggingStateSelector("loadState")) === "loaded";
  const isProgressLoaded = useSelector(progressStateSelector("loadState")) === "loaded";
  const router = useRouter();

  /**
   * Adds support for displaying the symptom tracking menu when the user
   * has navigated through to the app via a deep link.
   */
  useEffect(() => {
    const shouldTriggerDrawer =
      router.query?.displayTrackingMenu === "true" && isProgressLoaded && isSymptomLoggingLoaded;

    if (shouldTriggerDrawer) {
      triggerSymptomLoggingDrawer();
      router.replace("/progress");
    }
  }, [isProgressLoaded, router, isSymptomLoggingLoaded, triggerSymptomLoggingDrawer]);

  usePageTitle(appStrings.pageTitles.progress);

  return (
    <PostAuthContainer>
      <ProgressScreenContainer />
    </PostAuthContainer>
  );
};

export default Progress;
