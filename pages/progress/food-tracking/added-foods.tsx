import { appStrings } from "@Assets/app_strings";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { ViewAllAddedFoodsScreen } from "@Features/foodTracking/components/ViewAllAddedFoodsScreen";
import { usePageTitle } from "@Hooks/pageTitleHook";

export default function AddedFoods() {
  usePageTitle(appStrings.pageTitles.foodTrackingViewAddedFoods);

  return (
    <PostAuthContainer>
      <ViewAllAddedFoodsScreen />
    </PostAuthContainer>
  );
}
