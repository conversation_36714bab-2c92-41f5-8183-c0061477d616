import { appStrings } from "@Assets/app_strings";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { ViewRecipesScreen } from "@Features/foodTracking/components/ViewRecipesScreen";
import { usePageTitle } from "@Hooks/pageTitleHook";

export default function AddedFoods() {
  usePageTitle(appStrings.pageTitles.foodTrackingViewRecipes);

  return (
    <PostAuthContainer>
      <ViewRecipesScreen />
    </PostAuthContainer>
  );
}
