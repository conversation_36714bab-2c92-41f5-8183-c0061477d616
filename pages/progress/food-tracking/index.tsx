import { useEffect } from "react";
import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { FoodTrackingScreenContainer } from "@Features/foodTracking/FoodTrackingScreenContainer";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { usePageTitle } from "@Hooks/pageTitleHook";

export default function FoodTracking() {
  const { sendEventAnalytics } = useAnalyticsHook();

  const router = useRouter();

  usePageTitle(appStrings.pageTitles.foodTracking);

  useEffect(() => {
    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_OPENED);

    const onBeforeHistoryChange = (url: string) => {
      /**
       * If we're navigating to a page outside of food tracking, we want to send the event
       */
      if (!url.includes("food-tracking")) {
        sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_CLOSED);
      }
    };

    router.events.on("beforeHistoryChange", onBeforeHistoryChange);

    return () => {
      router.events.off("beforeHistoryChange", onBeforeHistoryChange);
    };
    /** We use an empty dependency array to reduce the number of times that the clickstream event fires */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <PostAuthContainer>
      <FoodTrackingScreenContainer />
    </PostAuthContainer>
  );
}
