import { useRouter } from "next/router";

import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { SlideshowScreenContainer } from "@Features/slideshow/SlideshowScreenContainer";

const Slideshow = () => {
  const router = useRouter();
  const { slideshow, actionId } = router.query;
  const slideshowId = typeof slideshow === "string" ? slideshow : slideshow?.[0];

  return (
    <PostAuthContainer>
      <SlideshowScreenContainer
        slideshowId={slideshowId ?? ""}
        actionTargetId={typeof actionId === "string" ? actionId : actionId?.[0]}
      />
    </PostAuthContainer>
  );
};

export default Slideshow;
