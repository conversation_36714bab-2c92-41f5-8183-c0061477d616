import { useSearchParams } from "next/navigation";

import { appStrings } from "@Assets/app_strings";
import { PreAuthContainer } from "@Components/PreAuthContainer/PreAuthContainer";
import { MFA_TYPE } from "@Features/multiFactorAuthentication/assets/constants";
import { MfaVerificationScreen } from "@Features/multiFactorAuthentication/MfaVerificationScreen";
import { usePageTitle } from "@Hooks/pageTitleHook";

const MfaVerification = () => {
  const searchParams = useSearchParams();

  usePageTitle(
    searchParams.get("mfaType") === MFA_TYPE.EMAIL
      ? appStrings.pageTitles.emailVerification
      : appStrings.pageTitles.smsVerification,
  );

  return (
    <PreAuthContainer>
      <MfaVerificationScreen />
    </PreAuthContainer>
  );
};

export default MfaVerification;
