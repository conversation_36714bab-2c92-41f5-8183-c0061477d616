import * as React from "react";
import { useEffect, useState } from "react";
import Router from "next/router";

import { Error404Screen } from "@Features/error404/Error404Screen";
import { handleUriNavigation } from "@Features/navigation/utils/navigation.util";

// This 404 page assists with dynamic routing when running Next.js
// as a static export build hosted on Cloud (S3-like) storage,
// without a backend. It allows Next.js file-based routing to work.

// How it works - the 404.html file is configured as the file to serve
// in the event of a 404, while returning a 200 OK status code instead of
// 404. Then within this page, we try going to the route the browser is
// requesting. If Next.js fails to look up the route, it displays the 404
// screen.
export default function Custom404() {
  const [isNotFound, setIsNotFound] = useState(false);

  useEffect(() => {
    const attemptToRoute = async () => {
      const { pathname, search } = window.location;
      const uri = `${pathname}${search}`;

      const route = handleUriNavigation(uri);

      try {
        await Router.replace(route ?? uri);
      } catch {
        setIsNotFound(true);
      }
    };

    attemptToRoute();
  }, []);

  if (isNotFound) {
    return <Error404Screen attemptedPath={window.location.pathname} />;
  }

  return null;
}
