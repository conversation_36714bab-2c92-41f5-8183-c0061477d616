import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { APPOINTMENT_API_FEATURE_FLAG } from "@Features/appointmentsNew/assets/constants";
import { CareTeamScreenContainer, isCareTeamSessionType } from "@Features/careTeam/CareTeamScreenContainer";
import { CareTeamScreenContainerNew } from "@Features/careTeam/CareTeamScreenContainerNew";
import { usePageTitle } from "@Hooks/pageTitleHook";
import { useFeatureFlag } from "@Hooks/useFeatureFlag";

const CareTeam = () => {
  const { isReady, treatment } = useFeatureFlag(APPOINTMENT_API_FEATURE_FLAG);
  const router = useRouter();
  const { role } = router.query;
  const careTeamRoleType = typeof role === "string" && isCareTeamSessionType(role) ? role : undefined;

  usePageTitle(appStrings.pageTitles.team);

  return (
    <PostAuthContainer>
      {isReady && treatment === "on" ? (
        <CareTeamScreenContainerNew role={careTeamRoleType} />
      ) : (
        <CareTeamScreenContainer role={careTeamRoleType} />
      )}
    </PostAuthContainer>
  );
};

export default CareTeam;
