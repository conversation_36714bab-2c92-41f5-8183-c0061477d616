import React, { useMemo } from "react";
import "@vivantehealth/design-tokens/dist/assets/fonts/fonts.css";
import { Provider } from "react-redux";
import { CssBaseline, ThemeProvider } from "@mui/material";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { SplitFactoryProvider } from "@splitsoftware/splitio-react";
import { AppProps } from "next/app";
import Head from "next/head";
import "@Assets/reactPdf.css";
import "react-pdf/dist/Page/TextLayer.css";
import "react-pdf/dist/Page/AnnotationLayer.css";

import { getVivanteEnvironment } from "@Config/config";
import { environmentVariables } from "@Config/environmentVariables";
import { store } from "@Store/store";
// We need to import the ScreenShell after the store import to avoid noop webpack errors.
// eslint-disable-next-line import/order
import { ScreenShell } from "@Components/ScreenShell/ScreenShell";
import { theme } from "@Theme/cylinderTheme";
import { DeviceInterceptor } from "components/DeviceInterceptor/DeviceInterceptor";

const getSplitSdkConfig = (): SplitIO.IBrowserSettings => {
  const environment = getVivanteEnvironment();

  const splitSdkConfig: SplitIO.IBrowserSettings = {
    core: {
      authorizationKey: environmentVariables[environment].SPLIT_IO_API_KEY,
      key: "anonymous",
    },
  };

  // Overwrite the Split configuration for local testing. Change flag_name to the feature flag name you want to test
  // if (typeof window !== "undefined") {
  //   if (window.location.hostname.includes("localhost")) {
  //     logger.info("Using localhost split config", window.location.hostname);
  //     splitSdkConfig.core.authorizationKey = "localhost";
  //     splitSdkConfig.core.key = "CylinderUser";
  //     splitSdkConfig.features = {
  //       flag_name: "on",
  //     };
  //   }
  // }

  return splitSdkConfig;
};

// https://github.com/vivantehealth/githrive-reactweb/issues/548
// Check if domain name has a period at the end and redirect to the domain without it.
function fixPeriodInDomain() {
  const url = window.location.href;
  const { hostname } = window.location;

  if (hostname.endsWith(".")) {
    const newUrl = url.replace(hostname, hostname.slice(0, -1));

    window.location.replace(newUrl);
  }
}

if (typeof window !== "undefined") {
  fixPeriodInDomain();
}

function App({ Component, pageProps }: AppProps) {
  const splitConfig = useMemo(() => getSplitSdkConfig(), []);

  return (
    <>
      <Head>
        <title>Member Portal - Cylinder Health</title>
        <link rel="icon" type="image/x-icon" href="/images/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=0" />
      </Head>
      <Provider store={store}>
        <SplitFactoryProvider config={splitConfig} updateOnSdkUpdate updateOnSdkTimedout={false}>
          <DeviceInterceptor>
            <ThemeProvider theme={theme}>
              <CssBaseline />
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <ScreenShell>
                  <Component {...pageProps} />
                </ScreenShell>
              </LocalizationProvider>
            </ThemeProvider>
          </DeviceInterceptor>
        </SplitFactoryProvider>
      </Provider>
    </>
  );
}

export default App;
