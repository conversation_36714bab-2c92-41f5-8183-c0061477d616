import { appStrings } from "@Assets/app_strings";
import { ProtectedRoute } from "@Components/ProtectedRoute/ProtectedRoute";
import { NurseTriageContainer } from "@Features/nurseTriage/NurseTriageContainer";
import { usePageTitle } from "@Hooks/pageTitleHook";
import { Routes } from "@Types";

const NurseTriagePage = () => {
  usePageTitle(appStrings.pageTitles.nurseTriage);

  return (
    <ProtectedRoute path={Routes.NURSE_TRIAGE}>
      <NurseTriageContainer />
    </ProtectedRoute>
  );
};

export default NurseTriagePage;
