import { Box, Button, Paper, Typography } from "@mui/material";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { AuthFormContainer } from "@Components/AuthFormContainer/AuthFormContainer";
import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";
import { useSignOutHook } from "@Hooks/signOutHook";
import { Routes } from "@Types";
import { linkifyText } from "@Utils/linkify";

const MEMBER_DEACTIVATED_STRINGS = appStrings.features.memberDeactivated;

const MemberDeactivated = () => {
  const router = useRouter();
  const { formWidth } = useResponsiveStylesHook();
  const { signOutOfApp } = useSignOutHook();

  const handleSignOut = () => {
    signOutOfApp();
    router.push(Routes.WELCOME);
  };

  return (
    <AuthFormContainer>
      <Paper sx={{ display: "flex", flexDirection: "column", gap: 5, width: formWidth }}>
        <AppIcon name="Info" size="lg" />

        <Box>
          <Typography variant="h3" mb={2}>
            {MEMBER_DEACTIVATED_STRINGS.header}
          </Typography>

          <Typography variant="body">{linkifyText(MEMBER_DEACTIVATED_STRINGS.body)}</Typography>
        </Box>

        <Button variant="primary" fullWidth onClick={handleSignOut}>
          {appStrings.buttonText.logOut}
        </Button>
      </Paper>
    </AuthFormContainer>
  );
};

export default MemberDeactivated;
