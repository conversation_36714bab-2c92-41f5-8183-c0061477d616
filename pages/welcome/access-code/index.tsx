import { appStrings } from "@Assets/app_strings";
import { PreAuthContainer } from "@Components/PreAuthContainer/PreAuthContainer";
import { AccessCodeScreenContainer } from "@Features/welcome/accessCode/AccessCodeScreenContainer";
import { usePageTitle } from "@Hooks/pageTitleHook";

const AccessCode = () => {
  usePageTitle(appStrings.pageTitles.accessCode);

  return (
    <PreAuthContainer>
      <AccessCodeScreenContainer />
    </PreAuthContainer>
  );
};

export default AccessCode;
