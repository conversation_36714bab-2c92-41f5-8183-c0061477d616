import { useSearchParams } from "next/navigation";

import { appStrings } from "@Assets/app_strings";
import { PreAuthContainer } from "@Components/PreAuthContainer/PreAuthContainer";
import { PartnerPreVerifySubscriber } from "@Features/partnerPreVerifySubscriber/PartnerPreVerifySubscriber";
import { usePageTitle } from "@Hooks/pageTitleHook";

const PartnerPreVerifySubscriberPage = () => {
  const searchParams = useSearchParams();
  const code = searchParams.get("eligibilityRecordId") ?? "";

  usePageTitle(appStrings.pageTitles.partnerPreVerify);

  return (
    <PreAuthContainer>
      <PartnerPreVerifySubscriber code={code} />
    </PreAuthContainer>
  );
};

export default PartnerPreVerifySubscriberPage;
