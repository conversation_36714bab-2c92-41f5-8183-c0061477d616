import { useEffect } from "react";
import * as Sen<PERSON> from "@sentry/nextjs";
import { useRouter } from "next/router";

import { Routes } from "@Types";
/**
 * TODO: Remove this file once the old URL is no longer in use. JL 2024-08-29
 * This page is strictly to provide backwards compatibility with the old URL (company-code) and redirect to the new URL (access-code)
 */
const CompanyCode = () => {
  const router = useRouter();

  useEffect(() => {
    Sentry.withScope((scope) => {
      scope.setLevel("log");
      scope.setFingerprint(["company-code"]);
      Sentry.captureMessage("User navigated to /welcome/company-code, redirecting to /welcome/access-code");
    });

    router.push(Routes.ACCESS_CODE);
  }, [router]);
};

export default CompanyCode;
