import { useSearchParams } from "next/navigation";

import { appStrings } from "@Assets/app_strings";
import { PreAuthContainer } from "@Components/PreAuthContainer/PreAuthContainer";
import { PartnerPreVerify } from "@Features/partnerPreVerify/PartnerPreVerify";
import { usePageTitle } from "@Hooks/pageTitleHook";

const WelcomePersonifyHealth = () => {
  const searchParams = useSearchParams();
  const code = searchParams.get("code") ?? "";

  usePageTitle(appStrings.pageTitles.partnerPreVerify);

  return (
    <PreAuthContainer>
      <PartnerPreVerify code={code} partner="PersonifyHealth" />
    </PreAuthContainer>
  );
};

export default WelcomePersonifyHealth;
