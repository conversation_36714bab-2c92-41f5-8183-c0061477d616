import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { CourseDetailScreenContainer } from "@Features/courses/components/CourseDetailScreenContainer";
import { usePageTitle } from "@Hooks/pageTitleHook";

const CourseDetail = () => {
  const router = useRouter();
  const courseId = Array.isArray(router.query.course) ? router.query.course[0] : router.query.course ?? "";

  usePageTitle(appStrings.pageTitles.courseDetails);

  return (
    <PostAuthContainer>
      <CourseDetailScreenContainer courseId={courseId} />
    </PostAuthContainer>
  );
};

export default CourseDetail;
