import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { CoursesCategoryScreenContainer } from "@Features/courses/components/CoursesCategoryScreenContainer";
import { usePageTitle } from "@Hooks/pageTitleHook";

const CoursesCategory = () => {
  const router = useRouter();
  const { category } = router.query;

  usePageTitle(appStrings.pageTitles.courseCategory);

  return (
    <PostAuthContainer>
      {/* No possibilities to become string[] thus string | undefined */}
      <CoursesCategoryScreenContainer category={category as string | undefined} />
    </PostAuthContainer>
  );
};

export default CoursesCategory;
