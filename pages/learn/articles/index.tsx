import { appStrings } from "@Assets/app_strings";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { ArticlesScreenContainer } from "@Features/articles/ArticlesScreenContainer";
import { usePageTitle } from "@Hooks/pageTitleHook";

const Article = () => {
  usePageTitle(appStrings.pageTitles.articleCatalog);

  return (
    <PostAuthContainer>
      <ArticlesScreenContainer />
    </PostAuthContainer>
  );
};

export default Article;
