import { appStrings } from "@Assets/app_strings";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { ArticleScreen } from "@Features/articles/components/ArticleScreen";
import { usePageTitle } from "@Hooks/pageTitleHook";

const ArticleDetails = () => {
  usePageTitle(appStrings.pageTitles.articleDetails);

  return (
    <PostAuthContainer>
      <ArticleScreen />
    </PostAuthContainer>
  );
};

export default ArticleDetails;
