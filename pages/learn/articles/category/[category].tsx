import { appStrings } from "@Assets/app_strings";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { ArticlesCategoryScreen } from "@Features/articles/components/ArticlesCategoryScreen";
import { usePageTitle } from "@Hooks/pageTitleHook";

const ArticlesCategory = () => {
  usePageTitle(appStrings.pageTitles.articleCategory);

  return (
    <PostAuthContainer>
      <ArticlesCategoryScreen />
    </PostAuthContainer>
  );
};

export default ArticlesCategory;
