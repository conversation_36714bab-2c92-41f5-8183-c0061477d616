import * as React from "react";
import * as Sentry from "@sentry/nextjs";
import { NextPageContext } from "next";

import { appStrings } from "@Assets/app_strings";
import { InformationCard } from "@Components/InformationCard/InformationCard";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { Routes } from "@Types";

const { generalErrorHeader, generalErrorSubheader, errorPageButtonText } = appStrings.errorPages;

function Error() {
  return (
    <PostAuthContainer>
      <InformationCard
        header={generalErrorHeader}
        subheaders={[generalErrorSubheader]}
        buttonText={errorPageButtonText}
        onClick={() => {
          window.location.replace(Routes.HOME);
        }}
      />
    </PostAuthContainer>
  );
}

Error.getInitialProps = async (contextData: NextPageContext) => {
  await Sentry.captureException(contextData);

  const { res, err } = contextData;
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404;

  return { statusCode };
};

export default Error;
