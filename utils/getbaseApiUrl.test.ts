import { describe, expect, test } from "vitest";

import { environmentVariables } from "@Config/environmentVariables";

import { getBaseApiUrl } from "./getBaseApiUrl";

describe("getBaseApiUrl", () => {
  test("Should return proper urls for dev4 environment", () => {
    process.env.NEXT_PUBLIC_BUILD_FLAVOR = "development";
    expect(getBaseApiUrl("BFF_URL")).toBe(environmentVariables.dev4.BFF_URL);
    expect(getBaseApiUrl("PASSIO_PROXY_URL")).toBe(environmentVariables.dev4.PASSIO_PROXY_URL);
    expect(getBaseApiUrl("SPLIT_IO_API_KEY")).toBe(environmentVariables.dev4.SPLIT_IO_API_KEY);
    expect(getBaseApiUrl("ARTICLE_API_URL")).toBe(environmentVariables.dev4.ARTICLE_API_URL);
    expect(getBaseApiUrl("MAIN_API_URL")).toBe(environmentVariables.dev4.MAIN_API_URL);
    expect(getBaseApiUrl("PARTNER_PREVERIFY_URL")).toBe(environmentVariables.dev4.PARTNER_PREVERIFY_URL);
  });

  test("Should return proper urls for staging environment", () => {
    process.env.NEXT_PUBLIC_BUILD_FLAVOR = "staging";
    expect(getBaseApiUrl("BFF_URL")).toBe(environmentVariables.staging.BFF_URL);
    expect(getBaseApiUrl("PASSIO_PROXY_URL")).toBe(environmentVariables.staging.PASSIO_PROXY_URL);
    expect(getBaseApiUrl("SPLIT_IO_API_KEY")).toBe(environmentVariables.staging.SPLIT_IO_API_KEY);
    expect(getBaseApiUrl("ARTICLE_API_URL")).toBe(environmentVariables.staging.ARTICLE_API_URL);
    expect(getBaseApiUrl("MAIN_API_URL")).toBe(environmentVariables.staging.MAIN_API_URL);
    expect(getBaseApiUrl("PARTNER_PREVERIFY_URL")).toBe(environmentVariables.staging.PARTNER_PREVERIFY_URL);
  });

  test("Should return proper urls for production environment", () => {
    process.env.NEXT_PUBLIC_BUILD_FLAVOR = "production";
    expect(getBaseApiUrl("BFF_URL")).toBe(environmentVariables.production.BFF_URL);
    expect(getBaseApiUrl("PASSIO_PROXY_URL")).toBe(environmentVariables.production.PASSIO_PROXY_URL);
    expect(getBaseApiUrl("SPLIT_IO_API_KEY")).toBe(environmentVariables.production.SPLIT_IO_API_KEY);
    expect(getBaseApiUrl("ARTICLE_API_URL")).toBe(environmentVariables.production.ARTICLE_API_URL);
    expect(getBaseApiUrl("MAIN_API_URL")).toBe(environmentVariables.production.MAIN_API_URL);
    expect(getBaseApiUrl("PARTNER_PREVERIFY_URL")).toBe(environmentVariables.production.PARTNER_PREVERIFY_URL);
  });
});
