import { describe, expect, test } from "vitest";

import { linkify, linkifyText } from "./linkify";

describe("linkify", () => {
  test("should convert http://, https://, and ftp:// URLs into clickable links", () => {
    const input = "Visit https://example.com and http://example.com and ftp://example.com";
    const output = linkify(input);

    expect(output).toBe(
      'Visit <a href="https://example.com" target="_blank" style="color: currentColor;">https://example.com</a> and <a href="http://example.com" target="_blank" style="color: currentColor;">http://example.com</a> and <a href="ftp://example.com" target="_blank" style="color: currentColor;">ftp://example.com</a>',
    );
  });

  test('should convert "www." URLs into clickable links', () => {
    const input = "Visit www.example.com";
    const output = linkify(input);

    expect(output).toBe(
      'Visit <a href="http://www.example.com" target="_blank" style="color: currentColor;">www.example.com</a>',
    );
  });

  test("should convert email addresses into mailto: links", () => {
    const input = "Email <NAME_EMAIL>";
    const output = linkify(input);

    expect(output).toBe(
      'Email me at <a href="mailto:<EMAIL>" style="color: currentColor;"><EMAIL></a>',
    );
  });

  test("should add attributes to existing links", () => {
    const input = '<a href="https://example.com">example</a>';
    const output = linkify(input);

    expect(output).toBe('<a href="https://example.com" target="_blank" style="color: currentColor;">example</a>');
  });
});

describe("linkifyText", () => {
  test("should convert plain text into HTML with clickable links", () => {
    const input = "Visit https://example.com";
    const output = linkifyText(input);

    expect(output).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          type: "a",
          props: {
            href: "https://example.com",
            target: "_blank",
            children: "https://example.com",
            style: { color: "currentColor" },
          },
        }),
      ]),
    );
  });

  test("should handle link text wrapped in extra tag correctly", () => {
    const input = "Visit <b>https://example.com</b>";
    const output = linkifyText(input);

    expect(output).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          type: "b",
          props: {
            children: expect.objectContaining({
              type: "a",
              props: {
                href: "https://example.com",
                target: "_blank",
                children: "https://example.com",
                style: { color: "currentColor" },
              },
            }),
          },
        }),
      ]),
    );
  });
});
