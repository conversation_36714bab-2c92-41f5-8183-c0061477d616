type BffError = {
  code: string;
  detail: string;
  title?: string;
};

export type BffJsonApiError = {
  detail: string;
  status: string;
  title?: string;
};

export type BffFetchError<T = BffError> = {
  data: {
    errors: T[];
    status_code: number;
  };
  status: number;
};

export const isFetchFromBffError = <T = BffError>(error: unknown): error is BffFetchError<T> => {
  return (
    error != null &&
    typeof error === "object" &&
    "data" in error &&
    error.data != null &&
    typeof error.data === "object" &&
    "errors" in error.data
  );
};
