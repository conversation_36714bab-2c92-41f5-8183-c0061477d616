import { VivanteApiError, VivanteException } from "@vivantehealth/vivante-core";
import * as Sentry from "@sentry/nextjs";

import { errorDispatcher, errorSnackbarDispatcher } from "@Features/error/utils/errorDispatcher";
import { SnackbarToggleState } from "@Features/snackbar/store/snackbarStateSlice";
import { RootState } from "@Store/store";

const isVivanteException = (error: unknown): error is VivanteException => {
  return VivanteException.isVivanteException(error);
};

const getErrorMessage = (error: VivanteApiError | Error | VivanteException | unknown) => {
  if (error instanceof VivanteApiError) {
    return error.detail;
  }

  if (error instanceof Error || isVivanteException(error)) {
    return error.message;
  }

  return "An unexpected error has occurred";
};

/**
 * Creates a function which allows typed extraction of slice attributes
 */
export const buildSliceStateSelector = <SLICEKEY extends keyof RootState>(sliceKey: SLICEKEY) => {
  return <ATTRIBUTE extends keyof RootState[SLICEKEY]>(attribute: ATTRIBUTE) =>
    (state: RootState): RootState[SLICEKEY][ATTRIBUTE] => {
      const slice = state[sliceKey];

      return slice[attribute];
    };
};

type ProcessErrorPropsBase = {
  error: VivanteApiError | Error | VivanteException | unknown;
};

type ProcessErrorWithoutNotification = ProcessErrorPropsBase & {
  errorDisplayType?: never;
  snackBarOptions?: never;
};

type ProcessErrorWithModal = ProcessErrorPropsBase & {
  errorDisplayType: "modal";
  snackBarOptions?: never;
};

type ProcessErrorWithSnackBar = ProcessErrorPropsBase & {
  errorDisplayType: "snackbar";
  snackBarOptions?: Partial<SnackbarToggleState>;
};

type ProcessErrorProps = ProcessErrorWithoutNotification | ProcessErrorWithModal | ProcessErrorWithSnackBar;

/**
 * Processes an error. If the error is an instance of VivanteApiError or Error,
 * it will be logged to Sentry. Additionally notification can be dispatched.
 * Only one form of notification can be displayed at a time.
 *
 * @param error Error to process
 * @param errorDisplayType Either 'modal' or 'snackbar'
 * @param snackBarOptions Options for the snack bar
 */
export const processError = ({
  error,
  errorDisplayType,
  snackBarOptions = { variant: "error" },
}: ProcessErrorProps) => {
  const message = getErrorMessage(error);

  if (error instanceof VivanteApiError || error instanceof Error || VivanteException.isVivanteException(error)) {
    Sentry.withScope((scope) => {
      scope.setLevel("error");
      Sentry.captureException(new Error(message));
    });
  }

  if (errorDisplayType === "modal") {
    errorDispatcher({
      message,
    });
  } else if (errorDisplayType === "snackbar") {
    errorSnackbarDispatcher({ isOpen: true, message, ...snackBarOptions });
  }
};
