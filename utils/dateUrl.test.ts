import { describe, it, expect } from "vitest";

import { encodeDateForURL, decodeDateFromURL } from "./dateUrl";

describe("encodeDateForURL", () => {
  it("should correctly encode a date object to a URL-safe string", () => {
    const date = new Date("2023-04-01T12:00:00Z");
    const encodedDate = encodeDateForURL(date);

    expect(encodedDate).toBe("2023-04-01");
  });
});

describe("decodeDateFromURL", () => {
  it("should correctly decode a date string to a date object", () => {
    const encodedDateString = "2023-04-01";
    const date = decodeDateFromURL(encodedDateString);

    expect(date).toEqual(new Date("2023-04-01T00:00:00.000Z"));
  });

  it("should return a today date object for invalid date format", () => {
    const invalidDateString = "invalid-date";
    const date = decodeDateFromURL(invalidDateString);

    expect(date).toBeInstanceOf(Date);
    expect(date.toString()).not.toBe("Invalid Date");
  });
});
