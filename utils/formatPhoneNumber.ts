/**
 * Formats a string into a 10 digit phone number format (XXX-XXX-XXXX).
 */
export const formatPhoneNumber = (phoneNumber: string) => {
  const sanitizedPhoneNumber = phoneNumber.replace(/\D/g, "");
  const phoneNumberLength = sanitizedPhoneNumber.length;

  if (phoneNumberLength <= 3) {
    return sanitizedPhoneNumber;
  }

  if (phoneNumberLength <= 6) {
    return `${sanitizedPhoneNumber.slice(0, 3)}-${sanitizedPhoneNumber.slice(3)}`;
  }

  return `${sanitizedPhoneNumber.slice(0, 3)}-${sanitizedPhoneNumber.slice(3, 6)}-${sanitizedPhoneNumber.slice(6, 10)}`;
};
