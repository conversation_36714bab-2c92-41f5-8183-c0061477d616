import { describe, test, expect } from "vitest";

import { formatPhoneNumber } from "./formatPhoneNumber";

describe("formatPhoneNumber", () => {
  test("Should format a 10 digit number with proper dashes", () => {
    expect(formatPhoneNumber("1234567890")).toBe("************");
  });

  test("Should maintain properly formatted number", () => {
    expect(formatPhoneNumber("************")).toBe("************");
  });

  test("Should not add a dash after the first 3 digits", () => {
    expect(formatPhoneNumber("123")).toBe("123");
  });

  test("Should add a dash after the first 4 digits", () => {
    expect(formatPhoneNumber("1234")).toBe("123-4");
  });

  test("Should add a dash after the first 3 digits and the next 4 digits", () => {
    expect(formatPhoneNumber("123-4567")).toBe("123-456-7");
  });

  test("Should remove any non-numeric characters", () => {
    expect(formatPhoneNumber("12a")).toBe("12");
    expect(formatPhoneNumber("123-5a5")).toBe("123-55");
    expect(formatPhoneNumber("123-5a5-5")).toBe("123-555");
    expect(formatPhoneNumber("123-5a5-565!!!")).toBe("123-555-65");
  });

  test("Should truncate a number longer than 10 digits", () => {
    expect(formatPhoneNumber("12345678901234567890")).toBe("************");
  });
});
