import * as Sentry from "@sentry/nextjs";
/**
 * Constant that represents the level of each log.
 * This is used to determine what to log based on the Logger's level.
 * */
const LOGGER_LEVEL = {
  info: 1,
  warn: 2,
  error: 3,
} as const;

type LoggerName = keyof typeof LOGGER_LEVEL;

class Logger {
  /**
   * Variable to set which kind of logs to be shown in running server.
   * If you set it to lower level, it will show all logs above that level.
   * Its default value is `info`.
   *
   *
   * 1. `info`: show all logs
   * 2. `warn`: show only warn and error logs
   * 3. `error`: show only error logs
   *  */
  private loggerLevel: (typeof LOGGER_LEVEL)[LoggerName];

  constructor(loggerLevel: LoggerName = "info") {
    this.loggerLevel = LOGGER_LEVEL[loggerLevel];
  }

  private stringifyErrorForLogging(error: unknown[]) {
    return error
      .map((data) => {
        if (typeof data === "object") {
          // Use JSON.stringify to log objects properly
          return JSON.stringify(data);
        }

        return data?.toString();
      })
      .join(", ");
  }

  public info(...additionalData: unknown[]): void {
    if (this.loggerLevel > LOGGER_LEVEL.info) {
      return;
    }

    this.emitLogMessage("info", additionalData);
  }

  public warn(...additionalData: unknown[]): void {
    if (this.loggerLevel > LOGGER_LEVEL.warn) {
      return;
    }

    Sentry.captureMessage(this.stringifyErrorForLogging(additionalData), "warning");

    this.emitLogMessage("warn", additionalData);
  }

  public error(...additionalData: unknown[]): void {
    Sentry.captureMessage(this.stringifyErrorForLogging(additionalData), "error");

    this.emitLogMessage("error", additionalData);
  }

  private emitLogMessage(massageType: LoggerName, ...additionalData: unknown[]): void {
    const data = additionalData.length > 0 ? additionalData : "";

    // Do not log if running in test environment
    if (process.env.VITEST === "true") return;

    // eslint-disable-next-line no-console
    console[massageType](...data);
  }
}

// Change here to switch the logger level
export const logger = new Logger("info");
