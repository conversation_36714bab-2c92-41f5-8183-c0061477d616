import parse from "html-react-parser";

export const linkify = (plainText: string): string => {
  let replacedText: string;

  // add attributes to existing links
  replacedText = plainText.replace(
    /<a\s([^>]*)href="([^"]*)">([^<]*)<\/a>/gi,
    '<a $1href="$2" target="_blank" style="color: currentColor;">$3</a>',
  );

  // linkify URLs starting with http://, https://, or ftp://
  replacedText = replacedText.replace(
    /(^|\s)(<[^>]+>)?(\b(https?|ftp):\/\/[-A-Z0-9+&@#/%?=~_|!:,.;]*[-A-Z0-9+&@#/%=~_|])(<\/[^>]+>)?/gim,
    '$1$2<a href="$3" target="_blank" style="color: currentColor;">$3</a>$5',
  );

  // linkify URLs starting with "www." (without // before it, or it'd re-link the ones done above).
  replacedText = replacedText.replace(
    /(^|\s)(<[^>]+>)?(www\.[\S]+(\b|$))(<\/[^>]+>)?/gim,
    '$1$2<a href="http://$3" target="_blank" style="color: currentColor;">$3</a>$5',
  );

  // Change email addresses to mailto:: links.
  replacedText = replacedText.replace(
    /(^|\s)(<[^>]+>)?(([a-zA-Z0-9\-_.])+@[a-zA-Z_]+?(\.[a-zA-Z]{2,14})+)(<\/[^>]+>)?/gim,
    '$1$2<a href="mailto:$3" style="color: currentColor;">$3</a>$6',
  );

  return replacedText;
};

export const linkifyText = (text: string) => parse(linkify(text));
