import type { Practitioner } from "@vivantehealth/vivante-core";
import { describe, test, expect } from "vitest";

import { getPractitionerDisplayableRole } from "./getPractitionerDisplayableRole";

describe("getPractitionerDisplayableRole", () => {
  const basePractitioner: Practitioner = {
    firstName: "John",
    lastName: "Doe",
    role: "",
    type: "",
    displayableRole: "",
    avatarLink: "",
    id: "1234",
  };

  test("Returns Care Guide for role CG", () => {
    expect(getPractitionerDisplayableRole({ ...basePractitioner, role: "CG" })).toBe("Care Guide");
  });

  test("Returns Gastroenterology for role MD and type GI", () => {
    expect(getPractitionerDisplayableRole({ ...basePractitioner, role: "MD", type: "GI" })).toBe("Gastroenterology");
  });

  test("Returns Internal Medicine for role MD and type INT and displayableRole of Internal Medicine", () => {
    expect(
      getPractitionerDisplayableRole({
        ...basePractitioner,
        role: "MD",
        type: "INT",
        displayableRole: "Internal Medicine",
      }),
    ).toBe("Internal Medicine");
  });

  test("Returns Internal Medicine for role MD and type INT and displayableRole of Family Medicine", () => {
    expect(
      getPractitionerDisplayableRole({
        ...basePractitioner,
        role: "MD",
        type: "INT",
        displayableRole: "Family Medicine",
      }),
    ).toBe("Family Medicine");
  });

  test("Returns Medical Doctor for role MD and unknown type", () => {
    expect(getPractitionerDisplayableRole({ ...basePractitioner, role: "MD", type: "OTHER" })).toBe("Medical Doctor");
  });

  test("Returns Physician Assistant for role PA", () => {
    expect(getPractitionerDisplayableRole({ ...basePractitioner, role: "PA" })).toBe("Gastroenterology");
  });

  test("Returns displayableRole if role is unknown", () => {
    expect(getPractitionerDisplayableRole({ ...basePractitioner, role: "XYZ", displayableRole: "Custom Role" })).toBe(
      "Custom Role",
    );
  });

  test("Returns empty string if role is unknown and displayableRole is undefined", () => {
    expect(getPractitionerDisplayableRole({ ...basePractitioner, role: "XYZ" })).toBe("");
  });
});
