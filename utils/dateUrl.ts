import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";

dayjs.extend(customParseFormat);

const DATE_FORMAT = "YYYY-MM-DD";

export const encodeDateForURL = (newDay: Date): string => {
  const formattedDate = dayjs(newDay).format(DATE_FORMAT);
  const encodedDate = encodeURIComponent(formattedDate);

  return encodedDate;
};

export const decodeDateFromURL = (encodedDateString: string): Date => {
  const decodedDateString = decodeURIComponent(encodedDateString);
  const parsedDate = dayjs(decodedDateString, DATE_FORMAT);

  if (!parsedDate.isValid()) {
    return dayjs().toDate();
  }

  return parsedDate.toDate();
};
