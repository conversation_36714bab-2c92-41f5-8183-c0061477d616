import { test, describe, expect } from "vitest";

import { isOfType } from "./isOfType";

type TestType = Readonly<{
  name: string;
  age: number;
}>;

describe("isOfType", () => {
  test("Should return true for valid object", () => {
    const data = { name: "<PERSON>", age: 30 };
    const result = isOfType<TestType>(data, ["name", "age"]);

    expect(result).toBe(true);
  });

  test("Should return false for object missing keys", () => {
    const data = { name: "<PERSON>" };
    const result = isOfType<TestType>(data, ["name", "age"]);

    expect(result).toBe(false);
  });

  test("Should return false for null data", () => {
    const data = null;
    const result = isOfType<TestType>(data, ["name", "age"]);

    expect(result).toBe(false);
  });

  test("Should return false for non-object data", () => {
    const data = "string";
    const result = isOfType<TestType>(data, ["name", "age"]);

    expect(result).toBe(false);
  });

  test("Should return false for object with extra keys", () => {
    const data = { name: "<PERSON>", age: 30, extra: "extra" };
    const result = isOfType<TestType>(data, ["name", "age"]);

    expect(result).toBe(true);
  });
});
