import { describe, test, expect } from "vitest";

import { isFetchFromBffError } from "./isFetchFromBffError";

describe("isFetchFromBffError", () => {
  test("Should return true for a valid BffFetchError", () => {
    const error = {
      data: {
        errors: [
          {
            code: "123",
            detail: "Some error detail",
          },
        ],
        status_code: 400,
      },
      status: 400,
    };

    expect(isFetchFromBffError(error)).toBe(true);
  });

  test("Should return false for an error without data", () => {
    const error = {
      status: 400,
    };

    expect(isFetchFromBffError(error)).toBe(false);
  });

  test("Should return false for an error with data but without errors", () => {
    const error = {
      data: {
        status_code: 400,
      },
      status: 400,
    };

    expect(isFetchFromBffError(error)).toBe(false);
  });

  test("Should return false for null", () => {
    expect(isFetchFromBffError(null)).toBe(false);
  });

  test("Should return false for a non-object error", () => {
    expect(isFetchFromBffError("string error")).toBe(false);
  });

  test("Should return false for an empty object", () => {
    expect(isFetchFromBffError({})).toBe(false);
  });
});
