import { Practitioner } from "@vivantehealth/vivante-core";

import { appStrings } from "@Assets/app_strings";

const CARE_TEAM_STRINGS = appStrings.features.careTeam;

export const getPractitionerDisplayableRole = (practitioner: Practitioner) => {
  if (practitioner.displayableRole && practitioner.displayableRole.length) return practitioner.displayableRole;

  if (practitioner.role === "CG") {
    return CARE_TEAM_STRINGS.careGuide;
  }

  if (practitioner.role === "MD") {
    if (practitioner.type === "GI") return CARE_TEAM_STRINGS.gastroenterology;
    if (practitioner.type === "INT") return practitioner.displayableRole;

    return CARE_TEAM_STRINGS.medicalDoctor;
  }

  if (practitioner.role === "PA") {
    return CARE_TEAM_STRINGS.physicianAssistant;
  }

  return "";
};
