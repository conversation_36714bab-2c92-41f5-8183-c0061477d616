import { describe, expect, test } from "vitest";

import { sentenceCaseLabel } from "./sentenceCaseLabel";

describe("sentenceCaseLabel", () => {
  test("Should convert the first letter of a sentence to uppercase and the rest to lowercase", () => {
    expect(sentenceCaseLabel("Hello world")).toBe("Hello world");
    expect(sentenceCaseLabel("hello World")).toBe("Hello world");
    expect(sentenceCaseLabel("HELLO WORLD")).toBe("Hello world");
    expect(sentenceCaseLabel("hElLo WoRlD")).toBe("Hello world");
  });

  test("Should return an empty string if the input is empty", () => {
    expect(sentenceCaseLabel("")).toBe("");
  });
});
