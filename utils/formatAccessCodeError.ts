import { VivanteException } from "@vivantehealth/vivante-core";

const isVivanteExceptiopn = (error: unknown): error is VivanteException => {
  return VivanteException.isVivanteException(error);
};

const isErrorWithMessage = (error: unknown): error is { error: { message: string } } => {
  return (
    error != null &&
    typeof error === "object" &&
    "error" in error &&
    error.error != null &&
    typeof error.error === "object" &&
    "message" in error.error
  );
};

export const formatAccessCodeError = (error: unknown) => {
  if (isVivanteExceptiopn(error) && error.shouldDisplay) {
    return new Error(error.message);
  }

  return new Error(isErrorWithMessage(error) ? error.error.message : "Unknown error");
};
