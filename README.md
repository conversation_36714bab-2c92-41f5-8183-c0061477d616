# githrive-reactweb
Cylinder web application written in React.js

# Style Guide
As we introduce and establish style guide rulings, we will update here.

- Design System
    - [Figma Design System](https://www.figma.com/design/9zYJO7UGJgB41H9nuwNTc5/Thrive-DS-v2)
    - [Rebrand Design Mockups](https://www.figma.com/design/8tkrRyyha0TKlyV8ZUfICc/Rebrand-Phase-1---Product-UI)
    - [Design token repository](https://github.com/vivantehealth/design-tokens)
        - Any changes to design tokens should be made in this repository followed by updating the library version in web and mobile to import the new changes
- Follow MaterialUI component usage
    - We should utilize MUI components as best as we can before creating our own components
        - If the MUI component is a composable component (eg `Dialog`) and we find that we have a common customized use case across files, this would be a use case to create our own component (eg `BaseModal`) utilizing the base MUI component
    - Create a theme file for MUI components as needed
        - Folder location: `theme/components/ComponentNameTheme.ts`
        - Add theme setup to `theme/cylinderTheme.ts`
    - Favor `sx` property with inline styles as needed. If the styles is expansive and impacts readability, than we should consider moving the styles to a separate object
        - When using `sx`, we should use MUI spacing tokens which we have setup through MUI theming to correlate to our tokens (eg 1 = 4px, 2 = 8px, etc). 
        - If unable to use the `sx` property (eg a non-MUI component), we have our spacing tokens in `assets/style_constants.ts` and should utilize those (eg `SPACING_4_PX`, `SPACING_8_PX`, etc)
    - Verify component usage instructions to ensure we are implementing the component properly for a11y
- Constants
    - Place all strings into `assets/app_strings.ts`
        - If you are referencing the same subsection of app strings two or more times in a component, you should create a constant outside of the component to use. You may end up with several constants if you're referencing multiple subsections.
            - `const MY_FEATURE_STRINGS = appStrings.features.myFeature;`
    - Global style constants should be placed in `assets/style_constants.ts`
    - Variable constants used throughout multiple files should be placed in the `assets` folder of its feature OR if global, placed in `assets/constants.ts`
- Folder Structure
    - We use a structure similar to [Bulletproof React](https://github.com/alan2207/bulletproof-react/blob/master/docs/project-structure.md)
        - We will *NOT* use `index.ts` or `index.tsx`, we should have the entry point Component at the root
        - Naming
            - `jsx`/`tsx` files should be PascalCase `ComponentName.tsx`
                - Corresponding component folder (if one exists) should be PascalCase `components/ComponentName/ComponentName.tsx`
            - Feature folder name should be camcelCase, `features/featureName`
        - Do *NOT* nest components more than one level deep (eg `src/features/myFeature/component/subFeature/component` is okay, but not further)
        - `store` will be the folder in which we place all Redux related files (slices, epics, etc)
            - Global store files sit in the root level `store` folder 
                - `store`
            - Store slices and epics should be placed in specific `features` even if they do not have any specific components
                - `features/featureName/store/featureNameSlice.ts`
        - Storybook files should sit next to their component and use the naming `ComponentName.stories.tsx`
            - There is one exception to this rule: when you are theming a MUI Component (so you don't have a component file or component folder), use the `components/stories` folder
        - Test files should sit next to their file and use the naming
            - `ComponentName.test.ts`
            - `functionName.test.ts`
        - Only add respective folders as needed, don't add them if they'll be empty
        - As we are using `NextJS`, we will use the `pages` folder instead of Bulletproof's `routes` folder

# Testing Cylinder against GIThrive
If an issue arises that we are trying to determine if it existed in GIThrive, you can pull down the [`githrive-assets-DO-NOT-DELETE` branch ](https://github.com/vivantehealth/githrive-reactweb/tree/githrive-assets-DO-NOT-DELETE) and test. This branch maintains the feature flag implementation that is set to `on` for localhost. You can go into `_app.tsx` and change line 44 to `'off'` to enable GIThrive isntead of Cylinder.

# Setup and Running Locally

## Prerequisites

- [Node.js 22](https://nodejs.org/en/download)
- [Yarn v1.22](https://classic.yarnpkg.com/lang/en/docs/install)

## Private npm registry

To access packages in our private github package registry, a personal access token needs to be included in the `~/.npmrc` file. Example:

```
//npm.pkg.github.com/:_authToken=THE_GITHUB_REGISTRY_PERSONAL_ACCESS_TOKEN
@vivantehealth:registry=https://npm.pkg.github.com/
```

_Github PATs can be generated at https://github.com/settings/tokens (make sure to allow single sign-on for the token after it's generated)_

## Running Locally

1. `yarn install`
2. `yarn dev`

Once run, the web application will be served at <a href="http://localhost:3000">`http://localhost:3000`</a>

## Testing

Testing is handled by [Vitest](https://vitest.dev/) (unit and integration tests) and [Playwright](https://playwright.dev/) (E2E tests). 

### Vitest

To run Vitest, use `yarn test` in your terminal or you can use the [Vitest VS Code extension](https://marketplace.visualstudio.com/items?itemName=vitest.explorer) to run directly within VS Code. This will run all available test files where the file name matches `*.test.ts?(x)`. We should aim to write tests as a develop new functionality or when correcting a bug that would have benefited from having a test in place to avoid potential regressions. 

### Playwright

To run Playwright tests, you'll need to create a local .env file in root folder with the following properties
```ts
TEST_USER_EMAIL="<EMAIL>"
TEST_USER_PASSWORD="your_test_account_password"
```
 followed by running one of the following commands
- `yarn test:e2e` : will start the interactive [UI mode](https://playwright.dev/docs/test-ui-mode) for testing 
- `yarn test:e2e:headless` : will run the E2E tests in headless ui mode. By default, all tests will be ran against Chromium, Firefox, and Webkit.

You can also install the [Playwright VS Code extension](https://marketplace.visualstudio.com/items?itemName=ms-playwright.playwright) to run directly within VS Code. [Using the VS Code extension allows for directly debugging tests without having to leave VS Code.](https://playwright.dev/docs/getting-started-vscode#debugging-tests)

Tests can either be written manually, [record a test via VS Code extension ](https://playwright.dev/docs/getting-started-vscode#record-a-new-test), or [record a test using Codegen](https://playwright.dev/docs/codegen-intro). [Recording a test](https://playwright.dev/docs/codegen) allows for performing actions directly in the browsers and have the tests automatically generated. These tests should still be carefully reviewed and modified. 
We should add E2E tests sparingly. They should cover the most critical paths to ensure no breaking changes are introduced. 
To add new tests, add them to the `tests` folder. They should follow the naming format of `*.spec.ts` (this ensures that Vitest does not try and run these tests while Playwright will not try to run `*.test.ts` files). 

## Linking local dependencies

To link a local dependency (eg `@vivantehealth/vivante-core`, `@vivantehealth/design-tokens`) for testing, you'll need to uncomment the line in `next.config.ts` under the `experimental` property `esmExternals: "loose"`. You'll then want to use `yarn dev:linked` instead of `yarn dev` as Turbopack does not support reading files/symlinks outside the project directory so we need to run the dev environment using webpack.

## Other Yarn Commands

* `yarn storybook` runs opens storybook at <a href="http://localhost:6006">`http://localhost:6006`</a>
* `yarn lint` runs linter
* `yarn test` runs vitest
* `yarn test:coveragge` run vitest with coverage
* `yarn start` requires a `yarn build` to be run first

## Run Built version

1. `yarn build`
2. `npx serve out`

Once run, the web application will be served at <a href="http://localhost:3000">`http://localhost:3000`</a>

## Sign Up Wizard In Running App

* Company ids will work based on what `env` you are in.
    * dev: `delphi`
    * staging: `stagingDelphiQA`
    * prod: `productionDelphiQA`

* First name
    * has to be `Homer` (required to bypass eligibility check)

* Last name and phone
    * have to be unique (duplicates from other signups will not go through)

* Specific signup question answers (needed in order to see specific UI cards)
    * Immediate medical attention: `yes`, in order to see urgent assistance screen
    * `Yes` to gastrointestinal condition
    * Which of following conditions:
        * `Yes` to Chron’s disease
        * `Yes` to IBS


## Testing Accessibility in Chrome
* Use `Chromvox Screen Reader`
* Due to an as-of-yet unresolved bug of not being able to turn off `chromevox`, set up a new profile in chrome for accessibility testing.
* Download chrome extension [chromevox](https://chrome.google.com/webstore/detail/screen-reader/kgejglhpjiefppelpmljglcjbhoiplfn)
* You can watch [this video](https://github.com/vivantehealth/githrive-reactweb/assets/7016167/489c1ffc-9c05-43b8-ab9b-5345fb449f66) for a walk through
* Go to any page, and use the tab index to navigate through the page
* Alternatively, you can click on the page to have `chromevox` read for you.

## Aria Labels
* Screen Readers will read ariaLabels without leaving space in between. In order to accomodate that we use punctuation to slow it down. In aria-labels throughout the codebase, you will see `:` `,` and `.` added in. This is the reason.