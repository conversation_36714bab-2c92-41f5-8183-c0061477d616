next-routes.conf

# VSCode configs
/.vscode

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo

# IDE - VSCode
.vscode/*
.idea

.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

.npmrc

# Sentry Config File
.sentryclirc

# Playwright
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/
