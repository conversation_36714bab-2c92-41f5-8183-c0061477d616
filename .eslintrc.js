module.exports = {
  parser: "@typescript-eslint/parser", // Specifies the ESLint parser
  plugins: ["prettier", "@typescript-eslint", "@stylistic", "jest-dom", "testing-library", "@vitest"],
  extends: ["plugin:storybook/recommended", "eslint:recommended", "plugin:@typescript-eslint/strict", "next", "prettier"],
  rules: {
    // Prohibits "true" value to be passed explicitly on boolean props
    "react/jsx-boolean-value": ["error", "never"],
    "no-console": "error", // use logger function instead
    "import/no-default-export": "error",
    "@next/next/no-img-element": "off",
    "@stylistic/padding-line-between-statements": [
      "error",
      // variables
      { blankLine: "always", prev: ["const", "let", "var"], next: "*" },
      { blankLine: "any", prev: ["const", "let", "var"], next: ["const", "let", "var"] },
      // functions/statements
      { blankLine: "always", prev: ["function", "for", "if"], next: "*" },
      { blankLine: "any", prev: ["function", "for", "if"], next: ["function", "for", "if"] },
    ],
     // This is the rule that specifies import order across the project
    "import/order": [
      "error",
      {
        "newlines-between": "always",
        groups: [
          ["builtin", "external"], // <- Dependency imports
          "internal", // <- Absolute imports
          ["sibling", "parent"], // <- Relative path imports
        ],
        // Special treatment for specific modules to set them in the top of the imports
        pathGroups: [
          {
            pattern: "react",
            group: "builtin",
            position: "before",
          },
          {
            pattern: "react-redux",
            group: "builtin",
            position: "before",
          },
          {
            pattern: "@vivantehealth/vivante-core",
            group: "builtin",
            position: "before",
          },
        ],
        distinctGroup: false, // Sets if the groups can be mingled together
        pathGroupsExcludedImportTypes: ["builtin"], // Sets to exclude the builtin imports from the pathGroups
        alphabetize: {
          order: "asc" /* sort in ascending order. Options: ["ignore", "asc", "desc"] */,
          caseInsensitive: true /* ignore case. Options: [true, false] */,
        },
      },
    ],
    "no-unused-expressions": "off",
    "@typescript-eslint/no-unused-expressions": ["error", {"allowShortCircuit": true, "allowTernary": true}],
    "@typescript-eslint/no-unused-vars": ["error", { "caughtErrors": "none"}]
  },
  overrides: [{
    files: ["*.stories.tsx", "*.stories.ts", ".storybook/*.ts"],
    rules: {
      "import/no-default-export": "off",
      "no-console": "off"
    }
  }, {
    files: ["pages/**/*.tsx", "vitest.config.mjs", "playwright.config.ts", "empty-module.ts"],
    rules: {
      "import/no-default-export": "off",
    }
  }, {
    files: ["**/api/*.ts"],
    /**
     * Turn off this rule for api files as RTK Query convention states to use void as ArgType when no arg is provided 
     * https://redux-toolkit.js.org/rtk-query/usage/queries#defining-query-endpoints
     */
    rules: {
      "@typescript-eslint/no-invalid-void-type": "off"
    }
  },
  {
    files: "*.spec.ts",
    extends: "plugin:playwright/recommended",
  },
  {
    files: ["*.test.ts", "*.test.tsx"],
    extends: ["plugin:testing-library/react", "plugin:jest-dom/recommended", "plugin:@vitest/legacy-recommended"],
    rules: {"testing-library/no-node-access": ["error", {"allowContainerFirstChild": true}]}
  }
]
}
