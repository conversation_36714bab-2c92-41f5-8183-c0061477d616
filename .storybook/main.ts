import type { StorybookConfig } from '@storybook/nextjs'
import path from 'path';
import webpack from 'webpack';

const config: StorybookConfig = {
  stories: [
    '../stories/**/*.mdx',
    '../**/*.stories.@(js|jsx|mjs|ts|tsx)',
  ],

  addons: [
    '@storybook/addon-links',
    '@storybook/addon-onboarding',
    '@storybook/addon-docs'
  ],

  framework: {
    name: '@storybook/nextjs',
    options: {},
  },

  docs: {},

  webpackFinal: async (config) => {
    config.module = config.module || {};
    config.module.rules = config.module.rules || [];
    config.plugins = config.plugins || []

    // This modifies the existing image rule to exclude .svg files
    // since you want to handle those files with @svgr/webpack
    const imageRule = config.module.rules.find((rule) => rule?.['test']?.test('.svg'));

    if (imageRule) {
      imageRule['exclude'] = /\.svg$/;
    }

    // to mock responsive width hook for GIThrive
    config.plugins.push(
      new webpack.NormalModuleReplacementPlugin(
        /common\/hooks\/responsiveStylesHook/,
        path.resolve(__dirname, './mockResponsiveStylesHook')
      )
    );
    // To mock responsive width hook for Cylinder
    config.plugins.push(
      new webpack.NormalModuleReplacementPlugin(
        /@Cylinder\/hooks\/responsiveStylesHook/,
        path.resolve(__dirname, './mockResponsiveStylesHook')
      )
    );
    // Configure .svg files to be loaded with @svgr/webpack
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });

    return config;
  },

  typescript: {
    reactDocgen: 'react-docgen-typescript'
  },

  core: {
    disableTelemetry: true
  }
}

export default config
