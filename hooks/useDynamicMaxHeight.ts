import { useEffect, useState } from "react";

export const useDynamicMaxHeight = (heightToRemove: number) => {
  const [maxHeight, setMaxHeight] = useState(window.innerHeight - heightToRemove);

  useEffect(() => {
    const handleResize = () => {
      setMaxHeight(window.innerHeight - heightToRemove);
    };

    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, [heightToRemove]);

  return { maxHeight } as const;
};
