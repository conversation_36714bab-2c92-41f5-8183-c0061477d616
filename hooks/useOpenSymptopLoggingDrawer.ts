import { useDispatch } from "react-redux";

import { ProgressStateSlice } from "@Features/progress/store/progressStateSlice";
import { SymptomLoggingStateSlice } from "@Features/symptomLogging/store/symptomLoggingStateSlice";

const DEFAULT_DRAWER_OPEN_DELAY = 500;
const { toggleSymptomLoggingDrawer, toggleReturnToCarePlan, resetToCurrentDayAndMonth } = ProgressStateSlice.actions;
const { prepareCreateSymptomLogQuestion } = SymptomLoggingStateSlice.actions;

export const useOpenSymptomLoggingDrawer = (returnToCarePlan: boolean, drawerOpenDelay?: number) => {
  const dispatch = useDispatch();

  const triggerDrawer = () => {
    /**
     * Reset the current date to today to ensure logging is done for the correct day
     * as the user may have selected to add tracking to a different day before.
     * We do this outside of the timeout to ensure the date is reset before the drawer opens
     */
    dispatch(resetToCurrentDayAndMonth());
    // Add a slight delay to allow the page to open prior to opening drawer
    setTimeout(() => {
      dispatch(prepareCreateSymptomLogQuestion());
      dispatch(toggleSymptomLoggingDrawer(true));
      dispatch(toggleReturnToCarePlan(returnToCarePlan));
    }, drawerOpenDelay ?? DEFAULT_DRAWER_OPEN_DELAY);
  };

  return triggerDrawer;
};
