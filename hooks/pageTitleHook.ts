import { useEffect } from "react";
import { useDispatch } from "react-redux";

import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";

export const usePageTitle = (title: string) => {
  const dispatch = useDispatch();

  useEffect(() => {
    setTimeout(() => {
      dispatch(NavigationStateSlice.actions.pageTitleChanged(title));
    }, 1);
  }, [dispatch, title]);
};
