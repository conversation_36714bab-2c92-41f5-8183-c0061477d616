import { useEffect, useRef, useState } from "react";

type LoaderStatus = "SHOW_CONTENT" | "DELAYED" | "SHOW_LOADER" | "MINIMUM_TIME_SHOWN";

const DEFAULT_DELAY = 250;
const DEFAULT_MINIMUM_SHOW_TIME = 750;

/**
 * Borrowed implementation details from: https://github.com/smeijer/spin-delay
 * Modified to use for our use case where we want to have 3 different states to work with
 * @returns
 * DELAYED = display null (or other content)
 * SHOW_LOADER | MINIMUM_TIME_SHOWN = display loader
 * SHOW_CONTENT = display loaded content
 */
export const useDelayedLoader = (
  loading: boolean,
  delay = DEFAULT_DELAY,
  minimumShowTime = DEFAULT_MINIMUM_SHOW_TIME,
) => {
  const [loaderStatus, setLoaderStatus] = useState<LoaderStatus>("DELAYED");
  const timeoutIdRef = useRef<NodeJS.Timeout | null>(null);

  const clearExistingTimeout = () => {
    if (timeoutIdRef.current) {
      clearTimeout(timeoutIdRef.current);
    }
  };

  useEffect(() => {
    if (loading && ["DELAYED", "SHOW_CONTENT"].includes(loaderStatus)) {
      clearExistingTimeout();

      timeoutIdRef.current = setTimeout(() => {
        if (!loading) {
          return setLoaderStatus("SHOW_CONTENT");
        }

        timeoutIdRef.current = setTimeout(() => {
          setLoaderStatus("MINIMUM_TIME_SHOWN");
        }, minimumShowTime);

        setLoaderStatus("SHOW_LOADER");
      }, delay);

      setLoaderStatus("DELAYED");
    }

    if (!loading && loaderStatus !== "SHOW_LOADER") {
      clearExistingTimeout();
      setLoaderStatus("SHOW_CONTENT");
    }
  }, [loading, loaderStatus, delay, minimumShowTime]);

  useEffect(() => {
    return () => clearExistingTimeout();
  }, []);

  return loaderStatus;
};
