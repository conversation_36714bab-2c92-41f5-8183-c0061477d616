// import { useSelector } from "react-redux";
import { SetNotificationsPreferenceProps } from "@vivantehealth/vivante-core";

import { styleConstants } from "@Assets/style_constants";
// import { memberPreferencesStateSelector } from "@Features/memberPreferences/store/memberPreferencesStateSlice";

export const useHeaderHook = () => {
  // uncomment these if announcement needs to be displayed
  // const memberPreferences = useSelector(memberPreferencesStateSelector("memberPreferences"));
  // const preferencesLoader = useSelector(memberPreferencesStateSelector("loadState"));
  const bannerName: SetNotificationsPreferenceProps["preferenceId"] = "cylinder";
  const isBannerVisible = false;
  // const isBannerVisible = preferencesLoader === "loading" ? false : memberPreferences?.preferences?.notifications?.[bannerName] ?? true;
  const topHeight = isBannerVisible
    ? `calc(${styleConstants.headerHeight} + ${styleConstants.bannerHeight})`
    : styleConstants.headerHeight;

  const contentHeight = `calc(100vh - ${topHeight})`;

  return {
    topHeight,
    contentHeight,
    bannerName,
    isBannerVisible,
  };
};
