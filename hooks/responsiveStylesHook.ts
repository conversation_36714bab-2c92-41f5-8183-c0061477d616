import { useSelector } from "react-redux";
import { useMediaQuery } from "@mui/material";

import { styleConstants } from "@Assets/style_constants";
import { navigationStateSelector } from "@Features/navigation/store/navigationStateSlice";

const SMALL_WIDTH = "calc(100% - 20px)";

const getContextWidth = (isSmallDesktop: boolean, isIntakeSurvey: boolean) => {
  if (isSmallDesktop) {
    return SMALL_WIDTH;
  }

  if (isIntakeSurvey) {
    return "684px";
  }

  return "1075px";
};

export const useResponsiveStylesHook = (isIntakeSurvey = false) => {
  const isNavDrawerOpen = useSelector(navigationStateSelector("navDrawerOpen"));
  const isSmallDesktop = useMediaQuery("(max-width: 520px)");
  const formWidth = isSmallDesktop ? SMALL_WIDTH : "450px";
  const contextWidth = getContextWidth(isSmallDesktop, isIntakeSurvey);

  const drawerWidth = isNavDrawerOpen ? styleConstants.navDrawerWidth : styleConstants.navDrawerClosedWidth;

  return {
    formWidth,
    drawerWidth,
    contextWidth,
  };
};
