import { useSelector } from "react-redux";
import { useSplitClient } from "@splitsoftware/splitio-react";

import { memberStateSelector } from "@Features/member/store/memberStateSlice";

/**
 * This hook is utilized to determine if a feature flag is ready and to get the current treatment value for a given flag
 */
export const useFeatureFlag = (flagName: string) => {
  const member = useSelector(memberStateSelector("member"));
  // As we initialize the Split SDK with anonymous as the key, we need to update it to use the actual member id
  const { client, isReady } = useSplitClient({ splitKey: member?.id });

  return { isReady, treatment: client?.getTreatment(flagName) } as const;
};

export const useFeatureFlagWithKey = (flagName: string, key: string) => {
  // Set up the custom key for the Split SDK
  const { client, isReady } = useSplitClient({ splitKey: key });

  return { isReady, treatment: client?.getTreatment(flagName) } as const;
};
