import { useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  SurveyContextNode,
  SurveyRedirectNode,
  SurveyTerminalNode,
  SurveyTerminalNodeAction,
  VivanteLinkResource,
  VivanteLinkResourceType,
  VivanteLinkType,
} from "@vivantehealth/vivante-core";
import { useRouter } from "next/router";

import { handleUriNavigation } from "@Features/navigation/utils/navigation.util";
import { SurveyStateSliceSelector, surveyStateSlice } from "@Features/survey/store/surveyStateSlice";
import { vivanteCoreContainer } from "@Lib/vivanteCore";

const { loadSurveyByLink } = surveyStateSlice.actions;

export const useSurveyHook = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [query, setQuery] = useState(router.query);
  const survey = useSelector(SurveyStateSliceSelector("survey"));
  const isSurveyLoading = useSelector(SurveyStateSliceSelector("loadState")) === "loading";
  const error = useSelector(SurveyStateSliceSelector("errorMessage"));
  const isContextNode =
    survey?.currentNode?.constructor === SurveyContextNode || survey?.currentNode?.constructor === SurveyTerminalNode;
  const isRedirectNode = survey?.currentNode?.constructor === SurveyRedirectNode;

  const fetchSurvey = useCallback(() => {
    const params = router.query as Record<string, string>;
    const surveyLink: VivanteLinkResource = {
      type: VivanteLinkType.RESOURCE,
      resource: VivanteLinkResourceType.SURVEY,
      queryParams: params,
    };

    dispatch(loadSurveyByLink(surveyLink));
  }, [dispatch, router.query]);

  useEffect(() => {
    if (query !== router.query) {
      setQuery(router.query);
      fetchSurvey();
    }
  }, [fetchSurvey, query, router.query]);

  useEffect(() => {
    if (!survey) {
      fetchSurvey();
    }
  }, [survey, fetchSurvey]);

  const getActionUris = () => {
    if (survey?.currentNode instanceof SurveyTerminalNode) {
      const actionUris = survey?.currentNode?.actions?.map((action: SurveyTerminalNodeAction) => {
        const uri = handleUriNavigation(action.uri);

        return { uri, title: action.title };
      });

      return actionUris;
    }
  };

  const answerSurveyAsync = async () => {
    if (survey?.id === undefined || survey?.currentNode === undefined) {
      return;
    }

    return vivanteCoreContainer.getSurveyUseCaseFactory().createSubmitNodeReplyUseCase().execute({
      surveyId: survey?.id,
      node: survey?.currentNode,
      answer: undefined,
      customValues: undefined,
    });
  };

  return {
    survey,
    isRedirectNode,
    isSurveyLoading,
    isContextNode,
    error,
    getActionUris,
    answerSurveyAsync,
    fetchSurvey,
  };
};
