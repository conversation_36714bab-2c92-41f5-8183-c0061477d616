import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

import {
  authenticationStateSelector,
  authenticationStateSlice,
} from "@Features/authentication/store/authenticationStateSlice";
import { selectIsEligible } from "@Features/eligibility/store/eligibilityProcessStateSlice";
import { memberStateSelector } from "@Features/member/store/memberStateSlice";
import { Routes } from "@Types";

import { useNetworkConnection } from "./networkConnectionHook";

export const useFirebaseAuth = () => {
  const dispatch = useDispatch();
  const isOnline = useNetworkConnection();
  const isAuthenticated: boolean = useSelector(authenticationStateSelector("isAuthenticated"));
  const loadState = useSelector(authenticationStateSelector("loadState"));
  const isFirebaseAuthLoading: boolean = loadState === "firebaseAuthLoading" || !loadState;
  const isLoaded: boolean = useSelector(authenticationStateSelector("firebaseLoaded"));
  const isMemberLoading = useSelector(memberStateSelector("loadState")) === "loading";
  const member = useSelector(memberStateSelector("member"));
  const isEligible = useSelector(selectIsEligible);
  const isLoading = [isFirebaseAuthLoading, isMemberLoading].includes(true);

  useEffect(() => {
    if (!isLoaded && isOnline) {
      dispatch(authenticationStateSlice.actions.initializeFirebaseAuth());
    }
  }, [dispatch, isLoaded, isOnline]);

  const checkAuthorization = (path?: Routes) => {
    switch (path) {
      case Routes.SURVEY:
      case Routes.NURSE_TRIAGE:
      case Routes.TODO:
        return checkMemberAuth(true);
      default:
        return checkMemberAuth();
    }
  };

  const checkMemberAuth = (isSurveyPath?: boolean) => {
    const isMemberEligible = member?.hasGroup || isEligible;
    const needsOnboarding = member?.setting?.onboardingPending && !isSurveyPath;
    const redirectUrl = !isMemberEligible ? Routes.ELIGIBILITY : needsOnboarding ? Routes.ONBOARDING : null;

    return {
      isAuthorized: isMemberEligible && !needsOnboarding,
      redirect: redirectUrl,
    };
  };

  return {
    isAuthenticated,
    isFirebaseAuthLoading: isLoading,
    checkAuthorization,
  };
};
