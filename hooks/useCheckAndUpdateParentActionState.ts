import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ActionPlanTargetState } from "@vivantehealth/vivante-core";

import { actionPlansStateSelector, setActionPlanTargetState } from "@Features/carePlan/store/actionPlansStateSlice";

// these are the only states that can be accepted as new states for a parent action, since only these states requires specific logic based on children states
type AcceptedActionPlanTargetNewState = Extract<
  ActionPlanTargetState,
  ActionPlanTargetState.COMPLETED | ActionPlanTargetState.SPECIFIED
>;

export const useCheckAndUpdateParentActionState = () => {
  const targetEntitiesState = useSelector(actionPlansStateSelector("targetEntities"));
  const driverEntitiesState = useSelector(actionPlansStateSelector("driverEntities"));

  const dispatch = useDispatch();

  const useCheckAndUpdateParentActionState = useCallback(
    (parentActionId: string, actionId: string, newState: AcceptedActionPlanTargetNewState) => {
      if (!parentActionId) {
        return;
      }

      const parentActionPlan = targetEntitiesState[parentActionId];

      if (parentActionPlan) {
        switch (newState) {
          case ActionPlanTargetState.COMPLETED: {
            const incompleteDrivers = parentActionPlan.drivers?.filter(
              (driver) => driverEntitiesState[driver].state !== "COMPLETED",
            );

            if (incompleteDrivers?.length === 1 && incompleteDrivers[0] === actionId) {
              dispatch(
                setActionPlanTargetState({
                  targetId: parentActionId,
                  newTargetState: ActionPlanTargetState.COMPLETED,
                }),
              );
            }

            break;
          }
          case ActionPlanTargetState.SPECIFIED: {
            const startedOrCompletedDrivers = parentActionPlan.drivers?.filter(
              (driver) => driverEntitiesState[driver].state && driverEntitiesState[driver].state !== "SPECIFIED",
            );

            if (startedOrCompletedDrivers?.length === 1 && startedOrCompletedDrivers[0] === actionId) {
              dispatch(
                setActionPlanTargetState({
                  targetId: parentActionId,
                  newTargetState: ActionPlanTargetState.SPECIFIED,
                }),
              );
            }

            break;
          }
          default:
            break;
        }
      }
    },
    [dispatch, driverEntitiesState, targetEntitiesState],
  );

  return useCheckAndUpdateParentActionState;
};
