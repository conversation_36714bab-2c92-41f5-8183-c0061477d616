import { useDispatch } from "react-redux";
import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";

import { sendAnalytics } from "@Features/analytics/store/analyticsEpics";

export type SendEventAnalyticsAction = ReturnType<typeof useAnalyticsHook>["sendEventAnalytics"];

export const useAnalyticsHook = () => {
  const dispatch = useDispatch();

  const sendEventAnalytics = (eventType: ClickStreamActivityEventType, extra?: Record<string, string | number>) => {
    return dispatch(
      sendAnalytics({
        eventType,
        ...(extra && { activityContextExtra: extra }),
      }),
    );
  };

  return { sendEventAnalytics };
};
