import { useState, useEffect } from "react";
import { useDispatch } from "react-redux";

import { addServerEventsSubscriptions, removeServerEventsSubscriptions } from "@Store/actions";

export const useNetworkConnection = () => {
  const [isOnline, setIsOnline] = useState<boolean | null>(null);
  const dispatch = useDispatch();

  useEffect(() => {
    const handleOnlineStatusChange = () => {
      if (isOnline === false && navigator.onLine) {
        // add subscriptions only if connection previously lost
        dispatch(addServerEventsSubscriptions());
      } else if (isOnline && !navigator.onLine) {
        dispatch(removeServerEventsSubscriptions());
      }

      setIsOnline(navigator.onLine);
    };

    if (typeof window !== "undefined") {
      setIsOnline(navigator.onLine);

      // Add event listeners for online/offline events
      window.addEventListener("online", handleOnlineStatusChange);
      window.addEventListener("offline", handleOnlineStatusChange);

      // Clean up event listeners on component unmount
      return () => {
        window.removeEventListener("online", handleOnlineStatusChange);
        window.removeEventListener("offline", handleOnlineStatusChange);
      };
    }
  }, [dispatch, isOnline]);

  return isOnline;
};
