import { useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import * as Sentry from "@sentry/nextjs";
import { useTimeout, useSessionStorage } from "usehooks-ts";

import { actionPlansStateSelector } from "@Features/carePlan/store/actionPlansStateSlice";
import { careTeamStateSelector } from "@Features/careTeam/store/careTeamStateSlice";
import { courseItemsStateSelector } from "@Features/courses/store/courseItemsStateSlice";
import { symptomLoggingStateSelector } from "@Features/symptomLogging/store/symptomLoggingStateSlice";
import { toDosSelectors } from "@Features/toDos/store/toDosStateSlice";

const WAIT_FOR_BOOTSTRAP_TIME_LIMIT = 1000;
const FAILED_TO_BOOTSTRAP_TIME_LIMIT = 10000;

export const useBootstrapCompleted = () => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isBootstrapped, setIsBootstrapped] = useSessionStorage("isBootstrapped", false);
  const actionPlansIsLoaded = useSelector(actionPlansStateSelector("loadState"));
  const courseItemsIsLoaded = useSelector(courseItemsStateSelector("loadState"));
  const toDosIsLoaded = useSelector(toDosSelectors("loadState"));
  const careTeamIsLoaded = useSelector(careTeamStateSelector("loadState"));
  const symptomLoggingIsLoaded = useSelector(symptomLoggingStateSelector("loadState"));
  const bootstrapCompleted = [
    actionPlansIsLoaded,
    courseItemsIsLoaded,
    toDosIsLoaded,
    careTeamIsLoaded,
    symptomLoggingIsLoaded,
  ].every((isLoaded) => isLoaded === "loaded");

  useTimeout(() => {
    /**
     * If the app is not bootstrapped after 10 seconds, we assume that something
     * went wrong and we log an error to Sentry. We set isBootstrapped to true to allow
     * the Log out button to be enabled even if the firebase error may still be thrown.
     * To solve race condition between the two timeouts, we verify that bootstrapCompleted
     * and isBootstrapped is false
     */
    if (!bootstrapCompleted && !isBootstrapped) {
      const unfulfilledModules = Object.entries({
        carePlansModule: actionPlansIsLoaded,
        courseItemsModules: courseItemsIsLoaded,
        toDosModule: toDosIsLoaded,
        careTeamModule: careTeamIsLoaded,
        symptomLoggingModule: symptomLoggingIsLoaded,
      }).filter(([, isLoaded]) => isLoaded !== "loaded");
      const unfulfilledModulesList = unfulfilledModules.map(([moduleName]) => moduleName).join(", ");

      Sentry.withScope((scope) => {
        scope.setLevel("warning");
        Sentry.captureException(
          new Error(
            `Bootstrapping took longer than 10 seconds, the following modules failed to load: ${unfulfilledModulesList}`,
          ),
        );
      });

      setIsBootstrapped(true);
    }
  }, FAILED_TO_BOOTSTRAP_TIME_LIMIT);

  useEffect(() => {
    if (bootstrapCompleted && !isBootstrapped) {
      /** Even after all modules are loaded, we still need to wait an additional
       * second for the bootstrapping to complete so as to not throw a firebase error
       * on logout.
       */
      timeoutRef.current = setTimeout(() => {
        setIsBootstrapped(true);
      }, WAIT_FOR_BOOTSTRAP_TIME_LIMIT);
    }

    return () => {
      if (timeoutRef?.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [bootstrapCompleted, isBootstrapped, setIsBootstrapped]);

  return isBootstrapped;
};
