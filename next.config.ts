/* eslint-disable @typescript-eslint/no-require-imports */
import { NextConfig } from "next";

let commitHash = "";

try {
  commitHash = `+${require("child_process").execSync("git rev-parse --short HEAD")}`;
} catch (err) {
  // eslint-disable-next-line no-console
  console.log(err);
}

const nextConfig: NextConfig = {
  output: "export",
  images: {
    unoptimized: true,
  },
  experimental: {
    externalDir: true,
    // esmExternals: "loose", Umcomment this line if you're using yarn link
  },
  env: {
    commitHash,
  },
  reactStrictMode: true,
  turbopack: {
    rules: {
      "*.svg": {
        loaders: ["@svgr/webpack"],
        as: "*.js",
      },
    },
    resolveAlias: {
      canvas: "./empty-module.ts", // This is to support react-pdf https://github.com/wojtekmaj/react-pdf?tab=readme-ov-file#nextjs
    },
  },
  webpack: (config) => {
    config.module.rules.push({
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    });
    config.resolve.alias.canvas = false;

    return config;
  },
};

// Injected content via Sentry wizard below

const { withSentryConfig } = require("@sentry/nextjs");

module.exports = withSentryConfig(
  nextConfig,
  {
    // For all available options, see:
    // https://github.com/getsentry/sentry-webpack-plugin#options

    // Suppresses source map uploading logs during build
    silent: true,
    org: "vivantehealth",
    project: "githrive-react-web",
    // Captures React component names for Session Replays
    // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/#step-9-capture-react-component-names-optional
    reactComponentAnnotation: {
      enabled: true,
    },
  },
  {
    // For all available options, see:
    // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

    // Upload a larger set of source maps for prettier stack traces (increases build time)
    widenClientFileUpload: true,

    // Transpiles SDK to be compatible with IE11 (increases bundle size)
    transpileClientSDK: true,

    // Hides source maps from generated client bundles
    hideSourceMaps: true,

    // Automatically tree-shake Sentry logger statements to reduce bundle size
    disableLogger: true,
  },
);
