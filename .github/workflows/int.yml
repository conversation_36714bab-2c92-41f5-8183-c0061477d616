name: "CI/CD (INT)"

on:
  # Allow manual trigger (environments will only run from the main branch)
  workflow_dispatch:
  push:
    branches:
      - "main"

permissions:
  contents: read
  # Allow PRs to be read to get commit sha of docker tag to promote
  pull-requests: read
  # Allow GITHUB_TOKEN to do things with OIDC tokens for GCP workload id auth
  id-token: write

jobs:
  get-build-sha:
    runs-on: ubuntu-latest
    name: Get build sha
    environment: int-ci

    steps:
    - name: Store environment variables for next jobs in workflow
      uses: actions/cache@v4
      with:
        path: gha-cache
        key: env-cache-${{ github.sha }}-${{ github.run_id }}-docker-int-${{ github.run_attempt }}
        restore-keys: |
          env-cache-${{ github.sha }}-${{ github.run_id }}

    # Get the head commit of the PR that introduced this commit. This is the
    # tag that should have been added to the docker image when it was built
    # and pushed
    - name: Get artifact tag
      uses: actions/github-script@v7
      id: fetch-sha
      with:
        script: |
          const result = await github.rest.repos.listPullRequestsAssociatedWithCommit({
            owner: context.repo.owner,
            repo: context.repo.repo,
            commit_sha:  context.sha
          });
          const prs = result.data.filter((pr) => pr.merged_at !== null && pr.merged_at !== "")
          if (prs.length !== 1) {
            throw new Error(`Expected exactly one merged PR introducing this commit. Found ${prs.length}`)
          }
          const sha = prs[0].head.sha
          return sha

    - name: Store environment variables
      shell: bash
      run: |
        mkdir -p ./gha-cache
        echo "TF_VAR_ci_build=${{ steps.fetch-sha.outputs.result }}" >> ./gha-cache/file-container-github-sha.env
        echo "TF_VAR_image_tag=${{ steps.fetch-sha.outputs.result }}" >> ./gha-cache/docker-tag-github-sha.env

  deploy-int-tf:
    name: Release terraform to INT
    uses: vivantehealth/terraform-stack-workflow/.github/workflows/ci.yml@v3
    needs: [get-build-sha]
    with:
      environment: int
      file_containers: "site-contents"
    secrets: inherit
