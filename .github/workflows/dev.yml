name: "CI/CD (DEV)"

on:
  # Trigger on push to any non-main branch
  push:
    branches-ignore:
      - "main"

permissions:
  contents: read
  id-token: write
  packages: read

jobs:
  build:
    runs-on: ubuntu-latest

    environment:
      name: dev-ci

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 22.x

      - name: Get build env info
        id: build-env-info
        run: |
          echo "node-version=$(node --version)" >> $GITHUB_OUTPUT
          echo "yarn-cache=$(yarn cache dir)"  >> $GITHUB_OUTPUT

      # https://github.com/actions/cache/blob/main/examples.md#node---yarn
      - uses: actions/cache@v4
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: ${{ steps.build-env-info.outputs.yarn-cache }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install NPM dependencies
        run: |
          echo "@vivantehealth:registry=https://npm.pkg.github.com/" >> .npmrc
          echo "//npm.pkg.github.com/:_authToken=${{ secrets.GITHUB_TOKEN }}" >> .npmrc
          yarn install
      - name: Check lint rules
        run: yarn lint
      - name: Check tests pass
        run: yarn test
      - name: Check types
        run: yarn ts
      - name: Build application
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
          SENTRY_PROJECT: ${{ secrets.SENTRY_PROJECT }}
        run: |
          mkdir build-flavors
          echo ${{ github.sha }} > build-flavors/commit-sha.txt
          NEXT_PUBLIC_BUILD_FLAVOR=development yarn build
          mv out/ build-flavors/out-dev/
          cp build-flavors/commit-sha.txt build-flavors/out-dev/commit-sha.txt
          NEXT_PUBLIC_BUILD_FLAVOR=staging yarn build
          mv out/ build-flavors/out-int/
          cp build-flavors/commit-sha.txt build-flavors/out-int/commit-sha.txt
          NEXT_PUBLIC_BUILD_FLAVOR=production yarn build
          mv out/ build-flavors/out-prd/
          cp build-flavors/commit-sha.txt build-flavors/out-prd/commit-sha.txt
      - name: Store environment variables for later jobs in other workflows
        uses: actions/cache@v4
        with:
          path: gha-cache
          key: env-cache-${{ github.sha }}-${{ github.run_id }}-file-container-${{ github.run_attempt }}
          restore-keys: |
            env-cache-${{ github.sha }}-${{ github.run_id }}

      - name: Write Sentry release name to environment variables cache
        shell: bash
        run: |
          mkdir -p ./gha-cache
          echo "TF_VAR_ci_build=${{ github.sha }}" >> ./gha-cache/file-container-github-sha.env

      # Default value for version is github.sha - https://github.com/getsentry/action-release#parameters
      # @TODO - Upload source maps to Sentry
      # - name: Create Sentry release
      #   uses: getsentry/action-release@v1
      #   env:
      #     SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      #     SENTRY_ORG: ${{ vars.SENTRY_ORG }}
      #     SENTRY_PROJECT: ${{ vars.SENTRY_PROJECT }}
      #   with:
      #     sourcemaps: ./dist
      #     url_prefix: ~/
      #     ignore_empty: true
      #     ignore_missing: true

      - name: Package build artifacts for deployment to all environments
        uses: vivantehealth/create-file-container-action@v1
        with:
          name: site-contents
          path: "build-flavors/"
          gcp_service_account: ${{ vars.GCP_SERVICE_ACCOUNT }}
          workload_identity_provider: ${{ vars.WORKLOAD_IDENTITY_PROVIDER }}
          docker_registry: ${{ vars.DOCKER_REGISTRY }}

  deploy-dev-tf:
    uses: vivantehealth/terraform-stack-workflow/.github/workflows/ci.yml@v3
    needs: [build]
    with:
      file_containers: "site-contents"
      environment: dev
      tf_log: "INFO"
    secrets: inherit
