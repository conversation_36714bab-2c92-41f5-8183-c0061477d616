# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: "github-actions"
    directory: "/" # Location of package manifests
    schedule:
      interval: "weekly"
    groups:
      # This is the name of your group, it will be used in PR titles and branch names
      gha-dependencies:
        patterns:
          - "*"
  - package-ecosystem: "terraform"
    directory: "/terraform"
    schedule:
      interval: "weekly"
    groups:
      # This is the name of your group, it will be used in PR titles and branch names
      tf-dependencies:
        patterns:
          - "*"      
