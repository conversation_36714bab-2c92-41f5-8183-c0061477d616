<!--  Add [NOT LIVE] to title of the PR if the change is unreachable by a user so that the change can be included into production deployment safely. Reason for that is the title of this PR is used in CodePush description. (e.g. [NOT LIVE] - Course redesign) -->

## Issue:
<!--  Link issue using keyword Resolves https://github.com/vivantehealth/zi/issues/XX  -->

## What this is:
<!-- Line items addressing:
* Include a summary of the change and relevant motivation
* clear description of bug, feature, or issue addressed in this PR
* clear description of changes made to accomplish addressing bug/issue/feature
* Also, mention context and a list any dependencies that are required for this change if there are any.
* summary of code & files added/edited/deleted -->

## Steps to Verify:
<!-- Verification steps for the reviewer.
* Include these in the issue as a comment so that whoever QAing the change can refer to it without clicking through links
* If it is a bug fix, include the steps to reproduce the bug
* Steps for a reviewer to manually verify the acceptance criteria. Example: "Open App, login, tap on X, tap on Y, you should see Z"
* Any necessary credentials or data for reproducing issue- eg login info, mock data  -->

## Media:
<!-- Screencaps, GIFs, or loom if the change includes UI/UX clearly showing how the work on this PR handles described issue.
Also, make sure to put the media on the issue so that anyone reviewing the ticket can see the visual changes.  -->
