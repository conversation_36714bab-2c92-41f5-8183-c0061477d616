// This file configures the initialization of Sentry on the client.
// The config you add here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

import { getVivanteEnvironment } from "@Config/config";

const environment = getVivanteEnvironment();
const isDev = environment === "dev4";

// Only log when in INT/PROD
if (!isDev) {
  Sentry.init({
    dsn: "https://<EMAIL>/4506378688397312",
    tracesSampleRate: 1,
    debug: false,
    maxValueLength: 750,
    replaysOnErrorSampleRate: 1.0,
    replaysSessionSampleRate: 0.1,
    integrations: [
      Sentry.replayIntegration({
        // Additional Replay configuration goes in here, for example:
        maskAllText: true,
        blockAllMedia: true,
      }),
    ],
    environment,
  });
}

/**
 * Instruments router navigations
 * https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/#initialize-sentry-client-side-and-server-side-sdks
 */
export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;
