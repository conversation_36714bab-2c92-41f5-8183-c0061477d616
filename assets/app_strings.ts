import { AppointmentAction } from "@Features/careTeam/CareTeamScreenContainer";
import { MFA_TYPE } from "@Features/multiFactorAuthentication/assets/constants";
import { MfaType } from "@Features/multiFactorAuthentication/types/mfa.types";

export const appStrings = {
  a11y: {
    home: "Home",
    article: "Article",
    interventionCard: "Intervention Card",
    carePlanCardState: (cardState: string) => `The current state of the card is ${cardState}.`,
    carePlanCard: "Care Plan Card",
    carePlanCardItem: (title: string, description: string, progress: { completed: number; total: number }) =>
      `Care plan item: ${title}. ${description ? `Description: ${description}.` : ""} Progress: ${
        progress.completed
      } out of ${progress.total} modules completed.`,
    carouselAction: {
      intervention: (title: string, description: string) =>
        `Start action: ${title}. ${description ? `Description: ${description}.` : ""}`,
      carePlan: (title: string, description: string) =>
        `Start care plan: ${title}. ${description ? `Description: ${description}.` : ""}`,
    },
    errorModalClose: "Close error modal",
    status: (status: string | undefined) => (status ? `Status: ${status}` : ""),
    subtitle: (subtitle: string) => `Subtitle: ${subtitle}`,
    linkLabel: (isLinkActive: boolean, linkName: string) => `${isLinkActive ? "Active link:" : "Link to"} ${linkName}.`,
    articleTime: (time: string) => `Length of time to read article: ${time}`,
    appointmentDuration: (duration: number) => `Appointment duration: ${duration} minutes`,
    appointmentType: (appointmentType: "Phone call" | "Video visit") => `Appointment type: ${appointmentType}`,
    birthdateMonth: "Select your birthdate month value",
    birthdateDay: "Input birthdate day value",
    birthdateYear: "Input birthdate year value",
    callToAction: (callToAction: string) => `Call To Action: ${callToAction}`,
    careTeamMember: (memberType: string, careTeamMemberName?: string) => `${memberType}: ${careTeamMemberName ?? ""}`,
    careTeamMemberAvatarText: (memberRole: string) => `${memberRole} profile image`,
    category: (category: string) => `Category: ${category}`,
    closeModal: "Close modal",
    closeDrawer: "Close drawer",
    goBack: "Go Back",
    slideshowImage: "Slideshow Image",
    conversationCard: (careTeamMember: string, lastMessage: string, dateOfLastMessage: string) =>
      `Conversation Card with ${careTeamMember}. ${lastMessage}. Message sent on ${dateOfLastMessage}`,
    chat: {
      messageFrom: (sender: string) => `, from ${sender}`,
      headerIconNoMessages: "chat",
      headerIconNewMessages: "new chat message available",
    },
    course: "Course",
    editAppointmentMenu: "Edit appointment dropdown. Use directional keys to navigate",
    skipToContent: "Skip to content",
    progressNoteCategory: (category: string) => `${category} note`,
    title: (title: string) => `Title: ${title}`,
    foodTracking: {
      foodDetailsDrawer: "Food details drawer",
      foodDetailsDrawerClose: "Close food details drawer",
      foodTrackingDrawer: "Food tracking drawer",
      addedFoodDrawer: "Add a new food drawer",
      closeAddedFoodDrawer: "Close create a new food drawer",
      addedFoodBackBtn: (previousScreen: "food overview" | "nutrition facts") => `Back to ${previousScreen} screen`,
      closeFoodTrackingDrawer: "Close food tracking drawer",
      recipeDrawer: "Create a new recipe drawer",
      closeRecipeDrawer: "Close create a new recipe drawer",
      closeRecipeIngredientModal: "Close remove ingredient modal",
      searchLabel: (hasSearchTerm: boolean) =>
        hasSearchTerm ? "Search for food. Press enter to search" : "Press enter to search.",
      recipeDetailsDrawer: "Recipe details drawer",
      recipeDetailsDrawerClose: "Close recipe details drawer",
      closeRemoveRecipeConfirmationModal: "Close remove recipe modal",
      closeRemoveIngredientConfirmationModal: "Close remove ingredient modal",
    },
    snackbarClose: "Close snackbar",
    surveyOtherField: (fieldName: string) => `Input value for ${fieldName} field`,
    scrollRight: "Scroll right",
    scrollLeft: "Scroll left",
    increaseValue: (valueType?: string) => `increase ${valueType ?? ""} value`,
    decreaseValue: (valueType?: string) => `decrease ${valueType ?? ""} value`,
  },
  errorPages: {
    error404Header: "Page not found",
    error404Subheader: "The page you are trying to access is not available. Please update your bookmark.",
    generalErrorHeader: `We ran into an issue`,
    generalErrorSubheader: `We are aware of the issue and are working to resolve it.`,
    errorPageButtonText: "Return to home",
    alerts: {
      errorHeader: "Error",
    },
  },
  features: {
    articles: {
      articles: "Articles",
      backToArticles: "Back to articles",
      searchArticles: "Search articles",
      searchNoMatches: "Hmm... no matches. Try searching for something else.",
      pdf: {
        loadingError: {
          header: "A loading error has occurred",
          subHeader: "PDF failed to load. Please refresh and try again.",
          buttonText: "Refresh",
        },
        print: "Print",
        download: "Download",
      },
      recommendedArticles: "If you liked this article",
    },
    authentication: {
      signInFormTitle: "Log in to your account",
      signInPrompt: "Please enter your email and password to log in to your account.",
      invalidEmailError: "Please enter a valid email address.",
      invalidPasswordError: "Password must contain at least eight characters, one uppercase letter, and one number.",
      passwordConfirmationError: "Passwords need to match.",
      passwordResetEmailSentSuccessfully:
        "An email was sent to the address you provided with a link to reset your password.",
      generalError: "Something went wrong. Please try again.",
      errorDiscriminator: "authentication failure",
      errorModal: {
        title: "Error",
        message: "We are experiencing a temporary error. Please try again later.",
        buttonText: "Okay",
      },
      genericAuthenticationError:
        "Something went wrong. Please try again <NAME_EMAIL> for assistance.",
      failedServerValidation: "Please verify the information you entered and try again.",
      invalidCredentials: "Invalid username or password. Please try again.",
      maxMfaSendAttemptsReached:
        "You have attempted to send the verification code more than 5 times and have reached the limit. Please try again later.",
    },
    careGuide: {
      landing: {
        welcome: "Welcome to Cylinder",
        launchJourney: "Launch your journey to a healthier gut",
        journeyStarted: "500+ members started their journey here",
        cards: {
          support: {
            header: "Kick off with support",
            body: "Start with a friendly intro call to help you make the most of your program.",
            buttonText: "Let's get started together",
            chipText: "Recommended",
          },
          ownPace: {
            header: "Go at your own pace",
            body: "Watch our program overview and connect directly with our care team.",
            buttonText: "Show me around",
          },
        },
      },
      scheduleLanding: {
        header: "Your time is valuable!",
        body: "Schedule your free 15-minute Care Guide call. They'll help you every step of the way:",
        card: {
          bulletPoints: [
            "Quick access to appointments",
            "Guidance to get the care you deserve",
            "Friendly, step-by-step support",
          ],
          percentage: "89%",
          percentageText: "of members say their Care Guide helped them get the most out of their program from day 1",
        },
        phoneOrVideoAltText: "phone or video call",
        videoCallWithCareGuideAltText: "video call with care guide",
      },
      scheduling: {
        selectACommunicationMethod: "Please select a call type to see availability.",
        noAvailableAppointmentsCareGuide:
          "Looks like your Care Guide doesn't have open appointments in the next couple of weeks. Give us a call at 1-************ so we can assist.",
        careGuideCall: "Care Guide Call",
        noCallsWhileDriving: "Note: for your safety we do not complete calls while you are driving.",
        review: "Review and confirm appointment",
        appointmentConfirmed: "Appointment confirmed! You will receive an appointment confirmation email.",
        missingTimeSlot: "Please edit your appointment and select a time slot to continue.",
      },
      gettingStarted: {
        header: "Getting started with Cylinder",
        body: "Watch the video below for a quick introduction",
      },
      readyToMeet: {
        header: "Ready to meet your Care Team?",
        body: "Meet our experts who'll help guide your gut health journey",
        doThisLater: "I'll do this later",
        scheduleCall: "Schedule call",
        careTeamImageAltText: "care team page",
      },
    },
    carePlan: {
      alertTitle: "Item cannot be opened at this time",
      alertSubtitle: "You have already completed this item",
      completedOn: "Completed on",
      earlierItems: "Earlier items",
      otherItems: "Other items",
      hideCompleted: "Hide completed",
      historyTab: "History",
      interventionCardIconAltText: "intervention card icon",
      interventionSectionTitle: "Action Needed",
      noHistoryMessage: 'Care plan items you mark as "done" will appear here. You can revisit them at any time.',
      phaseCompleteCardIconAltText: "completed card icon",
      phaseIncompleteCardIconAltText: "incomplete card icon",
      showCompleted: "Show completed",
      toDo: "To do",
      moduleLogo: "module logo",
      backButtonText: "Back to care plan",
      confirmDoneMessage: "Please confirm you completed this action.",
      cantBeUndone: "This can't be undone.",
      foodLoggingDisabledTitle: "Tracking on your web browser is under construction.",
      foodLoggingDisabledSubtitle: "Download the iOS or Android app now to track food today.",
      inProgress: "In progress",
      markDone: "Mark done",
    },
    careTeam: {
      appointments: "Appointments",
      appointmentConfirmed: "Appointment confirmed! You will receive an appointment confirmation email.",
      appointmentsEmptyState:
        "You have no upcoming appointments. Your Care team is eager to provide you the support you need. Click on schedule an appointment to book an appointment today.",
      calendars: {
        iCalendar: "iCalendar",
        google: "Google Calendar",
        yahoo: "Yahoo! Calendar",
        outlookLive: "Outlook Live Calendar",
        officeOutlook: "Office Outlook Calendar",
      },
      callModalHeader: "On-Call Request",
      callModalPhoneNumber: "18008277437",
      callModalSubheaderPartOne: "Call",
      callModalSubheaderPartTwo: "**************",
      callModalSubheaderPartThree: "to talk with an on-call expert now. We're here and ready to help.",
      conversations: "Conversations",
      conversationsEmptyState:
        "You have no conversations currently. Your Care Team is eager to provide you the support you need. If you are experiencing symptoms of a medical emergency or mental health crisis call 911.",
      editAppointmentButton: "Edit appointment",
      editConfirmation: (type?: AppointmentAction) => `Are you sure you want to ${type ?? "modify"} your appointment?`,
      editConfirmationTitle: (type?: AppointmentAction) =>
        type === undefined ? "Modify appointment" : `${type?.slice(0, 1).toUpperCase()}${type?.slice(1)} appointment`,
      joinMeetingButton: "Join meeting",
      menuItems: {
        addToCalendar: "Add to calendar",
        rescheduleAppointment: "Reschedule",
        cancelAppointment: "Cancel appointment",
      },
      pageHeader: "Care Team",
      pageSubheader:
        "Your Care Team is here to help you reach your health goals, whether it’s understanding symptoms, making lifestyle changes, or building a plan that works for you. Appointments are free, at no cost to you, and available by phone or video.",
      memberModalHeader: (memberRole: string) => `Meet your ${memberRole}`,
      memberModalSubeader: {
        healthCoach:
          "Your Health Coach will guide you through the Cylinder Program and support you in making long-term changes related to stress management, healthy sleep, physical activity, and even help in navigating the healthcare system.",
        dietitian:
          "Your Registered Dietitian will help you understand your condition and personalize your diet based on your unique symptoms, triggers, lifestyle, and goals. Together, your team is here to be your partners in improving your health.",
      },
      profileImageAltText: "practitioner profile image",
      phoneCallVisit: "Phone call",
      videoVisit: "Video visit",
      phoneCallFootnote:
        "*Your Care team member will call you from a toll free number at your scheduled appointment time.",
      videoCallFootnote: (minutes: number) =>
        `The above button activates ${minutes} minutes prior to the appointment, allowing you to join.`,
      appointmentConfirmationModal: {
        title: "Review and confirm appointment",
        locationNote: "You must be physically located in the state you specified at the time of your appointment.",
      },
      schedulingModal: {
        errorTitle: "Appointment Request",
        one: "1.",
        phoneCall: "Phone Call",
        pleaseSelectMember: "Please select a Care Team member to see availability.",
        pleaseSelectAMethod: "Please select a method of contact to see availability.",
        promptOne: "Who would you like to have this appointment with?",
        promptTwo: (careTeamType: "Care Team" | "Care Guide") =>
          `How would you like your ${careTeamType} to reach you?`,
        promptThree: "When would you like to have this appointment?",
        three: "3.",
        two: "2.",
        video: "Video",
        noAvailableAppointments: (careTeamMember: "Health Coach" | "Dietitian" | "Care Team Member") =>
          `Looks like your ${careTeamMember} doesn't have open appointments in the next couple of weeks. Check to see if other members of your Care Team are available or give us a call at 1-************ so we can assist.`,
      },
      careGuide: "Care Guide",
      medicalDoctor: "Medical Doctor",
      gastroenterology: "Gastroenterology",
      internalMedicine: "Internal Medicine",
      familyMedicine: "Family Medicine",
      physicianAssistant: "Gastroenterology",
      healthCoach: "Health Coach",
      dietitian: "Dietitian",
      cancellingAppointmentError:
        "We had trouble cancelling your appointment. Please try again <NAME_EMAIL>",
      existingAppointmentError:
        "You can only schedule one appointment with this care team member at a time. Please review your appointments on the Care Team page.",
      appointmentListError: "We were unable to load your appointments",
    },
    changePassword: {
      backToSettings: "Back to settings",
      changePassword: "Change password",
      currentPassword: "Current Password",
      forgotPassword: "Forgot password?",
      newPassword: "New Password",
      saveButtonText: "Update password",
      savedPasswordSuccessfully: "Password changed successfully",
      passwordsNeedToMatch: "Passwords need to match",
      currentPasswordIncorrect: "Your current password is incorrect",
      mfaFormHeader: "Please verify your identity",
      mfaFormSubHeader: (mfaType: MfaType) =>
        `To finalize your password change, we need to verify your identity. Please allow up to 1 minute for the ${
          mfaType === MFA_TYPE.EMAIL ? "email" : "text"
        } to arrive.`,
    },
    chat: {
      archivedConversationsHeader: "Archived Conversations",
      archivedConversationsBody: "This person is no longer a member of your Care team.",
      archivedConversationsSuggestion: "Find your new Care Team members.",
      careTeamHeader: "My Care Team",
      emptyMessagesHeader: "No conversations yet",
      emptyMessagesBody:
        "Your Care team is eager to provide you the support you need. If you are experiencing symptoms of medical emergency or mental health crisis call 911.",
      errorLoading: "We can't load the chat data due to a network error",
      errorSubmitting: "We can't send your message due to a network error",
      pageHeader: "Chat",
      placeHolder: "Type here...",
      you: "You",
    },
    memberConvertedToVirtualClinic: {
      tabs: {
        whatsNew: "What's new",
        currentFeatures: "Current Features",
      },
      newFeatures: {
        improvedCarePlan: {
          title: "Improved Care Plan",
          description: "More health information helps us personalize your care plan.",
        },
        consultationsWithDoctors: {
          title: "Consultations with Doctors",
          description: "Book a virtual appointment with a medical doctor.",
        },
        doctorOrderedTestsScreenings: {
          title: "Doctor Ordered Tests & Screenings",
          description: "Doctors can order labs, imaging, and screenings for you as needed.",
        },
      },
      currentFeatures: {
        healthCoach: "Health Coach",
        dietitian: "Dietitian",
        trackingTools: "Tracking Tools",
        helpfulResources: "Helpful Resources",
      },
      highDeductibleWarning: {
        title: "For Members with a HIP plan:",
        description:
          "Please note that accessing Cylinder services will incur out-of-pocket costs (up to $100 per month).",
      },
      updateHealthInformation: "Update health information to help us personalize your care plan",
      screens: {
        greatNews: {
          header: "Great News! New services now available.",
        },
        getStarted: {
          header: "To get started right away, please complete the following steps.",
        },
      },
      successOnCompletion: "You're ready to start using the new features",
      failedToUpdateCompletion: "We ran into an issue. Please close and reopen Cylinder to try again.",
    },
    codeVerification: {
      codeLabel: "6-digit code",
      codePlaceholder: "Enter code",
      verifyYourIdentityHeader: "Please verify your identity",
      /** Used to change copy when the user landed on a screen from login flow rather than sign up */
      formHeaderReturningUser: "Looks like you're on a new device",
      formSubheader:
        "You're almost there! We've sent you an email with a code to verify your identity. Please allow up to 1 minute for the email to arrive.\n\nIf you do not receive the email, please make sure to also check your spam folder.",
      /** Used to change copy when the user landed on a screen from login flow rather than sign up */
      formSubheaderReturningUser:
        "We've sent you an email with a code to verify your identity. Please allow up to 1 minute for the email to arrive.\n\nIf you do not receive the email, please make sure to also check your spam folder.",
      rememberThisDevice: "Remember this device",
      wrongCodeError: "Incorrect code. Try again",
      codeValidationError:
        "The code you've entered is invalid. Please enter the 6-digit code exactly as it appears in your message.",
      submission: "Submit",
      resendEmail: "Resend verification email",
      remainingTime: "Resend verification email in",
      resentEmail: "A new email has been sent",
      emailVerifyText1: "We've sent you a 6 digit code to ",
      emailVerifyReturnCTA: "here",
      emailVerifyText2: ". This is to verify your identity. If this email address is incorrect please update it ",
      emailVerifyText3: ". Please allow a couple of minutes for the email to arrive.",
      modal: {
        learnMore: "Learn More",
        rememberDevice: "Remember This Device",
        rememberDeviceBody:
          'What does "Remember this device" mean? When you choose to remember this device, we\'ll store a secure identifier on your device. This identifier allows us to recognize your device in the future, streamlining your login process.',
        benefits: "Benefits",
        benefitsBody: "Convenience: Skip the multi-factor authentication step on this device for future logins.",
        benefitsBody2: "Faster Access: Enjoy quicker access to your account without compromising security.",
        importantToNote: "Important to note",
        importantToNoteBody:
          "Device-specific: This setting only applies to the specific device you are currently using. ",
      },
      stateError: {
        title: "We encountered an issue",
        helpText: "Your session has expired. Please try again.",
        link: "Try again",
      },
      smsRegistrationSentCodeTo: "If this phone number is incorrect, ",
      smsRegistrationSentCodeToLink: "please update it here.",
      smsSubHeader:
        "You're almost there! We've sent you a text with a code to verify you're really you. Please allow up to 1 minute for the text to arrive.",
      smsSentCodeTo: (phoneNumber: string) => `We've sent the code to ${phoneNumber}.`,
      resendSmsIn: "Resend text message in",
      resendSmsCode: "Resend verification text",
      resentSmsCodeVerification: "A new text message has been sent",
      codeExpired:
        "The code you entered has expired. Codes are valid for 10 minutes. Please request a new code to continue.",
      maxAttemptsReached: "Maximum verification attempts reached. Please try again in 10 minutes.",
      successfulVerification: "Thanks for confirming your identity",
    },
    accessCode: {
      accessCodeFormTitle: "Enter your access code",
      accessCodePrompt:
        "Your access code can be found in a mailer, email, or other communication you've received about Cylinder Health",
      errorWrongAccessCode:
        "We could not validate the access code you entered. Please try again <NAME_EMAIL> for help.",
      errorMissingAccessCode: "Please enter a valid access code.",
      errorUnexpectedError:
        "Something went wrong. Please re-enter your access code and try again, <NAME_EMAIL> for help.",
      inputLabel: "Access code",
      registrationCodeError: "Cannot handle registration code:",
    },
    courses: {
      backToCourses: "Back to courses",
      backToCourseRoadmap: "Back to course roadmap",
      completed: "Completed",
      courseRoadmap: "Course roadmap",
      enrolled: "Enrolled",
      enroll: "Enroll",
      express: "Express",
      gutCheckOrdered: {
        header: "Your order has been placed!",
        orderPlacedSubheader: "Your order was placed! You should receive your order within 3-5 business days.",
        keepTrackingSubheader:
          "Keep logging foods, tracking symptoms, and working through your personalized action plan so we can help track your progress.",
      },
      inProgress: "In progress",
      listHeader: "Courses",
      noCourses:
        "Your personalized Cylinder experience will be available once you answer your health surveys. Head to the home page to continue.",
      progress: "Progress:",
      reEnroll: "Re-enroll",
      searchCourses: "Search courses",
      suggested: "Suggested",
      takeCourse: "Take course",
      tryThis: "Try this",
      unEnroll: "Unenroll",
    },
    deleteAccount: {
      header: "Delete account",
      subHeading: "We're sorry to see you go.",
      body: "To delete your account, please send an email, from the email address you registered with, to <EMAIL> with the following details:",
      details: "Subject: Account Deletion Request",
      details2: "Body: Include your full name and the reason for the account deletion request",
      post: "Following the request, your account will be deleted within 7 business days.",
    },
    eligibility: {
      helpScreenResultHeader: "Thank you",
      helpScreenResultSubheader: "You will receive an email within 1 business day with next steps.",
      submitTicketSubject: "Eligibility Help Submission [web]",
      submitTicketMessage: (reportDetailMessage: string) => `Eligibility Help Submission\n\n${reportDetailMessage}`,
      serverOrInvalidRequestError:
        "We encountered an issue. Please refresh the page and try again. If the issue persists, please contact <NAME_EMAIL>",
      eligibilityErrors: {
        eligibilityNotFound: {
          header: "We need to verify your eligibility",
          subheader:
            "We couldn't confirm your eligibility for Cylinder Health. Please check your information and try again. If you need help, contact <NAME_EMAIL>",
        },
        expiredEligibility: {
          header: "Not eligible",
          subheader:
            "You are not eligible to register with Cylinder Health at this time. If you believe this is an error, please contact <NAME_EMAIL>",
        },
        preExistingSubscription: {
          header: "Account already exists",
          subheader:
            "It looks like you already have an account with us. Please log in to get access, or contact <NAME_EMAIL> if you need help.",
        },
      },
    },
    foodTracking: {
      backButtonText: "Back to my progress",
      title: "Track a food",
      inputHintLabel: "Press enter to search",
      clearTextButtonAriaLabel: "Clear text",
      inputPlaceHolder: "Search for food",
      history: "History",
      sortButtonAriaLabel: "Sort food history",
      searchResults: "Search results",
      seeMore: "See more",
      seeLess: "See less",
      searchAllFoodsFor: (searchTerm: string) => `Search all foods for "${searchTerm}"`,
      servingSize: "Serving size",
      errorModal: {
        title: "Database error",
        message: "We are experiencing a temporary database error. Please try again later.",
        buttonText: "Okay",
      },
      sortOrder: {
        recentlyTracked: "Recently tracked",
        alphabeticalAsc: "A to Z",
        alphabeticalDesc: "Z to A",
      },
      tabs: {
        all: "All",
        addedFoods: "Added foods",
        myRecipes: "My recipes",
      },
      trackAFood: {
        numberOfServings: "Number of servings",
        foodLogTime: "When did you eat?",
        buttonText: {
          trackFood: "Track food",
          remove: "Remove",
          save: "Save",
        },
        title: "Track food",
        editTitle: "Edit food",
        trackedAFoodSuccessfully: "You have successfully tracked food!",
        edittedATrackedFoodSuccessfully: "You have successfully editted a tracked food!",
        removedATrackedFoodSuccessfully: "You have successfully removed a tracked food!",
        trackedARecipeSuccessfully: "You have successfully tracked a recipe!",
        edittedATrackedRecipeSuccessfully: "You have successfully editted a tracked recipe!",
        removedATrackedRecipeSuccessfully: "You have successfully removed a tracked recipe!",
        confirmDialog: {
          title: "Remove food",
          subTitle: "Are you sure you want to remove this food from My progress?",
          ariaLabel: "Close remove food modal",
        },
      },
      addedFood: {
        screens: {
          foodOverview: {
            title: "Create a new food",
            header: "Food overview",
            subHeader: "Please check your food package and provide the information requested below.",
            buttonText: "Next",
          },
          nutritionFacts: { title: "Add nutrition facts", header: "Nutrition facts", buttonText: "Next" },
          ingredientList: {
            title: "Add ingredients",
            header: "Ingredient list",
            subHeader:
              "Check the nutrition label on your food package for a list of ingredients. You may include that information by adding ingredients.",
            buttonText: "Create and track food",
            searchPlaceholder: "Search for ingredient",
            clearIngredientSearch: "Clear search",
            searchResults: "Search results",
            noResultsFound:
              "No results found. If the ingredient is industrially manufactured, please exclude it from your search.",
          },
        },
        brandName: "Brand name",
        foodName: "Food name",
        servingSize: "Serving size",
        servingsPerContainer: "Servings per container",
        addedSuccessfully: "You have successfully created and tracked a new food!",
        addANewFood: "Add a new food",
        viewAddedFoods: "View added foods",
      },
      viewAllAddedFoods: {
        title: "Added foods",
        inputPlaceholder: "Search foods",
        inputAriaLabel: "Search for added foods",
        drawer: {
          title: "Food details",
          buttonText: "Remove",
          successfullyRemovedFood: "You have successfully removed an added food!",
          nutritionFacts: "Nutrition facts",
          notAvailable: "N/A",
          ingredientList: "Ingredient list",
          ingredientListUnavailable: "Ingredients were not provided at the time the food was added to the database.",
          confirmDialog: {
            title: "Remove added food",
            subtitle: "Are you sure you want to remove this from your added foods?",
            ariaLabel: "Close remove added food modal",
          },
          servingsPerContainer: (servingsPerContainer: number, calories: number) =>
            `${servingsPerContainer} servings per container - ${calories} calories each`,
          servingSize: (servingSize: string) => `Serving size: ${servingSize}`,
        },
      },
      viewRecipes: {
        title: "Recipes",
        inputPlaceholder: "Search my recipes",
        drawer: {
          confirmRemoveRecipeDialog: {
            title: "Remove recipe",
            subtitle: "Are you sure you want to remove this from your recipes?",
            successfullyRemovedRecipe: "You have successfully removed a recipe.",
          },
          confirmRemoveIngredientDialog: {
            title: "Remove ingredient",
            subtitle: "Are you sure you want to remove this ingredient from your recipe?",
            successfullyRemovedIngredient: "Ingredient removed.",
          },
          screens: {
            recipeDetails: {
              title: "Recipe details",
              removeButtonText: "Remove recipe",
              editButtonText: "Edit recipe",
              ingredientList: "Ingredient list",
              header: {
                serves: (servings: number) => `Serves ${servings} ${servings > 1 ? "people" : "person"}`,
                caloriesPerServing: (calories: number) => `${Math.round(calories)} calories per serving`,
              },
            },
            editRecipe: {
              title: "Edit recipe",
              ingredientList: "Ingredient list",
              addIngredientButtonText: "Add ingredient",
              saveRecipeButtonText: "Save recipe",
            },
            editIngredient: {
              title: "Edit ingredient",
              removeIngredientButtonText: "Remove",
              saveIngredientButtonText: "Save",
            },
            addIngredients: {
              title: "Add ingredients",
              header: "Ingredients",
              subHeader:
                "Recipes consist of different ingredients. Be sure to add all the ingredients used in your recipe.",
              searchResults: "Search results",
              noResultsFound:
                "No results found. If the ingredient is industrially manufactured, please exclude it from your search.",
              searchPlaceholder: "Search for ingredient",
              clearIngredientSearch: "Clear search",
            },
            confirmIngredient: { title: "Confirm ingredient", buttonText: "Confirm ingredient" },
          },
        },
      },
      recipes: {
        addANewRecipe: "Add a new recipe",
        editRecipes: "Edit recipes",
        title: "Create a new recipe",
        ingredientRemoved: "Ingredient removed.",
        noSearchResults:
          "You have not created any recipes matching this search term yet. You can add a new recipe for easy tracking in the future.",
        confirmationDialog: {
          title: "Remove ingredient",
          subtitle: "Are you sure you want to remove this ingredient from your recipe?",
        },
        screens: {
          recipeOverview: {
            title: "Create a new recipe",
            header: "Food overview",
            buttonText: "Next",
          },
          recipeIngredients: {
            title: "Add ingredients",
            header: "Ingredients",
            subHeader:
              "Recipes consist of different ingredients. Be sure to add all the ingredients used in your recipe.",
            buttonText: "Create and track recipe",
            searchPlaceholder: "Search for ingredient",
            clearIngredientSearch: "Clear search",
            searchResults: "Search results",
            noResultsFound:
              "No results found. If the ingredient is industrially manufactured, please exclude it from your search.",
            successMessage: "You have successfully created and tracked a recipe!",
          },
          editIngredient: {
            remove: "Remove",
            save: "Save",
            title: "Edit ingredient",
          },
          confirmIngredient: {
            title: "Confirm ingredient",
            buttonText: "Confirm ingredient",
          },
        },
        recipeName: "Recipe name",
        servings: "Servings (serves how many?)",
      },
      emptyState: {
        all: {
          header: "History",
          subHeader: "You have not tracked any food yet. To begin tracking, search for a food and track it.",
          noSearchResults: "You have not tracked a food matching this search term yet.",
        },
        addedFoods: {
          header: "No added foods yet",
          subHeader:
            "This tab displays the food you have added to our database. If you are unable to find a specific food, please add it for easy tracking in the future.",
          noSearchResults:
            "You have not tracked any added foods matching this search term yet. You can add a new food or search this food in our food database.",
        },
        myRecipes: {
          header: "No recipes yet",
          subHeader:
            "This tab displays the recipes you have added to our database. Add a recipe to track it effortlessly in the future.",
        },
        shared: {
          noSearchResults:
            "No results found. If you are unable to find a specific food, please add it for easy tracking in the future.",
        },
      },
    },
    goalSetting: {
      backButtonText: (origin: string) => `Back to ${origin}`,
      cadence: {
        weekly: "Weekly",
        daily: "Daily",
      },
      days: {
        sunday: "Sun",
        monday: "Mon",
        tuesday: "Tue",
        wednesday: "Wed",
        thursday: "Thu",
        friday: "Fri",
        saturday: "Sat",
      },
      emptyText:
        "Ready to take the first step? Set your first goal now and begin your journey. Your dietitian and health coach are here to discuss it with you and provide support along the way.",
      firstGoal: "Set your first goal",
      newGoal: {
        snackBarSuccess: "You have successfully created a new goal!",
        snackBarFail: "There was an error creating your new goal.",
        drawerHeader: "Set new goal",
        exitDialog: {
          title: "Exit goal creation",
          subTitle: "Are you sure you want to stop creating this goal?",
          ariaLabel: "Close exit goal changes modal",
        },
        duration: {
          header: "For how long would you like to work on your goal?",
          durationLabel: "Number of weeks",
          singleWeek: "week",
          multipleWeeks: "weeks",
        },
        frequency: {
          header: "How often do you want to work on this goal?",
          frequencyLabel: "Amount",
          cadenceLabel: "Cadence",
          scheduleLabel: "Customize schedule",
          scheduleDecription: "Select the days you aim to achieve this goal:",
          singleTime: "time",
          multipleTimes: "times",
          timesOptions: {
            1: "1 time",
            2: "2 times",
            3: "3 times",
            4: "4 times",
            5: "5 times",
          },
        },
        title: {
          header: "What goal do you want to accomplish?",
          body: "Think about your vision. It’s time to set a goal. Think about your who, what, when, where, and why questions.",
          helpText: "Ex. I will walk for 30 minutes every day",
        },
        review: {
          header: "Review goal details",
          goalTitle: "Goal name",
          goalFrequency: "Frequency",
          goalDuration: "Time period",
        },
      },
      goalsdotMotifAltText: "Goals background dot motif",
      saveGoal: "Save goal",
      streak: (streakType: "Current" | "Longest") => `${streakType} streak`,
      streakName: "streak",
      welcomePage: {
        specificGoals: {
          header: "Goals are best when they're specific",
          body: "A goal is easier to achieve if it answers questions like who, what, when, where, and why. What exactly do I want to do? When will I do it? Who is involved? Where will it happen? And why do I want to reach this goal?",
          secondary: "Skip and get started",
          primary: "Next",
        },
        achievableGoals: {
          header: "Achievable goals will bring you success",
          body: "The best goal is one you can realistically achieve, even when you're not feeling motivated, or life gets busy. Ask yourself: How can I accomplish this goal? Do I have the skills and resources? Is it realistic and a small step toward my larger goals?",
          secondary: "Back",
          primary: "Next",
        },
        getStarted: {
          header: "Let's get started",
          body: "Set a goal today and be one step closer to living the life you've always wanted for yourself.",
          secondary: "Back",
          primary: "Get started",
        },
      },
    },
    home: {
      articlesContainerHeader: "Articles",
      progress: {
        header: "My Progress",
        subheaderGoals: "Goals",
        subheaderTracking: "Tracking",
      },
      backToHome: "Back to home",
      carePlanSummary: {
        actionNeeded: "Action needed",
        carePlan: "Care Plan",
        header: "Let’s get started",
      },
    },
    memberDeactivated: {
      header: "Your account has been deactivated",
      body: "It looks like your account is no longer active. If you believe this is a mistake or need assitance, please contact our support <NAME_EMAIL>",
    },
    gutCheck: {
      pastResults: {
        testHistoryTitle: "Test history",
        compareButton: "Compare",
        cardTitle: "GutCheck results",
        viewButton: "View",
        errorLoadingResults: "Could not load past results. Please try again later.",
        dateNotAvailable: "Date not available",
      },
      tabs: {
        myOrder: "My order",
        pastResults: "Past results",
        ariaLabel: "GutCheck tabs",
      },
      orderSuccessful: "Order Successful!",
      errors: {
        statusLoadTitle: "Unable to Load GutCheck Information",
        statusLoadSubtitle:
          "There was an issue retrieving your GutCheck status. Please refresh the page or try again later.",
        generalLoadTitle: "Something went wrong loading GutCheck",
        generalLoadSubtitle: "Please refresh the page, or try again later",
      },
      myOrderTab: {
        errorLoading: "There was an error loading your order information. Please try again.",
        noOrdersYet: "You haven't placed any GutCheck orders yet.",
        kitOrderedTitle: "GutCheck kit ordered",
        kitDeliveryInfo: "You will receive your kit 3-5 business days after ordering",
        resultsReady: {
          title: "Your results are in!",
          subtitle: "Your gut data is now available to view and download",
          resultsCardTitle: "GutCheck results",
          whatToDoTitle: "What should I do now?",
        },
        careOptions: {
          dietitianConsult: {
            title: "Your results tell a story",
            description: "Meet with a Dietitian to get all your questions answered and how to move forward",
            buttonText: "Schedule call",
          },
          webinar: {
            title: "Join a webinar",
            description: "Join the next webinar to better understand your results",
            buttonText: "Join webinar",
          },
        },
        careTeam: {
          title: "My Care Team",
          chatButtonText: "Chat",
          laceyMessage: "I see you got your GutCheck results, w...",
        },
      },
      resultsReadyPrompt: {
        trackProgressTitle: "Track your progress with another test",
        trackProgressSubtitle: "See how your microbiome changes over time.",
        orderNextTestButton: "Order next test",
      },
      historyItem: {
        title: "Order for GutCheck",
        orderedOn: "Ordered on:",
        orderId: "Order ID:",
      },
      noPastResultsMessage: "Your GutCheck history will appear here when ready.",
      orderForm: {
        labels: {
          address: "Address",
          aptSuite: "Apt, suite",
          zipCode: "Zip Code",
          state: "State",
          city: "City",
        },
        validationMessages: {
          addressRequired: "Address is required",
          zipCodeRequired: "Zip Code is required",
          stateRequired: "State is required",
          cityRequired: "City is required",
          addressMaxLength: "Address can be at most 50 characters",
          aptSuiteMaxLength: "Apt/Suite can be at most 50 characters",
          cityMaxLength: "City can be at most 40 characters",
          zipPattern: "Zip code must be 5 digits",
        },
        uiText: {
          shippingInformation: "Shipping information",
          submitButton: "Submit GutCheck order",
        },
      },
      buttonOrder: "Order GutCheck test",
      intro: {
        buttonOrder: "Order GutCheck test now",
        screen1: {
          title: "The GutCheck test: What it is and why you should take it",
          imageAltText: "Gut microbiome illustration",
          microbiomeSectionTitle: "What is the gut microbiome?",
          microbiomeDefinitionBody:
            "Your gut microbiome is home to trillions of bacteria. Caring for these tiny organisms can help with digestion, immune function, and reducing chronic disease risk.",
          balancedMicrobiomeBenefitsBody:
            "A balanced microbiome can support fewer digestive issues, better energy, improved sleep, and more.",
          gutCheckLimitationsBody:
            "While the GutCheck cannot diagnose conditions, it can help you learn more about what’s happening inside your gut, and how to support it over time.",
          orderButtonText: "Order GutCheck test now",
        },
        screen2: {
          title: "The GutCheck test: What it is and why you should take it",
          microbiomeWhatIsTitle: "The GutCheck is your free, at-home gut microbiome test:",
          microbiomeWhatIsDescription:
            "It gives you a snapshot of the bacteria living in your gut—and how they might be affecting your health.",
          microbiomeWhyTakeItTitle: "Your results help your Dietitian to:",
          microbiomeNotSureTitle: "Not sure if it's for you?",
          microbiomeWhyTakeItItems: [
            "Help you make targeted changes to your diet and routine",
            "Set realistic goals based on your health history",
            "Track progress over time with repeat GutChecks",
          ],
          microbiomeNotSureDescription:
            "Even if you're not having symptoms, the GutCheck can be a starting point for discussing how to optimize your diet and support overall health.",
        },
        screen3: {
          title: "The GutCheck test: Unlock better health in 4 simple steps",
          subTitle: "How it works?",
          keyItems: [
            {
              title: "Receive test kit",
              description: "You will receive your kit 3-5 business days after ordering",
            },
            {
              title: "Collect sample",
              description:
                "Collect a tiny stool sample in seconds with our easy-to-use kit—completely private and handled only by our lab",
            },
            {
              title: "Send test kit back",
              description: "Mail kit back - prepaid shipping labels included",
            },
            {
              title: "Get results",
              description:
                "Get your microbiome results and review with your Dietitian to discover ways to improve your health",
            },
          ],
        },
      },
      formSubmissionErrors: {
        badRequest: "There was a problem with your order information. Please check the details and try again.",
        unauthorized: "Your session has expired or you are not authorized. Please log in again.",
        conflict: "We were unable to place your order. Please contact support for assistance.",
        serverError: "A server error occurred while processing your order. Please try again later.",
        unexpectedError: "An unexpected error occurred while submitting your order. Please try again.",
      },
    },
    microbiome: {
      abundance: "Abundance",
      sampleId: "Sample ID",
      microbiomeImg: "GutCheck image",
      disclaimer:
        "The GutCheck Test is intended for informational purposes only, not intended for any medical or diagnostic uses or to provide any medical advice, and has not been reviewed or authorized by any regulatory authority for use in any clinical or diagnostic procedure. This report does not constitute medical advice and is not a substitute for any professional medical advice or clinical diagnostic testing. Consult with a healthcare professional if you have any questions regarding your health or a medical condition.",
      reportDetailsTitle: "Report Details",
      reportDetailsSubtitle: "Here's more information on the bacteria in your gut.",
      high: "High",
      howYouCompare: "How You Compare",
      low: "Low",
      noContentHeader: "Results pending...",
      noContentSubHeader: "No current GutCheck results yet!",
      noContentBody1: "Have you ordered your at-home GutCheck microbiome test yet?",
      noContentBody2:
        "A gut microbiome test is a free, at-home test that can tell you more about the types of bacteria that live in your gut. By understanding the makeup of your gut, you will understand more about how the foods you eat affect your digestion, immune system, weight, and overall health. Your Dietitian can give you diet and lifestyle tips to support your unique needs and keep your microbiome healthy.",
      noContentBody3:
        "Enroll in the gut microbiome course by clicking on “GutCheck: free at home Microbiome test” within your Care Plan. Answer the GutCheck questionnaire and follow the instructions to ship the kit to your home.",
      normal: "Normal",
      notDetected: "Not detected",
      overviewTitle: "Welcome to your GutCheck Report",
      referenceRange: "Reference Range",
      report: "Report",
      scores: {
        Diversity: {
          subtitle: "This score represents the variety of bacteria present in your gut.",
          title: "Diversity",
        },
        Inflammatory: {
          subtitle: "This score represents the bacteria present that may detract from your health.",
          title: "Health Detractors",
        },
        Protective: {
          subtitle: "This score represents the bacteria present that may support your health.",
          title: "Health Promoters",
        },
        Unique: {
          subtitle:
            "This score represents the amount of bacteria you have in your gut that less than 20% of other people have.",
          title: "Unique Bacteria",
        },
      },
      tabs: {
        overview: "Overview",
        reportDetails: "Report Details",
      },
    },
    nurseTriage: {
      title: "Your symptoms may indicate that you need urgent assistance",
      subtitle: "Cylinder is not appropriate for members needing urgent care.",
      body: "If you are experiencing symptoms that are not emergent or life threatening, but require immediate attention, we recommend that you contact our Nurse Triage Line by clicking the button below.",
      warning: "If this is a life threatening medical emergency, please call 911.",
      option: "I acknowledge this recommendation and understand that Cylinder is not appropriate for emergencies.",
      confirmationDialogAction: "Continue",
      phoneNumber: "tel:+18008277437",
      phoneNumberDisplay: "(*************",
    },
    onboarding: {
      errorHeader: "Oops! Something went wrong",
      errorSubHeader:
        "It looks like there's been an error. Try refreshing the page. If that doesn't work, log out and log back in using the button below.",
    },
    ordering: {
      insuranceInformation: "Insurance information",
      insuranceInformationConfirmation: "Confirm insurance information",
      insuranceInformationConfirmationSubheader: "Please confirm the following insurance information.",
      reviewAddressHeader: "Your order will be delivered to:",
      shippingAddress: "Shipping address",
      shippingConfirmation: "Confirm your shipping address",
      shippingConfirmationSubheader: "Please confirm the following shipping address for your lab order.",
      orderConfirmation: "Information received",
      orderConfirmationSubheaderOne:
        "We have received your information and will contact you shortly with updates about your order.",
      orderConfirmationSubheaderTwo: "If you have questions, please contact our support team at",
      noShippingAddress: "No shipping address",
      noInsuranceInformation: "No insurance information",
      confimrationContinue: "Return to your care plan",
    },
    partnerPreVerify: {
      error: {
        partOne: "It looks like we are having trouble with your Cylinder login. Please go back to",
        partTwo: "and try to sign into Cylinder again. Still not working? You can contact",
        partThree: "for next steps.",
      },
      pageHeader: "Cylinder is a program to help you improve your health starting with digestion.",
      partnerToLabel: {
        PersonifyHealth: "Personify Health",
        Accolade: "Accolade",
        Solera: "Solera",
      },
      signedUpPrompt: "Have you already signed up for Cylinder?",
      emailLink:
        '<a style="text-decoration: none;" href="mailto:<EMAIL>"><EMAIL></a>',
    },
    partnerPreVerifySubscriber: {
      registered: {
        title: "Account already exists",
        body: "It looks like you already have an account with us. Please log in to get access, or contact <NAME_EMAIL> if you need help.",
      },
      notRegistered: {
        title: "Welcome to Cylinder Health!",
        body: "Your journey to better gut health starts here. To get started, just create your account below.",
        buttonText: "Create my account",
      },
      userRecoverableError: {
        title: "Your session has expired",
        body: "You’ll need to restart your registration. Head back to your Cylinder subscription on the UHC Store and click “Activate” to begin again.",
        buttonText: (partnerName: string) => `Restart on ${partnerName}`,
      },
      userNonRecoverableError: {
        title: "We ran into an issue",
        body: "Something went wrong while loading your eligibility information. Please try again, or reach <NAME_EMAIL> if you need help.",
      },
    },
    progress: {
      amButtonText: "AM",
      pmButtonText: "PM",
      calendarInfoBarMessage: "Colors indicate your symptom severity",
      emptyTrackerHistory:
        "This is where you can keep track of your day. Be sure to log as much info as you can, so that Cylinder can help you find patterns and reduce symptoms",
      trackerHeader: "Tracking",
      food: "Food",
      h2: "Hydrogen",
      stool: "Stool",
      symptoms: "Symptoms",
      noSympomsTitle: "It's great to hear you're symptom-free right now!",
      noSymptomsBody1:
        "Click 'Save' below to record your status. This helps your wellness team analyze the connections between your diet, stool, and symptoms.",
      noSymptomsBody2:
        "Remember, symptoms may appear later. You can track multiple times a day, so be sure to log any new symptoms.",
      symptomsTabs: {
        withSymptoms: "I have symptoms",
        withoutSymptoms: "No symptoms",
      },
      trackedSymptomsSuccessSnackbar: "You have tracked your symptoms!",
      trackedSymptomsEditSuccessSnackbar: "You have successfully edited symptoms!",
      trackedNoSymptomsSuccessSnackbar: "Thanks for tracking! If symptoms appear later, remember to track them.",
      severityStrings: {
        noSymptoms: "No Symptoms",
        mildSymptoms: "Mild Symptoms",
        moderateSymptoms: "Moderate Symptoms",
        severeSymptoms: "Severe Symptoms",
      },
      notesHeader: "Notes",
      noteSaving: "Saving note",
      pageHeader: "My Progress",
      updateProgressButtonText: "Track progress",
      updateProgressDropdown: "Track progress menu dropdown",
      trackFood: "Track food",
      trackSymptoms: "Track symptoms",
      todaysDate: "Today's date",
      isCurrentlySelectedDate: "Is currently selected date.",
      hitEnterToSelectDate: "Hit enter to select date",
      infoModalHeader: "Symptom severity",
      infoModalSevere:
        "On red days, you tracked several moderately severe symptoms or at least 1 severe or very severe symptom. Keep an eye on these days for potential trigger foods or situations.",
      infoModalModerate:
        "On orange days, you experienced a couple of mild symptoms or at least 1 moderate symptom. Based on your trend, your symptoms may be getting better or worse. Keep an eye out for triggers.",
      infoModalNone:
        "On green days, you experienced no symptoms! These days may indicate some good behaviors that you want to adopt in the future.",
      symptomDrawerHeader: "Track symptoms",
      symptomDrawerSubHeader: "Are you experiencing any symptoms at this time?",
      symptomDrawerSliderHeader: "How would you rate the severity of the following symptoms?",
      symptomDrawerDisclaimer: "If you are experiencing symptoms of a medical emergency, please call 911.",
    },
    reportIssuesModal: {
      header: "Please provide a brief description of the issue you are experiencing",
      formLabel: "Your description",
      messageLengthError: "Your message must be between 8 and 65000 characters in length.",
    },
    resetPassword: {
      formHeader: "Reset your password",
      formSubtitle: "Please enter your email to reset your password",
      sentEmailHeader: "Email sent",
      sentEmailText:
        "If an account exists with the email provided, you will receive a password reset link shortly. Please check your inbox and follow the instructions to reset your password.",
    },
    samlSSO: {
      authenticationErrors: {
        failedMemberMatch: {
          title: "It looks like you haven't finished setting up your account",
          textBody:
            "Please click the Log in button below to finish. If you don't have an account yet, you'll need to click the Create account button to create one.",
          additionalTextBody:
            "After completing registration, you'll be able to use Single Sign-On (SSO) for future logins.",
        },
        redirectToPartner: {
          title: "It looks like you haven't finished setting up your account",
          textBody:
            "To use Cylinder, you'll need to complete your registration. Click the button below to go back to your provider's site and finish setting things up.",
        },
        failedSignin: {
          title: "Something went wrong",
          textBody:
            "We couldn't complete your sign-in. Please try again or reach <NAME_EMAIL> for help.",
        },
      },
      finishRegistrationBtnText: "Finish registration",
    },
    settings: {
      about: "About",
      account: "Account",
      changePasswordButtonText: "Change Password",
      contactUs: "Contact us",
      email: "Email",
      hipaaTerms: "HIPAA Terms",
      notifications: "Notifications",
      password: "Password",
      passwordPlaceholder: "****************",
      privacyPolicy: "Privacy Policy",
      receiveEmailNotifications: "Receive notifications by email",
      settings: "Settings",
      support: "Support",
      termsOfUse: "Terms of Use",
      trustedDevices: "Trusted devices",
      remove: "Remove",
      deviceAdded: "Added on:",
      a11y: {
        feedbackEmail: "feedback email",
        contactEmail: "contact us email",
        privacyPolicyLink: "privacy policy link",
        termsLink: "terms of use link",
        hipaaLink: "HIPAA terms link",
      },
    },
    scheduling: {
      ariaLabels: {
        nextDay: "Next day",
        previousDay: "Previous day",
      },
      chooseAppointmentTime: "Choose your appointment time",
      disclaimer: "You must be physically located in the state you specified at the time of your appointment.",
      confirmation: {
        header: "Review and confirm appointment",
        note: {
          one: "Note: If you're looking to review your GutCheck results, please attend a ",
          link: {
            text: "daily live event",
            href: "https://go.cylinderhealth.com/events/gutcheck-webinar/",
          },
          two: " or schedule time with your Dietitian.",
        },
      },
      noAvailableAppointments:
        "Looks like we don't have any available doctors on this day. Check other dates or give us a call at 1-************ so we can assist.",
      stateConfirmationHeader: "Please confirm the state you are in:",
      appointmentConfirmation: {
        getReady: "Get ready for your appointment",
        getReadyBulletPoints: [
          "Gather medical history and medications",
          "Test internet, microphone, and speakers",
          "Prepare questions & concerns",
          "Find a quiet and private space",
          "We will be calling you from 1-************",
        ],
      },
      locationNote: "You must be physically located in the state you specified at the time of your appointment.",
      medicalTeamNote:
        "The Cylinder medical team is made up of gastroenterologists, internal medicine, and family medicine physicians. By selecting your state, we'll ensure you're matched with doctors licensed in your state who will help you address your digestive issues.",
      schedulingError:
        "We encountered an issue. Please try a different time slot. If the issue persists, <NAME_EMAIL>",
      rescheduleLessThanTimeAllowed: "Rescheduling is unavailable within 1 minute of your appointment start time.",
      rescheduleError:
        "We've encountered a problem rescheduling your appointment. Please refresh the page and try again.",
      timeSlotRetrievalError: {
        title: "Unable to load appointments",
        body: "We had trouble retrieving available appointments. Please try refreshing the page <NAME_EMAIL> if the issue persists.",
        buttonText: "Refresh",
      },
    },
    signUp: {
      formHeader: "Create your account",
      formPrompt: "Please enter your email and password to create your account.",
      formDisclaimer:
        "Cylinder is not intended to be used for urgent or emergent conditions. If you think you may have a medical emergency, call your doctor or 911 immediately.",
      termsTextPart1: "I agree to the",
      termsAndConditions: "Terms and Conditions",
      termsTextPart2: " and",
      privacyPolicy: "Privacy Policy",
      confirmPasswordLabel: "Confirm Password",
      registrationCodeAssociationError:
        "It looks like we are having trouble creating your account. <NAME_EMAIL> for assistance.",
      registrationCodeAssociationDiscriminator: "registration code association failure",
      signUpError:
        "We encountered an issue while processing your request. Please try a different email address or log in if you already have an account",
      genericSignUpError:
        "Something went wrong. Please try again <NAME_EMAIL> for assistance.",
      successfulSignUpFailedLogin:
        "Your account was created successfully, but we ran into an issue signing you in. Please try logging in again.",
    },
    oidcSSO: {
      authenticationErrors: {
        popupBlocked: {
          title: "Sign-in popup blocked",
          textBody: "It looks like your browser has blocked the popup window needed to complete your sign-in.",
          additionalTextBody: "To resolve this:",
          steps: [
            "Locate the popup blocker icon (usually in the top-right corner of your browser).",
            "Allow popups for this site.",
            "Refresh the page and try signing in again.",
          ],
          footerText:
            "If you continue to experience issues, please check your browser settings <NAME_EMAIL> for assistance.",
        },
        popupClosedByUser: {
          title: "Sign-in popup closed",
          textBody:
            "It looks like you closed the sign-in popup. Please click the button below to try again or refresh the page.",
        },
        failedMemberMatch: {
          title: "It looks like you haven't finished setting up your account",
          textBody:
            "Please click the Log in button below to finish. If you don't have an account yet, you'll need to click the Create account button to create one.",
          additionalTextBody:
            "After completing registration, you'll be able to use Single Sign-On (SSO) for future logins.",
        },
        unableToSignIn: {
          title: "Unable to Sign In",
          textBody:
            "There was an issue with your sign-in request. Please try again or log in below with your Cylinder username and password.",
          additionalTextBody: "If issues persist, contact <EMAIL> for assistance.",
        },
        nonUserRecoverableError: {
          title: "Unable to Sign In",
          textBody:
            "There was an issue with your sign-in request. <NAME_EMAIL> for assistance.",
        },
      },
    },
    survey: {
      feet: "Feet",
      inches: "Inches",
      weight: "Weight (lbs)",
      datePlaceholder: "Select a date",
      dotMotifAltText: (position: "top" | "bottom") => `${position} background dot motif`,
      ariaLabels: {
        inchesInput: "Enter height in inches",
        feetInput: "Enter height in feet",
        weightInput: "Enter weight in pounds",
        informationDescription: (title?: string) => `${title ?? ""} description`,
      },
      surveyFetchFailed: (isIntakeSurvey: boolean) =>
        `We ran into an issue loading your ${
          isIntakeSurvey ? "onboarding questionnaire" : "survey"
        }. Please try refreshing the page or click Retry below. If the problem persists, <NAME_EMAIL>`,
    },
    updateVerificationMethod: {
      updatedVerificationMethodHeader: "We've updated our verification method",
      updatedVerificationMethodSubheader:
        "For added security, we are now using SMS verification instead of email to verify your identity. Please provide your phone number below to complete the verification process.",
      updatePhoneNumberHeader: "Update your phone number",
      updatedPhoneNumberSubheader:
        "Please enter your phone number below to ensure you can verify your identity and access your account.",
    },
    welcome: {
      header: "Welcome to Better Digestive Health",
      newMemberButton: "Register now",
      existingMemberButton: "Already a member? Log in now",
    },
    welcomeMobileApp: {
      body: "This page is not optimized for mobile web. To get the most out of Cylinder, download our app. With Cylinder, you'll have instant access to customized care for any gut health condition, from minor discomfort to chronic issues.",
      download: "Download it now on Apple or Google Play for easy access to better digestive health, anytime anywhere.",
      contact: {
        one: "If you need any help, please feel free to reach out by email at ",
        email: "<EMAIL>",
        two: " or call us at ",
        phone: {
          text: "1.833.33MYGUT (**************)",
          number: "1-************",
        },
      },
      copyright: (year: number) => `©${year} Cylinder Health® - All Rights Reserved`,
      footerLinks: {
        terms: "Terms and Conditions",
        privacy: "Privacy Policy",
        hipaa: "HIPAA",
      },
    },
  },
  audioComponent: {
    noSupportMessage: "Your browser does not support the audio element.",
  },
  havingTrouble: {
    helpText: "Having trouble?",
    helpTextLink: "Contact support",
  },
  smsUsageDisclaimer: {
    one: "By providing your phone number, you agree to receive text messages from Cylinder Health. Message frequency varies. Message and data rates may apply. Text HELP or email ",
    link: {
      text: "<EMAIL>",
      href: "mailto:<EMAIL> ",
    },
    two: " for assistance. Text STOP to opt-out and stop receiving messages from Cylinder Health.",
  },
  sharedFormText: {
    requiredMessage: "This field is required",
    patternMessage: "Please provide a valid input",
    minLengthMessage: "This field is too short",
    maxLengthMessage: "This field is too long",
    formatMaximumMessage: "This field overlaps maximum",
    formatMinimumMessage: "This field is below minimum",
    email: "email",
    password: "password",
    mobilePhone: "Mobile phone",
    numberOnly: "This field only accepts numbers",
    invalidDateTime: "Please provide a valid date and time",
    optional: "Optional",
    required: "Required",
    invalidMobilePhone: "Please enter a valid phone number",
  },
  buttonText: {
    answerNow: "Answer now",
    back: "Back",
    backToLogin: "Back to login",
    cancel: "Cancel",
    callNow: "Call now",
    close: "Close",
    complete: "Complete",
    confirm: "Confirm",
    confirmAppointment: "Confirm appointment",
    continue: "Continue",
    continueToCylinder: "Continue to Cylinder",
    createAccount: "Create account",
    delete: "Delete",
    dismiss: "Dismiss",
    doThisNow: "Do this now",
    done: "Done",
    edit: "Edit",
    forgotPassword: "Forgot password?",
    gotIt: "Got it",
    goBack: "Go back",
    logOut: "Log out",
    logIn: "Log in",
    needHelp: "Need help?",
    next: "Next",
    no: "No",
    ok: "OK",
    previous: "Previous",
    readMore: "Read more",
    reportIssue: "Report an issue",
    resume: "Resume",
    retry: "Retry",
    review: "Review",
    reset: "Reset",
    remindMeLater: "Remind me later",
    save: "Save",
    scheduleAnAppointment: "Schedule an appointment",
    scheduleAppointment: "Schedule appointment",
    select: "Select",
    send: "Send",
    sendMessage: "Send message",
    settings: "Settings",
    skip: "Skip",
    submit: "Submit",
    start: "Start",
    tryAgain: "Try again",
    update: "Update",
    viewAll: "View all",
    yes: "Yes",
    yesCancel: "Yes, cancel",
  },
  // This part of the object is used to map the tab title. Check `screenNamePageTitleKeyMapper.ts` where these values are used
  pageTitles: {
    appointments: "Appointment Schedule - Cylinder",
    articleCatalog: "Articles Catalogue - Cylinder",
    articleCategory: "Article Category - Cylinder",
    articleDetails: "Article Details - Cylinder",
    changePassword: "Change Password - Cylinder",
    courseCatalog: "Courses Catalogue - Cylinder",
    courseCategory: "Course Category - Cylinder",
    courseDetails: "Course Details - Cylinder",
    carePlan: "Care Plan - Cylinder",
    carePlanDetail: "Module - Cylinder",
    chat: "Chat - Cylinder",
    accessCode: "Do you have an access code?",
    deleteAccount: "Delete Account - Cylinder",
    foodTracking: "Food Tracking - Cylinder",
    foodTrackingViewAddedFoods: "Added Foods - Cylinder",
    foodTrackingViewRecipes: "Recipes - Cylinder",
    cylinder: "Cylinder",
    goalSettingWelcome: "Goals Welcome - Cylinder",
    gutCheck: "Gut Check - Cylinder",
    home: "Cylinder",
    nurseTriage: "Nurse Triage - Cylinder",
    gutCheckResults: "Gut Check Results - Cylinder",
    pageNotFound: "Page Not Found - Cylinder",
    passwordReset: "Password Reset - Cylinder",
    progress: "My Progress - Cylinder",
    settings: "Settings - Cylinder",
    signIn: "Sign In - Cylinder",
    signUp: "Sign Up - Cylinder",
    emailVerification: "Email Verification - Cylinder",
    smsVerification: "SMS Verification - Cylinder",
    partnerPreVerify: "Cylinder",
    team: "Care Team - Cylinder",
    welcome: "Welcome to Cylinder",
    updateVerificationMethod: "Update Verification Method - Cylinder",
  },
  cylinderLinks: {
    termsAndConditions: "https://cylinderhealth.com/terms-of-use",
    privacyPolicy: "https://cylinderhealth.com/privacy-policy",
    hipaaLink: "https://cylinderhealth.com/hipaa",
    contactUsEmail: "<EMAIL>",
    feedbackEmail: "<EMAIL>",
    mobileApp: {
      androidAppStore: "https://play.google.com/store/apps/details?id=com.vivantehealth.githrive",
      appleAppStore: "https://itunes.apple.com/us/app/githrive/id1350680801",
    },
  },
  announcements: {
    cylinderReleaseBold: "GIThrive is now Cylinder",
    cylinderReleaseLink: "https://app.cylinderhealth.com/learn/articles/04515d8f-a36a-4363-8964-b674d3557c1a",
  },
} as const;
