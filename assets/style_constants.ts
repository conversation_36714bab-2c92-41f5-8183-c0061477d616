import { spacing, radius } from "@vivantehealth/design-tokens";

const headerHeight = "64px";

export const styleConstants = {
  headerHeight,
  bannerHeight: "52px",
  contentMaxWidth: "684px !important",
  contentHeightIntakeSurvey: `calc(100vh - ${headerHeight})`,
  navDrawerWidth: 240,
  navDrawerClosedWidth: 96,
};

export const iconSize = {
  // defaults
  xs: { width: "12px", height: "12px" },
  sm: { width: "16px", height: "16px" },
  smd: { width: "20px", height: "20px" },
  md: { width: "24px", height: "24px" },
  lg: { width: "32px", height: "32px" },
  xl: { width: "40px", height: "40px" },
  // custom
  logo: { width: "170px", height: "42px" },
  headerLogo: { width: "97px", height: "24px" },
} as const;

export const SPACING_0_PX = `${spacing.space0}px`;
export const SPACING_4_PX = `${spacing.space1}px`;
export const SPACING_8_PX = `${spacing.space2}px`;
export const SPACING_12_PX = `${spacing.space3}px`;
export const SPACING_16_PX = `${spacing.space4}px`;
export const SPACING_24_PX = `${spacing.space5}px`;
export const SPACING_32_PX = `${spacing.space6}px`;
export const SPACING_40_PX = `${spacing.space7}px`;
export const SPACING_48_PX = `${spacing.space8}px`;
export const SPACING_56_PX = `${spacing.space9}px`;
export const SPACING_64_PX = `${spacing.space10}px`;
export const SPACING_80_PX = `${spacing.space11}px`;

export const RADIUS_0_PX = `${radius.radius0}px`;
export const RADIUS_4_PX = `${radius.radius1}px`;
export const RADIUS_8_PX = `${radius.radius2}px`;
export const RADIUS_12_PX = `${radius.radius3}px`;
export const RADIUS_16_PX = `${radius.radius4}px`;
export const RADIUS_FULL_PX = `${radius.radiusFull}px`;
