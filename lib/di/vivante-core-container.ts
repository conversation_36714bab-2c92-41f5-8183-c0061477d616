import {
  ClientInfoProvider,
  FirebaseClient,
  ServerEventsClient,
  CalendarClient,
  HttpClient,
  BearerAuthenticatedHttpClient,
  ClickStreamClient,
  ClickStreamRepository,
  AccoladeDecoderWebRepository,
  AccoladeDecoderUseCaseFactory,
  SoleraDecoderUseCaseFactory,
  SoleraDecoderWebRepository,
  CustomUserFoodsFirestoreRepository,
  NutritionUseCaseFactory,
  RecipeLogFirestoreRepository,
} from "@vivantehealth/vivante-core";
import {
  ActionPlanUseCaseFactory,
  ActivityUseCaseFactory,
  AuthenticationUseCaseFactory,
  ArticlesUseCaseFactory,
  CareTeamUseCaseFactory,
  ClickStreamUseCaseFactory,
  ConversationsUseCaseFactory,
  CoursesUseCaseFactory,
  EligibilityUseCaseFactory,
  GutcheckUseCaseFactory,
  HistoryUseCaseFactory,
  MemberUseCaseFactory,
  MonitoringUseCaseFactory,
  ServerEventsUseCaseFactory,
  SessionsUseCaseFactory,
  ShippingAddressUseCaseFactory,
  SlideshowUseCaseFactory,
  TicketReportUseCaseFactory,
  ToDosUseCaseFactory,
  UriParseUseCase,
  WitchUseCaseFactory,
  OIDCDecoderUseCaseFactory,
  NotesUseCaseFactory,
  OrderingUseCaseFactory,
  SurveyUseCaseFactory,
  AppointmentsUseCaseFactory,
  MemberPreferencesUseCaseFactory,
  SSOUseCaseFactory,
} from "@vivantehealth/vivante-core";
import {
  MemberUserWebRepository,
  CareTeamUserWebRepository,
  SessionsWebRepository,
  AuthenticationRepository,
  ToDosWebRepository,
  WitchWebRepository,
  ArticlesWebRepository,
  GiMateFirestoreRepository,
  ObservableAuthClient,
  ActionPlanFirestoreRepository,
  SlideshowWebRepository,
  QuestionAnswersFirestoreRepository,
  MonitorQuestionFirestoreRepository,
  SymptomsFirestoreRepository,
  StoolsFirestoreRepository,
  ConversationsWebRepository,
  CoursesWebRepository,
  ServerEventsWebRepository,
  ActivityWebRepository,
  ShippingAddressWebRepository,
  TicketReportWebRepository,
  GutCheckWebRepository,
  FoodLogFirestoreRepository,
  EligibilityWebRepository,
  EligibilityProcessWebRepository,
  CalendarRepository,
  OIDCDecoderWebRepository,
  NotesFirestoreRepository,
  NoteCategoriesMockRepository,
  OrderingWebRepository,
  SurveyWebRepository,
  AppointmentsWebRepository,
  MemberPreferencesFirestoreRepository,
  SSORepository,
} from "@vivantehealth/vivante-core";
import { Environment, EnvironmentConfig, environments } from "@vivantehealth/vivante-core";

import { WebFirebaseApp, WebFirebaseClient } from "@Lib/infrastructure/cloud-store";

import { WebFirebaseAuthClient } from "../infrastructure";
import { AxiosHttpClient } from "../infrastructure";
import { WebServerEventsClient } from "../infrastructure";
import { WebCalendarClient } from "../infrastructure";
import { MatomoClickStreamClient } from "../infrastructure";
import { WebClientInfoProvider } from "../infrastructure/client-info-provider/web-client-info-provider";

export class VivanteCoreContainer {
  private envConfig!: EnvironmentConfig;

  public authClient!: ObservableAuthClient;

  private bearerAuthenticatedHttpClient!: BearerAuthenticatedHttpClient;

  private httpClient!: HttpClient;

  public firebaseClient!: FirebaseClient;

  private serverEventsClient!: ServerEventsClient;

  public webFirebaseApp!: WebFirebaseApp;

  private clientInfoProvider!: ClientInfoProvider;

  private calendarClient!: CalendarClient;

  private clickStreamClient!: ClickStreamClient;

  /**
   * Repositories
   */
  private authenticationRepository!: AuthenticationRepository;

  private memberUserRepository!: MemberUserWebRepository;

  private careTeamUserRepository!: CareTeamUserWebRepository;

  private sessionsRepository!: SessionsWebRepository;

  private toDosRepository!: ToDosWebRepository;

  private giMateRepository!: GiMateFirestoreRepository;

  private witchRepository!: WitchWebRepository;

  private articlesRepository!: ArticlesWebRepository;

  private symptomsRepository!: SymptomsFirestoreRepository;

  private actionPlanRepository!: ActionPlanFirestoreRepository;

  private slideshowRepository!: SlideshowWebRepository;

  private monitorQuestionRepository!: MonitorQuestionFirestoreRepository;

  private questionAnswersRepository!: QuestionAnswersFirestoreRepository;

  private stoolsRepository!: StoolsFirestoreRepository;

  private conversationsRepository!: ConversationsWebRepository;

  private coursesRepository!: CoursesWebRepository;

  private serverEventsRepository!: ServerEventsWebRepository;

  private activityRepository!: ActivityWebRepository;

  private gutCheckRepository!: GutCheckWebRepository;

  private shippingAddressRepository!: ShippingAddressWebRepository;

  private ticketReportRepository!: TicketReportWebRepository;

  private customUserFoodsRepository!: CustomUserFoodsFirestoreRepository;

  private foodLogRepository!: FoodLogFirestoreRepository;

  private recipeLogRepository!: RecipeLogFirestoreRepository;

  private eligibilityRepository!: EligibilityWebRepository;

  private eligibilityProcessRepository!: EligibilityProcessWebRepository;

  private calendarRepository!: CalendarRepository;

  private oidcDecoderRepository!: OIDCDecoderWebRepository;

  private accoladeDecoderRepository!: AccoladeDecoderWebRepository;

  private soleraDecoderRepository!: SoleraDecoderWebRepository;

  private clickStreamRepository!: ClickStreamRepository;

  private notesRepository!: NotesFirestoreRepository;

  private noteCategoriesRepository!: NoteCategoriesMockRepository;

  private orderingRepository!: OrderingWebRepository;

  private surveyRepository!: SurveyWebRepository;

  private appointmentsRepository!: AppointmentsWebRepository;

  private memberPreferencesRepository!: MemberPreferencesFirestoreRepository;

  private ssoRepository!: SSORepository;

  constructor(env: Environment) {
    this.setEnvironment(env);

    this.setEnvironment = this.setEnvironment.bind(this);
  }

  public setEnvironment(env: Environment): void {
    this.envConfig = environments[env];
    this.webFirebaseApp = new WebFirebaseApp(this.envConfig);
    this.authClient = new ObservableAuthClient(new WebFirebaseAuthClient(this.webFirebaseApp));
    this.httpClient = AxiosHttpClient.createDefault();
    this.bearerAuthenticatedHttpClient = new BearerAuthenticatedHttpClient(this.httpClient, this.authClient);
    this.firebaseClient = new WebFirebaseClient(this.webFirebaseApp);

    this.serverEventsClient = new WebServerEventsClient(this.envConfig.sseURL, this.authClient);
    this.clientInfoProvider = new WebClientInfoProvider();
    this.calendarClient = new WebCalendarClient();

    this.clickStreamClient = new MatomoClickStreamClient(this.envConfig.clickStreamEndpoint);
    /**
     * Repositories
     */
    this.authenticationRepository = new AuthenticationRepository(this.authClient);
    this.memberUserRepository = new MemberUserWebRepository(
      this.bearerAuthenticatedHttpClient,
      this.envConfig.memberApi,
      this.serverEventsClient,
    );
    this.careTeamUserRepository = new CareTeamUserWebRepository(
      this.bearerAuthenticatedHttpClient,
      this.envConfig.memberApi,
      this.serverEventsClient,
    );
    this.sessionsRepository = new SessionsWebRepository(
      this.bearerAuthenticatedHttpClient,
      this.envConfig.appointmentApi,
    );
    this.toDosRepository = new ToDosWebRepository(
      this.bearerAuthenticatedHttpClient,
      this.envConfig.memberApi,
      this.serverEventsClient,
    );
    this.giMateRepository = new GiMateFirestoreRepository(this.firebaseClient);
    this.witchRepository = new WitchWebRepository(this.bearerAuthenticatedHttpClient, this.envConfig.witchApi);
    this.articlesRepository = new ArticlesWebRepository(
      this.bearerAuthenticatedHttpClient,
      this.envConfig.articleApi,
      this.clientInfoProvider,
    );

    this.customUserFoodsRepository = new CustomUserFoodsFirestoreRepository(this.firebaseClient);
    this.foodLogRepository = new FoodLogFirestoreRepository(this.firebaseClient);
    this.recipeLogRepository = new RecipeLogFirestoreRepository(this.firebaseClient);
    this.symptomsRepository = new SymptomsFirestoreRepository(this.firebaseClient);
    this.actionPlanRepository = new ActionPlanFirestoreRepository(this.firebaseClient);
    this.slideshowRepository = new SlideshowWebRepository(
      this.bearerAuthenticatedHttpClient,
      this.envConfig.slideshowApi,
    );
    this.monitorQuestionRepository = new MonitorQuestionFirestoreRepository(this.firebaseClient);
    this.questionAnswersRepository = new QuestionAnswersFirestoreRepository(this.firebaseClient);
    this.stoolsRepository = new StoolsFirestoreRepository(this.firebaseClient);
    this.conversationsRepository = new ConversationsWebRepository(
      this.bearerAuthenticatedHttpClient,
      this.envConfig.conversationsApi,
      this.serverEventsClient,
    );
    this.coursesRepository = new CoursesWebRepository(
      this.bearerAuthenticatedHttpClient,
      this.envConfig.courseApi,
      this.serverEventsClient,
      this.clientInfoProvider,
    );
    this.serverEventsRepository = new ServerEventsWebRepository(this.serverEventsClient);
    this.activityRepository = new ActivityWebRepository(this.bearerAuthenticatedHttpClient, this.envConfig.activityApi);
    this.gutCheckRepository = new GutCheckWebRepository(
      this.bearerAuthenticatedHttpClient,
      this.envConfig.microbiomeApi,
    );
    this.shippingAddressRepository = new ShippingAddressWebRepository(
      this.bearerAuthenticatedHttpClient,
      this.envConfig.shippingAddressApi,
    );
    this.ticketReportRepository = new TicketReportWebRepository(
      this.bearerAuthenticatedHttpClient,
      this.envConfig.ticketReportApi,
    );

    this.eligibilityRepository = new EligibilityWebRepository(this.httpClient, this.envConfig.eligibilityApi);

    this.eligibilityProcessRepository = new EligibilityProcessWebRepository(
      this.bearerAuthenticatedHttpClient,
      this.envConfig.eligibilityProcessApi,
    );

    this.calendarRepository = new CalendarRepository(this.calendarClient);
    this.oidcDecoderRepository = new OIDCDecoderWebRepository(
      this.bearerAuthenticatedHttpClient,
      this.envConfig.oidcDecodeEndpoint,
    );

    this.accoladeDecoderRepository = new AccoladeDecoderWebRepository(
      this.bearerAuthenticatedHttpClient,
      this.envConfig.accoladeDecodeEndpoint,
    );

    this.soleraDecoderRepository = new SoleraDecoderWebRepository(
      this.bearerAuthenticatedHttpClient,
      this.envConfig.soleraDecodeEndpoint,
    );

    this.clickStreamRepository = new ClickStreamRepository(this.clickStreamClient);

    this.notesRepository = new NotesFirestoreRepository(this.firebaseClient);

    this.noteCategoriesRepository = new NoteCategoriesMockRepository();

    this.orderingRepository = new OrderingWebRepository(
      this.bearerAuthenticatedHttpClient,
      this.envConfig.orderingEndpoint,
    );

    this.surveyRepository = new SurveyWebRepository(this.bearerAuthenticatedHttpClient, this.envConfig.surveyEndpoint);

    this.appointmentsRepository = new AppointmentsWebRepository(
      this.bearerAuthenticatedHttpClient,
      this.envConfig.appointmentApi,
      this.serverEventsClient,
    );

    this.memberPreferencesRepository = new MemberPreferencesFirestoreRepository(this.firebaseClient);

    this.ssoRepository = new SSORepository(this.firebaseClient);
  }

  /**
   * UseCase Factories
   */

  public getWebFirebaseApp(): WebFirebaseApp {
    return this.webFirebaseApp;
  }

  public getAuthenticationUseCaseFactory(): AuthenticationUseCaseFactory {
    return new AuthenticationUseCaseFactory(this.authenticationRepository, this.memberUserRepository);
  }

  public getMemberUseCaseFactory(): MemberUseCaseFactory {
    return new MemberUseCaseFactory(this.memberUserRepository);
  }

  public getCareTeamUseCaseFactory(): CareTeamUseCaseFactory {
    return new CareTeamUseCaseFactory(this.careTeamUserRepository, this.sessionsRepository);
  }

  public getSessionsUseCaseFactory(): SessionsUseCaseFactory {
    return new SessionsUseCaseFactory(this.sessionsRepository, this.careTeamUserRepository, this.calendarRepository);
  }

  public getToDosUseCaseFactory(): ToDosUseCaseFactory {
    return new ToDosUseCaseFactory(this.toDosRepository);
  }

  public getWitchUseCaseFactory(): WitchUseCaseFactory {
    return new WitchUseCaseFactory(this.witchRepository, this.toDosRepository);
  }

  public getArticlesUseCaseFactory(): ArticlesUseCaseFactory {
    return new ArticlesUseCaseFactory(this.articlesRepository);
  }

  public getHistoryUseCaseFactory(): HistoryUseCaseFactory {
    return new HistoryUseCaseFactory(
      this.giMateRepository,
      this.foodLogRepository,
      this.symptomsRepository,
      this.stoolsRepository,
    );
  }

  public getActionPlanUseCaseFactory(): ActionPlanUseCaseFactory {
    return new ActionPlanUseCaseFactory(
      this.actionPlanRepository,
      this.calendarRepository,
      this.symptomsRepository,
      this.stoolsRepository,
      this.foodLogRepository,
    );
  }

  public getSlideshowUseCaseFactory(): SlideshowUseCaseFactory {
    return new SlideshowUseCaseFactory(this.slideshowRepository);
  }

  public getUriParseUseCase(): UriParseUseCase {
    return new UriParseUseCase();
  }

  public getMonitoringUseCaseFactory(): MonitoringUseCaseFactory {
    return new MonitoringUseCaseFactory(this.monitorQuestionRepository, this.questionAnswersRepository);
  }

  public getCoursesUseCaseFactory(): CoursesUseCaseFactory {
    return new CoursesUseCaseFactory(this.coursesRepository);
  }

  public getConversationsUseCaseFactory(): ConversationsUseCaseFactory {
    return new ConversationsUseCaseFactory(this.conversationsRepository);
  }

  public getServerEventsUseCaseFactory(): ServerEventsUseCaseFactory {
    return new ServerEventsUseCaseFactory(this.serverEventsRepository);
  }

  public getActivityUseCaseFactory(): ActivityUseCaseFactory {
    return new ActivityUseCaseFactory(this.activityRepository);
  }

  public getGutcheckUseCaseFactory(): GutcheckUseCaseFactory {
    return new GutcheckUseCaseFactory(this.gutCheckRepository);
  }

  public getShippingAddressUseCaseFactory(): ShippingAddressUseCaseFactory {
    return new ShippingAddressUseCaseFactory(this.shippingAddressRepository);
  }

  public getTicketUseCaseFactory(): TicketReportUseCaseFactory {
    return new TicketReportUseCaseFactory(this.ticketReportRepository);
  }

  public getEligibilityUseCaseFactory(): EligibilityUseCaseFactory {
    return new EligibilityUseCaseFactory(this.eligibilityRepository, this.eligibilityProcessRepository);
  }

  public getOIDCDecoderUseCaseFactory(): OIDCDecoderUseCaseFactory {
    return new OIDCDecoderUseCaseFactory(this.oidcDecoderRepository);
  }

  public getAccoladeDecoderUseCaseFactory(): AccoladeDecoderUseCaseFactory {
    return new AccoladeDecoderUseCaseFactory(this.accoladeDecoderRepository);
  }

  public getSoleraDecoderUseCaseFactory(): SoleraDecoderUseCaseFactory {
    return new SoleraDecoderUseCaseFactory(this.soleraDecoderRepository);
  }

  public getClickStreamUseCaseFactory(): ClickStreamUseCaseFactory {
    return new ClickStreamUseCaseFactory(this.clickStreamRepository);
  }

  public getNotesUseCaseFactory(): NotesUseCaseFactory {
    return new NotesUseCaseFactory(this.notesRepository, this.noteCategoriesRepository);
  }

  public getOrderingUseCaseFactory(): OrderingUseCaseFactory {
    return new OrderingUseCaseFactory(this.orderingRepository);
  }

  public getSurveyUseCaseFactory(): SurveyUseCaseFactory {
    return new SurveyUseCaseFactory(this.surveyRepository);
  }

  public getAppointmentsUseCaseFactory(): AppointmentsUseCaseFactory {
    return new AppointmentsUseCaseFactory(this.appointmentsRepository, this.calendarRepository);
  }

  public getNutritionUseCaseFactory(): NutritionUseCaseFactory {
    return new NutritionUseCaseFactory(
      this.foodLogRepository,
      this.customUserFoodsRepository,
      this.recipeLogRepository,
    );
  }

  public getMemberPreferencesUseCaseFactory(): MemberPreferencesUseCaseFactory {
    return new MemberPreferencesUseCaseFactory(this.memberPreferencesRepository);
  }

  public activateSSOUseCaseFactory(): SSOUseCaseFactory {
    return new SSOUseCaseFactory(this.ssoRepository);
  }
}
