import { HttpClient, Params, Response } from "@vivantehealth/vivante-core";
import { StatusHandlerHttpClient } from "@vivantehealth/vivante-core";
import axios, { AxiosError, AxiosResponse } from "axios";

// const isProd: boolean = environment.production
export class AxiosHttpClient implements HttpClient {
  private constructor() {}

  private static readonly _timeout = 30000;

  public static createDefault(): HttpClient {
    return new StatusHandlerHttpClient(new AxiosHttpClient());
  }

  // tslint:disable-next-line: member-ordering
  private static mapAxiosException(error: Error): Response {
    const axiosError = error as AxiosError;

    if (axiosError.isAxiosError) {
      if (axiosError.response !== undefined) {
        return AxiosHttpClient.mapAxiosResponse(axiosError.response);
      }

      return {
        headers: {},
        status: -1,
        statusText: axiosError.message || axiosError.code,
      };
    }

    return {
      headers: {},
      status: -1,
      statusText: error.message,
    };
  }

  private static mapAxiosResponse(axiosResponse: AxiosResponse): Response {
    return {
      body: axiosResponse.data || null,
      status: axiosResponse.status,
      statusText: axiosResponse.statusText,
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      headers: axiosResponse.headers,
    };
  }

  public get = async (params: Params): Promise<Response> => {
    const timeout = params.timeoutInSeconds ? params.timeoutInSeconds * 1000 : AxiosHttpClient._timeout;

    try {
      const axiosResponse = await axios.get(params.url, {
        headers: params.headers,
        timeout,
        validateStatus: () => false,
      });

      return AxiosHttpClient.mapAxiosResponse(axiosResponse);
    } catch (error) {
      if (error instanceof Error) {
        return AxiosHttpClient.mapAxiosException(error);
      }

      return AxiosHttpClient.mapAxiosException(new Error(JSON.stringify(error)));
    }
  };

  public post = async (params: Params): Promise<Response> => {
    const timeout = params.timeoutInSeconds ? params.timeoutInSeconds * 1000 : AxiosHttpClient._timeout;

    try {
      const axiosResponse = await axios.post(params.url, params.body, {
        headers: params.headers,
        timeout,
        validateStatus: () => false,
      });

      return AxiosHttpClient.mapAxiosResponse(axiosResponse);
    } catch (error) {
      if (error instanceof Error) {
        return AxiosHttpClient.mapAxiosException(error);
      }

      return AxiosHttpClient.mapAxiosException(new Error(JSON.stringify(error)));
    }
  };

  public patch = async (params: Params): Promise<Response> => {
    const timeout = params.timeoutInSeconds ? params.timeoutInSeconds * 1000 : AxiosHttpClient._timeout;

    try {
      const axiosResponse = await axios.patch(params.url, params.body, {
        headers: params.headers,
        timeout,
        validateStatus: () => false,
      });

      return AxiosHttpClient.mapAxiosResponse(axiosResponse);
    } catch (error) {
      if (error instanceof Error) {
        return AxiosHttpClient.mapAxiosException(error);
      }

      return AxiosHttpClient.mapAxiosException(new Error(JSON.stringify(error)));
    }
  };

  public delete = async (params: Params): Promise<Response> => {
    const timeout = params.timeoutInSeconds ? params.timeoutInSeconds * 1000 : AxiosHttpClient._timeout;

    try {
      const axiosResponse = await axios.delete(params.url, {
        headers: params.headers,
        timeout,
        validateStatus: () => false,
      });

      return AxiosHttpClient.mapAxiosResponse(axiosResponse);
    } catch (error) {
      if (error instanceof Error) {
        return AxiosHttpClient.mapAxiosException(error);
      }

      return AxiosHttpClient.mapAxiosException(new Error(JSON.stringify(error)));
    }
  };
}
