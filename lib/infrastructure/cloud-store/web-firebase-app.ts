import { EnvironmentConfig } from "@vivantehealth/vivante-core";
import { FirebaseApp, initializeApp } from "firebase/app";
import firebase from "firebase/compat/app";
const VIVANTE_FIREBASE_APP_BASE = "VIVANTE_FIREBASE";

export class WebFirebaseApp {
  private webFirebaseApp: FirebaseApp | undefined = undefined;

  constructor(private env: EnvironmentConfig) {
    this.initializeFirebase();
  }

  public async getFirebase(): Promise<FirebaseApp | undefined> {
    if (!this.webFirebaseApp) {
      await this.initializeFirebase();
    }

    return this.webFirebaseApp;
  }

  private async initializeFirebase() {
    const firebaseAppName = `${VIVANTE_FIREBASE_APP_BASE}_${this.env.name}`;

    const credentials = this.env.vivanteFirebaseWebCredentials;

    // use existing firebase app if available
    for (const app of firebase.apps) {
      if (app.name === firebaseAppName) {
        this.webFirebaseApp = app;
        return;
      }
    }

    // otherwise create new
    this.webFirebaseApp = initializeApp(credentials, firebaseAppName);
  }
}
