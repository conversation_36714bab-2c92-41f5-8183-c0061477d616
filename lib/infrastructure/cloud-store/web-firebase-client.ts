import { FirebaseClient, FirestoreClient } from "@vivantehealth/vivante-core";
import { FirebaseApp } from "firebase/app";
import { getAuth, getIdTokenResult, OAuthProvider, onAuthStateChanged, signInWithRedirect, User } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { from, Observable } from "rxjs";
import { switchMap } from "rxjs/operators";

import { getFirebaseAuth } from "@Utils/getFirebaseAuth";

import { WebFirebaseApp } from "./web-firebase-app";
import { WebFirestoreClient } from "./web-firestore-client";

export class WebFirebaseClient implements FirebaseClient {
  private firebaseInstance: FirebaseApp | undefined;

  constructor(private firebaseApp: WebFirebaseApp) {
    this.initializeFirebase();
  }

  private async initializeFirebase() {
    this.firebaseInstance = await this.firebaseApp.getFirebase();
  }

  public async getMemberId(): Promise<string> {
    const firebaseAuth = getAuth(this.firebaseInstance);
    const user = firebaseAuth.currentUser;

    if (!user) {
      throw new Error(`No user logged in on this Firebase instance: ${this.firebaseInstance?.name}`);
    }

    const { claims } = await getIdTokenResult(user);
    const memberId = claims["https://vivantehealth.com/sub"];

    if (!memberId || typeof memberId !== "string") {
      throw new Error(`Could not find memberId in claims ${JSON.stringify(claims)}`);
    }

    return memberId;
  }

  public async getFirestore(): Promise<FirestoreClient> {
    const firebase = await this.firebaseApp.getFirebase();

    if (!firebase) {
      throw new Error("Firebase is not initialized");
    }

    return new WebFirestoreClient(getFirestore(firebase));
  }

  public getMemberIdObservable(): Observable<string | undefined> {
    return from(getFirebaseAuth()).pipe(
      switchMap(
        (firebase) =>
          new Observable<User | null>((subscriber) => {
            const unsubscribe = onAuthStateChanged(firebase, (user) => {
              if (user) {
                subscriber.next(user);
              }
            });

            // Add Tear Down Logic
            subscriber.add(() => {
              if (unsubscribe) unsubscribe();
            });
          }),
      ),
      switchMap((user) => {
        return from(WebFirebaseClient.mapUserToMemberId(user));
      }),
    );
  }

  private static async mapUserToMemberId(user: User | null): Promise<string | undefined> {
    if (!user) {
      return undefined;
    }

    const { claims } = await getIdTokenResult(user);
    const memberId = claims["https://vivantehealth.com/sub"];

    return memberId && typeof memberId === "string" ? memberId : undefined;
  }

  public async activateSSO(providerName: string) {
    const firebaseAuth = getAuth(this.firebaseInstance);
    const ssoProvider = new OAuthProvider(providerName);

    ssoProvider.addScope("profile");
    ssoProvider.addScope("email");
    await signInWithRedirect(firebaseAuth, ssoProvider);
  }
}
