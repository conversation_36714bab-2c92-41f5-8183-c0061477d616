import {
  DocumentData,
  FirestoreClient,
  FirestoreDocument,
  Query,
  FirestoreCollectionReference,
  FirestoreDocumentReference,
  FirestoreSetOptions,
  FirestoreFieldValue,
} from "@vivantehealth/vivante-core";
import {
  collection,
  Firestore,
  CollectionReference as FirebaseFirestoreCollectionReference,
  DocumentReference as FirebaseFirestoreDocumentReference,
  QueryDocumentSnapshot,
  DocumentSnapshot,
  where,
  orderBy,
  limit,
  startAfter,
  getDoc,
  setDoc,
  updateDoc,
  onSnapshot,
  doc,
  addDoc,
  writeBatch,
  deleteDoc,
  getDocs,
  query as firestoreQuery,
  QueryOrderByConstraint,
  QueryLimitConstraint,
  QueryStartAtConstraint,
  QueryFieldFilterConstraint,
} from "firebase/firestore";
import { Observable, Subscriber } from "rxjs";

import { WebFirestoreFieldValue } from "./web-firestore-field-value";

const constructCollectionPath = (parentPath: string, childPath: string) => {
  const parentPathParts = parentPath?.split("/");
  const childPathParts = childPath.split("/");

  if (parentPathParts[parentPathParts.length - 1] === childPathParts[0]) {
    return `${parentPath}/${childPathParts.slice(1).join("/")}`;
  }

  return parentPath ? `${parentPath}/${childPath}` : childPath;
};

export class WebFirestoreClient implements FirestoreClient {
  constructor(private webFirestore: Firestore) {}

  public collection(collectionId: string): FirestoreCollectionReference {
    const collectionRef = collection(this.webFirestore, collectionId);

    return new CollectionReference(collectionRef, this.webFirestore);
  }

  public fieldValue(): FirestoreFieldValue {
    return new WebFirestoreFieldValue();
  }

  public async getDocument(collection: string, documentId: string): Promise<FirestoreDocument | undefined> {
    return await this.collection(collection).document(documentId).get();
  }

  public async get(collection: string, query?: Query | undefined): Promise<FirestoreDocument[]> {
    return await this.collection(collection).get(query);
  }

  public async addDocument(collection: string, document: DocumentData): Promise<string> {
    return await this.collection(collection).add(document);
  }

  public async addDocumentWithId(collection: string, documentId: string, document: DocumentData): Promise<void> {
    await this.collection(collection).document(documentId).set(document);
  }

  public async addDocuments(collection: string, documents: DocumentData[]): Promise<void> {
    return await this.collection(collection).batchAdd(documents);
  }

  public async updateDocument(collection: string, documentId: string, document: DocumentData): Promise<void> {
    await this.collection(collection).document(documentId).update(document);
  }

  public getDocumentObservable(collection: string, documentId: string): Observable<FirestoreDocument | undefined> {
    return this.collection(collection).document(documentId).getObservable();
  }
}

class CollectionReference implements FirestoreCollectionReference {
  constructor(
    private collectionRef: FirebaseFirestoreCollectionReference,
    private webFirestore: Firestore,
    private parent?: string,
  ) {}

  public document(documentId: string): FirestoreDocumentReference {
    const fullCollectionId = constructCollectionPath(this.parent ?? "", `${this.collectionRef.id}/${documentId}`);

    return new DocumentReference(doc(this.collectionRef, documentId), this.webFirestore, fullCollectionId);
  }

  public async get(query?: Query): Promise<FirestoreDocument[]> {
    const collectionQuery = firestoreQuery(this.collectionRef, ...getFirestoreQueryForCollection(query));

    const querySnapshot = await getDocs(collectionQuery);

    if (querySnapshot.empty) {
      return [];
    }

    return querySnapshot.docs.map((doc) => mapQueryDocumentSnapshotToFirestoreDocument(doc));
  }

  public getObservable(query?: Query): Observable<FirestoreDocument[]> {
    const collectionQuery = firestoreQuery(this.collectionRef, ...getFirestoreQueryForCollection(query));

    return new Observable((subscriber: Subscriber<FirestoreDocument[]>) => {
      const unsubscribeFromSnapshot = onSnapshot(
        collectionQuery,
        {},
        (querySnapshot) => {
          const docs = querySnapshot.docs.map((doc) => mapQueryDocumentSnapshotToFirestoreDocument(doc));

          subscriber.next(docs);
        },
        (error) => {
          if (error.code === "permission-denied") {
            // Catch error and do nothing
            // Prevents permission denied error from surfacing on logout
          } else {
            subscriber.error(error);
          }
        },
      );

      // Add Tear Down Logic
      subscriber.add(() => {
        unsubscribeFromSnapshot();
      });
    });
  }

  public async add(document: DocumentData): Promise<string> {
    const docRef = await addDoc(this.collectionRef, document);

    return docRef.id;
  }

  public async batchAdd(documents: DocumentData[]): Promise<void> {
    const batch = writeBatch(this.webFirestore);

    for (let i = 0; i < documents.length; i++) {
      const docRef = doc(this.collectionRef);

      batch.set(docRef, documents[i]);
    }

    await batch.commit();
  }

  public async delete(documentId: string) {
    return await deleteDoc(doc(this.collectionRef, documentId));
  }
}

class DocumentReference implements FirestoreDocumentReference {
  constructor(
    private docRef: FirebaseFirestoreDocumentReference,
    private webFirestore: Firestore,
    private parent?: string,
  ) {}

  public collection(collectionId: string): FirestoreCollectionReference {
    const fullCollectionId = constructCollectionPath(this.parent ?? "", collectionId);

    return new CollectionReference(
      collection(this.webFirestore, fullCollectionId),
      this.webFirestore,
      fullCollectionId,
    );
  }

  public async get(): Promise<FirestoreDocument | undefined> {
    const docSnapshot = await getDoc(this.docRef);

    return mapDocumentSnapshotToFirestoreDocument(docSnapshot);
  }

  public getObservable(): Observable<FirestoreDocument | undefined> {
    return new Observable((subscriber: Subscriber<FirestoreDocument | undefined>) => {
      const unsubscribeFromSnapshot = onSnapshot(
        this.docRef,
        {},
        (docSnapshot) => {
          subscriber.next(mapDocumentSnapshotToFirestoreDocument(docSnapshot));
        },
        (error) => {
          if (error.code === "permission-denied") {
            // Catch error and do nothing
            // Prevents permission denied error from surfacing on logout
          } else {
            subscriber.error(error);
          }
        },
      );

      // Add Tear Down Logic
      subscriber.add(() => {
        unsubscribeFromSnapshot();
      });
    });
  }

  public async update(document: DocumentData): Promise<void> {
    await updateDoc(this.docRef, document);
  }

  public async set(document: DocumentData, options?: FirestoreSetOptions): Promise<void> {
    if (!options) {
      await setDoc(this.docRef, document);
      return;
    }

    await setDoc(this.docRef, document, options);
  }
}

function mapDocumentSnapshotToFirestoreDocument(snapshot: DocumentSnapshot): FirestoreDocument | undefined {
  const snapshotData = snapshot.data();

  if (!snapshot.exists || !snapshotData) {
    return undefined;
  }

  return {
    id: snapshot.id,
    data: snapshotData,
  };
}

function mapQueryDocumentSnapshotToFirestoreDocument(snapshot: QueryDocumentSnapshot): FirestoreDocument {
  return {
    id: snapshot.id,
    data: snapshot.data(),
  };
}

type QueryModifiers =
  | QueryOrderByConstraint
  | QueryLimitConstraint
  | QueryStartAtConstraint
  | QueryFieldFilterConstraint;

function getFirestoreQueryForCollection(query?: Query) {
  const queryModifiers: QueryModifiers[] = [];

  if (query?.where && query.where.length > 0) {
    for (const [field, operator, searchValue] of query.where) {
      queryModifiers.push(where(field, operator, searchValue));
    }
  }

  if (query?.orderBy) {
    const [sortField, sortDirection] = query.orderBy;

    queryModifiers.push(orderBy(sortField, sortDirection));
  }

  if (query?.limit) {
    queryModifiers.push(limit(query.limit));
  }

  if (query?.startAfter) {
    queryModifiers.push(startAfter(query.startAfter));
  }

  return queryModifiers;
}
