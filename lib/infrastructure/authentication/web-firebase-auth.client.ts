import { AuthClient, SSOLoginParams } from "@vivantehealth/vivante-core";
import { FirebaseApp } from "firebase/app";
import {
  AuthError,
  createUserWithEmailAndPassword,
  EmailAuthProvider,
  getAuth,
  IdTokenResult,
  reauthenticateWithCredential,
  sendPasswordResetEmail,
  signInWithEmailAndPassword,
  signOut,
  updatePassword,
} from "firebase/auth";

import { logger } from "@Utils/logger";

import { WebFirebaseApp } from "../cloud-store";

interface FirebaseServerError {
  error: FirebaseServerErrorDetails;
}

interface FirebaseServerErrorDetails {
  code: number;
  message: string;
  status: string;
}

const MIN_EXPIRATION_OFFSET_IN_SECONDS = 10;

export class WebFirebaseAuthClient implements AuthClient {
  private firebaseInstance: FirebaseApp | undefined;

  constructor(private firebaseApp: WebFirebaseApp) {
    this.initializeFirebase();
  }

  private async initializeFirebase() {
    this.firebaseInstance = await this.firebaseApp.getFirebase();
  }

  public async signUpWithEmailAndPassword(email: string, password: string): Promise<void> {
    const firebaseAuth = getAuth(this.firebaseInstance);

    try {
      await createUserWithEmailAndPassword(firebaseAuth, email, password);
    } catch (error) {
      const firebaseError = error as AuthError;

      if (WebFirebaseAuthClient.isAlreadyExistsError(firebaseError)) {
        // eslint-disable-next-line no-throw-literal
        throw "email-already-in-use";
      }

      throw WebFirebaseAuthClient.mapKnownFirebaseErrorToVivanteError(firebaseError);
    }
  }

  public async signInWithEmailAndPassword(email: string, password: string): Promise<void> {
    const firebaseAuth = getAuth(this.firebaseInstance);

    try {
      await signInWithEmailAndPassword(firebaseAuth, email, password);
    } catch (error) {
      throw WebFirebaseAuthClient.mapKnownFirebaseErrorToVivanteError(error as AuthError);
    }
  }

  public async signOut(): Promise<void> {
    const firebaseAuth = getAuth(this.firebaseInstance);

    await signOut(firebaseAuth);
  }

  public async getValidAccessToken(): Promise<string | null> {
    const firebaseAuth = getAuth(this.firebaseInstance);
    const user = firebaseAuth.currentUser;

    if (!user) {
      return null; // no user logged in
    }

    let tokenResult = await user.getIdTokenResult();

    if (WebFirebaseAuthClient.isAboutToExpire(tokenResult)) {
      tokenResult = await user.getIdTokenResult(true);
    }

    if (WebFirebaseAuthClient.hasVivanteClaims(tokenResult)) {
      return tokenResult.token;
    }

    // Pre google-identity token. Force refresh.
    const refreshedTokenResult = await user.getIdTokenResult(true);

    if (WebFirebaseAuthClient.hasVivanteClaims(refreshedTokenResult)) {
      return refreshedTokenResult.token;
    }

    // Member not migrated to google identity. Force logout.
    await this.signOut();
    return null;
  }

  public async resetPasswordForEmail(email: string): Promise<void> {
    const firebaseAuth = getAuth(this.firebaseInstance);

    try {
      await sendPasswordResetEmail(firebaseAuth, email);
    } catch (error) {
      throw WebFirebaseAuthClient.mapKnownFirebaseErrorToVivanteError(error as AuthError);
    }
  }

  public async updatePassword(currentPassword: string, newPassword: string): Promise<void> {
    const firebaseAuth = getAuth(this.firebaseInstance);

    try {
      await this.reauthenticateWithCredential(currentPassword);
      if (firebaseAuth?.currentUser == null) {
        throw new Error("No user logged in");
      }

      await updatePassword(firebaseAuth.currentUser, newPassword);
    } catch (error) {
      throw WebFirebaseAuthClient.mapKnownFirebaseErrorToVivanteError(error as AuthError);
    }
  }

  private async reauthenticateWithCredential(currentPassword: string): Promise<void> {
    const firebaseAuth = getAuth(this.firebaseInstance);
    const currentUser = firebaseAuth?.currentUser;

    try {
      if (!currentUser) {
        throw new Error("No user logged in");
      }
      if (!currentUser.email) {
        throw new Error("No email found");
      }

      const { email } = currentUser;
      const credential = EmailAuthProvider.credential(email, currentPassword);

      await reauthenticateWithCredential(currentUser, credential);
    } catch (error) {
      throw WebFirebaseAuthClient.mapKnownFirebaseErrorToVivanteError(error as AuthError);
    }
  }

  private static mapKnownFirebaseErrorToVivanteError(firebaseError: AuthError) {
    switch (firebaseError.code) {
      case "auth/invalid-email": {
        return "invalid-email";
      }
      case "auth/email-already-in-use": {
        return "email-already-in-use";
      }
      case "auth/weak-password": {
        return "weak-password";
      }
      case "auth/user-not-found": {
        return "user-not-found";
      }
      case "auth/wrong-password": {
        return "wrong-password";
      }
      case "auth/requires-recent-login": {
        return "requires-recent-login";
      }
      case "auth/too-many-requests": {
        return "too-many-requests";
      }
      default: {
        return firebaseError;
      }
    }
  }

  private static isAlreadyExistsError(firebaseError: AuthError): boolean {
    // expected error:
    // {
    //   "error": {
    //     "code": 409,
    //     "message": "Email already exists",
    //     "status": "ALREADY_EXISTS"
    //   }
    // }

    if (firebaseError.code !== "auth/internal-error") {
      return false;
    }

    if (!firebaseError.message) {
      return false;
    }

    const startOfErrorObject = firebaseError.message.indexOf("{");
    const endOfErrorObject = firebaseError.message.lastIndexOf("}");

    if (startOfErrorObject === -1 || endOfErrorObject === -1) {
      return false;
    }

    let serverError: FirebaseServerError | undefined;

    try {
      serverError = JSON.parse(firebaseError.message.substring(startOfErrorObject, endOfErrorObject + 1));
    } catch (parsingError) {
      logger.error("Unable to parse error", firebaseError);
      serverError = undefined;
    }

    const code = serverError?.error?.code;
    const status = serverError?.error?.status;

    if (!code || !status) {
      return false;
    }

    return code === 409 && status === "ALREADY_EXISTS";
  }

  private static isAboutToExpire(tokenResult: IdTokenResult): boolean {
    const expiration = new Date(tokenResult.expirationTime).getTime();
    const now = new Date().getTime();
    const diffInSeconds = (expiration - now) / 1000;

    return diffInSeconds < MIN_EXPIRATION_OFFSET_IN_SECONDS;
  }

  private static hasVivanteClaims(tokenResult: IdTokenResult): boolean {
    const { claims } = tokenResult;
    /**
     * An SSO user when first signing in prior to having a matching traditional account will only have the
     * authentication_system_user_id and NOT the sub claim. Therefore, we check for either claim to determine if
     * the user is authorized to access the application.
     * ! An SSO user can only login via web so we do not need to check for the authentication_system_user_id claim in mobile
     * */
    const isAuthorized = !!(claims?.["https://vivantehealth.com/sub"] || claims?.authentication_system_user_id);

    return isAuthorized;
  }

  // Not implemented methods (no SSO for firebase auth)
  redirectToSSOLoginPage!: (loginParams?: SSOLoginParams) => Promise<void>;

  SSOLogout!: () => Promise<void>;
}
