import {
  ClickStreamActivity,
  ClickStreamActivityContext,
  ClickStreamActivityEvent,
  ClickStreamClient,
} from "@vivantehealth/vivante-core";
import MatomoTracker from "@jonkoops/matomo-tracker";
import { v4 as uuidV4 } from "uuid";

export class MatomoClickStreamClient implements ClickStreamClient {
  private tracker: MatomoTracker;

  private memberId: string | undefined;

  private sessionId: string;

  constructor(clickStreamEndpoint: string) {
    this.sessionId = uuidV4();

    this.tracker = new MatomoTracker({
      urlBase: clickStreamEndpoint,
      siteId: 1,
      trackerUrl: clickStreamEndpoint, // optional, default value: `${urlBase}matomo.php`
      srcUrl: "https://storage.googleapis.com/cylinderhealth-media-assets-ea9d/cylinder/webassets/csmain.js",
      heartBeat: {
        // optional, enabled by default
        active: false, // optional, default value: true
        // seconds: 10, // optional, default value: `15
      },
      linkTracking: false, // optional, default value: true
      configurations: {
        // optional, default value: {}
        // any valid matomo configuration, all below are optional
        setSecureCookie: true,
        setRequestMethod: "POST",
        "HeatmapSessionRecording.addConfig": {}, // disable Heatmap & Session Recording
      },
    });
  }

  public trackActivity(activity: ClickStreamActivity) {
    if (activity.context?.memberId !== this.memberId) {
      this.memberId = activity.context?.memberId;
      this.tracker.pushInstruction("setUserId", this.memberId);
    }
    // screens
    if (activity.event.category === "SCREEN" && activity.event.action === "ENTER") {
      if (activity.context && activity.context.currentScreen) {
        const activityContext = activity.context;
        const { currentScreen } = activity.context;
        const extraInfo = this.parseContextToParams(activity.timestamp, activityContext);
        const path = `/${currentScreen}${extraInfo ? "?" : ""}${extraInfo}`;

        this.tracker.trackPageView({
          href: path,
          documentTitle: currentScreen,
        });
        // console.log('MatomoClickStreamClient', 'SCREEN', path, currentScreen);
      }

      return;
    }

    // everything else is events
    const { category, action }: ClickStreamActivityEvent = activity.event;
    const extraInfo = this.parseContextToParams(activity.timestamp, activity.context);
    const path = `/${activity.context?.currentScreen}${extraInfo ? "?" : ""}${extraInfo}`;

    this.tracker.trackEvent({
      href: path,
      category,
      action,
    });
    // console.log('MatomoClickStreamClient', 'EVENT', category, action, fullAction);
  }

  private parseContextToParams = (eventTs: number, context?: ClickStreamActivityContext): string => {
    const paramsArray: string[] = [];

    paramsArray.push(`session_id=${this.sessionId}`);
    paramsArray.push(`event_ts=${eventTs}`);

    const currentScreen = context?.currentScreen;
    const extra = context?.extra;

    if (currentScreen) {
      paramsArray.push(`currentScreen=${currentScreen}`);
    }

    if (extra) {
      // eslint-disable-next-line no-restricted-syntax
      for (const [key, value] of Object.entries(extra)) {
        paramsArray.push(`${key}=${value}`);
      }
    }

    return paramsArray.join("&");
  };
}
