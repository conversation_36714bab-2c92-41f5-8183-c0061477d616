import { ServerEventsClient, ObservableAuthClient, CloudEvent } from "@vivantehealth/vivante-core";
import { Observable, Subject } from "rxjs";
import { SSE } from "sse.js";

import { logger } from "@Utils/logger";

export class WebServerEventsClient implements ServerEventsClient {
  private eventSource: SSE | null = null;

  private eventSubject: Subject<CloudEvent> = new Subject();

  private lastEventId: string | null = null;

  constructor(
    private streamUrl: string,
    private authClient: ObservableAuthClient,
  ) {}

  public async startStreaming(): Promise<void> {
    const authToken = await this.authClient.getValidAccessToken();

    if (authToken) {
      this.stopStreaming();
      logger.info("[SSE] Starting streaming");
      const options = {
        headers: {
          Authorization: `Bearer ${authToken}`,
          ...(this.lastEventId ? { "Last-Event-ID": this.lastEventId } : {}),
        },
      };

      this.eventSource = new SSE(this.streamUrl, options);
      this.eventSource.stream();
      this.addListeners();
    }
  }

  public async stopStreaming(): Promise<void> {
    logger.info("[SSE] Stopping streaming");
    this.removeListeners();
    this.eventSource?.close();
    this.eventSource = null;
  }

  public getEventStream(): Observable<CloudEvent> {
    return this.eventSubject;
  }

  private addListeners() {
    if (this.eventSource) {
      this.eventSource.onmessage = (event: Event) => {
        logger.info("[SSE] Message received", event);
        if (event.type === "message") {
          const messageEvent = event as MessageEvent;

          if (messageEvent.data) {
            this.eventSubject.next(JSON.parse(messageEvent.data));
          }

          this.lastEventId = messageEvent.lastEventId;
        }
      };

      this.eventSource.onopen = (event: Event) => {
        logger.info("[SSE] Connection opened", event);
      };

      this.eventSource.onerror = (event: Event) => {
        logger.info("[SSE] Connection error", event);
        this.startStreaming();
      };
    }
  }

  private removeListeners() {
    if (this.eventSource) {
      this.eventSource.onmessage = () => {};
      this.eventSource.onopen = () => {};
      this.eventSource.onerror = () => {};
    }
  }
}
