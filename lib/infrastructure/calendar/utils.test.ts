import { describe, expect, test } from "vitest";

import { CalendarUtils } from "./utils";
import { CalendarURLBuilder } from "./utils";

describe("CalendarUtils", () => {
  describe("getMilitaryHours", () => {
    test("Should convert 12-hour format to 24-hour format", () => {
      expect(CalendarUtils.getMilitaryHours(1)).toBe("100");
      expect(CalendarUtils.getMilitaryHours(12)).toBe("1200");
      expect(CalendarUtils.getMilitaryHours(6)).toBe("600");
    });
  });

  describe("getHoursDuration", () => {
    test("Should calculate the duration between two dates with timezone", () => {
      const startDate = "2024-01-01T10:00:00Z";
      const endDate = "2024-01-01T12:30:00Z";
      const timezone = 5;
      const duration = CalendarUtils.getHoursDuration(startDate, endDate, timezone);

      expect(duration).toBe("300");
    });

    test("Should calculate the duration between two dates", () => {
      const startDate = "2024-01-01T10:00:00Z";
      const endDate = "2024-01-01T14:30:00Z";
      const duration = CalendarUtils.getHoursDuration(startDate, endDate);

      expect(duration).toBe("500");
    });
  });

  describe("formatIcsText", () => {
    test("Should return an empty string if the input is empty", () => {
      expect(CalendarUtils.formatIcsText()).toBe("");
    });

    test("Should remove line breaks and truncate the string if necessary", () => {
      const str = "Lorem ipsum dolor sit amet,\nconsectetur adipiscing elit.";
      const formattedStr = CalendarUtils.formatIcsText(str, 30);

      expect(formattedStr).toBe("Lorem ipsum dolor sit amet,\\nc");
    });
  });

  describe("toUniversalTime", () => {
    test("Should format the time as a universal timestamp", () => {
      const timestamp = "2024-01-01T10:00:00";
      const formattedTime = CalendarUtils.toUniversalTime(timestamp);

      expect(formattedTime).toBe("20240101T100000");
    });
  });

  describe("toUTCTime", () => {
    test("Should format the time with UTC offset", () => {
      const timestamp = "2024-01-01T10:00:00Z";
      const formattedTime = CalendarUtils.toUTCTime(timestamp);

      expect(formattedTime).toBe("20240101T100000");
    });
  });

  describe("getIcsFileName", () => {
    test("Should return default event.ics name if not title provided", () => {
      expect(CalendarUtils.getIcsFileName()).toBe("event.ics");
    });
    test("Should return the ICS file name", () => {
      const title = "Meeting with John Doe";
      const fileName = CalendarUtils.getIcsFileName(title);

      expect(fileName).toBe("Meeting with John Doe.ics");
    });
  });

  describe("getIcsBlob", () => {
    test("Should return the ICS blob", () => {
      const icsData = "BEGIN:VCALENDAR\nEND:VCALENDAR";
      const icsBlob = CalendarUtils.getIcsBlob(icsData);

      expect(icsBlob).toBeInstanceOf(Blob);
    });
  });

  describe("getTimeCreated", () => {
    test("Should return the time created", () => {
      const todaysDate = new Date();
      const timeCreated = CalendarUtils.getTimeCreated();

      expect(timeCreated.slice(0, 4)).toBe(todaysDate.getFullYear().toString());
      expect(timeCreated.slice(4, 6)).toBe((todaysDate.getMonth() + 1).toString().padStart(2, "0"));
      expect(timeCreated.slice(6, 8)).toBe(todaysDate.getDate().toString().padStart(2, "0"));
    });
  });
});

describe("CalendarURLBuilder", () => {
  const data = {
    startDate: "2024-01-01T10:00:00Z",
    endDate: "2024-01-01T12:30:00Z",
    title: "Meeting with John Doe",
    description: "Discuss project details",
    meetingLink: "https://example.com/meeting",
  };

  describe("getYahooCalendarUrl", () => {
    test("Should return the Yahoo Calendar URL", () => {
      const url = CalendarURLBuilder.getYahooCalendarUrl(data);

      expect(url).toBe(
        "http://calendar.yahoo.com/?v=60&view=d&type=20&TITLE=Meeting with John Doe&ST=2024-01-01T10:00:00Z&DUR=300&DESC=Discuss project details&in_loc=https://example.com/meeting",
      );
    });
  });

  describe("getMicrosoftCalendarUrl", () => {
    test("Should return the Microsoft Calendar URL", () => {
      const url = CalendarURLBuilder.getMicrosoftCalendarUrl(data);

      expect(url).toBe(
        "https://outlook.live.com/owa/?path=/calendar/view/Month&rru=addevent&subject=Meeting with John Doe&body=Discuss project details&startdt=2024-01-01T10:00:00Z&enddt=2024-01-01T12:30:00Z&location=https://example.com/meeting",
      );
    });
  });

  describe("getGoogleCalendarUrl", () => {
    test("Should return the Google Calendar URL", () => {
      const url = CalendarURLBuilder.getGoogleCalendarUrl(data);

      expect(url).toBe(
        "https://www.google.com/calendar/render?action=TEMPLATE&text=Meeting with John Doe&dates=2024-01-01T10:00:00Z/2024-01-01T12:30:00Z&details=Discuss project details&location=https://example.com/meeting",
      );
    });
  });

  describe("getIcsCalendar", () => {
    test("Should return the ICS calendar data", () => {
      const icsData = CalendarURLBuilder.getIcsCalendar(data);

      expect(icsData).toContain(`BEGIN:VCALENDAR
VERSION:2.0
BEGIN:VEVENT
CLASS:PUBLIC
DESCRIPTION:Discuss project details
URL:https://example.com/meeting
DTSTART:2024-01-01T10:00:00Z
DTEND:2024-01-01T12:30:00Z
LOCATION:https://example.com/meeting
SUMMARY:Meeting with John Doe
TRANSP:TRANSPARENT
END:VEVENT
END:VCALENDAR`);
    });
  });
});
