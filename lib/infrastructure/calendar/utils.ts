/* eslint-disable @typescript-eslint/no-extraneous-class */
// eslint-disable-next-line max-classes-per-file
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import utc from "dayjs/plugin/utc";
dayjs.extend(utc);
dayjs.extend(duration);

export class CalendarUtils {
  /**
   * Return 12-hour format to 24-hour.
   */
  static getMilitaryHours(hours: number) {
    if (hours < 1) {
      return `00${60 / (1 / hours)}`;
    }

    return `${Math.round(hours)}00`;
  }

  /**
   * Gets the duration between dates.
   */
  static getHoursDuration(startDate?: string, endDate?: string, timezone?: number): string {
    const start = dayjs(startDate);
    const end = dayjs(endDate);

    if (timezone) {
      start.utcOffset(timezone);
      end.utcOffset(timezone);
    }

    const hours = dayjs.duration(end.diff(start)).asHours();

    return this.getMilitaryHours(hours);
  }

  /**
   * Removes line breaks and ensures that the string is no
   * longer than maxLength chars (or 75 chars if none specified).
   */
  static formatIcsText(str?: string, maxLength?: number) {
    if (!str) {
      return "";
    }

    const updatedNewLines = str.replace(/\n/g, "\\n");
    const truncatedString = updatedNewLines.substring(0, maxLength);

    return truncatedString;
  }

  /**
   * Format time as a universal timestamp format w.r.t.
   */
  static toUniversalTime(timestamp: string) {
    const dt = dayjs(timestamp);

    return dt.format("YYYYMMDDTHHmmss");
  }

  /**
   * get date with UTC offset needed for Microsoft calendar
   */
  static toUTCTime(timestamp: string) {
    const dt = dayjs(timestamp).utc();

    return dt.format("YYYYMMDDTHHmmss");
  }

  /**
   * The name of the file will be the event title with alphanumeric chars
   * having the extension `.ics`.
   */
  static getIcsBlob(icsData: string) {
    return new Blob([icsData], {
      type: "application/octet-stream",
    });
  }

  /**
   * Transforms given string to be valid file name.
   */
  static getIcsFileName(title?: string) {
    if (!title) {
      return "event.ics";
    }

    return `${title.replace(/[^\w ]+/g, "")}.ics`;
  }

  /**
   * Returns a random base 36 hash for iCal UID.
   */
  static getUid() {
    return Math.random().toString(36).substr(2);
  }

  /**
   * Returns a universal timestamp of current time.
   */
  static getTimeCreated() {
    return dayjs().format("YYYYMMDDTHHmmss");
  }
}

/**
 * Calendar event to be used in Calendar URL builder.
 * We need this type as Calendar Event from vivante-core deviates from CalendarURLBuilder's expectation
 */
type CalendarEvent = Partial<{
  startDate?: string;
  endDate?: string;
  title?: string;
  description?: string;
  meetingLink?: string;
}>;

export class CalendarURLBuilder {
  static getYahooCalendarUrl(data: CalendarEvent) {
    let yahooCalendarUrl = "http://calendar.yahoo.com/?v=60&view=d&type=20";
    // FA: Note for Duration for yahoo calendar: http://chris.photobooks.com/tests/calendar/Notes.html
    const duration = CalendarUtils.getHoursDuration(data.startDate, data.endDate);

    yahooCalendarUrl += `&TITLE=${data.title}`;
    yahooCalendarUrl += `&ST=${data.startDate}&DUR=${duration}`;
    yahooCalendarUrl += `&DESC=${data.description}`;
    yahooCalendarUrl += `&in_loc=${data.meetingLink}`;

    return yahooCalendarUrl;
  }

  static getMicrosoftCalendarUrl(data: CalendarEvent) {
    let microsoftCalendarUrl = "https://outlook.live.com/owa/?path=/calendar/view/Month&rru=addevent";

    microsoftCalendarUrl += `&subject=${data.title}`;
    microsoftCalendarUrl += `&body=${data.description}`;
    microsoftCalendarUrl += `&startdt=${data.startDate}&enddt=${data.endDate}`;
    microsoftCalendarUrl += `&location=${data.meetingLink}`;

    return microsoftCalendarUrl;
  }

  static getGoogleCalendarUrl(data: CalendarEvent) {
    let googleCalendarUrl = "https://www.google.com/calendar/render?action=TEMPLATE";

    googleCalendarUrl += `&text=${data.title}`;
    googleCalendarUrl += `&dates=${data.startDate}/${data.endDate}`;
    googleCalendarUrl += `&details=${data.description}`;
    googleCalendarUrl += `&location=${data.meetingLink}`;

    return googleCalendarUrl;
  }

  static getIcsCalendar(data: CalendarEvent) {
    return [
      "BEGIN:VCALENDAR",
      "VERSION:2.0",
      "BEGIN:VEVENT",
      "CLASS:PUBLIC",
      `DESCRIPTION:${CalendarUtils.formatIcsText(data.description, 600)}`,
      `URL:${data.meetingLink}`,
      `DTSTART:${data.startDate}`,
      `DTEND:${data.endDate}`,
      `LOCATION:${CalendarUtils.formatIcsText(data.meetingLink, 64)}`,
      `SUMMARY:${CalendarUtils.formatIcsText(data.title, 66)}`,
      "TRANSP:TRANSPARENT",
      "END:VEVENT",
      "END:VCALENDAR",
      `UID:${CalendarUtils.getUid()}`,
      `DTSTAMP:${CalendarUtils.getTimeCreated()}`,
      "PRODID:angular-addtocalendar",
    ].join("\n");
  }
}
