import { CalendarEvent, CalendarClient } from "@vivantehealth/vivante-core";
import dayjs, { Dayjs } from "dayjs";
import utc from "dayjs/plugin/utc";
import { saveAs } from "file-saver";
dayjs.extend(utc);

import { CalendarProviderOption } from "./calendar-provider-option";
import { CalendarURLBuilder, CalendarUtils } from "./utils";

export class WebCalendarClient implements CalendarClient {
  private static utcDateToString(dateInUTC: Dayjs): string {
    return dayjs.utc(dateInUTC).format("YYYY-MM-DDTHH:mm:ss.SSS[Z]");
  }

  private static dateToStringShortFormat(date?: Date): string {
    if (!date) return "";

    return dayjs(date).format("YYYYMMDDTHHmmss");
  }

  private static downloadIcsFile(calendarEvent: CalendarEvent): void {
    const data = {
      title: calendarEvent.title,
      startDate: WebCalendarClient.dateToStringShortFormat(calendarEvent.start),
      endDate: WebCalendarClient.dateToStringShortFormat(calendarEvent.end),
      description: calendarEvent.description,
      meetingLink: calendarEvent.meetingLink,
    };
    const fileName = CalendarUtils.getIcsFileName(calendarEvent.title),
      icsData = CalendarURLBuilder.getIcsCalendar(data),
      icsBlob = CalendarUtils.getIcsBlob(icsData);

    saveAs(icsBlob, fileName);
  }

  private static handleGoogleCalendar(calendarEvent: CalendarEvent) {
    const data = {
      title: calendarEvent.title,
      startDate: WebCalendarClient.dateToStringShortFormat(calendarEvent.start),
      endDate: WebCalendarClient.dateToStringShortFormat(calendarEvent.end),
      description: encodeURIComponent(calendarEvent.description ?? ""),
      meetingLink: calendarEvent.meetingLink,
    };
    const url = CalendarURLBuilder.getGoogleCalendarUrl(data);

    WebCalendarClient.goToLink(url);
  }

  private static handleOutlookLiveCalendar(calendarEvent: CalendarEvent) {
    const data = {
      title: calendarEvent.title,
      startDate: WebCalendarClient.utcDateToString(dayjs(calendarEvent.start)),
      endDate: WebCalendarClient.utcDateToString(dayjs(calendarEvent.end)),
      description: calendarEvent.description,
      meetingLink: calendarEvent.meetingLink,
    };
    const url = CalendarURLBuilder.getMicrosoftCalendarUrl(data);

    WebCalendarClient.goToLink(url);
  }

  private static handleYahooCalendarCalendar(calendarEvent: CalendarEvent) {
    const data = {
      title: calendarEvent.title,
      startDate: WebCalendarClient.dateToStringShortFormat(calendarEvent.start),
      endDate: WebCalendarClient.dateToStringShortFormat(calendarEvent.end),
      description: encodeURIComponent(calendarEvent.description ?? ""),
      meetingLink: calendarEvent.meetingLink,
    };
    const url = CalendarURLBuilder.getYahooCalendarUrl(data);

    WebCalendarClient.goToLink(url);
  }

  private static goToLink(url: string) {
    window.open(url, "_blank");
  }

  public createCalendarEvent(calendarEvent: CalendarEvent) {
    const calendarProviderOption: CalendarProviderOption = calendarEvent.extras;

    switch (calendarProviderOption) {
      case "iCalendar":
        WebCalendarClient.downloadIcsFile(calendarEvent);
        break;
      case "GoogleCalendar":
        WebCalendarClient.handleGoogleCalendar(calendarEvent);
        break;
      case "OutlookLive":
        WebCalendarClient.handleOutlookLiveCalendar(calendarEvent);
        break;
      case "YahooCalendar":
        WebCalendarClient.handleYahooCalendarCalendar(calendarEvent);
        break;
      case "OfficeOutlook":
        WebCalendarClient.downloadIcsFile(calendarEvent);
        break;
      default:
    }
  }
}
