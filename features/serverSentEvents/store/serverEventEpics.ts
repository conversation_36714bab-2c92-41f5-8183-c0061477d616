import { Action } from "@reduxjs/toolkit";
import { Epic, ofType } from "redux-observable";
import { Observable } from "rxjs";
import { ignoreElements, map } from "rxjs/operators";

import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { addServerEventsSubscriptions, removeServerEventsSubscriptions } from "@Store/actions";
import { logger } from "@Utils/logger";

const addServerEventSubscriptionEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(addServerEventsSubscriptions.type),
    map(() => {
      try {
        vivanteCoreContainer.getServerEventsUseCaseFactory().createSubscribeToServerEventsUseCase().execute();
      } catch (error) {
        logger.error(error);
      }
    }),
    ignoreElements(),
  );
};

const removeServerEventSubscriptionEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(removeServerEventsSubscriptions.type),
    map(() => {
      try {
        vivanteCoreContainer.getServerEventsUseCaseFactory().createUnsubscribeFromServerEventsUseCase().execute();
      } catch (error) {
        logger.error(error);
      }
    }),
    ignoreElements(),
  );
};

export const serverEventEpics = [addServerEventSubscriptionEpic, removeServerEventSubscriptionEpic];
