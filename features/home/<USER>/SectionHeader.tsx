import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";

import { appStrings } from "@Assets/app_strings";

type SectionHeaderProps = Readonly<{
  headerText: string;
  onViewAllClick: () => void;
}>;

export const SectionHeader = ({ headerText, onViewAllClick }: SectionHeaderProps) => {
  return (
    <Box display="flex" justifyContent="space-between">
      <Typography variant="h2Serif">{headerText}</Typography>
      <Button variant="secondary" size="small" onClick={onViewAllClick}>
        {appStrings.buttonText.viewAll}
      </Button>
    </Box>
  );
};
