import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";
import { color } from "@vivantehealth/design-tokens";

type ToDoCardProps = Readonly<{
  title: string;
  subtitle: string;
  buttonLabel: string;
  onClick: () => void;
}>;

export const ToDoCard = ({ title, subtitle, buttonLabel, onClick }: ToDoCardProps) => {
  return (
    <Paper
      sx={{
        textAlign: "left",
        display: "flex",
        alignItems: "center",
        p: 4,
        gap: 5,
        cursor: "pointer",
      }}
      onClick={onClick}
      component="button"
      aria-label={`${subtitle} - ${title}: ${buttonLabel}`}
    >
      <Box width={"100%"}>
        <Typography variant="h3" tabIndex={-1}>
          {title}
        </Typography>

        <Typography variant="body" sx={{ mt: 1, color: color.text.subtle }} tabIndex={-1}>
          {subtitle}
        </Typography>
      </Box>

      <Button component="span" variant="secondary" sx={{ whiteSpace: "nowrap" }} tabIndex={-1}>
        {buttonLabel}
      </Button>
    </Paper>
  );
};
