import { CarePlanIntervention, CourseItem, ToDo } from "@vivantehealth/vivante-core";
import { ActionPlanModule } from "@vivantehealth/vivante-core";
import { Box, Typography } from "@mui/material";
import { visuallyHidden } from "@mui/utils";

import { appStrings } from "@Assets/app_strings";
import { Article } from "@Features/articles/types/articles.types";

import { EducationList } from "./EducationList";
import { GetStartedSection } from "./GetStartedSection";
import { ProgressSection } from "./ProgressSection";
import { ToDoList } from "./ToDoList";

type HomeScreenProps = Readonly<{
  articles: Article[];
  toDos: ToDo[] | null;
  courseItems: CourseItem[];
  interventions: CarePlanIntervention[];
  carePlans: ActionPlanModule[];
  onToDoClick: (toDoId: string) => void;
  onCourseItemClick: (courseItem: CourseItem) => void;
  onArticleClick: (article: Article) => void;
  onArticlesViewAllClick: () => void;
}>;

export const HomeScreen = ({
  articles,
  toDos,
  courseItems,
  interventions,
  carePlans,
  onToDoClick,
  onCourseItemClick,
  onArticleClick,
  onArticlesViewAllClick,
}: HomeScreenProps) => {
  return (
    <Box display="flex" gap={7} flexDirection="column">
      <Typography sx={visuallyHidden} variant="h1">
        {appStrings.a11y.home}
      </Typography>
      {(toDos && toDos.length > 0) || courseItems.length > 0 ? (
        <ToDoList
          toDos={toDos}
          courseItems={courseItems}
          onToDoClick={onToDoClick}
          onCourseItemClick={onCourseItemClick}
        />
      ) : null}
      <GetStartedSection interventions={interventions} carePlans={carePlans} />
      <ProgressSection />
      <EducationList
        articles={articles}
        onArticleClick={onArticleClick}
        onArticlesViewAllClick={onArticlesViewAllClick}
      />
    </Box>
  );
};
