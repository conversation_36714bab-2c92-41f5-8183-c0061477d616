import { ActionPlanModule, CarePlanIntervention } from "@vivantehealth/vivante-core";
import { Box, Fade, Skeleton } from "@mui/material";
import { color, radius } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { CarePlanHomeCard } from "@Components/CarePlanCard/CarePlanHomeCard";
import { handleInterventionSelected } from "@Features/carePlan/utils/carePlan.util";
import { Routes } from "@Types";

type CarePlanCarouselProps = Readonly<{
  interventions?: CarePlanIntervention[];
  carePlans?: ActionPlanModule[];
}>;

export const CarePlanCarousel = ({ interventions, carePlans }: CarePlanCarouselProps) => {
  const shouldDisplaySkeleton = !interventions?.length && !carePlans?.length;

  const skeletonCard = (
    <Box sx={{ width: "100%" }}>
      <Box
        p={4}
        pr={5}
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignContent: "center",
          flex: "0 0 100%",
          background: color.background.surface.secondary,
          border: `1px solid ${color.border.surface.subtle}`,
          borderRadius: radius.radius1,
          flexWrap: "wrap",
        }}
      >
        <Box display="flex" flexDirection="column" flex="0 0 75%">
          <Skeleton
            variant="rectangular"
            width={132}
            height={24}
            sx={{ borderRadius: radius.radius2, backgroundColor: "rgba(255, 255, 255, 0.68)" }}
          />
          <Box>
            <Skeleton
              variant="rectangular"
              width="50%"
              height={16}
              sx={{ mt: 3, borderRadius: radius.radius2, backgroundColor: "rgba(255, 255, 255, 0.68)" }}
            />
            <Skeleton
              variant="rectangular"
              width="100%"
              height={20}
              sx={{ mt: 1, borderRadius: radius.radius2, backgroundColor: "rgba(255, 255, 255, 0.68)" }}
            />
          </Box>
        </Box>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Skeleton
            variant="rectangular"
            width={62}
            height={32}
            sx={{ borderRadius: radius.radius2, backgroundColor: "rgba(255, 255, 255, 0.68)" }}
          />
        </Box>
      </Box>
    </Box>
  );
  const skeletonElement = shouldDisplaySkeleton ? (
    <Box
      sx={{
        display: "grid",
        width: "100%",
        height: "100%",
        zIndex: 1,
        gap: 3,
      }}
    >
      {skeletonCard}
      {skeletonCard}
      {skeletonCard}
    </Box>
  ) : null;

  return (
    <Box sx={{ position: "relative" }}>
      {skeletonElement}

      <Fade in={!shouldDisplaySkeleton} timeout={300}>
        <Box
          sx={{
            display: "flex",
            flexWrap: "wrap",
            gap: 3,
          }}
        >
          {interventions?.map((intervention: CarePlanIntervention) => {
            const { title, subtitle, id, action } = intervention;
            const selectionMeta = action ? handleInterventionSelected(id, action) : undefined;
            const hrefWithInterventionId = selectionMeta?.includes("appointments")
              ? `${selectionMeta}&interventionId=${id}`
              : selectionMeta;

            return (
              <CarePlanHomeCard
                title={title}
                subtitle={subtitle}
                buttonText={appStrings.buttonText.start}
                href={hrefWithInterventionId}
                key={id}
                cardType="intervention"
              />
            );
          })}
          {carePlans?.map((carePlan: ActionPlanModule) => {
            const {
              title,
              subtitle,
              progress: { completed },
              id,
            } = carePlan;

            return (
              <CarePlanHomeCard
                title={title}
                subtitle={subtitle}
                buttonText={completed ? appStrings.buttonText.resume : appStrings.buttonText.start}
                href={`${Routes.CARE_PLAN}/${id}`}
                key={id}
                cardType="carePlan"
              />
            );
          })}
        </Box>
      </Fade>
    </Box>
  );
};
