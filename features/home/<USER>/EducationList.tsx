import { Box, Grid } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { EducationCard } from "@Components/EducationCard/EducationCard";
import { Article } from "@Features/articles/types/articles.types";

import { SectionHeader } from "./SectionHeader";

type EducationListProps = Readonly<{
  articles: Article[];
  onArticleClick: (article: Article) => void;
  onArticlesViewAllClick: () => void;
}>;

const SKELETON_CARD_ITERATOR = [1, 2, 3, 4, 5, 6];

export const EducationList = ({ articles, onArticleClick, onArticlesViewAllClick }: EducationListProps) => {
  const educationContent =
    articles.length > 0
      ? articles.map((article: Article) => {
          const ariaLabel = `${appStrings.a11y.article}. ${
            article?.articleCategory ? `${appStrings.a11y.category(article?.articleCategory)}.` : ""
          } ${appStrings.a11y.title(article.title)}. ${appStrings.a11y.articleTime(
            article.time,
          )}. ${appStrings.a11y.callToAction(appStrings.buttonText.readMore)}.`;

          return (
            <EducationCard
              key={article.id}
              ariaLabel={ariaLabel}
              title={article.title}
              body={article.body}
              imageSrc={article.imageLink}
              category={article.articleCategory}
              buttonText={appStrings.buttonText.readMore}
              icon="Clock"
              iconText={article.time}
              onClick={() => onArticleClick(article)}
            />
          );
        })
      : SKELETON_CARD_ITERATOR.map((cardNumber) => (
          <EducationCard
            key={`educationCardSkeleton-${cardNumber}`}
            title=""
            imageSrc=""
            ariaLabel=""
            buttonText=""
            body=""
            isSkeletonLoading
          />
        ));

  return (
    <Box>
      <SectionHeader
        headerText={appStrings.features.home.articlesContainerHeader}
        onViewAllClick={onArticlesViewAllClick}
      />
      <Grid container spacing={4} direction="row" pt={4}>
        {educationContent}
      </Grid>
    </Box>
  );
};
