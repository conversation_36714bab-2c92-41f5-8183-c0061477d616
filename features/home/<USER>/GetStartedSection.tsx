import { useDispatch } from "react-redux";
import { ActionPlanModule, CarePlanIntervention } from "@vivantehealth/vivante-core";
import { Box } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { Routes } from "@Types";

import { CarePlanCarousel } from "./CarePlanCarousel";
import { SectionHeader } from "./SectionHeader";

const CARE_PLAN_STRINGS = appStrings.features.home.carePlanSummary;

type GetStartedSectionProps = Readonly<{
  interventions: CarePlanIntervention[];
  carePlans: ActionPlanModule[];
}>;

export const GetStartedSection = ({ interventions, carePlans }: GetStartedSectionProps) => {
  const dispatch = useDispatch();

  const onViewAllClick = () => dispatch(NavigationStateSlice.actions.navigateTo({ path: Routes.CARE_PLAN }));

  return (
    <Box>
      <SectionHeader headerText={CARE_PLAN_STRINGS.header} onViewAllClick={onViewAllClick} />
      <Box mt={5}>
        <CarePlanCarousel interventions={interventions} carePlans={carePlans} />
      </Box>
    </Box>
  );
};
