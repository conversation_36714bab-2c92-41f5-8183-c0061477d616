import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  ClickStreamActivityEventType,
  CourseItem,
  CourseItemResourceType,
  CourseItemSubType,
} from "@vivantehealth/vivante-core";
import { ShippingAddress } from "@vivantehealth/vivante-core";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { ActivityScreenContainer } from "@Features/activity/ActivityScreenContainer";
import { useGetArticleNewsFeedQuery } from "@Features/articles/api/articlesApi";
import { useNavigateArticles } from "@Features/articles/hooks/useNavigateArticles";
import { Article } from "@Features/articles/types/articles.types";
import { determineArticleMediaType } from "@Features/articles/utils/articles.util";
import { selectModulesSlice } from "@Features/carePlan/store/actionPlansStateSlice";
import { selectCourseItems } from "@Features/courses/store/courseItemsStateSlice";
import { CoursesStateSlice } from "@Features/courses/store/coursesStateSlice";
import { NavOptions, setActiveNavOption } from "@Features/navigation/store/navigationStateSlice";
import { handleCourseItemNavigation } from "@Features/navigation/utils/navigation.util";
import { ShippingFormContainer } from "@Features/shippingForm/ShippingFormContainer";
import { ShippingFormStateSlice } from "@Features/shippingForm/store/shippingFormStateSlice";
import { toDosSelectors } from "@Features/toDos/store/toDosStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { loadHomeScreen } from "@Store/actions";
import { Routes } from "@Types";
import { isOfType } from "@Utils/isOfType";

import { HomeScreen } from "./components/HomeScreen";

const { loadEnrollment, setCurrentCourseItem } = CoursesStateSlice.actions;

export const HomeScreenContainer = () => {
  const [showActivityScreen, setShowActivityScreen] = useState<boolean>(false);
  const [showShippingScreen, setShowShippingScreen] = useState<boolean>(false);
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const { data: newsFeed } = useGetArticleNewsFeedQuery();
  const { navigateToArticle } = useNavigateArticles();
  const toDos = useSelector(toDosSelectors("todos"));
  const courseItems = useSelector(selectCourseItems);
  const { topCompletedInterventions, topCompletedActionPlans } = useSelector(selectModulesSlice);
  const router = useRouter();

  useEffect(() => {
    sendEventAnalytics(ClickStreamActivityEventType.CONTENT_VIEWED_HOME);
    // Disable eslint warning for this line because we only want this to run on initial render
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    dispatch(loadHomeScreen());
  }, [dispatch]);

  const handleToDoClick = (toDoId: string) => router.push(`/todo/${toDoId}`);

  const handleCourseItemClick = (courseItem: CourseItem) => {
    dispatch(setCurrentCourseItem(courseItem));
    if (courseItem.resource?.type === CourseItemResourceType.ACTIVITY) {
      setShowActivityScreen(true);
      return;
    }
    if (courseItem?.subtype === CourseItemSubType.SHIPPING_ADDRESS) {
      dispatch(loadEnrollment(courseItem.currentEnrollmentId));
      setShowShippingScreen(true);
      return;
    }

    handleCourseItemNavigation(courseItem);
  };

  const handleArticleClick = (article: Article) =>
    navigateToArticle(article, Routes.ARTICLES, determineArticleMediaType(article));

  const handleArticlesViewAllClick = () => {
    router.push(Routes.ARTICLES);
    dispatch(setActiveNavOption(NavOptions.ARTICLES));
  };

  if (showActivityScreen) {
    return (
      <ActivityScreenContainer
        onClose={() => setShowActivityScreen(false)}
        backButtonText={appStrings.features.home.backToHome}
      />
    );
  }

  if (showShippingScreen) {
    return (
      <ShippingFormContainer
        onBackClick={() => {
          setShowShippingScreen(false);
        }}
        onSubmit={(shippingAddress) => {
          if (isOfType<ShippingAddress>(shippingAddress, ["address", "city", "state", "zipCode"])) {
            dispatch(ShippingFormStateSlice.actions.submitShippingForm(shippingAddress));
          }
        }}
        onContinue={() => {
          dispatch(CoursesStateSlice.actions.completeCourseItem({}));
          setShowShippingScreen(false);
        }}
      />
    );
  }

  return (
    <HomeScreen
      articles={newsFeed?.slice(0, 6) ?? []}
      toDos={toDos}
      courseItems={courseItems}
      interventions={topCompletedInterventions}
      carePlans={topCompletedActionPlans}
      onToDoClick={handleToDoClick}
      onCourseItemClick={handleCourseItemClick}
      onArticleClick={handleArticleClick}
      onArticlesViewAllClick={handleArticlesViewAllClick}
    />
  );
};
