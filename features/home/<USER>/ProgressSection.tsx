import { useDispatch } from "react-redux";
import { Box, Grid, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { GOAL_SETTINGS_FLAG } from "@Features/goalSetting/constants";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { useFeatureFlag } from "@Hooks/useFeatureFlag";
import { Routes } from "@Types";

import { EmptyGoals } from "./progress/EmptyGoals";
import { ProgressHomeCalendar } from "./progress/ProgressHomeCalendar";
import { SectionHeader } from "./SectionHeader";

export const ProgressSection = () => {
  const dispatch = useDispatch();
  const { isReady, treatment } = useFeatureFlag(GOAL_SETTINGS_FLAG);
  const shouldDisplayGoals = isReady && treatment === "on";

  const onViewAllClick = () =>
    dispatch(NavigationStateSlice.actions.navigateTo({ path: Routes.PROGRESS, screenName: "HistoryDayView" }));

  return (
    <Box>
      <SectionHeader headerText={appStrings.features.home.progress.header} onViewAllClick={onViewAllClick} />
      <Grid container spacing={4} direction="row" pt={4}>
        {shouldDisplayGoals ? (
          <Grid item md={6} sm={6} xs={12} position="relative">
            <Typography variant="h4" color={color.text.strong} mb={4}>
              {appStrings.features.home.progress.subheaderGoals}
            </Typography>
            <EmptyGoals />
          </Grid>
        ) : null}
        <Grid item md={6} sm={6} xs={12} position="relative">
          <Typography variant="h4" color={color.text.strong} mb={4}>
            {appStrings.features.home.progress.subheaderTracking}
          </Typography>
          <ProgressHomeCalendar />
        </Grid>
      </Grid>
    </Box>
  );
};
