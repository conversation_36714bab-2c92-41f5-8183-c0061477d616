import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { startOfMonth, endOfMonth } from "date-fns";

import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { ProgressCalendar } from "@Features/progress/components/ProgressCalendar";
import { useMonthDebounce } from "@Features/progress/hooks/useMonthDebounce";
import { progressStateSelector, ProgressStateSlice } from "@Features/progress/store/progressStateSlice";
import { Routes } from "@Types";

const staticDate = new Date(); // Needs to live outside of the component to prevent re-rendering

export const ProgressHomeCalendar = () => {
  const dispatch = useDispatch();
  const symptomScores = useSelector(progressStateSelector("symptomScores"));
  const { handleMonthChange, isMonthLoading } = useMonthDebounce(symptomScores);
  const { updateCurrentDay, loadSymptomScoresOfSelectedDay } = ProgressStateSlice.actions;

  useEffect(() => {
    dispatch(
      loadSymptomScoresOfSelectedDay({
        startingDate: startOfMonth(staticDate),
        endingDate: endOfMonth(staticDate),
      }),
    );
  }, [dispatch, loadSymptomScoresOfSelectedDay]);

  const onSetDayClick = (newDay: Date) => {
    dispatch(updateCurrentDay(newDay));
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: Routes.PROGRESS,
        screenName: "HistoryDayView",
      }),
    );
  };

  return (
    <ProgressCalendar
      isLoading={isMonthLoading}
      isSymptomScoresLoading={isMonthLoading}
      currentDate={staticDate}
      handleMonthClick={handleMonthChange}
      handleDayClick={onSetDayClick}
      symptomScores={symptomScores}
    />
  );
};
