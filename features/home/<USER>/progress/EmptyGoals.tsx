import { useState } from "react";
import { useDispatch } from "react-redux";
import { Typography, Paper, Button } from "@mui/material";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { TileButton } from "@Components/TileButton/TileButton";
import { NewGoalDrawer } from "@Features/goalSetting/components/NewGoalDrawer/NewGoalDrawer";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { Routes } from "@Types";

export const EmptyGoals = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { addGoal } = router.query;

  // TODO get this flag from API later
  const [skipEducation, setSkipEducation] = useState(true);

  const handleAddGoal = () => {
    if (!skipEducation) {
      dispatch(
        NavigationStateSlice.actions.navigateTo({
          path: `${Routes.GOAL_SETTING_WELCOME_SPECIFIC}?origin=home`,
          screenName: "GoalSettingWelcome",
        }),
      );
    } else {
      router.replace({
        pathname: router.pathname,
        query: "addGoal=true",
      });
    }
  };

  return (
    <Paper sx={{ display: "grid", gap: 4, py: 4 }}>
      <Typography variant="bodyDense">{appStrings.features.goalSetting.emptyText}</Typography>
      <TileButton icon="Plus" fullWidth onClick={handleAddGoal}>
        {appStrings.features.goalSetting.firstGoal}
      </TileButton>
      {/* this button and text will be removed when we finish the whole workflow */}
      <Typography variant="bodyDense">Will skip education screens: {skipEducation ? "yes" : "no"}</Typography>
      <Button variant="secondary" onClick={() => setSkipEducation(!skipEducation)} size="small">
        Toggle between education screens and add a new goal drawer
      </Button>
      <NewGoalDrawer
        isDrawerOpen={addGoal === "true"}
        closeDrawerCallback={() => {
          router.push({
            pathname: router.pathname,
            query: "",
          });
        }}
      />
    </Paper>
  );
};
