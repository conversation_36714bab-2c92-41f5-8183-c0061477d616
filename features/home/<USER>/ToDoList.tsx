import { CourseItem, ToDo } from "@vivantehealth/vivante-core";
import Box from "@mui/material/Box";

import { appStrings } from "@Assets/app_strings";

import { ToDoCard } from "./ToDoCard";

type ToDoListProps = Readonly<{
  toDos: ToDo[] | null;
  onToDoClick: (toDoId: string) => void;
  courseItems: CourseItem[] | null;
  onCourseItemClick: (courseItem: CourseItem) => void;
}>;

export const ToDoList = ({ toDos, courseItems, onToDoClick, onCourseItemClick }: ToDoListProps) => {
  return (
    <Box display="grid" gap={4}>
      {toDos?.map((toDo: ToDo) => (
        <ToDoCard
          key={toDo.id}
          title={toDo.title}
          subtitle={toDo.description}
          buttonLabel={appStrings.buttonText.answerNow}
          onClick={() => onToDoClick(toDo.id)}
        />
      ))}
      {courseItems?.map((courseItem: CourseItem) => (
        <ToDoCard
          key={courseItem.id}
          title={courseItem.title}
          subtitle={courseItem.description}
          buttonLabel={appStrings.buttonText.doThisNow}
          onClick={() => onCourseItemClick(courseItem)}
        />
      ))}
    </Box>
  );
};
