import { bffApi } from "@Api/bffApi";
import { DraftGoal, GoalData } from "@Features/goalSetting/types";

// https://app.clickup.com/9003177726/v/dc/8ca35qy-41533/8ca35qy-62633 For API documentation

export const goalSettingsApi = bffApi.injectEndpoints({
  endpoints: (builder) => ({
    getGoal: builder.query<GoalData, string>({
      query: (goalId: string) => `/v1/goal/${goalId}`,
      providesTags: ["Goal"],
    }),
    getAllGoals: builder.query<GoalData[], string>({
      query: (memberId: string) => `/v1/goals/${memberId}`,
      providesTags: ["Goal"],
    }),
    createGoal: builder.mutation<GoalData, DraftGoal>({
      query: (draftGoal: DraftGoal) => ({
        url: "/v1/goal",
        method: "POST",
        body: draftGoal,
      }),
      invalidatesTags: ["Goal"],
    }),
    updateEntireGoalData: builder.mutation<GoalData, GoalData>({
      query: (goal: GoalData) => ({
        url: `/v1/goal/${goal.goalId}`,
        method: "PUT",
        body: goal,
      }),
      invalidatesTags: ["Goal"],
    }),
    updatePartialGoalData: builder.mutation<GoalData, Partial<GoalData>>({
      query: (goal: Partial<GoalData>) => ({
        url: `/v1/goal/${goal.goalId}`,
        method: "PATCH",
        body: goal,
      }),
      invalidatesTags: ["Goal"],
    }),
    deleteGoal: builder.mutation<void, string>({
      query: (goalId: string) => ({
        url: `/v1/goal/${goalId}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Goal"],
    }),
  }),
});

export const {
  useGetAllGoalsQuery,
  useGetGoalQuery,
  useDeleteGoalMutation,
  useUpdateEntireGoalDataMutation,
  useCreateGoalMutation,
  useUpdatePartialGoalDataMutation,
} = goalSettingsApi;
