type GoalStatus = "ACTIVE" | "INACTIVE" | "COMPLETED" | "DELETED";

type CADENCE_LOWERCASE = "Daily" | "Weekly";

export type CADENCE = Uppercase<CADENCE_LOWERCASE>;

export type GoalState = TitleState & FrequencyState & DurationState;

export type FormScreens = "goalTitle" | "goalFrequency" | "goalDuration" | "goalReview";

export type TitleState = Readonly<{
  title: string;
}>;

export type FrequencyState = Readonly<{
  frequency: number;
  cadence: CADENCE_LOWERCASE;
  days: Set<string>;
}>;

export type DurationState = Readonly<{
  duration: number;
}>;

export type DraftGoal = Readonly<{
  title: string;
  category: string;
  frequency: {
    timePeriodLength: number;
    numberOfTimesPerFrequency: number;
    cadence: CADENCE;
    days: string[];
  };
}>;

type FrequencyData = Readonly<{
  timePeriodLength: number;
  numberOfTimesPerFrequency: number;
  cadence: CADENCE;
  days: string[];
}>;

type StepData = Readonly<{
  stepId: string;
  creationDate: string;
  completedDate: string;
  completions: number;
}>;

export type GoalData = Readonly<{
  goalId: string;
  title: string;
  category: string;
  creationDate: string;
  updatedDate: string;
  frequency: FrequencyData;
  steps: StepData[];
  currentStreak: number;
  longestStreak: number;
  status: GoalStatus;
}>;
