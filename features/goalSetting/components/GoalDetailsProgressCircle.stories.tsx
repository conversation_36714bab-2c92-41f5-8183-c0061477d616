import type { Meta, StoryObj } from "@storybook/nextjs";
import { useArgs } from "storybook/preview-api";

import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import { GoalDetailsProgressCircle } from "./GoalDetailsProgressCircle";

const meta: Meta<typeof GoalDetailsProgressCircle> = {
  title: "@Features/goalSetting/GoalDetailsProgressCircle",
  component: GoalDetailsProgressCircle,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: cylinderThemeDecorator,
  argTypes: {
    completedCount: { control: "number", defaultValue: 3 },
    totalCount: { control: "number", defaultValue: 5 },
  },
};

export default meta;
type Story = StoryObj<typeof GoalDetailsProgressCircle>;

export const Primary: Story = {
  args: {
    completedCount: 3,
    totalCount: 5,
  },
  render: function Component(args) {
    const [{ completedCount }, setArgs] = useArgs();

    const setCompletedCount = (value: number) => {
      setArgs({ completedCount: value });
    };

    return (
      <GoalDetailsProgressCircle {...args} completedCount={completedCount} updateCompletedCount={setCompletedCount} />
    );
  },
};
