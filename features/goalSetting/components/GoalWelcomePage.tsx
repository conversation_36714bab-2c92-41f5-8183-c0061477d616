import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
import { Box, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { BackButton } from "@Components/BackButton/BackButton";
import { FormActions } from "@Components/form/FormActions";
import { NavigationStateSlice, NavOptions } from "@Features/navigation/store/navigationStateSlice";
import { ScreenName } from "@Features/navigation/types/screenName";

const GOAL_STRINGS = appStrings.features.goalSetting;
const WELCOME_STRINGS = GOAL_STRINGS.welcomePage;
const FIGMA_BOX_WIDTH = 448;
const FIGMA_HEADER_WIDTH = 376;

export type OriginType = "home" | "my-progress";
export type ScreenType = "specificGoals" | "achievableGoals" | "getStarted";
type RouteType = { path: string; screenName: ScreenName };
type GoalWelcomePageProps = Readonly<{
  screen: ScreenType;
  originalRoute: RouteType;
  backRoute: RouteType;
  forwardRoute: RouteType;
  origin: OriginType;
}>;

export const GoalWelcomePage = ({ screen, originalRoute, backRoute, forwardRoute, origin }: GoalWelcomePageProps) => {
  const dispatch = useDispatch();
  const { header, body, primary, secondary } = WELCOME_STRINGS[screen];

  useEffect(() => {
    dispatch(NavigationStateSlice.actions.setActiveNavOption(NavOptions.PROGRESS));
  }, [dispatch]);

  const onNavigateBack = () => {
    dispatch(NavigationStateSlice.actions.navigateTo(originalRoute));
  };

  const onBack = () => {
    dispatch(NavigationStateSlice.actions.navigateTo(backRoute));
  };

  const onNext = () => {
    dispatch(NavigationStateSlice.actions.navigateTo(forwardRoute));
  };

  return (
    <>
      <Box display="grid" justifyContent="center" position="relative" zIndex={1}>
        <Box mb={8}>
          <BackButton onClick={onNavigateBack}>{GOAL_STRINGS.backButtonText(origin.replace("-", " "))}</BackButton>
        </Box>
        <Box display="grid" gap={6} textAlign="center" maxWidth={FIGMA_BOX_WIDTH} justifyItems="center">
          <Typography variant="h1Serif" maxWidth={FIGMA_HEADER_WIDTH}>
            {header}
          </Typography>
          <Typography variant="body" color={color.text.strong}>
            {body}
          </Typography>
          <Box width="100%">
            <FormActions
              primaryText={primary}
              secondaryText={secondary}
              secondaryCallback={onBack}
              primaryCallback={onNext}
            />
          </Box>
        </Box>
      </Box>
      <img
        src="/images/white_dot_motif_tilted.webp"
        style={{ position: "absolute", right: 0, bottom: 0 }}
        alt={GOAL_STRINGS.goalsdotMotifAltText}
      />
    </>
  );
};
