import type { <PERSON>a, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import { StreakCard } from "./StreakCard";

const meta: Meta<typeof StreakCard> = {
  title: "@Features/goalSetting/StreakCard",
  component: StreakCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: cylinderThemeDecorator,
};

export default meta;
type Story = StoryObj<typeof StreakCard>;

export const Primary: Story = {
  args: {
    streakLength: 5,
    streakType: "Current",
    streakInterval: "days",
  },
};
