import { useRef, useState, useEffect } from "react";
import { Box } from "@mui/material";
import { color, typography } from "@vivantehealth/design-tokens";
import { motion, AnimatePresence } from "framer-motion";

import { MotionBox } from "@Components/animation/MotionBox";
import { AppIcon } from "@Components/AppIcon/AppIcon";

type ProgressCircleProps = Readonly<{
  completedCount: number;
  totalCount: number;
}>;

const COMPONENT_SIZE = 48;
const CIRCLE_STROKE_WIDTH = 2;
const CIRCLE_DIAMETER = COMPONENT_SIZE - CIRCLE_STROKE_WIDTH * 2;

export const ProgressCircle = ({ completedCount, totalCount }: ProgressCircleProps) => {
  const spanRef = useRef<HTMLSpanElement>(null);
  const [boxWidth, setBoxWidth] = useState(0);

  // Function used to make dash offsets a cleaner calculation using path length
  const calcStrokeDashOffset = (currentProgress: number, currentTotal: number) => {
    if (currentProgress >= currentTotal) {
      return 0;
    }

    const PATHLENGTH = 100;
    const strokeDashOffset = PATHLENGTH * (1 - currentProgress / currentTotal);

    return strokeDashOffset;
  };

  const strokeColor = completedCount < totalCount ? color.palette.blue[200] : color.palette.green[200];
  const isCompleted = completedCount >= totalCount;

  useEffect(() => {
    if (spanRef.current) {
      setBoxWidth(spanRef.current.offsetWidth);
    }
  }, []);

  return (
    <Box
      sx={{
        display: "grid",
        gridTemplateRows: "1fr",
        gridTemplateColumns: "1fr",
        gridTemplateAreas: "'content'",
        overflow: "hidden",
        width: `${COMPONENT_SIZE}px`,
        height: `${COMPONENT_SIZE}px`,
        placeItems: "center",
      }}
    >
      <svg
        style={{ gridArea: "content" }}
        width={COMPONENT_SIZE}
        height={COMPONENT_SIZE}
        viewBox={`0 0 ${COMPONENT_SIZE} ${COMPONENT_SIZE}`}
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle
          r={CIRCLE_DIAMETER / 2}
          cx={COMPONENT_SIZE / 2}
          cy={COMPONENT_SIZE / 2}
          fill="transparent"
          stroke={color.background.surface.secondary}
          strokeWidth={CIRCLE_STROKE_WIDTH}
        />
        <motion.circle
          r={CIRCLE_DIAMETER / 2}
          cx={COMPONENT_SIZE / 2}
          cy={COMPONENT_SIZE / 2}
          strokeWidth={CIRCLE_STROKE_WIDTH}
          strokeLinecap="round"
          animate={{ strokeDashoffset: calcStrokeDashOffset(completedCount, totalCount), stroke: strokeColor }}
          fill="transparent"
          strokeDasharray="100"
          pathLength="100"
          transition={{ duration: 0.3 }}
          initial={{ strokeDashoffset: 0 }}
          style={{ transform: "rotate(-90deg)", transformOrigin: "center" }}
        />
      </svg>

      <MotionBox
        sx={{
          fontSize: typography.caption.fontSize,
          fontWeight: typography.caption.fontWeight,
          gridArea: "content",
          display: "flex",
        }}
        initial={false}
        animate={{ y: isCompleted ? -20 : 0, opacity: isCompleted ? 0 : 1 }}
        transition={{ delay: 1, duration: 0.3 }}
      >
        <Box minWidth={boxWidth} position="relative" display="inline-block">
          <AnimatePresence initial={false} mode="wait">
            <motion.span
              ref={spanRef}
              key={completedCount}
              initial={{ y: "100%", opacity: 0 }}
              animate={{ y: "0%", opacity: 1 }}
              exit={{ y: "-100%", opacity: 0 }}
              transition={{ duration: 0.3 }}
              style={{ position: "absolute" }}
            >
              {completedCount}
            </motion.span>
          </AnimatePresence>
        </Box>
        <span> /{totalCount}</span>
      </MotionBox>

      <MotionBox
        sx={{ gridArea: "content", display: "flex", opacity: 0 }}
        initial={false}
        animate={{ y: isCompleted ? 0 : 20, opacity: isCompleted ? 1 : 0 }}
        transition={{ delay: 1, duration: 0.3 }}
      >
        <AppIcon name="Check" containerStyles={{ display: "grid", placeItems: "center" }} />
      </MotionBox>
    </Box>
  );
};
