import { <PERSON>a, StoryObj } from "@storybook/nextjs";
import { useArgs } from "storybook/preview-api";

import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import { GoalSettingsCard } from "./GoalSettingsCard";

const meta: Meta<typeof GoalSettingsCard> = {
  component: GoalSettingsCard,
  title: "@Features/goalSetting/GoalSettingsCard",
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    ...cylinderThemeDecorator,
    (Story) => (
      <div style={{ width: "643px" }}>
        <Story />
      </div>
    ),
  ],
  argTypes: {},
};

export default meta;
type Story = StoryObj<typeof GoalSettingsCard>;

export const Primary: Story = {
  args: {
    completedCount: 2,
    totalCount: 6,
    streakInterval: "days",
    href: "/",
    isPastGoal: false,
    goalTitle: "Meditate for 5 minutes",
    streakLength: 0,
  },
  render: function Component(args) {
    const [{ completedCount, totalCount, href, streakInterval, streakLength, isPastGoal, goalTitle }, setArgs] =
      useArgs();

    const updateCompletedCount = () => {
      if (completedCount === totalCount) {
        return;
      } else if (completedCount === totalCount - 1) {
        setArgs({ completedCount: completedCount + 1, streakLength: streakLength + 1 });
      } else {
        setArgs({ completedCount: completedCount + 1 });
      }
    };

    return (
      <GoalSettingsCard
        {...args}
        completedCount={completedCount}
        totalCount={totalCount}
        href={href}
        streakInterval={streakInterval}
        streakLength={streakLength}
        isPastGoal={isPastGoal}
        goalTitle={goalTitle}
        updateCompletedCount={updateCompletedCount}
      />
    );
  },
};

export const PastGoals: Story = {
  args: {
    streakInterval: "days",
    href: "/",
    isPastGoal: true,
    goalTitle: "Meditate for 5 minutes",
  },
};
