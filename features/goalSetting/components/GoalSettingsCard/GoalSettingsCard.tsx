import { Box, Chip, Paper, Typography } from "@mui/material";
import Link from "next/link";

import { appStrings } from "@Assets/app_strings";
import { SPACING_16_PX } from "@Assets/style_constants";
import { GoalProgress } from "@Features/goalSetting/components/GoalSettingsCard/GoalProgress";
import { PastGoalSettingsBlock } from "@Features/goalSetting/components/GoalSettingsCard/PastGoalSettingsBlock";
import { StreakChip } from "@Features/goalSetting/components/GoalSettingsCard/StreakChip";

type GoalSettingsCardProps = Readonly<{
  completedCount: number;
  totalCount: number;
  updateCompletedCount: () => void;
  streakLength: number;
  streakInterval: "days" | "weeks";
  href: string;
  isPastGoal: boolean;
  goalTitle: string;
}>;

export const GoalSettingsCard = ({
  completedCount,
  totalCount,
  updateCompletedCount,
  streakInterval,
  streakLength,
  href,
  isPastGoal,
  goalTitle,
}: GoalSettingsCardProps) => {
  const goalType =
    streakInterval === "days"
      ? appStrings.features.goalSetting.cadence.daily
      : appStrings.features.goalSetting.cadence.weekly;

  if (isPastGoal) {
    return (
      <Paper sx={{ width: "100%", p: 0 }}>
        <PastGoalSettingsBlock href={href} goalTitle={goalTitle} goalType={goalType} />
      </Paper>
    );
  }

  return (
    <Paper sx={{ width: "100%", p: 0, display: "flex", gap: 5 }}>
      <Link
        href={href}
        style={{ display: "block", flex: "1 1 auto", padding: SPACING_16_PX, paddingRight: 0, textDecoration: "none" }}
      >
        <Typography variant="h4" mb={2}>
          {goalTitle}
        </Typography>
        <Box display="flex" gap={3}>
          <Chip label={goalType} size="small" variant="inactive" />
          <StreakChip streakLength={streakLength} streakInterval={streakInterval} />
        </Box>
      </Link>
      <Box display="flex" alignItems="center" p={4} paddingLeft={0}>
        <GoalProgress completedCount={completedCount} totalCount={totalCount} updateProgress={updateCompletedCount} />
      </Box>
    </Paper>
  );
};
