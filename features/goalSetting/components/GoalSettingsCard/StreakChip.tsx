import { useEffect, useRef, useState } from "react";
import { Box, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { AnimatePresence, motion } from "framer-motion";

import { appStrings } from "@Assets/app_strings";
import { MotionBox } from "@Components/animation/MotionBox";
import { AppIcon } from "@Components/AppIcon/AppIcon";

type StreakChipProps = Readonly<{
  streakLength: number;
  streakInterval: "days" | "weeks";
}>;

export const StreakChip = ({ streakLength, streakInterval }: StreakChipProps) => {
  const spanRef = useRef<HTMLSpanElement>(null);
  const [boxWidth, setBoxWidth] = useState(0);
  // to make sure the number does not animate from 0 to 1
  const streakDisplayValue = streakLength == 0 ? 1 : streakLength;

  useEffect(() => {
    if (spanRef.current) {
      setBoxWidth(spanRef.current.offsetWidth);
    }
  }, [streakLength]);

  const streakDisplay = streakInterval.slice(0, -1);
  const parentVariants = {
    show: {
      transition: {
        staggerChildren: 0.05,
        delayChildren: 1.1,
      },
    },
  };

  const childVariants = {
    show: {
      y: ["100%", 0],
      transition: {
        duration: 0.3,
        type: "spring",
        stiffness: 200,
        damping: 20,
        mass: 1.5,
      },
    },
    hide: {
      y: [0, "100%"],
    },
  };

  return (
    <Box overflow="hidden">
      <MotionBox
        display="flex"
        gap={1}
        alignItems="center"
        initial={false}
        animate={streakLength > 0 ? "show" : "hide"}
        variants={parentVariants}
      >
        <motion.div variants={childVariants}>
          <AppIcon name="Fire" color={color.icon.brand} />
        </motion.div>
        <motion.div variants={childVariants}>
          <Typography variant="bodyDense" overflow="hidden" display="flex" gap={1}>
            <Box minWidth={boxWidth} height="20px" position="relative" display="inline-block">
              <AnimatePresence initial={false} mode="sync">
                <motion.span
                  ref={spanRef}
                  key={streakDisplayValue}
                  initial={{ y: "100%", opacity: 0 }}
                  animate={{ y: "0%", opacity: 1 }}
                  exit={{ y: "-100%", opacity: 0 }}
                  transition={{ duration: 0.5, delay: 1.1 }}
                  style={{ position: "absolute" }}
                >
                  {streakDisplayValue}
                </motion.span>
              </AnimatePresence>
            </Box>
            <span>
              {streakDisplay} {appStrings.features.goalSetting.streakName}
            </span>
          </Typography>
        </motion.div>
      </MotionBox>
    </Box>
  );
};
