import { Box, Chip, Typography } from "@mui/material";
import Link from "next/link";

import { SPACING_16_PX, SPACING_24_PX } from "@Assets/style_constants";
import { OutlinedIconButton } from "@Components/OutlinedIconButton/OutlinedIconButton";

type PastGoalSettingsBlockProps = Readonly<{
  goalTitle: string;
  goalType: string;
  href: string;
}>;

export const PastGoalSettingsBlock = ({ goalTitle, goalType, href }: PastGoalSettingsBlockProps) => {
  return (
    <Link href={href} style={{ display: "flex", padding: SPACING_16_PX, gap: SPACING_24_PX, textDecoration: "none" }}>
      <Box flex="1 0 auto">
        <Typography variant="h4" mb={2}>
          {goalTitle}
        </Typography>
        <Chip size="small" label={goalType} variant="inactive" />
      </Box>
      <Box display="flex" alignItems="center">
        <OutlinedIconButton icon="RightChevron" onClick={() => {}} />
      </Box>
    </Link>
  );
};
