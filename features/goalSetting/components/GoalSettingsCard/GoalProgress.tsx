import { Box, Button } from "@mui/material";
import { motion } from "framer-motion";

import { RADIUS_FULL_PX, SPACING_12_PX, iconSize } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { ProgressCircle } from "@Features/goalSetting/components/GoalSettingsCard/ProgressCircle";

type GoalProgressProps = Readonly<{
  completedCount: number;
  totalCount: number;
  updateProgress: () => void;
}>;

export const GoalProgress = ({ completedCount, totalCount, updateProgress }: GoalProgressProps) => {
  const GAP_PLUS_BUTTON_WIDTH = 44;

  return (
    <Box overflow="hidden">
      <motion.div
        style={{ gap: SPACING_12_PX, alignItems: "center", display: "flex" }}
        animate={{ x: completedCount >= totalCount ? GAP_PLUS_BUTTON_WIDTH : 0 }}
        transition={{ duration: 0.5 }}
        initial={false}
      >
        <ProgressCircle completedCount={completedCount} totalCount={totalCount} />
        <Button
          variant="secondary"
          onClick={updateProgress}
          sx={{ borderRadius: RADIUS_FULL_PX, p: 1, minWidth: 0, ...iconSize.lg }}
        >
          <AppIcon name="Plus" size="sm" containerStyles={{ display: "grid", placeItems: "center" }} />
        </Button>
      </motion.div>
    </Box>
  );
};
