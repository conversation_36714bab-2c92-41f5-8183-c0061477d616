import type { Meta, StoryObj } from "@storybook/nextjs";
import { useArgs } from "storybook/preview-api";

import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import { GoalProgress } from "./GoalProgress";

const meta: Meta<typeof GoalProgress> = {
  title: "@Features/goalSetting/GoalProgress",
  component: GoalProgress,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: cylinderThemeDecorator,
  argTypes: {
    completedCount: { control: "number", defaultValue: 2 },
    totalCount: { control: "number", defaultValue: 6 },
  },
};

export default meta;
type Story = StoryObj<typeof GoalProgress>;

export const Primary: Story = {
  args: {
    completedCount: 2,
    totalCount: 6,
  },
  render: function Component(args) {
    const [{ completedCount, totalCount }, setArgs] = useArgs();

    const updateProgress = () => {
      if (completedCount === totalCount) {
        return;
      }

      setArgs({ completedCount: completedCount + 1 });
    };

    return (
      <GoalProgress {...args} completedCount={completedCount} totalCount={totalCount} updateProgress={updateProgress} />
    );
  },
};
