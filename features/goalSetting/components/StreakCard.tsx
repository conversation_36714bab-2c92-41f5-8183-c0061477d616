import { Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";

type StreakCardProps = Readonly<{
  streakLength: number;
  streakType: "Current" | "Longest";
  streakInterval: "days" | "weeks";
}>;
// TODO: Update to use design tokens for icon colors
export const StreakCard = ({ streakLength, streakType, streakInterval }: StreakCardProps) => {
  const streakLengthToDisplay = `${streakLength} ${streakLength === 1 ? streakInterval.slice(0, -1) : streakInterval}`;

  return (
    <Paper sx={{ py: 3, px: 6, display: "flex", flexDirection: "column", alignItems: "center" }}>
      <Typography variant="bodyDense" mb={2} color={color.text.subtle}>
        {appStrings.features.goalSetting.streak(streakType)}
      </Typography>

      <AppIcon name="Fire" size="lg" color={streakType === "Current" ? "#FBCA6F" : "#F58457"} />

      <Typography variant="h3Serif" mt={1}>
        {streakLengthToDisplay}
      </Typography>
    </Paper>
  );
};
