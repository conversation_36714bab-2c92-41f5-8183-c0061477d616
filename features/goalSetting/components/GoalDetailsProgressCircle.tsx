import { Box, CircularProgress, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { OutlinedIconButton } from "@Components/OutlinedIconButton/OutlinedIconButton";
// Per Figma design size https://www.figma.com/design/qGJjRp7DkQ1rmFRLngo1yX/Goal-Setting?node-id=1328-72506&t=xyIhtqYaILsqUoFD-4
const CIRCLE_SIZE = 208;

type GoalDetailsProgressCircleProps = Readonly<{
  completedCount: number;
  totalCount: number;
  updateCompletedCount: (value: number) => void;
}>;
// TODO: Update colors and typography to proper design tokens when they're available
export const GoalDetailsProgressCircle = ({
  completedCount,
  totalCount,
  updateCompletedCount,
}: GoalDetailsProgressCircleProps) => {
  return (
    <Box display="flex" flexDirection="column" gap={5} alignItems="center">
      <Box position="relative" display="flex" alignItems="center" justifyContent="center">
        <CircularProgress
          variant="determinate"
          value={100}
          size={CIRCLE_SIZE}
          thickness={1.8}
          sx={{ color: color.palette.blue[100], position: "absolute" }}
        />

        <CircularProgress
          variant="determinate"
          value={(completedCount / totalCount) * 100}
          size={CIRCLE_SIZE}
          thickness={1.8}
          sx={{ color: "#4363F6" }}
        />

        <Box position="absolute">
          <Typography variant="h1Serif">{`${completedCount} / ${totalCount}`}</Typography>
        </Box>
      </Box>

      <Box display="flex" gap={6} alignItems="center">
        <OutlinedIconButton
          icon="Minus"
          onClick={() => updateCompletedCount(completedCount - 1)}
          disabled={completedCount === 0}
        />

        <OutlinedIconButton
          icon="Plus"
          onClick={() => updateCompletedCount(completedCount + 1)}
          disabled={completedCount === totalCount}
        />
      </Box>
    </Box>
  );
};
