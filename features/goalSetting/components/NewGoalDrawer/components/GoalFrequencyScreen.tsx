import { useState } from "react";
import { Box, Button, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { useForm, FormProvider } from "react-hook-form";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { FormDropdown, FormSwitchSelect, FormDaySelect } from "@Components/form/Fields";
import { PROGRESS_BAR_HEIGHT } from "@Features/goalSetting/constants";
import { GoalState, FrequencyState } from "@Features/goalSetting/types";

const GOAL_STRINGS = appStrings.features.goalSetting;
const GOAL_FREQUENCY_STRINGS = appStrings.features.goalSetting.newGoal.frequency;

type GoalFrequencyScreenProps = Readonly<{
  goalsProps: GoalState;
  handleOnSubmit: (formData: FrequencyState) => void;
}>;

export const GoalFrequencyScreen = ({ goalsProps, handleOnSubmit }: GoalFrequencyScreenProps) => {
  const defaultValues = {
    frequency: goalsProps.frequency,
    cadence: goalsProps.cadence,
    days: goalsProps.days,
  };
  const methods = useForm({ defaultValues });
  const { handleSubmit, setValue, watch } = methods;
  const [showSchedulePicker, setShowSchedulePicker] = useState(goalsProps.days.size > 0 && goalsProps.days.size < 7);
  const currentCadence = watch("cadence");
  const currentDays = watch("days");
  const isButtonDisabled = showSchedulePicker && currentDays.size === 0;

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={handleSubmit((data: typeof defaultValues) => handleOnSubmit(data))}
        style={{ height: `calc(100% - ${PROGRESS_BAR_HEIGHT})` }}
      >
        <Box display="flex" flexDirection="column" height={"100%"}>
          <Typography variant="h2Serif" color={color.text.default} mb={5}>
            {GOAL_FREQUENCY_STRINGS.header}
          </Typography>
          <Box display="flex" flexDirection="column" gap={5}>
            <FormDropdown
              label={GOAL_FREQUENCY_STRINGS.frequencyLabel}
              options={Object.entries(GOAL_FREQUENCY_STRINGS.timesOptions).map(([key, value]) => ({
                value: key,
                label: value,
              }))}
              name="frequency"
            />
            <Box display="grid" justifyItems="start" gap={4}>
              <FormSwitchSelect
                label={GOAL_FREQUENCY_STRINGS.cadenceLabel}
                name="cadence"
                options={[GOAL_STRINGS.cadence.daily, GOAL_STRINGS.cadence.weekly]}
                onChangeCallback={() => {
                  setShowSchedulePicker(false);
                  setValue("days", new Set());
                }}
              />
              {currentCadence === GOAL_STRINGS.cadence.daily &&
                (!showSchedulePicker ? (
                  <Button
                    variant="secondary"
                    size="small"
                    startIcon={<AppIcon name="Calendar" size="sm" />}
                    onClick={() => setShowSchedulePicker(true)}
                  >
                    {GOAL_FREQUENCY_STRINGS.scheduleLabel}
                  </Button>
                ) : (
                  <>
                    <Typography variant="body" color={color.text.strong}>
                      {GOAL_FREQUENCY_STRINGS.scheduleDecription}
                    </Typography>
                    <FormDaySelect name="days" />
                  </>
                ))}
            </Box>
          </Box>
          <Button type="submit" variant="primary" fullWidth sx={{ mt: "auto" }} disabled={isButtonDisabled}>
            {appStrings.buttonText.next}
          </Button>
        </Box>
      </form>
    </FormProvider>
  );
};
