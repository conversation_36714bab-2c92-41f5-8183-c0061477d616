import { CSSProperties } from "react";
import { <PERSON>, Button, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { OutlinedIconButton } from "@Components/OutlinedIconButton/OutlinedIconButton";
import { PROGRESS_BAR_HEIGHT } from "@Features/goalSetting/constants";
import { GoalState, FormScreens } from "@Features/goalSetting/types";

import { getWeeksLabel, getFrequencyLabel } from "../utils";

const GOAL_STRINGS = appStrings.features.goalSetting;
const GOAL_REVIEW_STRINGS = GOAL_STRINGS.newGoal.review;
const DAYS = new Map(Object.entries(GOAL_STRINGS.days).map(([name, day]) => [name.toUpperCase(), day]));
const SINGLE_LINE_CHAR = 35;

type ReviewItemProps = Readonly<{
  title: string;
  onEdit: () => void;
  children: React.ReactNode;
  editBtnAlignment?: CSSProperties["alignItems"];
}>;

const ReviewItem = ({ title, onEdit, editBtnAlignment = "center", children }: ReviewItemProps) => {
  return (
    <Box display="grid" gap={4}>
      <Typography variant="h4" color={color.text.strong}>
        {title}
      </Typography>
      <Paper sx={{ p: 4 }}>
        <Box display="flex" justifyContent="space-between" alignItems={editBtnAlignment} gap={1}>
          <Box display="grid" alignItems="center" gap={2}>
            {children}
          </Box>
          <OutlinedIconButton icon="Edit" onClick={onEdit} />
        </Box>
      </Paper>
    </Box>
  );
};

type GoalReviewScreenProps = Readonly<{
  goalsProps: GoalState;
  handleOnSubmit: () => void;
  handleOnEdit: (screenName: FormScreens) => void;
  isSubmitting: boolean;
}>;

export const GoalReviewScreen = ({ goalsProps, handleOnSubmit, handleOnEdit, isSubmitting }: GoalReviewScreenProps) => {
  return (
    <Box display="flex" flexDirection="column" height={`calc(100% - ${PROGRESS_BAR_HEIGHT})`}>
      <Typography variant="h2Serif" mb={6}>
        {GOAL_REVIEW_STRINGS.header}
      </Typography>
      <Box display="flex" flexDirection="column" gap={5} mb={5}>
        <ReviewItem
          title={GOAL_REVIEW_STRINGS.goalTitle}
          onEdit={() => handleOnEdit("goalTitle")}
          editBtnAlignment={goalsProps.title.length > SINGLE_LINE_CHAR ? "flex-start" : "center"}
        >
          <Typography
            variant="body"
            sx={{
              overflow: "auto",
              overflowWrap: "break-word",
              whiteSpace: "pre-wrap",
            }}
          >
            {goalsProps.title}
          </Typography>
        </ReviewItem>
        <ReviewItem
          title={GOAL_REVIEW_STRINGS.goalFrequency}
          onEdit={() => handleOnEdit("goalFrequency")}
          editBtnAlignment="flex-start"
        >
          <Box display="flex" alignItems="center" gap={2}>
            <AppIcon name="Repeat" />
            <Typography variant="body">{getFrequencyLabel(goalsProps.frequency)}</Typography>
          </Box>
          <Box display="flex" alignItems="center" gap={2}>
            <AppIcon name="EventRepeat" />
            <Typography variant="body">
              {Array.from(goalsProps.days)
                .map((day) => DAYS.get(day))
                .join(", ")}
            </Typography>
          </Box>
        </ReviewItem>
        <ReviewItem title={GOAL_REVIEW_STRINGS.goalDuration} onEdit={() => handleOnEdit("goalDuration")}>
          <Box display="flex" alignItems="center" gap={2}>
            <AppIcon name="Calendar" />
            <Typography variant="body">{getWeeksLabel(goalsProps.duration)}</Typography>
          </Box>
        </ReviewItem>
      </Box>
      <Button
        type="submit"
        variant="primary"
        fullWidth
        sx={{ mt: "auto" }}
        onClick={handleOnSubmit}
        disabled={isSubmitting}
      >
        {appStrings.features.goalSetting.saveGoal}
      </Button>
    </Box>
  );
};
