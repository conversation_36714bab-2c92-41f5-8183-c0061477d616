import { Box, Button, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { useForm, FormProvider } from "react-hook-form";

import { appStrings } from "@Assets/app_strings";
import { FormTextArea } from "@Components/form/Fields";
import { PROGRESS_BAR_HEIGHT } from "@Features/goalSetting/constants";
import { GoalState, TitleState } from "@Features/goalSetting/types";

const SHARED_FORM_STRINGS = appStrings.sharedFormText;
const GOAL_TITLE_STRINGS = appStrings.features.goalSetting.newGoal.title;

type GoalTitleScreenProps = Readonly<{
  goalsProps: GoalState;
  handleOnSubmit: (formData: TitleState) => void;
}>;

export const GoalTitleScreen = ({ goalsProps, handleOnSubmit }: GoalTitleScreenProps) => {
  const defaultValues = {
    title: goalsProps.title,
  };
  const methods = useForm({ defaultValues });
  const { handleSubmit, watch } = methods;
  const isButtonDisabled = watch(["title"]).some((value) => value.toString().length === 0);

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={handleSubmit((data: typeof defaultValues) => handleOnSubmit(data))}
        style={{ height: `calc(100% - ${PROGRESS_BAR_HEIGHT})` }}
      >
        <Box display="flex" flexDirection="column" height="100%">
          <Box display="flex" flexDirection="column" gap={5}>
            <Box>
              <Typography variant="h2Serif" mb={2} color={color.text.default}>
                {GOAL_TITLE_STRINGS.header}
              </Typography>
              <Typography variant="bodyDense" color={color.text.strong}>
                {GOAL_TITLE_STRINGS.body}
              </Typography>
            </Box>
            <FormTextArea
              helperText={GOAL_TITLE_STRINGS.helpText}
              name="title"
              rules={{
                required: { value: true, message: SHARED_FORM_STRINGS.requiredMessage },
                maxLength: { value: 400, message: SHARED_FORM_STRINGS.maxLengthMessage },
              }}
            />
          </Box>
          <Button type="submit" variant="primary" disabled={isButtonDisabled} fullWidth sx={{ mt: "auto" }}>
            {appStrings.buttonText.next}
          </Button>
        </Box>
      </form>
    </FormProvider>
  );
};
