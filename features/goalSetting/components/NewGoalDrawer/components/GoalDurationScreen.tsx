import { Box, Button, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { useForm, FormProvider } from "react-hook-form";

import { appStrings } from "@Assets/app_strings";
import { FormDropdown } from "@Components/form/Fields";
import { PROGRESS_BAR_HEIGHT } from "@Features/goalSetting/constants";
import { GoalState, DurationState } from "@Features/goalSetting/types";

import { getWeeksLabel } from "../utils";

const GOAL_DURATION_STRINGS = appStrings.features.goalSetting.newGoal.duration;
const VALID_NUMBER_WEEKS = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

type GoalDurationScreenProps = Readonly<{
  goalsProps: GoalState;
  handleOnSubmit: (formData: DurationState) => void;
}>;

export const GoalDurationScreen = ({ goalsProps, handleOnSubmit }: GoalDurationScreenProps) => {
  const defaultValues = {
    duration: goalsProps.duration,
  };
  const methods = useForm({ defaultValues });
  const { handleSubmit } = methods;

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={handleSubmit((data: typeof defaultValues) => handleOnSubmit(data))}
        style={{ height: `calc(100% - ${PROGRESS_BAR_HEIGHT})` }}
      >
        <Box display="flex" flexDirection="column" height="100%">
          <Box display="flex" flexDirection="column" gap={5}>
            <Typography variant="h2Serif" mb={2} color={color.text.default}>
              {GOAL_DURATION_STRINGS.header}
            </Typography>
            <FormDropdown
              label={GOAL_DURATION_STRINGS.durationLabel}
              options={VALID_NUMBER_WEEKS.map((number) => ({
                value: number,
                label: getWeeksLabel(number),
              }))}
              name="duration"
            />
          </Box>
          <Button type="submit" variant="primary" fullWidth sx={{ mt: "auto" }}>
            {appStrings.buttonText.next}
          </Button>
        </Box>
      </form>
    </FormProvider>
  );
};
