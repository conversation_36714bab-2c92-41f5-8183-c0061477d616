import { describe, it, expect } from "vitest";

import { getWeeksLabel, getFrequencyLabel } from "./utils";

describe("getWeeksLabel", () => {
  it("should return the correct label for a single week", () => {
    const result = getWeeksLabel(1);

    expect(result).toBe("1 week");
  });

  it("should return the correct label for multiple weeks", () => {
    const result = getWeeksLabel(3);

    expect(result).toBe("3 weeks");
  });
});

describe("getFrequencyLabel", () => {
  it("should return the correct label for a single time", () => {
    const result = getFrequencyLabel(1);

    expect(result).toBe("1 time");
  });

  it("should return the correct label for multiple times", () => {
    const result = getFrequencyLabel(5);

    expect(result).toBe("5 times");
  });
});
