import { useState } from "react";
import { useDispatch } from "react-redux";
import { Box, Button, LinearProgress, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { BaseDrawer } from "@Components/BaseDrawer/BaseDrawer";
import { BaseModal } from "@Components/BaseModal/BaseModal";
import { errorDispatcher } from "@Features/error/utils/errorDispatcher";
import { useCreateGoalMutation } from "@Features/goalSetting/api/goalSettingsApi";
import { DraftGoal, GoalState, FormScreens, FrequencyState, CADENCE } from "@Features/goalSetting/types";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";
import { logger } from "@Utils/logger";

import { GoalDurationScreen } from "./components/GoalDurationScreen";
import { GoalFrequencyScreen } from "./components/GoalFrequencyScreen";
import { GoalReviewScreen } from "./components/GoalReviewScreen";
import { GoalTitleScreen } from "./components/GoalTitleScreen";

const BUTTON_TEXT = appStrings.buttonText;
const DAY_STRINGS = appStrings.features.goalSetting.days;
const GOAL_STRINGS = appStrings.features.goalSetting.newGoal;
const CLOSING_ANIM_DURATION = 300;

const BACK_BUTTON_TO_SCREEN_MAP = {
  goalFrequency: "goalTitle",
  goalDuration: "goalFrequency",
  goalReview: "goalDuration",
} as const;

const PROGRESS_MAP = {
  goalTitle: 20,
  goalFrequency: 40,
  goalDuration: 60,
  goalReview: 80,
} as const;

const CADENCE_MAPPER = {
  Daily: "DAILY",
  Weekly: "WEEKLY",
} as const satisfies Record<string, CADENCE>;

const hasBackScreenName = (screenName: string): screenName is keyof typeof BACK_BUTTON_TO_SCREEN_MAP =>
  screenName in BACK_BUTTON_TO_SCREEN_MAP;

const serializeGoal = (goalState: GoalState): DraftGoal => ({
  title: goalState.title,
  category: "custom",
  frequency: {
    timePeriodLength: goalState.duration,
    numberOfTimesPerFrequency: goalState.frequency,
    cadence: CADENCE_MAPPER[goalState.cadence],
    days: Array.from(goalState.days),
  },
});

const defaultGoalState: GoalState = { title: "", frequency: 1, cadence: "Daily", days: new Set([]), duration: 4 };

type NewGoalDrawerProps = Readonly<{
  isDrawerOpen: boolean;
  closeDrawerCallback: () => void;
}>;

export const NewGoalDrawer = ({ isDrawerOpen, closeDrawerCallback }: NewGoalDrawerProps) => {
  const dispatch = useDispatch();

  const [currentFormScreen, setCurrentFormScreen] = useState<FormScreens>("goalTitle");
  const [isExitDialogOpen, setIsExitDialogOpen] = useState(false);
  const [goal, setGoal] = useState<GoalState>(defaultGoalState);
  const [createGoal, { isLoading: isSubmitting }] = useCreateGoalMutation();

  const resetToDefaults = () => {
    // add tiny delay to allow the drawer to close before the resetting the state
    setTimeout(() => {
      setCurrentFormScreen("goalTitle");
      setGoal(defaultGoalState);
    }, CLOSING_ANIM_DURATION);
  };

  const onSaveAll = () => {
    const payload = serializeGoal(goal);

    createGoal(payload)
      .unwrap()
      .then(() => {
        closeDrawerCallback();
        resetToDefaults();
        dispatch(SnackbarStateSlice.actions.toggleSnackbar({ isOpen: true, message: GOAL_STRINGS.snackBarSuccess }));
      })
      .catch((error) => {
        logger.error(error);
        // this displays the error message in a modal and sends log to sentry
        errorDispatcher({ message: GOAL_STRINGS.snackBarFail });
      });
  };

  const onClose = () => {
    setIsExitDialogOpen(false);
    closeDrawerCallback();
    resetToDefaults();
  };

  const onSubmitFrequency = (formData: FrequencyState) => {
    setCurrentFormScreen("goalDuration");
    const { days, ...restForm } = formData;
    let fillDays = days;

    if (formData.cadence === "Daily" && days.size === 0) {
      // if user never customised days we want to send all days in a week
      fillDays = new Set(Object.keys(DAY_STRINGS).map((name) => name.toUpperCase()));
    } else if (formData.cadence === "Weekly") {
      // user can't pick days for weekly cadence, we default to SUNDAY
      fillDays = new Set(["SUNDAY"]);
    }

    setGoal((prev) => ({ ...prev, ...restForm, days: fillDays }));
  };

  return (
    <>
      <BaseModal
        isModalOpen={isExitDialogOpen}
        title={GOAL_STRINGS.exitDialog.title}
        bodyContent={<Typography variant="body">{GOAL_STRINGS.exitDialog.subTitle}</Typography>}
        onClose={onClose}
        closeBtnAriaLabel={GOAL_STRINGS.exitDialog.ariaLabel}
        actions={
          <Box display="flex" gap={4} width="100%">
            <Button variant="secondary" onClick={() => setIsExitDialogOpen(false)} fullWidth>
              {BUTTON_TEXT.no}
            </Button>
            <Button variant="primary" onClick={onClose} fullWidth>
              {BUTTON_TEXT.yes}
            </Button>
          </Box>
        }
      />
      <BaseDrawer
        isDrawerOpen={isDrawerOpen}
        onClose={() => setIsExitDialogOpen(true)}
        showBackBtn={hasBackScreenName(currentFormScreen)}
        onBack={() =>
          hasBackScreenName(currentFormScreen)
            ? setCurrentFormScreen(BACK_BUTTON_TO_SCREEN_MAP[currentFormScreen])
            : undefined
        }
        header={GOAL_STRINGS.drawerHeader}
      >
        <LinearProgress variant="determinate" value={PROGRESS_MAP[currentFormScreen]} sx={{ mt: 4, mb: 5 }} />
        {currentFormScreen === "goalTitle" && (
          <GoalTitleScreen
            goalsProps={goal}
            handleOnSubmit={(formData) => {
              setCurrentFormScreen("goalFrequency");
              setGoal((prev) => ({ ...prev, ...formData }));
            }}
          />
        )}
        {currentFormScreen === "goalFrequency" && (
          <GoalFrequencyScreen goalsProps={goal} handleOnSubmit={onSubmitFrequency} />
        )}
        {currentFormScreen === "goalDuration" && (
          <GoalDurationScreen
            goalsProps={goal}
            handleOnSubmit={(formData) => (
              setCurrentFormScreen("goalReview"), setGoal((prev) => ({ ...prev, ...formData }))
            )}
          />
        )}
        {currentFormScreen === "goalReview" && (
          <GoalReviewScreen
            goalsProps={goal}
            handleOnSubmit={onSaveAll}
            handleOnEdit={setCurrentFormScreen}
            isSubmitting={isSubmitting}
          />
        )}
      </BaseDrawer>
    </>
  );
};
