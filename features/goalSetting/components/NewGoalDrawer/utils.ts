import { appStrings } from "@Assets/app_strings";

const GOAL_DURATION_STRINGS = appStrings.features.goalSetting.newGoal.duration;
const GOAL_FREQUENCY_STRINGS = appStrings.features.goalSetting.newGoal.frequency;

export const getWeeksLabel = (numberOfWeeks: number): string => {
  return `${numberOfWeeks} ${
    numberOfWeeks > 1 ? GOAL_DURATION_STRINGS.multipleWeeks : GOAL_DURATION_STRINGS.singleWeek
  }`;
};

export const getFrequencyLabel = (frequency: number): string => {
  return `${frequency} ${frequency > 1 ? GOAL_FREQUENCY_STRINGS.multipleTimes : GOAL_FREQUENCY_STRINGS.singleTime}`;
};
