import { render, screen } from "@testing-library/react";
import { vi, describe, test, expect, beforeEach, Mock } from "vitest";

import { TestWrapper } from "@TestUtils/TestWrapper";
import { Routes } from "@Types";

import * as gutCheckApi from "./api/gutCheckApi";
import { GutCheckDisplay } from "./GutCheckDisplay";
import { GUT_CHECK_INTRO_SCREENS } from "./types/gutCheck.types";

let mockRouter: {
  push: Mock;
  replace: Mock;
  isReady: boolean;
  query: Record<string, string | undefined>;
  pathname: string | undefined;
};

vi.mock("next/router", () => ({
  useRouter: () => mockRouter,
}));

const mockedUseGetGutcheckStatusQuery = vi.spyOn(gutCheckApi, "useGetGutcheckStatusQuery");

describe("GutCheckDisplay", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockRouter = {
      push: vi.fn(),
      replace: vi.fn(),
      isReady: true,
      query: {},
      pathname: "/gutcheck",
    };
  });

  test("renders loading spinner when isLoading is true", () => {
    mockedUseGetGutcheckStatusQuery.mockReturnValue({
      data: undefined,
      isLoading: true,
      isFetching: true,
      refetch: vi.fn(),
    });

    render(<GutCheckDisplay />, { wrapper: TestWrapper });

    expect(screen.getByRole("progressbar", { hidden: true })).toBeInTheDocument();
  });

  describe("Redirection Logic", () => {
    test("does not redirect and renders tabs when user is not eligible to order", () => {
      mockedUseGetGutcheckStatusQuery.mockReturnValue({
        data: { isEligibleToOrder: false, hasGutCheckHistory: false },
        isLoading: false,
        isFetching: false,
        refetch: vi.fn(),
      });

      render(<GutCheckDisplay />, { wrapper: TestWrapper });

      expect(mockRouter.push).not.toHaveBeenCalled();
      expect(screen.getByRole("tab", { name: /my order/i })).toBeInTheDocument();
      expect(screen.getByRole("tab", { name: /past results/i })).toBeInTheDocument();
    });

    test("redirects to intro screen when user is eligible and has no gut check history", () => {
      mockedUseGetGutcheckStatusQuery.mockReturnValue({
        data: { isEligibleToOrder: true, hasGutCheckHistory: false },
        isLoading: false,
        isFetching: false,
        refetch: vi.fn(),
      });

      render(<GutCheckDisplay />, { wrapper: TestWrapper });

      expect(mockRouter.push).toHaveBeenCalledWith(
        `${Routes.GUT_CHECK_INTRO}/${GUT_CHECK_INTRO_SCREENS.MICROBIOME_ROLE}`,
      );
    });

    test("does not redirect and renders tabs when user is eligible and has gut check history", () => {
      mockedUseGetGutcheckStatusQuery.mockReturnValue({
        data: { isEligibleToOrder: true, hasGutCheckHistory: true },
        isLoading: false,
        isFetching: false,
        refetch: vi.fn(),
      });

      render(<GutCheckDisplay />, { wrapper: TestWrapper });

      expect(mockRouter.push).not.toHaveBeenCalled();
      expect(screen.getByRole("tab", { name: /my order/i })).toBeInTheDocument();
      expect(screen.getByRole("tab", { name: /past results/i })).toBeInTheDocument();
    });
  });
});
