import { useLayoutEffect, useState } from "react";
import { Tabs, Tab, Box } from "@mui/material";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { TabsPanel } from "@Components/TabsPanel/TabsPanel";
import { tabsA11yProps } from "@Components/TabsPanel/utils/tabsA11yProps";
import { Routes } from "@Types";

import { useGetGutcheckStatusQuery } from "./api/gutCheckApi";
import { TabKey, TabValues } from "./assets/constants";
import { GutCheckError } from "./components/shared/GutCheckError";
import { MyOrderTab } from "./components/tabs/MyOrderTab";
import { PastResultsTab } from "./components/tabs/PastResultsTab";
import { GUT_CHECK_INTRO_SCREENS } from "./types/gutCheck.types";

const GUTCHECK_STRINGS = appStrings.features.gutCheck;

export const GutCheckDisplay = () => {
  const router = useRouter();
  const [activeTabName, setActiveTabName] = useState<TabValues>(TabKey.MY_ORDER);
  const { data: gutCheckStatus, isLoading, isError: isStatusError } = useGetGutcheckStatusQuery();
  const { isEligibleToOrder = false, hasGutCheckHistory = false } = gutCheckStatus || {};

  // Define the redirect condition: ensures router is ready, data isn't loading, and eligibility criteria are met.
  const shouldRedirect = router.isReady && !isLoading && isEligibleToOrder && !hasGutCheckHistory;

  useLayoutEffect(() => {
    if (shouldRedirect) {
      router.push(`${Routes.GUT_CHECK_INTRO}/${GUT_CHECK_INTRO_SCREENS.MICROBIOME_ROLE}`);
    }
  }, [shouldRedirect, router]);

  const handleTabChange = (_: React.SyntheticEvent, newValue: TabValues) => {
    setActiveTabName(newValue);
  };

  if (isLoading || shouldRedirect) {
    return <LoadingSpinner open />;
  }

  if (isStatusError) {
    return (
      <GutCheckError
        title={GUTCHECK_STRINGS.errors.statusLoadTitle}
        subtitle={GUTCHECK_STRINGS.errors.statusLoadSubtitle}
      />
    );
  }

  return (
    <Box sx={{ width: "100%" }}>
      <Tabs value={activeTabName} onChange={handleTabChange} aria-label={GUTCHECK_STRINGS.tabs.ariaLabel}>
        <Tab label={GUTCHECK_STRINGS.tabs.myOrder} value={TabKey.MY_ORDER} {...tabsA11yProps(TabKey.MY_ORDER)} />
        <Tab
          label={GUTCHECK_STRINGS.tabs.pastResults}
          value={TabKey.PAST_RESULTS}
          {...tabsA11yProps(TabKey.PAST_RESULTS)}
        />
      </Tabs>
      <TabsPanel tabName={TabKey.MY_ORDER} currentSelectedTabName={activeTabName} sx={{ marginTop: 7 }}>
        <MyOrderTab />
      </TabsPanel>
      <TabsPanel tabName={TabKey.PAST_RESULTS} currentSelectedTabName={activeTabName} sx={{ marginTop: 7 }}>
        <PastResultsTab />
      </TabsPanel>
    </Box>
  );
};
