import { GutCheckHistoryResponse } from "../types/gutCheck.types";

// NOTE: results already available
export const mockGutCheckHistoryResults: GutCheckHistoryResponse = [
  {
    id: "d10f3488-803d-47cd-937c-cb5979daf129",
    orderedAt: "2025-01-21T15:23:00Z",
    results: {
      conditions: [],
      microbiome: {},
      symptoms: [],
    },
    status: "results_available",
  },
  {
    id: "0378f298-b042-4380-bc01-2503902f3ccb",
    orderedAt: "2024-11-31T08:33:00Z",
    results: {
      conditions: [],
      microbiome: {},
      symptoms: [],
    },
    status: "results_available",
  },
];

// NOTE: results where the order is still in progress
export const mockGutCheckHistory: GutCheckHistoryResponse = [
  {
    id: "d10f3488-803d-47cd-937c-cb5979daf129",
    orderedAt: "2025-01-21T15:23:00Z",
    status: "ordered",
  },
  {
    id: "0378f298-b042-4380-bc01-2503902f3ccb",
    orderedAt: "2024-11-31T08:33:00Z",
    results: {
      conditions: [],
      microbiome: {},
      symptoms: [],
    },
    status: "results_available",
  },
];

export const mockResponseGetGutCheckResultsAvailableWithMicrobiome: GutCheckHistoryResponse = [
  {
    id: "4d8c2175-128b-41cd-ade0-d15cad08c486",
    orderedAt: "2025-01-22T15:23:00Z",
    results: {
      conditions: [
        {
          bacteria: [
            {
              abundance: 0.3,
              cdf: 0.3453,
              compareToHealthPercentage: 0.5,
              definition: [
                {
                  key: "key",
                  max: 1.54,
                  min: 0.1,
                },
              ],
              description: "This is a description",
              details: "This is a details",
              effect: "Protective",
              key: "a_bacteria",
              levelEffect: "good",
              levelMap: "low",
              logNormalizedAbundance: 0.234,
              normalizedHealthyAverageLevels: {
                max: 0.5,
                min: 0.1,
              },
              presentHealthPercentage: 0.234,
              taxLevel: "This is a tax level",
              title: "Bacteria Title in Conditions",
              type: "bacteria type",
            },
          ],
          comment: "This is a comment",
          isExpected: true,
        },
      ],
      microbiome: {
        diversity: {
          title: "diversity",
          definition: [
            {
              map: "low",
              min: 0,
              max: 0.4,
              type: "range",
            },
            {
              map: "normal",
              min: 0.2,
              max: 0.5,
              type: "range",
            },
            {
              map: "high",
              min: 0.8,
              max: 1,
              type: "range",
            },
          ],
          value: 0.3,
        },
        inflammatory: {
          title: "inflammatory",
          definition: [
            {
              map: "low",
              min: 0,
              max: 0.4,
              type: "range",
            },
            {
              map: "normal",
              min: 0.4,
              max: 0.8,
              type: "range",
            },
            {
              map: "high",
              min: 0.8,
              max: 1,
              type: "range",
            },
          ],
          value: 0.2,
        },
        protective: {
          title: "protective",
          definition: [
            {
              map: "low",
              min: 0,
              max: 0.4,
              type: "range",
            },
            {
              map: "normal",
              min: 0.4,
              max: 0.8,
              type: "range",
            },
            {
              map: "high",
              min: 0.8,
              max: 1,
              type: "range",
            },
          ],
          value: 0.6,
        },
        unique: {
          title: "unique",
          definition: [
            {
              map: "low",
              min: 0,
              max: 0.4,
              type: "range",
            },
            {
              map: "normal",
              min: 0.4,
              max: 0.8,
              type: "range",
            },
            {
              map: "high",
              min: 0.8,
              max: 1,
              type: "range",
            },
          ],
          value: 0.8,
        },
      },
      symptoms: [
        {
          description: "This is a description",
          key: "some_symptom",
          title: "Symptom",
          type: "symptom",
          logged: {
            count: 10,
            mild: 0.12938294,
            moderate: 0.42938295,
            severe: 0.7666344,
          },
          logStatus: true,
          symptom_type: "gut_health",
        },
      ],
    },
    status: "results_available",
  },
  {
    id: "d10f3488-803d-47cd-937c-cb5979daf129",
    orderedAt: "2025-01-21T15:23:00Z",
    results: {
      conditions: [
        {
          bacteria: [
            {
              abundance: 0.3,
              cdf: 0.3453,
              compareToHealthPercentage: 0.5,
              definition: [
                {
                  key: "key",
                  max: 1.54,
                  min: 0.1,
                },
              ],
              description: "This is a description",
              details: "This is a details",
              effect: "Protective",
              key: "a_bacteria",
              levelEffect: "good",
              levelMap: "low",
              logNormalizedAbundance: 0.234,
              normalizedHealthyAverageLevels: {
                max: 0.5,
                min: 0.1,
              },
              presentHealthPercentage: 0.234,
              taxLevel: "This is a tax level",
              title: "Bacteria Title in Conditions",
              type: "bacteria type",
            },
          ],
          comment: "This is a comment",
          isExpected: true,
        },
      ],
      microbiome: {
        diversity: {
          title: "diversity",
          definition: [
            {
              map: "low",
              min: 0,
              max: 0.4,
              type: "range",
            },
            {
              map: "normal",
              min: 0.2,
              max: 0.5,
              type: "range",
            },
            {
              map: "high",
              min: 0.8,
              max: 1,
              type: "range",
            },
          ],
          value: 0.55,
        },
        inflammatory: {
          title: "inflammatory",
          definition: [
            {
              map: "low",
              min: 0,
              max: 0.4,
              type: "range",
            },
            {
              map: "normal",
              min: 0.4,
              max: 0.8,
              type: "range",
            },
            {
              map: "high",
              min: 0.8,
              max: 1,
              type: "range",
            },
          ],
          value: 0.77,
        },
        protective: {
          title: "protective",
          definition: [
            {
              map: "low",
              min: 0,
              max: 0.4,
              type: "range",
            },
            {
              map: "normal",
              min: 0.4,
              max: 0.8,
              type: "range",
            },
            {
              map: "high",
              min: 0.8,
              max: 1,
              type: "range",
            },
          ],
          value: 0.44,
        },
        unique: {
          title: "unique",
          definition: [
            {
              map: "low",
              min: 0,
              max: 0.4,
              type: "range",
            },
            {
              map: "normal",
              min: 0.4,
              max: 0.8,
              type: "range",
            },
            {
              map: "high",
              min: 0.8,
              max: 1,
              type: "range",
            },
          ],
          value: 0.8,
        },
      },
      symptoms: [
        {
          description: "This is a description",
          key: "some_symptom",
          logStatus: true,
          logged: {
            count: 10,
            mild: 0.12938294,
            moderate: 0.42938295,
            severe: 0.7666344,
          },
          symptom_type: "gut_health",
          title: "Symptom",
          type: "symptom",
        },
      ],
    },
    status: "results_available",
  },
  {
    id: "0378f298-b042-4380-bc01-2503902f3ccb",
    orderedAt: "2024-11-31T08:33:00Z",
    results: { conditions: [], microbiome: {}, symptoms: [] },
    status: "results_available",
  },
];
