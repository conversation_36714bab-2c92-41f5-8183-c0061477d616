import { useGetGutcheckHistoryQuery, useGetGutcheckStatusQuery } from "../api/gutCheckApi";

export const useGutCheckHistory = () => {
  const {
    data: gutCheckStatus,
    isLoading: isStatusLoading,
    error: statusError,
    isError: isStatusError,
  } = useGetGutcheckStatusQuery();

  const shouldFetchHistory = gutCheckStatus?.hasGutCheckHistory && gutCheckStatus?.isEligibleToOrder;

  const {
    data: gutCheckHistory = [],
    isLoading: isHistoryLoading,
    error: historyError,
    isError: isHistoryError,
  } = useGetGutcheckHistoryQuery(undefined, {
    skip: !shouldFetchHistory,
  });

  return {
    gutCheckHistory,
    isLoading: Boolean(isStatusLoading || (shouldFetchHistory && isHistoryLoading)),
    error: statusError || historyError,
    isError: isStatusError || isHistoryError,
    hasHistory: gutCheckStatus?.hasGutCheckHistory ?? false,
    isEligible: gutCheckStatus?.isEligibleToOrder ?? false,
  };
};
