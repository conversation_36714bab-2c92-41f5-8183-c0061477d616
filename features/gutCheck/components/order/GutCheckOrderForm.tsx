import React from "react";
import { useSelector } from "react-redux";
import { AppointmentUSState } from "@vivantehealth/vivante-core";
import { Box, Paper, Typography, Grid, Button } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { useRouter } from "next/router";
import { useForm, FormProvider } from "react-hook-form";

import { appStrings } from "@Assets/app_strings";
import { FormInput, FormDropdown } from "@Components/form/Fields";
import { memberStateSelector } from "@Features/member/store/memberStateSlice";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";
import { useAppDispatch } from "@Store/hooks";
import { Routes } from "@Types";

import { useCreateGutcheckOrderMutation } from "../../api/gutCheckApi";
import { completeGutCheckIntervention } from "../../api/gutCheckFirestoreApi";
import { GutcheckError } from "../../types/gutCheck.types";
import { isPostGutCheckOrderError } from "../../utils/GutCheck.utils";
import { processGutCheckFormSubmission } from "../../utils/processGutCheckFormSubmission";
import { GutCheckContainer } from "../shared/GutCheckContainer";

type GutCheckOrderFormFields = {
  address: string;
  aptSuite?: string;
  zip: string;
  state: string;
  city: string;
};

const defaultValues: GutCheckOrderFormFields = {
  address: "",
  aptSuite: "",
  zip: "",
  state: "",
  city: "",
};

const GUT_CHECK_ORDER_FORM_STRINGS = appStrings.features.gutCheck.orderForm;

export const GutCheckOrderForm = () => {
  const dispatch = useAppDispatch();
  const methods = useForm<GutCheckOrderFormFields>({ defaultValues, mode: "onSubmit" });
  const router = useRouter();
  const member = useSelector(memberStateSelector("member"));
  const [createGutcheckOrder] = useCreateGutcheckOrderMutation();

  const handleBack = () => {
    router.back();
  };

  const onSubmit = async (data: GutCheckOrderFormFields) => {
    if (member === undefined) {
      return;
    }

    try {
      await createGutcheckOrder({
        firstName: member.firstName,
        lastName: member.lastName,
        shippingAddress: {
          street1: data.address,
          street2: data.aptSuite,
          city: data.city,
          state: data.state,
          zipCode: data.zip,
        },
      }).unwrap();

      dispatch(
        SnackbarStateSlice.actions.toggleSnackbar({
          isOpen: true,
          message: appStrings.features.gutCheck.orderSuccessful,
          variant: "success",
        }),
      );

      await completeGutCheckIntervention();
      router.push(`${Routes.GUT_CHECK_NEW}`);
    } catch (error: unknown) {
      if (isPostGutCheckOrderError<GutcheckError>(error)) {
        const processedError = processGutCheckFormSubmission(error);

        dispatch(
          SnackbarStateSlice.actions.toggleSnackbar({
            message: processedError.message,
            isOpen: true,
            variant: "error",
          }),
        );
      }
    }
  };

  // Disabled submit button if all fields are empty except aptSuite
  const formValues = methods.watch(["address", "zip", "state", "city"]);
  const disabledSubmitButton = !Object.values(formValues).every((value) => value.trim() !== "");

  return (
    <GutCheckContainer handleGoBack={handleBack}>
      <FormProvider {...methods}>
        <form
          onSubmit={methods.handleSubmit(onSubmit)}
          style={{ width: "100%", display: "flex", flexDirection: "column", gap: 16 }}
        >
          <Paper sx={{ display: "flex", p: 4, alignItems: "center", gap: 1, alignSelf: "stretch" }}>
            <Box
              display="flex"
              flexDirection="column"
              justifyContent="center"
              alignItems="flex-start"
              gap={1}
              flex="1 0 0"
            >
              <Typography variant="h4">
                {member?.firstName} {member?.lastName}
              </Typography>
              <Typography variant="body" color={color.text.strong}>
                {member?.email}
              </Typography>
            </Box>
          </Paper>

          <Paper
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: 1,
              borderRadius: 4,
            }}
          >
            <Typography variant="h3" mb={5}>
              {GUT_CHECK_ORDER_FORM_STRINGS.uiText.shippingInformation}
            </Typography>

            <Grid container spacing={4}>
              <Grid item xs={12}>
                <FormInput
                  name="address"
                  label={GUT_CHECK_ORDER_FORM_STRINGS.labels.address}
                  id="address-input"
                  type="text"
                  required
                  rules={{
                    required: {
                      value: true,
                      message: GUT_CHECK_ORDER_FORM_STRINGS.validationMessages.addressRequired,
                    },
                    maxLength: {
                      value: 50,
                      message: GUT_CHECK_ORDER_FORM_STRINGS.validationMessages.addressMaxLength,
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormInput
                  name="aptSuite"
                  label={GUT_CHECK_ORDER_FORM_STRINGS.labels.aptSuite}
                  id="aptSuite-input"
                  type="text"
                  rules={{
                    maxLength: {
                      value: 50,
                      message: GUT_CHECK_ORDER_FORM_STRINGS.validationMessages.aptSuiteMaxLength,
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormInput
                  name="city"
                  label={GUT_CHECK_ORDER_FORM_STRINGS.labels.city}
                  id="city-input"
                  type="text"
                  required
                  rules={{
                    required: {
                      value: true,
                      message: GUT_CHECK_ORDER_FORM_STRINGS.validationMessages.cityRequired,
                    },
                    maxLength: {
                      value: 40,
                      message: GUT_CHECK_ORDER_FORM_STRINGS.validationMessages.cityMaxLength,
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormDropdown
                  name="state"
                  label={GUT_CHECK_ORDER_FORM_STRINGS.labels.state}
                  required
                  rules={{
                    required: {
                      value: true,
                      message: GUT_CHECK_ORDER_FORM_STRINGS.validationMessages.stateRequired,
                    },
                  }}
                  options={Object.keys(AppointmentUSState).map((state: string) => ({ value: state, label: state }))}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormInput
                  name="zip"
                  label={GUT_CHECK_ORDER_FORM_STRINGS.labels.zipCode}
                  id="zip-input"
                  type="text"
                  required
                  rules={{
                    required: {
                      value: true,
                      message: GUT_CHECK_ORDER_FORM_STRINGS.validationMessages.zipCodeRequired,
                    },
                    pattern: {
                      value: /^[0-9]{5}$/,
                      message: GUT_CHECK_ORDER_FORM_STRINGS.validationMessages.zipPattern,
                    },
                  }}
                />
              </Grid>
            </Grid>
          </Paper>

          <Box mt={2} display="flex" justifyContent="center">
            <Button
              type="submit"
              variant="primary"
              fullWidth
              aria-label={GUT_CHECK_ORDER_FORM_STRINGS.uiText.submitButton}
              sx={{ maxWidth: 400 }}
              disabled={disabledSubmitButton}
            >
              {GUT_CHECK_ORDER_FORM_STRINGS.uiText.submitButton}
            </Button>
          </Box>
        </form>
      </FormProvider>
    </GutCheckContainer>
  );
};
