import { Box, Paper, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

import { HowItWorks } from "../shared/HowItWorks";

export const GUT_CHECK_STRINGS = appStrings.features.gutCheck;

export const KitOrderedSection = () => {
  return (
    <>
      <Paper sx={{ display: "flex", px: 4, py: 5, mb: 2 }}>
        <Box display="flex" flexDirection="column" justifyContent="center" alignItems="flex-start" gap={1}>
          <Typography variant="h3">{GUT_CHECK_STRINGS.myOrderTab.kitOrderedTitle}</Typography>
          <Typography variant="bodyDense">{GUT_CHECK_STRINGS.myOrderTab.kitDeliveryInfo}</Typography>
        </Box>
      </Paper>
      <Paper sx={{ width: "100%", p: 4, mt: 5 }}>
        <Box display="flex" flexDirection="column" gap={3}>
          <HowItWorks />
        </Box>
      </Paper>
    </>
  );
};
