import { Paper } from "@mui/material";
import { styled } from "@mui/material/styles";
import { color } from "@vivantehealth/design-tokens";

import { SPACING_16_PX } from "@Assets/style_constants";

export const StyledResultsPaper = styled(Paper)(() => ({
  position: "relative",
  display: "flex",
  padding: SPACING_16_PX,
  gap: 1,
  marginBottom: SPACING_16_PX,
  alignItems: "center",
  justifyContent: "center",
  backgroundColor: color.background.brand.fill5Light,
  borderColor: "transparent",

  "&::before": {
    content: '""',
    position: "absolute",
    inset: 0,
    padding: "2px", // Border width
    background: `linear-gradient(135deg, ${color.background.brand.fill5} 0%, ${color.background.brand.fill3} 100%)`,
    borderRadius: "inherit",
    // masking creates lighter background
    mask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
    maskComposite: "exclude",
    WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
    WebkitMaskComposite: "xor",
  },
}));
