import { Box, Button } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";

type GutCheckContainerProps = Readonly<{
  showBackButton?: boolean;
  handleGoBack?: () => void;
  children: React.ReactNode;
}>;

export const GutCheckContainer = ({ showBackButton = true, handleGoBack, children }: GutCheckContainerProps) => {
  return (
    <Box display="flex" flexDirection="column" gap={6} width="100%">
      {showBackButton ? (
        <Button
          variant="secondary"
          startIcon={<AppIcon name="LeftChevron" />}
          sx={{ alignSelf: "flex-start" }}
          onClick={() => handleGoBack?.()}
        >
          {appStrings.buttonText.back}
        </Button>
      ) : null}

      {children}
    </Box>
  );
};
