import { Box, Paper, Typography } from "@mui/material";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { BackButton } from "@Components/BackButton/BackButton";
import { Routes } from "@Types";

type GutCheckErrorProps = Readonly<{
  title: string;
  subtitle: string;
}>;

export const GutCheckError = ({ title, subtitle }: GutCheckErrorProps) => {
  const router = useRouter();

  const handleReturnHome = () => {
    router.push(Routes.HOME);
  };

  return (
    <>
      <BackButton onClick={handleReturnHome}>{appStrings.errorPages.errorPageButtonText}</BackButton>
      <Paper sx={{ p: 5, mb: 2, mt: 5 }}>
        <Box display="flex" flexDirection="column" alignItems="flex-start">
          <Typography variant="h3" sx={{ mb: 3 }}>
            {title}
          </Typography>

          <Typography variant="body">{subtitle}</Typography>
        </Box>
      </Paper>
    </>
  );
};
