import React from "react";
import { Typography, Box } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";

import { listToIconMap } from "../../assets/constants";

const GUT_CHECK_STRINGS = appStrings.features.gutCheck;

export const HowItWorks = () => {
  return (
    <>
      <Box display="flex" flexDirection="column" gap={3} alignSelf="flex-start">
        <Typography variant="h4">{GUT_CHECK_STRINGS.intro.screen3.subTitle}</Typography>
      </Box>
      {GUT_CHECK_STRINGS.intro.screen3.keyItems.map((keyItem, index) => (
        <Box
          key={keyItem.title}
          display="grid"
          gridTemplateColumns="min-content 1fr"
          gridTemplateRows="auto auto"
          columnGap={4}
          alignItems="center"
        >
          <Box sx={{ gridColumn: "span 1" }} alignItems="center">
            <AppIcon
              name={listToIconMap[index]}
              size="sm"
              color={color.icon.strong}
              containerStyles={{ minWidth: 13 }} // prevents shrinking of icon
            />
          </Box>
          <Typography alignItems="center" variant="body" color={color.text.strong} sx={{ gridColumn: "span 1" }}>
            {keyItem.title}
          </Typography>
          <Box sx={{ gridColumn: "span 1" }} />
          <Typography variant="bodyDense" sx={{ gridColumn: "span 1" }}>
            {keyItem.description}
          </Typography>
        </Box>
      ))}
    </>
  );
};
