import { <PERSON>, Button, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import Image from "next/image";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { RADIUS_16_PX } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import GutCheckCard from "@PublicImages/gutCheck/gut-check-kit.webp";
import { Routes } from "@Types";

import { GUT_CHECK_INTRO_SCREENS, IntroScreenProps } from "../../types/gutCheck.types";
import { GutCheckContainer } from "../shared/GutCheckContainer";
const GUT_CHECK_STRINGS = appStrings.features.gutCheck;
const IMAGE_WIDTH = 500;
const IMAGE_HEIGHT = 300;

const BulletPoint = ({ title }: { title: string }) => {
  return (
    <Box display="flex" alignItems="center" gap={2}>
      <AppIcon name="RightArrow" size="sm" />

      <Typography variant="bodyDense">{title}</Typography>
    </Box>
  );
};

export const WhatAndWhy = ({ handleGoToScreen }: IntroScreenProps) => {
  const router = useRouter();

  const handleOrderClick = () => {
    router.push(Routes.GUT_CHECK_ORDER);
  };

  const handleNextClick = () => {
    handleGoToScreen(GUT_CHECK_INTRO_SCREENS.UNLOCK_BETTER_HEALTH);
  };

  return (
    <GutCheckContainer handleGoBack={() => handleGoToScreen(GUT_CHECK_INTRO_SCREENS.MICROBIOME_ROLE)}>
      <Box display="flex" justifyContent="center">
        <Box display="flex" flexDirection="column" gap={6} alignItems="center" position="relative">
          <Typography variant="h2Serif" textAlign={"center"}>
            {GUT_CHECK_STRINGS.intro.screen2.title}
          </Typography>

          <Paper sx={{ width: "100%", p: 4 }}>
            <Box display="flex" flexDirection="column" gap={4}>
              <Image
                src={GutCheckCard}
                alt=""
                aria-hidden="true"
                width={IMAGE_WIDTH}
                height={IMAGE_HEIGHT}
                style={{ borderRadius: RADIUS_16_PX, width: "100%", height: "auto" }}
              />

              <Box display="flex" flexDirection="column" gap={3} mt={2} alignSelf="flex-start">
                <Typography variant="h4">{GUT_CHECK_STRINGS.intro.screen2.microbiomeWhatIsTitle}</Typography>
                <Typography variant="bodyDense" color={color.text.strong}>
                  {GUT_CHECK_STRINGS.intro.screen2.microbiomeWhatIsDescription}
                </Typography>
              </Box>
              <Box display="flex" flexDirection="column" gap={3} alignItems="flex-start">
                <Typography variant="h4" pb={1}>
                  {GUT_CHECK_STRINGS.intro.screen2.microbiomeWhyTakeItTitle}
                </Typography>
                <Box display="flex" flexDirection="column" gap={2}>
                  {GUT_CHECK_STRINGS.intro.screen2.microbiomeWhyTakeItItems.map((bulletPoint) => (
                    <BulletPoint title={bulletPoint} key={bulletPoint} />
                  ))}
                </Box>
              </Box>
              <Box display="flex" flexDirection="column" gap={3} alignItems="flex-start">
                <Typography variant="h4" pb={1}>
                  {GUT_CHECK_STRINGS.intro.screen2.microbiomeNotSureTitle}
                </Typography>
                <Typography variant="bodyDense" color={color.text.strong}>
                  {GUT_CHECK_STRINGS.intro.screen2.microbiomeNotSureDescription}
                </Typography>
              </Box>
            </Box>
          </Paper>

          <Box display="flex" gap={2} width="100%">
            <Button variant="secondary" fullWidth onClick={handleOrderClick}>
              {GUT_CHECK_STRINGS.intro.buttonOrder}
            </Button>
            <Button variant="primary" fullWidth onClick={handleNextClick}>
              {appStrings.buttonText.next}
            </Button>
          </Box>
        </Box>
      </Box>
    </GutCheckContainer>
  );
};
