import { fireEvent, render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";

import { TestWrapper } from "@TestUtils/TestWrapper";
import { Routes } from "@Types";

import { UnlockBetterHealth } from "./UnlockBetterHealth";
import { GUT_CHECK_INTRO_SCREENS } from "../../types/gutCheck.types";

const mockedRouterPush = vi.fn();

vi.mock("next/navigation", () => ({
  useRouter: () => ({
    push: mockedRouterPush,
    replace: vi.fn(),
    query: {},
    pathname: "/",
    asPath: "/",
  }),
}));

describe("UnlockBetterHealth", () => {
  const handleGoToScreen = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render the component with title and buttons", () => {
    render(
      <TestWrapper>
        <UnlockBetterHealth handleGoToScreen={handleGoToScreen} />
      </TestWrapper>,
    );

    expect(screen.getByText("The GutCheck test: Unlock better health in 4 simple steps")).toBeInTheDocument();
    expect(screen.getByText("Order GutCheck test")).toBeInTheDocument();
    expect(screen.getByText("Back")).toBeInTheDocument();
  });

  it("should call handleGoToScreen with WHAT_AND_WHY when Back button is clicked", () => {
    render(
      <TestWrapper>
        <UnlockBetterHealth handleGoToScreen={handleGoToScreen} />
      </TestWrapper>,
    );

    fireEvent.click(screen.getByText("Back"));
    expect(handleGoToScreen).toHaveBeenCalledWith(GUT_CHECK_INTRO_SCREENS.WHAT_AND_WHY);
  });

  it("should call router.push with Routes.GUT_CHECK_ORDER when 'Order GutCheck test now' button is clicked", () => {
    render(
      <TestWrapper>
        <UnlockBetterHealth handleGoToScreen={handleGoToScreen} />
      </TestWrapper>,
    );

    fireEvent.click(screen.getByText("Order GutCheck test"));
    expect(mockedRouterPush).toHaveBeenCalledWith(Routes.GUT_CHECK_ORDER);
  });
});
