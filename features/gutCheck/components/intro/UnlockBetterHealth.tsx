import { Box, Button, Paper, Typography } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/navigation";

import { appStrings } from "@Assets/app_strings";
import GutCheckHowItWorksBanner from "@PublicImages/gutCheck/gut-check-how-it-works-banner.webp";
import { Routes } from "@Types";

import { GUT_CHECK_INTRO_SCREENS, GutCheckIntroScreens } from "../../types/gutCheck.types";
import { GutCheckContainer } from "../shared/GutCheckContainer";
import { HowItWorks } from "../shared/HowItWorks";

const GUT_CHECK_STRINGS = appStrings.features.gutCheck;
const IMAGE_WIDTH = 1304;
const IMAGE_HEIGHT = 440;

type IntroScreenProps = Readonly<{
  handleGoToScreen: (screenName: GutCheckIntroScreens) => void;
}>;

export const UnlockBetterHealth = ({ handleGoToScreen }: IntroScreenProps) => {
  const router = useRouter();

  const handleOrderClick = () => {
    router.push(Routes.GUT_CHECK_ORDER);
  };

  return (
    <GutCheckContainer handleGoBack={() => handleGoToScreen(GUT_CHECK_INTRO_SCREENS.WHAT_AND_WHY)}>
      <Box display="flex" justifyContent="center">
        <Box display="flex" flexDirection="column" gap={6} alignItems="center" position="relative">
          <Typography variant="h2Serif" textAlign={"center"}>
            {GUT_CHECK_STRINGS.intro.screen3.title}
          </Typography>

          <Paper sx={{ width: "100%", p: 4 }}>
            <Box display="flex" flexDirection="column" gap={4} mt={2}>
              <Image
                src={GutCheckHowItWorksBanner}
                alt=""
                style={{ borderRadius: 4, width: "100%", height: "auto" }}
                aria-hidden="true"
                width={IMAGE_WIDTH}
                height={IMAGE_HEIGHT}
              />
              <HowItWorks />
            </Box>
          </Paper>

          <Box display="flex" gap={2} width="50%">
            <Button variant="primary" fullWidth onClick={handleOrderClick}>
              {GUT_CHECK_STRINGS.buttonOrder}
            </Button>
          </Box>
        </Box>
      </Box>
    </GutCheckContainer>
  );
};
