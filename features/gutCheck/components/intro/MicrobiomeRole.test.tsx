import { fireEvent, render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";

import { mockedNextRouter } from "@TestUtils/mockedNextRouter.util";
import { TestWrapper } from "@TestUtils/TestWrapper";
import { Routes } from "@Types";

import { MicrobiomeRole } from "./MicrobiomeRole";
import { GUT_CHECK_INTRO_SCREENS } from "../../types/gutCheck.types";

describe("MicrobiomeRole", () => {
  const handleGoToScreen = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render the component with title and buttons", () => {
    render(
      <TestWrapper>
        <MicrobiomeRole handleGoToScreen={handleGoToScreen} />
      </TestWrapper>,
    );

    expect(screen.getByText("The GutCheck test: What it is and why you should take it")).toBeInTheDocument();
    expect(screen.getByText("Next")).toBeInTheDocument();
    expect(screen.getByText("Order GutCheck test now")).toBeInTheDocument();
  });

  it("should call router.push with Routes.HOME when back button is clicked", () => {
    render(
      <TestWrapper>
        <MicrobiomeRole handleGoToScreen={handleGoToScreen} />
      </TestWrapper>,
    );

    fireEvent.click(screen.getByText("Back"));
    expect(mockedNextRouter.mockedRouterPush).toHaveBeenCalledWith(Routes.HOME);
  });

  it("should call handleGoToScreen with WHAT_AND_WHY when Next button is clicked", () => {
    render(
      <TestWrapper>
        <MicrobiomeRole handleGoToScreen={handleGoToScreen} />
      </TestWrapper>,
    );

    fireEvent.click(screen.getByText("Next"));
    expect(handleGoToScreen).toHaveBeenCalledWith(GUT_CHECK_INTRO_SCREENS.WHAT_AND_WHY);
  });

  it("should call router.push with Routes.GUT_CHECK_ORDER when 'Order Gut Check Test' button is clicked", () => {
    render(
      <TestWrapper>
        <MicrobiomeRole handleGoToScreen={handleGoToScreen} />
      </TestWrapper>,
    );

    fireEvent.click(screen.getByText("Order GutCheck test now"));
    expect(mockedNextRouter.mockedRouterPush).toHaveBeenCalledWith(Routes.GUT_CHECK_ORDER);
  });
});
