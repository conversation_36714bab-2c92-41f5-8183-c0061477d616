import { fireEvent, render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";

import { mockedNextRouter } from "@TestUtils/mockedNextRouter.util";
import { TestWrapper } from "@TestUtils/TestWrapper";
import { Routes } from "@Types";

import { WhatAndWhy } from "./WhatAndWhy";
import { GUT_CHECK_INTRO_SCREENS } from "../../types/gutCheck.types";

describe("WhatAndWhy", () => {
  const handleGoToScreen = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render the component with title, content, and buttons", () => {
    render(
      <TestWrapper>
        <WhatAndWhy handleGoToScreen={handleGoToScreen} />
      </TestWrapper>,
    );

    expect(screen.getByText("The GutCheck test: What it is and why you should take it")).toBeInTheDocument();

    expect(screen.getByText("The GutCheck is your free, at-home gut microbiome test:")).toBeInTheDocument();
    expect(screen.getByText("Your results help your Dietitian to:")).toBeInTheDocument();
    expect(screen.getByText("Not sure if it's for you?")).toBeInTheDocument();

    const dietitianHelpItems = [
      "Help you make targeted changes to your diet and routine",
      "Set realistic goals based on your health history",
      "Track progress over time with repeat GutChecks",
    ];

    dietitianHelpItems.forEach((item) => {
      expect(screen.getByText(item)).toBeInTheDocument();
    });

    expect(
      screen.getByText(
        "It gives you a snapshot of the bacteria living in your gut—and how they might be affecting your health.",
      ),
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        "Even if you're not having symptoms, the GutCheck can be a starting point for discussing how to optimize your diet and support overall health.",
      ),
    ).toBeInTheDocument();

    expect(screen.getByText("Order GutCheck test now")).toBeInTheDocument();
    expect(screen.getByText("Next")).toBeInTheDocument();
    expect(screen.getByText("Back")).toBeInTheDocument();
  });

  it("should call handleGoToScreen with MicrobiomeRole when back button is clicked", () => {
    render(
      <TestWrapper>
        <WhatAndWhy handleGoToScreen={handleGoToScreen} />
      </TestWrapper>,
    );

    fireEvent.click(screen.getByText("Back"));
    expect(handleGoToScreen).toHaveBeenCalledWith(GUT_CHECK_INTRO_SCREENS.MICROBIOME_ROLE);
  });

  it("should call handleGoToScreen with UnlockBetterHealth when Next button is clicked", () => {
    render(
      <TestWrapper>
        <WhatAndWhy handleGoToScreen={handleGoToScreen} />
      </TestWrapper>,
    );

    fireEvent.click(screen.getByText("Next"));
    expect(handleGoToScreen).toHaveBeenCalledWith(GUT_CHECK_INTRO_SCREENS.UNLOCK_BETTER_HEALTH);
  });

  it("should call router.push with Routes.GUT_CHECK_ORDER when 'Order' button is clicked", () => {
    render(
      <TestWrapper>
        <WhatAndWhy handleGoToScreen={handleGoToScreen} />
      </TestWrapper>,
    );

    fireEvent.click(screen.getByText("Order GutCheck test now"));
    expect(mockedNextRouter.mockedRouterPush).toHaveBeenCalledWith(Routes.GUT_CHECK_ORDER);
  });
});
