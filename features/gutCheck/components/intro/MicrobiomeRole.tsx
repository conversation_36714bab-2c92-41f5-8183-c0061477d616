import { Box, Button, Paper, Typography } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { RADIUS_16_PX } from "@Assets/style_constants";
import GutCheckMicrobiomeWide from "@PublicImages/gutCheck/microbiome_wide.webp";
import { Routes } from "@Types";

import { GUT_CHECK_INTRO_SCREENS, GutCheckIntroScreens } from "../../types/gutCheck.types";
import { GutCheckContainer } from "../shared/GutCheckContainer";
const GUT_CHECK_STRINGS = appStrings.features.gutCheck.intro.screen1;
const IMAGE_WIDTH = 652;
const IMAGE_HEIGHT = 220;

type MicrobiomeRoleProps = Readonly<{
  handleGoToScreen: (screenName: GutCheckIntroScreens) => void;
}>;

export const MicrobiomeRole = ({ handleGoToScreen }: MicrobiomeRoleProps) => {
  const router = useRouter();

  const onBackCallback = () => {
    router.push(Routes.HOME);
  };

  const onNextCallback = () => {
    handleGoToScreen(GUT_CHECK_INTRO_SCREENS.WHAT_AND_WHY);
  };

  const onOrderTestCallback = () => {
    router.push(Routes.GUT_CHECK_ORDER);
  };

  return (
    <GutCheckContainer handleGoBack={onBackCallback}>
      <Box display="flex" justifyContent="center">
        <Box display="flex" flexDirection="column" gap={6} alignItems="center" position="relative">
          <Typography variant="h2Serif" textAlign={"center"}>
            {GUT_CHECK_STRINGS.title}
          </Typography>

          <Paper sx={{ width: "100%", p: 4 }}>
            <Box display="flex" flexDirection="column" gap={4}>
              <Image
                src={GutCheckMicrobiomeWide}
                alt={GUT_CHECK_STRINGS.imageAltText}
                style={{ borderRadius: RADIUS_16_PX, width: "100%", height: "auto" }}
                priority
                width={IMAGE_WIDTH}
                height={IMAGE_HEIGHT}
              />

              <Box display="flex" flexDirection="column" gap={3} mt={2} alignSelf="stretch">
                <Typography variant="h4">{GUT_CHECK_STRINGS.microbiomeSectionTitle}</Typography>
                <Typography variant="bodyDense">{GUT_CHECK_STRINGS.microbiomeDefinitionBody}</Typography>
              </Box>

              <Box display="flex" flexDirection="column" gap={3} alignSelf="stretch">
                <Typography variant="h4">{GUT_CHECK_STRINGS.microbiomeSectionTitle}</Typography>
                <Typography variant="bodyDense">{GUT_CHECK_STRINGS.balancedMicrobiomeBenefitsBody}</Typography>
                <Typography variant="bodyDense">{GUT_CHECK_STRINGS.gutCheckLimitationsBody}</Typography>
              </Box>
            </Box>
          </Paper>

          <Box display="flex" gap={2} width="100%">
            <Button variant="secondary" fullWidth onClick={onOrderTestCallback}>
              {GUT_CHECK_STRINGS.orderButtonText}
            </Button>
            <Button variant="primary" fullWidth onClick={onNextCallback}>
              {appStrings.buttonText.next}
            </Button>
          </Box>
        </Box>
      </Box>
    </GutCheckContainer>
  );
};
