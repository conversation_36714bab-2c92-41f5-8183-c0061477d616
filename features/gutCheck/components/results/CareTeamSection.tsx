import { useSelector } from "react-redux";
import { CareTeamUserType } from "@vivantehealth/vivante-core";
import { Box, Button, Card, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { RADIUS_FULL_PX } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { careTeamStateSelector } from "@Features/careTeam/store/careTeamStateSlice";
import { Routes } from "@Types";

import { DietitianChatOverview } from "./DietitianChatOverview";

const GUT_CHECK_STRINGS = appStrings.features.gutCheck;

export const CareTeamSection = () => {
  const router = useRouter();
  const careTeamUsers = useSelector(careTeamStateSelector("careTeamUsers"));
  const dietitianUser = careTeamUsers?.find((user) => user.userType === CareTeamUserType.DIETITIAN);

  const handleChatButtonClick = () => {
    router.push(Routes.CHAT);
  };

  return (
    <Card sx={{ p: 4, borderRadius: 4 }}>
      <Box
        sx={{
          bgcolor: color.background.brand.fill1Light,
          width: 32,
          height: 32,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          borderRadius: RADIUS_FULL_PX,
          mb: 2,
        }}
      >
        <AppIcon
          name="CareTeam"
          size="smd"
          containerStyles={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            color: color.background.brand.fill1,
          }}
        />
      </Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography variant="h4">{GUT_CHECK_STRINGS.myOrderTab.careTeam.title}</Typography>
        <Button variant="secondary" size="small" onClick={handleChatButtonClick}>
          {GUT_CHECK_STRINGS.myOrderTab.careTeam.chatButtonText}
        </Button>
      </Box>
      {dietitianUser && <DietitianChatOverview careTeamMember={dietitianUser} />}
    </Card>
  );
};
