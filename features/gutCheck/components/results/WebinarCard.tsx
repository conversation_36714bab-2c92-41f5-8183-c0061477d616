import { <PERSON>, <PERSON><PERSON>, <PERSON>, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { getVivanteEnvironment } from "@Config/config";

import { WEBINAR_URL_DEV, WEBINAR_URL_PROD_LIKE } from "../../assets/constants";

const WEBINAR_LINK = getVivanteEnvironment() === "dev4" ? WEBINAR_URL_DEV : WEBINAR_URL_PROD_LIKE;

const GUT_CHECK_STRINGS = appStrings.features.gutCheck;

export const WebinarCard = () => {
  const handleOpenWebinar = () => {
    window.open(WEBINAR_LINK, "_blank", "noopener,noreferrer");
  };

  return (
    <Card
      sx={{
        p: 4,
        borderRadius: 4,
        height: "100%",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          bgcolor: color.background.brand.fill3Light,
          width: 32,
          height: 32,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          borderRadius: "50%",
          mb: 2,
        }}
      >
        <AppIcon
          name="PlayVideo"
          size="smd"
          containerStyles={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            color: color.background.brand.fill3,
          }}
        />
      </Box>

      <Typography variant="h4" mb={2}>
        {GUT_CHECK_STRINGS.myOrderTab.careOptions.webinar.title}
      </Typography>

      <Typography variant="bodyDense" mb={4}>
        {GUT_CHECK_STRINGS.myOrderTab.careOptions.webinar.description}
      </Typography>

      <Button fullWidth variant="secondary" onClick={handleOpenWebinar}>
        {GUT_CHECK_STRINGS.myOrderTab.careOptions.webinar.buttonText}
      </Button>
    </Card>
  );
};
