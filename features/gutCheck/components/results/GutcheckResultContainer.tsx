import { useMemo } from "react";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { MicrobiomeScreen } from "@Features/microbiome/components/MicrobiomeScreen";
import { Routes } from "@Types";

import { useGutCheckHistory } from "../../hooks/useGutCheckHistory";
import { transformGutCheckItemToSample } from "../../utils/GutCheck.utils";
import { GutCheckError } from "../shared/GutCheckError";

const GUT_CHECK_STRINGS = appStrings.features.gutCheck;

export const GutcheckResultContainer = () => {
  const router = useRouter();
  const { id } = router.query;
  const gutCheckId = typeof id === "string" ? id : id?.[0];

  const { gutCheckHistory, isLoading, isError } = useGutCheckHistory();

  const samples = useMemo(() => gutCheckHistory?.map((item) => transformGutCheckItemToSample(item)), [gutCheckHistory]);

  if (isError) {
    return (
      <GutCheckError
        title={GUT_CHECK_STRINGS.errors.generalLoadTitle}
        subtitle={GUT_CHECK_STRINGS.errors.generalLoadSubtitle}
      />
    );
  }

  const selectedSampleIndex = samples?.findIndex((sample) => sample.id === gutCheckId);

  const handleSampleSelect = (sampleIndex: number) => {
    const sampleId = samples?.[sampleIndex]?.id;

    if (!sampleId) return;

    router.push(`${Routes.GUT_CHECK_RESULTS}/${sampleId}`);
  };

  return (
    <MicrobiomeScreen
      key={gutCheckId}
      samples={samples || []}
      loading={isLoading}
      selectedSampleIndex={selectedSampleIndex}
      onSampleSelect={handleSampleSelect}
    />
  );
};
