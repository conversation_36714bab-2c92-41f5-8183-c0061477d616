import { CareTeamUserType, type CareTeamUser } from "@vivantehealth/vivante-core";
import { fireEvent, render, screen } from "@testing-library/react";
import { vi, describe, test, expect, beforeEach } from "vitest";

import { CareTeamStateSlice } from "@Features/careTeam/store/careTeamStateSlice";
import { store } from "@Store/store";
import { mockedNextRouter } from "@TestUtils/mockedNextRouter.util";
import { TestWrapper } from "@TestUtils/TestWrapper";
import { Routes } from "@Types";

import { CareTeamSection } from "./CareTeamSection";

vi.mock("@Features/gutCheck/components/results/DietitianChatOverview", () => ({
  DietitianChatOverview: vi.fn(({ careTeamMember }) => (
    <div data-testid="dietitian-chat-overview" data-care-team-member-id={careTeamMember?.id} />
  )),
}));

const mockCareTeamUsersWithDietitian: CareTeamUser[] = [
  {
    id: "dietitian1",
    firstName: "<PERSON>",
    lastName: "Dietitian",
    userType: CareTeamUserType.DIETITIAN,
    avatarLink: "https://example.com/diana.jpg",
    careTeamExpertise: "",
  },
  {
    id: "sherpa1",
    firstName: "Colin",
    lastName: "Sherpa",
    userType: CareTeamUserType.SHERPA,
    avatarLink: "https://example.com/colin.jpg",
    careTeamExpertise: "",
  },
];

const mockCareTeamUsersWithoutDietitian: CareTeamUser[] = [
  {
    id: "sherpa1",
    firstName: "Colin",
    lastName: "Sherpa",
    userType: CareTeamUserType.SHERPA,
    avatarLink: "https://example.com/colin.jpg",
    careTeamExpertise: "",
  },
];

describe("CareTeamSection", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    store.dispatch(CareTeamStateSlice.actions.loadCareTeamSuccess([]));
  });

  test("renders the title and chat button", () => {
    render(<CareTeamSection />, { wrapper: TestWrapper });

    expect(screen.getByText("My Care Team")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Chat" })).toBeInTheDocument();
  });

  test("navigates to chat page when chat button is clicked", () => {
    render(<CareTeamSection />, { wrapper: TestWrapper });

    fireEvent.click(screen.getByRole("button", { name: "Chat" }));
    expect(mockedNextRouter.mockedRouterPush).toHaveBeenCalledWith(Routes.CHAT);
  });

  describe("DietitianChatOverview display", () => {
    test("renders DietitianChatOverview when a dietitian is present in careTeamUsers", () => {
      store.dispatch(CareTeamStateSlice.actions.loadCareTeamSuccess(mockCareTeamUsersWithDietitian));
      render(<CareTeamSection />, { wrapper: TestWrapper });

      const overview = screen.getByTestId("dietitian-chat-overview");

      expect(overview).toBeInTheDocument();
      expect(overview).toHaveAttribute("data-care-team-member-id", "dietitian1");
    });

    test("does not render DietitianChatOverview when no dietitian is present", () => {
      store.dispatch(CareTeamStateSlice.actions.loadCareTeamSuccess(mockCareTeamUsersWithoutDietitian));
      render(<CareTeamSection />, { wrapper: TestWrapper });

      expect(screen.queryByTestId("dietitian-chat-overview")).not.toBeInTheDocument();
    });

    test("does not render DietitianChatOverview when careTeamUsers is empty", () => {
      render(<CareTeamSection />, { wrapper: TestWrapper });
      expect(screen.queryByTestId("dietitian-chat-overview")).not.toBeInTheDocument();
    });
  });
});
