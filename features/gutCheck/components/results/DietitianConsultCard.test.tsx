import { CareTeamUserType, type CareTeamUser } from "@vivantehealth/vivante-core";
import { fireEvent, render, screen } from "@testing-library/react";
import { vi, describe, test, expect, beforeEach } from "vitest";

import { CareTeamStateSlice } from "@Features/careTeam/store/careTeamStateSlice";
import { store } from "@Store/store";
import { mockedNextRouter } from "@TestUtils/mockedNextRouter.util";
import { TestWrapper } from "@TestUtils/TestWrapper";
import { Routes } from "@Types";

import { DietitianConsultCard } from "./DietitianConsultCard";

vi.mock("@Components/CareTeamAvatar/CareTeamAvatar", () => ({
  CareTeamAvatar: vi.fn(({ avatarLink, altText }) => (
    <div data-testid="care-team-avatar" data-avatar-link={avatarLink} data-alt-text={altText} />
  )),
}));

const mockCareTeamUsersWithDietitian = [
  {
    id: "1",
    firstName: "Jane",
    lastName: "Doe",
    userType: CareTeamUserType.DIETITIAN,
    avatarLink: "https://example.com/valid-avatar.jpg",
  },
];

const mockCareTeamUsersWithInvalidAvatar = [
  {
    id: "1",
    firstName: "Jane",
    lastName: "Doe",
    userType: CareTeamUserType.DIETITIAN,
    avatarLink: "https://example.com/avatar1.png",
  },
];

const mockCareTeamUsersWithoutDietitian = [
  {
    id: "1",
    firstName: "John",
    lastName: "Smith",
    userType: CareTeamUserType.SHERPA,
    avatarLink: "https://example.com/valid-avatar.jpg",
  },
];

describe("DietitianConsultCard", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    store.dispatch(CareTeamStateSlice.actions.loadCareTeamSuccess(mockCareTeamUsersWithDietitian as CareTeamUser[]));
  });

  test("renders the title, description, and button", () => {
    render(<DietitianConsultCard />, { wrapper: TestWrapper });

    expect(screen.getByText("Your results tell a story")).toBeInTheDocument();
    expect(
      screen.getByText("Meet with a Dietitian to get all your questions answered and how to move forward"),
    ).toBeInTheDocument();

    expect(screen.getByRole("button", { name: "Schedule call" })).toBeInTheDocument();
  });

  test("renders the CareTeamAvatar when dietitian has a valid avatar link", () => {
    render(<DietitianConsultCard />, { wrapper: TestWrapper });

    expect(screen.getByTestId("care-team-avatar")).toBeInTheDocument();
  });

  test("does not render the CareTeamAvatar when dietitian has an invalid avatar link", () => {
    // Dispatch action to set state for this specific test case
    store.dispatch(
      CareTeamStateSlice.actions.loadCareTeamSuccess(mockCareTeamUsersWithInvalidAvatar as CareTeamUser[]),
    );

    render(<DietitianConsultCard />, { wrapper: TestWrapper });
    expect(screen.queryByTestId("care-team-avatar")).not.toBeInTheDocument();
  });

  test("does not render the CareTeamAvatar when there is no dietitian", () => {
    // Dispatch action to set state for this specific test case
    store.dispatch(CareTeamStateSlice.actions.loadCareTeamSuccess(mockCareTeamUsersWithoutDietitian as CareTeamUser[]));

    render(<DietitianConsultCard />, { wrapper: TestWrapper });

    expect(screen.queryByTestId("care-team-avatar")).not.toBeInTheDocument();
  });

  test("navigates to the scheduling route when the button is clicked", () => {
    render(<DietitianConsultCard />, { wrapper: TestWrapper });

    fireEvent.click(screen.getByRole("button", { name: "Schedule call" }));
    expect(mockedNextRouter.mockedRouterPush).toHaveBeenCalledWith(Routes.CARE_TEAM_SCHEDULING);
  });
});
