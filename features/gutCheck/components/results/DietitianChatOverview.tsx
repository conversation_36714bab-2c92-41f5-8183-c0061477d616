import { useDispatch, useSelector } from "react-redux";
import { CareTeamUser } from "@vivantehealth/vivante-core";
import { Paper } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { CareTeamAvatar } from "@Components/CareTeamAvatar/CareTeamAvatar";
import { CareTeamConversationCardText } from "@Features/careTeam/components/CareTeamConversationCardText";
import { getUserType } from "@Features/careTeam/utils/careteam.utils";
import { setCurrentRoomType } from "@Features/chat/store/chatStateSlice";
import { chatStateSelector } from "@Features/chat/store/chatStateSlice";
import { getLatestConversation } from "@Features/chat/utils/chat.utils";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";

const A11Y_STRINGS = appStrings.a11y;
const DATE_FORMAT = "MMM DD";

type DietitianChatOverviewProps = Readonly<{
  careTeamMember: CareTeamUser;
}>;

export const DietitianChatOverview = ({ careTeamMember }: DietitianChatOverviewProps) => {
  const dispatch = useDispatch();
  const { firstName, avatarLink, userType } = careTeamMember;
  const dietitianChat = useSelector(chatStateSelector("dietitian"));
  const latestConversation = getLatestConversation(dietitianChat);

  const dateOfLastMessage = latestConversation ? dayjs(latestConversation.createdAt).format(DATE_FORMAT) : "";
  const careTeamMemberType = getUserType(careTeamMember);

  const latestConversationText = latestConversation?.textHTML?.replace(/(<([^>]+)>)/gi, "") || careTeamMemberType;

  const handleNavigateToConversation = () => {
    dispatch(setCurrentRoomType(userType));
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: "/chat",
        screenName: "Chat",
      }),
    );
  };

  return (
    <Paper
      sx={{
        display: "flex",
        padding: 4,
        justifyContent: "center",
        alignItems: "center",
        marginBottom: 4,
        width: "100%",
        cursor: "pointer",
        ":hover": {
          borderColor: color.border.action.hover,
        },
      }}
      component="button"
      onClick={handleNavigateToConversation}
      aria-label={A11Y_STRINGS.conversationCard(firstName, latestConversationText, dateOfLastMessage)}
    >
      <CareTeamAvatar
        avatarLink={avatarLink}
        altText={A11Y_STRINGS.careTeamMemberAvatarText(getUserType(careTeamMember).toLowerCase())}
        hasUnreadMessages={!latestConversation?.memberRead}
      />
      <CareTeamConversationCardText
        careTeamMember={firstName}
        lastConversationMessage={latestConversationText}
        dateOfLastMessage={dateOfLastMessage}
      />
    </Paper>
  );
};
