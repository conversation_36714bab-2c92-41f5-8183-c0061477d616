import { Box, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

type TextDividerProps = Readonly<{
  text?: string;
  sx?: object;
}>;

export const TextDivider = ({ text, sx = {} }: TextDividerProps) => {
  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        width: "100%",
        ...sx,
      }}
    >
      <Box
        sx={{
          position: "relative",
          flexGrow: 1,
          maxWidth: "72px",
          height: "1px",
        }}
      >
        <Box
          sx={{
            position: "absolute",
            inset: 0,
            height: "1px",
            background: `repeating-linear-gradient(
              to right,
              transparent 0px,
              transparent 2px,
              ${color.icon.subtle} 2px,
              ${color.icon.subtle} 4px
            )`,
            maskImage: `linear-gradient(to left, ${color.icon.strong} 0%, transparent 100%)`,
            WebkitMaskImage: `linear-gradient(to left, ${color.icon.strong} 0%, transparent 100%)`,
          }}
        />
      </Box>

      <Typography
        variant="actionDense"
        sx={{
          textAlign: "center",
          px: 2,
        }}
      >
        {text}
      </Typography>

      <Box
        sx={{
          position: "relative",
          flexGrow: 1,
          maxWidth: "72px",
          height: "1px",
        }}
      >
        <Box
          sx={{
            position: "absolute",
            inset: 0,
            height: "1px",
            background: `repeating-linear-gradient(
              to right,
              transparent 0px,
              transparent 2px,
              ${color.icon.subtle} 2px,
              ${color.icon.subtle} 4px
            )`,
            maskImage: `linear-gradient(to right, ${color.icon.strong} 0%, transparent 100%)`,
            WebkitMaskImage: `linear-gradient(to right, ${color.icon.strong} 0%, transparent 100%)`,
          }}
        />
      </Box>
    </Box>
  );
};
