import { <PERSON>, <PERSON><PERSON>, Card, Divider, Grid, Paper, Typography } from "@mui/material";
import { color, radius } from "@vivantehealth/design-tokens";
import Link from "next/link";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { Routes } from "@Types";

import { CareTeamSection } from "./CareTeamSection";
import { DietitianConsultCard } from "./DietitianConsultCard";
import { TextDivider } from "./TextDivider";
import { WebinarCard } from "./WebinarCard";
import { useGetGutcheckStatusQuery } from "../../api/gutCheckApi";
import { GutcheckStatus } from "../../types/gutCheck.types";
import { StyledResultsPaper } from "../shared/StyledResultsPaper";
import { GUT_CHECK_STRINGS } from "../tabs/MyOrderTab";

const RESULTS_READY_STRINGS = appStrings.features.gutCheck.resultsReadyPrompt;
const ICON_SIZE = "48px";

type ResultsReadySectionWrapperProps = Readonly<{
  orderId: string;
}>;

export const ResultsReadySectionWrapper = ({ orderId }: ResultsReadySectionWrapperProps) => {
  const { data: gutCheckStatus } = useGetGutcheckStatusQuery();

  return <ResultsReadySection gutCheckStatus={gutCheckStatus} orderId={orderId} />;
};

type ResultsReadySectionProps = Readonly<{
  gutCheckStatus: GutcheckStatus | undefined;
  orderId: string;
}>;

export const ResultsReadySection = ({ gutCheckStatus, orderId }: ResultsReadySectionProps) => {
  const router = useRouter();

  const handleOrderNextTest = () => {
    router.push(Routes.GUT_CHECK_ORDER);
  };

  return (
    <>
      {gutCheckStatus?.isEligibleToOrder && (
        <StyledResultsPaper>
          <Box display="flex" flexDirection="column" gap={1} flex="1 0 0">
            <Typography variant="h4">{RESULTS_READY_STRINGS.trackProgressTitle}</Typography>
            <Typography variant="bodyDense">{RESULTS_READY_STRINGS.trackProgressSubtitle}</Typography>
          </Box>
          <Button size="small" variant="cardDark" onClick={handleOrderNextTest}>
            {RESULTS_READY_STRINGS.orderNextTestButton}
          </Button>
        </StyledResultsPaper>
      )}

      <Paper sx={{ display: "flex", flexDirection: "column", px: 4, py: 5, justifyContent: "center" }}>
        <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center">
          <AppIcon
            name="CarePlan"
            size="md"
            containerStyles={{
              padding: 3,
              backgroundColor: color.background.tracking.stool,
              borderRadius: radius.radiusFull,
              width: ICON_SIZE,
              height: ICON_SIZE,
              color: color.background.brand.fill1,
            }}
          />
          <Typography variant="h3" mt={4}>
            {GUT_CHECK_STRINGS.myOrderTab.resultsReady.title}
          </Typography>
          <Typography variant="bodyDense" mt={1}>
            {GUT_CHECK_STRINGS.myOrderTab.resultsReady.subtitle}
          </Typography>
        </Box>

        <Divider sx={{ my: 4, backgroundColor: color.background.surface.secondary }} />

        <Link href={`${Routes.GUT_CHECK_RESULTS}/${orderId}`} passHref legacyBehavior>
          <Card
            component="a"
            sx={{
              px: 4,
              py: 3,
              borderRadius: `${radius.radius4} !important`, // !important because it wants to be overridden by 64px
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              textDecoration: "none",
              cursor: "pointer",
            }}
          >
            <Typography variant="actionDense">{GUT_CHECK_STRINGS.myOrderTab.resultsReady.resultsCardTitle}</Typography>
            <AppIcon size="sm" name="RightChevron" />
          </Card>
        </Link>
      </Paper>

      <TextDivider text={GUT_CHECK_STRINGS.myOrderTab.resultsReady.whatToDoTitle} sx={{ mb: 4, mt: 5 }} />

      <Grid container spacing={4}>
        <Grid item xs={12} md={6}>
          <DietitianConsultCard />
        </Grid>

        <Grid item xs={12} md={6}>
          <WebinarCard />
        </Grid>

        <Grid item xs={12}>
          <CareTeamSection />
        </Grid>
      </Grid>
    </>
  );
};
