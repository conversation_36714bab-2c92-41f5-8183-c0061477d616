import { fireEvent, render, screen } from "@testing-library/react";
import { vi, describe, test, expect, beforeEach } from "vitest";

import { mockedNextRouter } from "@TestUtils/mockedNextRouter.util";
import { TestWrapper } from "@TestUtils/TestWrapper";
import { Routes } from "@Types";

import { ResultsReadySection } from "./ResultsReadySection";

vi.mock("./DietitianConsultCard", () => ({
  DietitianConsultCard: vi.fn(() => <div data-testid="dietitian-consult-card" />),
}));

vi.mock("./WebinarCard", () => ({
  WebinarCard: vi.fn(() => <div data-testid="webinar-card" />),
}));

vi.mock("./CareTeamSection", () => ({
  CareTeamSection: vi.fn(() => <div data-testid="care-team-section" />),
}));

describe("ResultsReadySection", () => {
  const mockOrderId = "test-order-123";

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("Core Rendering and Conditional Visibility", () => {
    test("renders correctly when user is NOT eligible to order next test", () => {
      render(
        <ResultsReadySection
          orderId={mockOrderId}
          gutCheckStatus={{ isEligibleToOrder: false, hasGutCheckHistory: true }}
        />,
        { wrapper: TestWrapper },
      );

      // Check for main title and subtitle
      expect(screen.getByText("Your results are in!")).toBeInTheDocument();
      expect(screen.getByText("Your gut data is now available to view and download")).toBeInTheDocument();

      // Check for 'View My Results' link/card
      const viewResultsLink = screen.getByRole("link", { name: "GutCheck results" });

      expect(viewResultsLink).toBeInTheDocument();
      expect(viewResultsLink).toHaveAttribute("href", `${Routes.GUT_CHECK_RESULTS}/${mockOrderId}`);

      // Check for mocked child components
      expect(screen.getByTestId("dietitian-consult-card")).toBeInTheDocument();
      expect(screen.getByTestId("webinar-card")).toBeInTheDocument();
      expect(screen.getByTestId("care-team-section")).toBeInTheDocument();

      // Check that 'Order Next Test' section is NOT visible
      expect(screen.queryByText("Track your progress with another test")).not.toBeInTheDocument();
      expect(screen.queryByText("Order next test")).not.toBeInTheDocument();
    });

    test("does NOT render 'Order Next Test' section when gutCheckStatus data is undefined", () => {
      render(<ResultsReadySection orderId={mockOrderId} gutCheckStatus={undefined} />, {
        wrapper: TestWrapper,
      });

      // Check that 'Order Next Test' section is NOT visible
      expect(screen.queryByText("Track your progress with another test")).not.toBeInTheDocument();
      expect(screen.queryByText("Order next test")).not.toBeInTheDocument();

      // Verify other elements are still present as a sanity check
      expect(screen.getByText("Your results are in!")).toBeInTheDocument();
      const viewResultsLink = screen.getByRole("link", { name: "GutCheck results" });

      expect(viewResultsLink).toBeInTheDocument();
    });

    test("renders 'Order Next Test' section and handles button click when user IS eligible", () => {
      render(
        <ResultsReadySection
          orderId={mockOrderId}
          gutCheckStatus={{ isEligibleToOrder: true, hasGutCheckHistory: true }}
        />,
        { wrapper: TestWrapper },
      );

      // Check that 'Order Next Test' section IS visible
      expect(screen.getByText("Track your progress with another test")).toBeInTheDocument();
      const orderNextTestButton = screen.getByRole("button", {
        name: "Order next test",
      });

      expect(orderNextTestButton).toBeInTheDocument();

      // Click the button
      fireEvent.click(orderNextTestButton);
      expect(mockedNextRouter.mockedRouterPush).toHaveBeenCalledWith(Routes.GUT_CHECK_ORDER);
    });
  });
});
