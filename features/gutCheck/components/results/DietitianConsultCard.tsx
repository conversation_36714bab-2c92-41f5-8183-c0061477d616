import { useSelector } from "react-redux";
import { CareTeamUserType } from "@vivantehealth/vivante-core";
import { Avatar, Box, Button, Card, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { CareTeamAvatar } from "@Components/CareTeamAvatar/CareTeamAvatar";
import { careTeamStateSelector } from "@Features/careTeam/store/careTeamStateSlice";
import { Routes } from "@Types";

const GUT_CHECK_STRINGS = appStrings.features.gutCheck;

const AVATAR_SIZE = "32px";
const AVATAR_OVERLAP = "-6px";
const OUTLINE_WIDTH = "2px";
const ICON_SIZE = "20px";

export const DietitianConsultCard = () => {
  const router = useRouter();
  const careTeamUsers = useSelector(careTeamStateSelector("careTeamUsers"));

  const dietitian = careTeamUsers?.find((user) => user.userType === CareTeamUserType.DIETITIAN);
  const isValidAvatarImage = dietitian?.avatarLink && !dietitian.avatarLink.includes("avatar1.png");

  const handleScheduleClick = () => {
    router.push(Routes.CARE_TEAM_SCHEDULING);
  };

  return (
    <Card
      sx={{
        p: 4,
        borderRadius: 4,
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box display="flex" alignItems="center" mb={2}>
        {isValidAvatarImage && (
          <CareTeamAvatar
            avatarLink={dietitian?.avatarLink}
            altText={dietitian ? `${dietitian.firstName} avatar` : "Dietitian avatar"}
            hasUnreadMessages={false}
            width={AVATAR_SIZE}
          />
        )}
        <Avatar
          sx={{
            bgcolor: color.background.brand.fill5Light,
            width: AVATAR_SIZE,
            height: AVATAR_SIZE,
            outline: `${OUTLINE_WIDTH} solid ${color.palette.neutral[0]}`, // not border to respect width and height
            marginLeft: AVATAR_OVERLAP,
          }}
        >
          <AppIcon
            name="VideoCall"
            size="smd"
            svgStyles={{
              width: ICON_SIZE,
              height: ICON_SIZE,
            }}
            containerStyles={{
              color: color.background.brand.fill5,
            }}
          />
        </Avatar>
      </Box>
      <Typography variant="h4" mb={2}>
        {GUT_CHECK_STRINGS.myOrderTab.careOptions.dietitianConsult.title}
      </Typography>
      <Typography variant="bodyDense" mb={4}>
        {GUT_CHECK_STRINGS.myOrderTab.careOptions.dietitianConsult.description}
      </Typography>
      <Button fullWidth variant="secondary" onClick={handleScheduleClick}>
        {GUT_CHECK_STRINGS.myOrderTab.careOptions.dietitianConsult.buttonText}
      </Button>
    </Card>
  );
};
