import { useMemo } from "react";
import { Box, Typography, Paper, Button, CircularProgress } from "@mui/material";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { Routes } from "@Types";

import { useGutCheckHistory } from "../../hooks/useGutCheckHistory";
import { formatGutCheckDate } from "../../utils/GutCheck.utils";

const GUT_CHECK_STRINGS = appStrings.features.gutCheck;

export const PastResultsTab = () => {
  const router = useRouter();
  const { gutCheckHistory: ordersFromHook, isLoading, isError } = useGutCheckHistory();

  const displayableOrders = useMemo(
    () =>
      ordersFromHook.filter((order) =>
        Boolean(order && order.id && order.orderedAt && order.status === "results_available"),
      ),
    [ordersFromHook],
  );

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" mt={8} data-testid="loading-indicator">
        <CircularProgress />
      </Box>
    );
  }

  if (isError) {
    return (
      <Box display="flex" flexDirection="column" textAlign="center" alignItems="center" gap={2} mt={8}>
        <Typography variant="h6" color="error">
          {GUT_CHECK_STRINGS.pastResults.errorLoadingResults}
        </Typography>
      </Box>
    );
  }

  const handleViewResultsClick = (orderId: string) => {
    router.push(`${Routes.GUT_CHECK_RESULTS}/${orderId}`);
  };

  if (displayableOrders.length === 0) {
    return (
      <Box display="flex" justifyContent="center">
        <Typography variant="h2Serif">{GUT_CHECK_STRINGS.noPastResultsMessage}</Typography>
      </Box>
    );
  }

  return (
    <Box display="flex" flexDirection="column" gap={3} mt={4} data-testid="past-results-list">
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
        <Typography variant="h4">{GUT_CHECK_STRINGS.pastResults.testHistoryTitle}</Typography>
      </Box>

      {displayableOrders.map((order) => (
        <Paper key={order.id} sx={{ display: "flex", p: 4, gap: 1, alignItems: "center", justifyContent: "center" }}>
          <Box display="flex" flexDirection="column" gap={1} flex="1 0 0">
            <Typography variant="h4" fontWeight="medium">
              {GUT_CHECK_STRINGS.pastResults.cardTitle}
            </Typography>
            <Typography variant="bodyDense">
              {formatGutCheckDate(order.orderedAt, GUT_CHECK_STRINGS.pastResults.dateNotAvailable)}
            </Typography>
          </Box>
          <Button size="small" variant="secondary" onClick={() => handleViewResultsClick(order.id)}>
            {GUT_CHECK_STRINGS.pastResults.viewButton}
          </Button>
        </Paper>
      ))}
    </Box>
  );
};
