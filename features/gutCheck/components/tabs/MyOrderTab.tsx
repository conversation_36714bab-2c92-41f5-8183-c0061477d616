import { appStrings } from "@Assets/app_strings";
import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";

import { useGutCheckHistory } from "../../hooks/useGutCheckHistory";
import { KitOrderedSection } from "../order/KitOrderedSection";
import { ResultsReadySectionWrapper } from "../results/ResultsReadySection";
import { GutCheckError } from "../shared/GutCheckError";

export const GUT_CHECK_STRINGS = appStrings.features.gutCheck;

export const MyOrderTab = () => {
  const { gutCheckHistory, isLoading, isError } = useGutCheckHistory();

  const latestOrder = gutCheckHistory && gutCheckHistory.length > 0 ? gutCheckHistory[0] : null;
  const hasOrderedStatus = latestOrder?.status === "ordered";
  const hasResultsAvailable = latestOrder?.status === "results_available";

  if (isLoading) {
    return <LoadingSpinner open overlay={false} />;
  }

  if (hasResultsAvailable && latestOrder) {
    return <ResultsReadySectionWrapper orderId={latestOrder.id} />;
  }

  if (hasOrderedStatus) {
    return <KitOrderedSection />;
  }

  if (isError || !gutCheckHistory || gutCheckHistory.length === 0) {
    return (
      <GutCheckError
        title={GUT_CHECK_STRINGS.errors.generalLoadTitle}
        subtitle={GUT_CHECK_STRINGS.errors.generalLoadSubtitle}
      />
    );
  }

  return null;
};
