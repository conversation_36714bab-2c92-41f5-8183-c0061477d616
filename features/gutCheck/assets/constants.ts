import { IconVariant } from "@Components/AppIcon/AppIcon";

export const NEW_GUTCHECK_EXPERIENCE_FRONTEND_FF = "new_gutcheck_experience_frontend";

export const listToIconMap: IconVariant[] = ["Home", "Stool", "SendMsg", "Document"];

export const TabKey = {
  MY_ORDER: "my-order",
  PAST_RESULTS: "past-results",
} as const;

export type TabValues = (typeof TabKey)[keyof typeof TabKey];

export const WEBINAR_URL_DEV = "https://app.cylinderhealth.com/learn/articles/acd7b0bc-f974-4fcb-b832-89c8f8417a4f";
export const WEBINAR_URL_PROD_LIKE =
  "https://app.cylinderhealth.com/learn/articles/b1d72300-d8d1-48e9-ac42-72d13e718fe7?categoryId=2f9d169a-8e72-4470-9b9a-e241584c1be2&mediaType=image&originatingScreen=/learn/articles";
