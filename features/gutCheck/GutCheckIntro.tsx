import { useState } from "react";
import { Box } from "@mui/material";
import { useRouter } from "next/router";

import { Routes } from "@Types";

import { MicrobiomeRole } from "./components/intro/MicrobiomeRole";
import { UnlockBetterHealth } from "./components/intro/UnlockBetterHealth";
import { WhatAndWhy } from "./components/intro/WhatAndWhy";
import { GUT_CHECK_INTRO_SCREENS, GutCheckIntroScreens, IntroScreenProps } from "./types/gutCheck.types";

const SCREEN_COMPONENTS: Record<GutCheckIntroScreens, React.ComponentType<IntroScreenProps>> = {
  [GUT_CHECK_INTRO_SCREENS.MICROBIOME_ROLE]: MicrobiomeRole,
  [GUT_CHECK_INTRO_SCREENS.WHAT_AND_WHY]: WhatAndWhy,
  [GUT_CHECK_INTRO_SCREENS.UNLOCK_BETTER_HEALTH]: UnlockBetterHealth,
};

export const GutCheckIntro = ({ screen }: { screen: GutCheckIntroScreens }) => {
  const router = useRouter();

  const [currentScreen, setCurrentScreen] = useState<GutCheckIntroScreens>(screen);

  const handleGoToScreen = (screenName: GutCheckIntroScreens) => {
    setCurrentScreen(screenName);

    router.replace(`${Routes.GUT_CHECK_INTRO}/${screenName}`);
  };

  const CurrentScreenComponent = SCREEN_COMPONENTS[currentScreen];

  return (
    <Box>
      <Box display="flex" justifyContent="center" pt={0} position={"relative"} zIndex={1}>
        <CurrentScreenComponent handleGoToScreen={handleGoToScreen} />
      </Box>

      <img
        src="/images/white_dot_motif_tilted.webp"
        style={{ position: "fixed", right: 0, bottom: 0, zIndex: 0 }}
        alt=""
        aria-hidden="true"
      />
    </Box>
  );
};
