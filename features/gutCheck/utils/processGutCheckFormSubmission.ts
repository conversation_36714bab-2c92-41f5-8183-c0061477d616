import * as Sentry from "@sentry/nextjs";

import { appStrings } from "@Assets/app_strings";

import { GutcheckError } from "../types/gutCheck.types";

export const processGutCheckFormSubmission = (error: GutcheckError) => {
  Sentry.captureException(error);

  const status = error?.status;

  switch (status) {
    case "400":
      return {
        success: false,
        message: appStrings.features.gutCheck.formSubmissionErrors.badRequest,
      };
    case "401":
      return {
        success: false,
        message: appStrings.features.gutCheck.formSubmissionErrors.unauthorized,
      };
    case "409":
      return {
        success: false,
        message: appStrings.features.gutCheck.formSubmissionErrors.conflict,
      };
    case "500":
      return {
        success: false,
        message: appStrings.features.gutCheck.formSubmissionErrors.serverError,
      };
    default:
      return {
        success: false,
        message: appStrings.features.gutCheck.formSubmissionErrors.unexpectedError,
      };
  }
};
