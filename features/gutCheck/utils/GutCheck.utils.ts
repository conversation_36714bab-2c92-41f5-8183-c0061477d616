import {
  BacteriumLevelMap,
  CareTeamUserType,
  MicrobiomeScoreDefinition,
  Sample,
  SymptomAndBacteria,
} from "@vivantehealth/vivante-core";
import { format, isValid, parseISO } from "date-fns";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";

import { sentenceCaseLabel } from "@Utils/sentenceCaseLabel";

import {
  Bacteria,
  BacteriaDefinition,
  GutCheckHistoryItem,
  GutcheckError,
  MicrobiomeKey,
  MicrobiomeScore,
  MicrobiomeScoreDefinition as GutCheckMicrobiomeScoreDefinition,
  SymptomAndBacteria as GutCheckSymptomAndBacteria,
} from "../types/gutCheck.types";

dayjs.extend(customParseFormat);

/**
 * @description
 * This function takes a date string in the format YYYY-MM-DD and returns the day part of the date.
 *
 * @param date - date in YYYY-MM-DD format
 * @returns the day part of the date as a string without leading zero
 * @example
 * getDateDay("2023-10-05") // returns "5"
 * getDateDay("2023-10-15") // returns "15"
 */
export const getDateDay = (date: string): string => {
  const parsed = dayjs(date, "YYYY-MM-DD", true);

  if (!parsed.isValid()) return "";

  return String(parsed.date());
};

/**
 * Determines the dynamic message to display for the care team section based on user type and latest messages.
 *
 * @param userType - The type of the care team user (e.g., "SHERPA", "DIETITIAN").
 * @param latestCoachMsgText - The latest message text from the health coach.
 * @param latestDietitianMsgText - The latest message text from the dietitian.
 * @param defaultMessage - The default message to use if no specific conditions are met.
 * @returns The message string to display.
 */
export const getLastCareTeamMessage = (
  userType: CareTeamUserType | undefined,
  latestCoachMsgText: string | undefined,
  latestDietitianMsgText: string | undefined,
): string => {
  if (userType === CareTeamUserType.SHERPA && latestCoachMsgText) {
    return latestCoachMsgText;
  }
  if (userType === CareTeamUserType.DIETITIAN && latestDietitianMsgText) {
    return latestDietitianMsgText;
  }

  return "";
};

/**
 * Formats a date string for display in GutCheck features.
 * If the date is invalid or not provided, returns the fallback string.
 * @param dateString The date string to format (expected in ISO format or parsable by Date constructor).
 * @param fallbackString The string to return if the date is not available or invalid.
 * @returns The formatted date string or the fallback string.
 */
export const formatGutCheckDate = (dateString: string | null | undefined, fallbackString: string): string => {
  if (!dateString) {
    return fallbackString;
  }

  // Try parsing as ISO first, then fall back to direct Date constructor for wider compatibility
  let date = parseISO(dateString);

  if (!isValid(date)) {
    date = new Date(dateString);
  }

  if (isValid(date)) {
    return format(date, "MMMM d, yyyy");
  }

  return fallbackString;
};

/**
 * @description
 * This function takes a level map string and returns the normalized level map.
 *
 * @param levelMap - The level map string to normalize.
 * @returns The normalized level map.
 */
export const normalizeLevelMap = (levelMap?: Bacteria["levelMap"]): BacteriumLevelMap => {
  if (!levelMap) return "Normal";

  switch (levelMap.toLowerCase()) {
    case "low":
      return "Low";
    case "high":
      return "High";
    case "normal":
      return "Normal";
    case "not_in_sample":
    case "not in sample":
      return "Not in sample";
    default:
      return "Normal";
  }
};

export const transformBacteria = (bacterium: Bacteria) => ({
  title: bacterium.title,
  abundance: bacterium.abundance ?? 0,
  effect: bacterium.effect,
  key: bacterium.key,
  levelEffect: bacterium.levelEffect ?? "good",
  levelMap: normalizeLevelMap(bacterium.levelMap),
  cdf: bacterium.cdf ?? undefined,
  definition: bacterium.definition?.map(
    (def) =>
      ({
        key: def.key,
        min: def.min ?? 0,
        max: def.max ?? 1,
      }) satisfies BacteriaDefinition,
  ),
  description: bacterium.description ?? "",
  details: bacterium.details ?? "",
  presentHealthyPercentage: bacterium.presentHealthPercentage ?? undefined,
  taxLevel: bacterium.taxLevel ?? undefined,
  logNormalizedAbundance: bacterium.logNormalizedAbundance ?? undefined,
  normalizedHealthyAverageLevels: bacterium.normalizedHealthyAverageLevels ?? undefined,
});

export const transformSymptomsAndBacteria = (
  symptoms: GutCheckSymptomAndBacteria[] | undefined,
): SymptomAndBacteria[] => {
  if (!symptoms) return [];

  return symptoms.map((symptom): SymptomAndBacteria => {
    if (symptom.type === "bacteria") {
      const item: SymptomAndBacteria = {
        key: symptom.key,
        title: symptom.title,
        description: symptom.description ?? "",
        bacteria: symptom.bacteria?.map(transformBacteria) ?? [],
        logStatus: false,
        logged: null,
        symptomType: "gut_health",
      };

      return item;
    } else {
      // symptom.type === "symptom"
      const item: SymptomAndBacteria = {
        key: symptom.key,
        title: symptom.title,
        description: symptom.description ?? "",
        bacteria: [],
        logStatus: symptom.logStatus,
        logged: {
          mild: symptom.logged.mild,
          moderate: symptom.logged.moderate,
          severe: symptom.logged.severe,
          totalCount: symptom.logged.count,
        },
        symptomType: symptom.symptom_type ?? "gut_health",
      };

      return item;
    }
  });
};

type CoreSymptomAndBacteriaItem = Sample["symptomAndBacteria"][number];

const transformMicrobiomeScoreDefinition = (def: GutCheckMicrobiomeScoreDefinition): MicrobiomeScoreDefinition => ({
  map: def.map ?? "normal",
  min: def.min ?? 0,
  max: def.max ?? 1,
  type: def.type ?? "range",
  label: getLabelFromMap(def.map),
});

const getLabelFromMap = (map?: string): string => {
  if (map === "low") return "Low";
  if (map === "high") return "High";

  return "Reference Range";
};

const mapToSampleSymptomFormat = (symptoms: GutCheckSymptomAndBacteria[] | undefined): Sample["symptomAndBacteria"] => {
  if (!symptoms || symptoms.length === 0) return [];

  return symptoms.map((symptom): CoreSymptomAndBacteriaItem => {
    if (symptom.type === "bacteria") {
      const processedBacteria = (symptom.bacteria || []).map(transformBacteria);

      return {
        key: symptom.key,
        title: symptom.title,
        description: symptom.description ?? "",
        bacteria: processedBacteria,
        logStatus: false,
        logged: null,
        symptomType: "logged",
      };
    } else {
      return {
        key: symptom.key,
        title: symptom.title,
        description: symptom.description ?? "",
        symptomType: symptom.symptom_type === "gut_health" ? "gut_health" : "logged",
        logStatus: symptom.logStatus ?? false,
        logged: symptom.logged
          ? {
              mild: symptom.logged.mild ?? 0,
              moderate: symptom.logged.moderate ?? 0,
              severe: symptom.logged.severe ?? 0,
              totalCount: symptom.logged.count ?? 0,
            }
          : null,
        bacteria: [],
      };
    }
  });
};

/**
 * Transforms microbiome data from a GutCheck item into the format expected by Sample
 *
 * @param microbiome - The microbiome data from GutCheck results
 * @returns Transformed microbiome scores for a Sample
 */
const transformMicrobiomeData = (microbiome: Record<string, MicrobiomeScore> = {}): Sample["microbiomeScores"] => {
  return Object.entries(microbiome).map(([key, microbiomeData]) => ({
    key: key as MicrobiomeKey,
    title: sentenceCaseLabel(microbiomeData.title),
    value: microbiomeData.value,
    definition: microbiomeData.definition.map(transformMicrobiomeScoreDefinition),
  }));
};

/**
 * Creates a default empty Sample object with the given ID
 *
 * @param id - The ID to use for the empty Sample
 * @returns An empty Sample object with the given ID
 */
const createEmptySample = (id: string): Sample => ({
  id,
  microbiomeScores: [],
  symptomAndBacteria: [],
});

/**
 * Transforms a GutCheck history item into the Sample format used by the application
 *
 * @param item - The GutCheck history item to transform
 * @returns A Sample object with data extracted from the GutCheck item
 */
export const transformGutCheckItemToSample = (item: GutCheckHistoryItem): Sample => {
  if (!item.results) {
    return createEmptySample(item.id);
  }

  const microbiomeScores = transformMicrobiomeData(item.results.microbiome);
  const symptomAndBacteria = mapToSampleSymptomFormat(item.results.symptoms);

  return {
    id: item.id,
    microbiomeScores,
    symptomAndBacteria,
  };
};

export const isPostGutCheckOrderError = <T extends GutcheckError>(error: unknown): error is T => {
  return (
    error != null &&
    typeof error === "object" &&
    "data" in error &&
    error.data != null &&
    typeof error.data === "object"
  );
};
