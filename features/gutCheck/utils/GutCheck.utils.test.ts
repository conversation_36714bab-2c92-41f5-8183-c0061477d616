import { describe, expect, it } from "vitest"; // Updated to use 'it' from vitest

import { getDateDay } from "./GutCheck.utils";

describe("getDateDay", () => {
  it("should return the day part of a valid date string", () => {
    expect(getDateDay("2023-10-05")).toBe("5");
    expect(getDateDay("2023-10-15")).toBe("15");
  });

  it("should return an empty string for an invalid date string", () => {
    expect(getDateDay("2023-10")).toBe("");
    expect(getDateDay("invalid-date")).toBe("");
    expect(getDateDay("2023")).toBe("");
  });

  it("should return an empty string for a date string with incorrect length", () => {
    expect(getDateDay("20231005")).toBe("");
    expect(getDateDay("23-10-05")).toBe("");
    expect(getDateDay("")).toBe("");
  });
});
