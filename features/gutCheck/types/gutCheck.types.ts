export const GUT_CHECK_INTRO_SCREENS = {
  MICROBIOME_ROLE: "MicrobiomeRole",
  WHAT_AND_WHY: "WhatAndWhy",
  UNLOCK_BETTER_HEALTH: "UnlockBetterHealth",
} as const;

export type GutCheckIntroScreens = (typeof GUT_CHECK_INTRO_SCREENS)[keyof typeof GUT_CHECK_INTRO_SCREENS];

export type IntroScreenProps = Readonly<{
  handleGoToScreen: (screenName: GutCheckIntroScreens) => void;
}>;

export type GutcheckStatus = {
  hasGutCheckHistory: boolean;
  isEligibleToOrder: boolean;
};

export type GutcheckOrderRequest = {
  firstName: string;
  lastName: string;
  shippingAddress: {
    street1: string;
    street2?: string;
    city: string;
    state: string;
    zipCode: string;
  };
};

export type GutCheckHistoryItem = {
  id: string;
  orderedAt: string;
  status: GutCheckHistoryItemStatus;
  results?: GutCheckResultsData;
};

export type GutCheckHistoryItemStatus =
  | "ordered"
  | "order_failed"
  | "in_transit_to_member"
  | "delivered_to_member"
  | "in_transit_to_lab"
  | "delivered_to_lab"
  | "processing_in_lab"
  | "results_available";

export type GutCheckHistoryResponse = GutCheckHistoryItem[];

export type GutCheckPostApiErrorCode =
  | "MALFORMED_REQUEST" // 400
  | "UNAUTHORIZED" // 401
  | "ELIGIBILITY_FAILURE" // 409
  | "INTERNAL_SERVER_ERROR"; // 500

export type GutCheckApiError<T_ErrorCode = GutCheckPostApiErrorCode> = {
  code: T_ErrorCode;
  detail: string;
  status: string;
  title: string;
  id?: string;
  links?: { [key: string]: string };
  source?: {
    pointer?: string;
    parameter?: string;
    header?: string;
  };
};

export type GutCheckResultsData = Readonly<{
  conditions: ConditionMicrobiomeOutcome[];
  microbiome: Microbiome;
  symptoms: SymptomAndBacteria[];
}>;

export type MicrobiomeKey = "inflammatory" | "protective" | "unique" | "diversity";

export type Microbiome = Readonly<Partial<Record<MicrobiomeKey, MicrobiomeScore>>>;

export type MicrobiomeScore = Readonly<{
  value: number;
  title: string;
  definition: MicrobiomeScoreDefinition[];
}>;

export type MicrobiomeScoreDefinition = Readonly<{
  map: "low" | "normal" | "high";
  max: number;
  min: number;
  type: "range";
}>;

export type ConditionMicrobiomeOutcome = Readonly<{
  comment: string;
  isExpected: boolean;
  bacteria: Bacteria[];
}>;

export type SymptomAndBacteria = Readonly<SymptomData | BacteriaData>;

export type SymptomData = Readonly<{
  key: string;
  type: "symptom";
  title: string;
  logged: SymptomLogged;
  logStatus: boolean;
  description?: string;
  symptom_type?: "gut_health" | "logged";
}>;

export type BacteriaData = Readonly<{
  key: string;
  type: "bacteria";
  title: string;
  description?: string;
  bacteria?: Bacteria[];
}>;

export type SymptomLogged = Readonly<{
  mild: number;
  moderate: number;
  severe: number;
  count: number;
}>;

export type Bacteria = Readonly<{
  cdf?: number | null;
  key: string;
  type?: string;
  title: string;
  effect: "Inflammatory" | "Protective";
  details?: string | null;
  abundance?: number;
  levelMap?: "Not in sample" | "not_in_sample" | "low" | "Low" | "normal" | "Normal" | "high" | "High";
  taxLevel?: string | null;
  levelEffect?: "bad" | "good";
  compareToHealthPercentage?: number;
  presentHealthPercentage?: number;
  definition?: BacteriaDefinition[] | null;
  description?: string | null;
  logNormalizedAbundance?: number | null;
  normalizedHealthyAverageLevels?: NormalizedHealthyAverageLevels;
}>;

export type BacteriaDefinition = Readonly<{
  key: string;
  min?: number;
  max?: number;
}>;

export type NormalizedHealthyAverageLevels = Readonly<{
  min: number;
  max: number;
}>;

export type GutcheckError = {
  id: string;
  status: string;
  code: string;
  title: string;
  detail: string;
};
