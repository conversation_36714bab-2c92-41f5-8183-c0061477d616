import { bffApi } from "@Api/bffApi";

import type { GutcheckStatus, GutcheckOrderRequest, GutCheckHistoryResponse } from "../types/gutCheck.types";

export const gutCheckApi = bffApi
  .enhanceEndpoints({ addTagTypes: ["GutCheckEligibility", "GutCheckOrder", "GutCheckHistory"] })
  .injectEndpoints({
    endpoints: (builder) => ({
      getGutcheckStatus: builder.query<GutcheckStatus, void>({
        query: () => ({
          url: "/v1/gutcheck/eligibility",
          method: "GET",
        }),
        providesTags: ["GutCheckEligibility"],
      }),
      createGutcheckOrder: builder.mutation<void, GutcheckOrderRequest>({
        query: (body) => ({
          url: "/v1/gutcheck",
          method: "POST",
          body,
        }),
        invalidatesTags: ["GutCheckOrder", "GutCheckHistory"],
      }),
      getGutcheckHistory: builder.query<GutCheckHistoryResponse, void>({
        query: () => ({
          url: "/v1/gutcheck",
          method: "GET",
        }),
        providesTags: (result) =>
          result
            ? [
                ...result.map(({ id }) => ({ type: "GutCheckHistory" as const, id })),
                { type: "GutCheckHistory", id: "LIST" },
              ]
            : [{ type: "GutCheckHistory", id: "LIST" }],
      }),
    }),
  });

export const { useGetGutcheckStatusQuery, useCreateGutcheckOrderMutation, useGetGutcheckHistoryQuery } = gutCheckApi;
