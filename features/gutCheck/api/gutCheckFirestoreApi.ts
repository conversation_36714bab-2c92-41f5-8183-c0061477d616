import * as Sentry from "@sentry/react";

import { vivanteCoreContainer } from "@Lib/vivanteCore";

const CARE_PLAN_COLLECTION = "carePlan";
const CARE_PLAN_STATE_COLLECTION = "state";
const GUTCHECK_MICROBIOME_INTRO_DOCUMENT = "order_gutcheck";

export const completeGutCheckIntervention = async () => {
  const firebaseClient = vivanteCoreContainer.firebaseClient;
  const [firestore, memberId] = await Promise.all([firebaseClient.getFirestore(), firebaseClient.getMemberId()]);

  const firestoreGutCheckDocument = firestore
    .collection(CARE_PLAN_COLLECTION)
    .document(memberId)
    .collection(CARE_PLAN_STATE_COLLECTION)
    .document(GUTCHECK_MICROBIOME_INTRO_DOCUMENT);

  const firestoreDocument = await firestoreGutCheckDocument.get();

  if (firestoreDocument) {
    try {
      await firestoreGutCheckDocument.update({ state: "COMPLETED" });
    } catch (err) {
      Sentry.withScope((scope) => {
        scope.setLevel("warning");
        Sentry.captureException(new Error(`MID: ${memberId} was unable to update gut check intervention state`));
      });
    }
  } else {
    try {
      await firestoreGutCheckDocument.set({ state: "COMPLETED", timesOpened: 1 });
    } catch (err) {
      Sentry.withScope((scope) => {
        scope.setLevel("warning");
        Sentry.captureException(new Error(`MID: ${memberId} was unable to set a new gut check intervention state`));
      });
    }
  }
};
