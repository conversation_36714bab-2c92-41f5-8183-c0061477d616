import { describe, test, expect } from "vitest";

import { appStrings } from "@Assets/app_strings";
import { MFA_TYPE } from "@Features/multiFactorAuthentication/assets/constants";
import { BffFetchError } from "@Utils/isFetchFromBffError";

import { processFirstFactorSignInError } from "./processFirstFactorSignInError";

const AUTHENTICATION_STRINGS = appStrings.features.authentication;

describe("processFirstFactorSignInError", () => {
  test("Should return SIGN_IN_ERROR for status 400", () => {
    const error: BffFetchError = {
      status: 400,
      data: { errors: [{ code: "", title: "", detail: "" }], status_code: 400 },
    };

    const result = processFirstFactorSignInError(error);

    expect(result).toEqual({ type: "SIGN_IN_ERROR", error: AUTHENTICATION_STRINGS.failedServerValidation });
  });

  test("Should return SIGN_IN_ERROR for INVALID_CREDENTIALS", () => {
    const error: BffFetchError = {
      status: 401,
      data: { errors: [{ code: "INVALID_CREDENTIALS", title: "Invalid credentials", detail: "" }], status_code: 401 },
    };

    const result = processFirstFactorSignInError(error);

    expect(result).toEqual({ type: "SIGN_IN_ERROR", error: AUTHENTICATION_STRINGS.invalidCredentials });
  });

  test("Should return MFA_REQUIRED for MFA_REQUIRED_CODE_SENT_VIA_EMAIL with the email in the verificationCodeSentTo", () => {
    const error: BffFetchError = {
      status: 401,
      data: {
        errors: [{ code: "MFA_REQUIRED_CODE_SENT_VIA_EMAIL", title: "", detail: "<EMAIL>" }],
        status_code: 401,
      },
    };

    const result = processFirstFactorSignInError(error);

    expect(result).toEqual({
      type: "MFA_REQUIRED",
      mfaSupportedMethod: MFA_TYPE.EMAIL,
      verificationCodeSentTo: "<EMAIL>",
    });
  });

  test("Should return MFA_REQUIRED for MFA_REQUIRED_CODE_SENT_VIA_SMS with the phone number in the verificaitonCodeSentTo", () => {
    const error: BffFetchError = {
      status: 401,
      data: {
        errors: [{ code: "MFA_REQUIRED_CODE_SENT_VIA_SMS", title: "", detail: "1234567890" }],
        status_code: 401,
      },
    };

    const result = processFirstFactorSignInError(error);

    expect(result).toEqual({
      type: "MFA_REQUIRED",
      mfaSupportedMethod: MFA_TYPE.SMS,
      verificationCodeSentTo: "1234567890",
    });
  });

  test("Should return SIGN_IN_ERROR for MFA_ERROR_MAX_SEND_ATTEMPTS_REACHED", () => {
    const error: BffFetchError = {
      status: 401,
      data: {
        errors: [{ code: "MFA_ERROR_MAX_SEND_ATTEMPTS_REACHED", title: "Too many attempts made", detail: "" }],
        status_code: 401,
      },
    };

    const result = processFirstFactorSignInError(error);

    expect(result).toEqual({ type: "SIGN_IN_ERROR", error: AUTHENTICATION_STRINGS.maxMfaSendAttemptsReached });
  });

  test("Should return SIGN_IN_ERROR for 500 error code", () => {
    const error: BffFetchError = {
      status: 500,
      data: { errors: [{ code: "MFA_ERROR", title: "Unknown error", detail: "" }], status_code: 500 },
    };

    const result = processFirstFactorSignInError(error);

    expect(result).toEqual({ type: "SIGN_IN_ERROR", error: AUTHENTICATION_STRINGS.genericAuthenticationError });
  });

  test("Should return SIGN_IN_ERROR for random error code", () => {
    const error: BffFetchError = {
      status: 418,
      data: { errors: [{ code: "IM_A_TEAPOT", title: "I'm a teapot", detail: "I'm a teapot" }], status_code: 418 },
    };

    const result = processFirstFactorSignInError(error);

    expect(result).toEqual({ type: "SIGN_IN_ERROR", error: AUTHENTICATION_STRINGS.genericAuthenticationError });
  });

  test("Should return MFA_PHONE_NUMBER with error MISSING_PHONE_NUMBER for MFA_REQUIRED_MISSING_PHONE_NUMBER", () => {
    const error: BffFetchError = {
      status: 401,
      data: { errors: [{ code: "MFA_REQUIRED_MISSING_PHONE_NUMBER", title: "", detail: "" }], status_code: 401 },
    };

    const result = processFirstFactorSignInError(error);

    expect(result).toEqual({ type: "MFA_PHONE_NUMBER", error: "MISSING_PHONE_NUMBER" });
  });

  test("Should return MFA_PHONE_NUMBER with error INVALID_PHONE_NUMBER for MFA_ERROR_INVALID_PHONE", () => {
    const error: BffFetchError = {
      status: 401,
      data: { errors: [{ code: "MFA_ERROR_INVALID_PHONE", title: "", detail: "" }], status_code: 401 },
    };

    const result = processFirstFactorSignInError(error);

    expect(result).toEqual({ type: "MFA_PHONE_NUMBER", error: "INVALID_PHONE_NUMBER" });
  });
});
