import * as Sentry from "@sentry/nextjs";

import { appStrings } from "@Assets/app_strings";
import { MFA_TYPE } from "@Features/multiFactorAuthentication/assets/constants";
import { MfaType } from "@Features/multiFactorAuthentication/types/mfa.types";
import { BffFetchError } from "@Utils/isFetchFromBffError";

type ProcessFirstFactorSignInError =
  | { type: "SIGN_IN_ERROR"; error: string }
  | { type: "MFA_REQUIRED"; mfaSupportedMethod: string; verificationCodeSentTo: string }
  | { type: "MFA_PHONE_NUMBER"; error: "MISSING_PHONE_NUMBER" | "INVALID_PHONE_NUMBER" };

const AUTHENTICATION_STRINGS = appStrings.features.authentication;
const FIRST_FACTOR_SIGN_IN_ERROR_MESSAGES = {
  INVALID_CREDENTIALS: AUTHENTICATION_STRINGS.invalidCredentials,
  MFA_ERROR_MAX_SEND_ATTEMPTS_REACHED: AUTHENTICATION_STRINGS.maxMfaSendAttemptsReached,
  MFA_ERROR: AUTHENTICATION_STRINGS.genericAuthenticationError,
} as const;
/** Potential Error Codes related to MFA from calling the authentication First factor endpoint mapped to their respective MFA type */
const MFA_CODE_ERRORS_TO_MFA_TYPE = {
  MFA_REQUIRED_CODE_SENT_VIA_EMAIL: MFA_TYPE.EMAIL,
  MFA_REQUIRED_CODE_SENT_VIA_SMS: MFA_TYPE.SMS,
} as const satisfies Record<string, MfaType>;

const isMfaCodeError = (code: string): code is keyof typeof MFA_CODE_ERRORS_TO_MFA_TYPE => {
  return code in MFA_CODE_ERRORS_TO_MFA_TYPE;
};

const isSignInErrorCode = (code: string): code is keyof typeof FIRST_FACTOR_SIGN_IN_ERROR_MESSAGES => {
  return code in FIRST_FACTOR_SIGN_IN_ERROR_MESSAGES;
};

export const processFirstFactorSignInError = (error: BffFetchError): ProcessFirstFactorSignInError => {
  const { data } = error;
  const { code: errorCode, title, detail: errorDetail } = data.errors[0];

  if (isMfaCodeError(errorCode)) {
    /**
     * Two factor is required and we need to navigate to the MFA verification screen
     * We include the error detail which is the phone number or email the verification code was sent to
     */
    return {
      type: "MFA_REQUIRED",
      mfaSupportedMethod: MFA_CODE_ERRORS_TO_MFA_TYPE[errorCode],
      verificationCodeSentTo: errorDetail,
    };
  }

  if (errorCode === "MFA_REQUIRED_MISSING_PHONE_NUMBER") {
    return { type: "MFA_PHONE_NUMBER", error: "MISSING_PHONE_NUMBER" };
  }

  if (errorCode === "MFA_ERROR_INVALID_PHONE") {
    return { type: "MFA_PHONE_NUMBER", error: "INVALID_PHONE_NUMBER" };
  }

  Sentry.captureException(
    new Error(
      `First factor sign in error. Error code: ${errorCode}, Error message: ${title}, Error detail: ${errorDetail}`,
    ),
  );
  // Validation error
  if (error.status === 400) {
    return { type: "SIGN_IN_ERROR", error: AUTHENTICATION_STRINGS.failedServerValidation };
  }

  return {
    type: "SIGN_IN_ERROR",
    error: isSignInErrorCode(errorCode)
      ? FIRST_FACTOR_SIGN_IN_ERROR_MESSAGES[errorCode]
      : AUTHENTICATION_STRINGS.genericAuthenticationError,
  };
};
