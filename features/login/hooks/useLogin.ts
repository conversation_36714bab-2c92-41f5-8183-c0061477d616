import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import * as Sentry from "@sentry/nextjs";
import { FirebaseError } from "firebase/app";
import { useRouter } from "next/router";

import { useLazyFirstFactorSignInQuery } from "@Features/authentication/api/authenticationApi";
import { LOCAL_STORAGE_MOBILE_PHONE } from "@Features/authentication/assets/constants";
import { useAuthentication } from "@Features/authentication/hooks/useAuthentication";
import { initializeFirebaseAuth, signInSuccess } from "@Features/authentication/store/authenticationEpics";
import {
  authenticationStateSelector,
  authenticationStateSlice,
} from "@Features/authentication/store/authenticationStateSlice";
import { MFA_TYPE, SCREEN_VARIANT } from "@Features/multiFactorAuthentication/assets/constants";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { Routes } from "@Types";
import { isFetchFromBffError } from "@Utils/isFetchFromBffError";

import { processFirstFactorSignInError } from "../utils/processFirstFactorSignInError";

type SignInWithFirstFactor = {
  email: string;
  password: string;
  authenticationType: (typeof SCREEN_VARIANT)[keyof typeof SCREEN_VARIANT];
  mobilePhone?: string;
};

export const useLogin = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { sendEventAnalytics } = useAnalyticsHook();
  const [invokeFirstFactorSignIn] = useLazyFirstFactorSignInQuery();
  const { authenticateUserWithCustomToken, authenticateNewlyCreatedUser } = useAuthentication();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loginError, setLoginError] = useState("");

  const isCurrentlyRegistering = useSelector(authenticationStateSelector("isCurrentlyRegistering"));

  const handleLoginError = (errorMessage: string) => {
    setIsSubmitting(false);
    setLoginError(errorMessage);

    sendEventAnalytics(ClickStreamActivityEventType.LOGIN_ERROR);
    Sentry.captureMessage(`Auth Error: ${errorMessage}`, "error");
  };

  /**
   * Store the mobile phone number in the redux store and local storage so we can access it on the verification screen,
   * use it when submitting the eligibiltiy form (as it will be hidden for the user), or if the user refreshes the page we
   * still have the value.
   */
  const storeMFAMobilePhone = (mobilePhone: string) => {
    dispatch(authenticationStateSlice.actions.storeMFAMobilePhone(mobilePhone));
    localStorage.setItem(LOCAL_STORAGE_MOBILE_PHONE, mobilePhone);
  };

  const signInWithFirstFactor = async ({ email, password, authenticationType, mobilePhone }: SignInWithFirstFactor) => {
    try {
      const { signin_token } = await invokeFirstFactorSignIn({
        email,
        password,
        phone: mobilePhone,
      }).unwrap();

      return signin_token;
    } catch (error) {
      if (isFetchFromBffError(error)) {
        const processedSignInError = processFirstFactorSignInError(error);

        /**
         * If we're missing the phone number but it's required for MFA (eg changed over from Email to SMS MFA) OR we have
         * an invalid phone number we navigate to the update verification method screen to add/update the phone number.
         * */
        if (processedSignInError.type === "MFA_PHONE_NUMBER") {
          dispatch(
            NavigationStateSlice.actions.navigateTo({
              path: `${
                processedSignInError.error === "MISSING_PHONE_NUMBER"
                  ? Routes.UPDATE_VERIFICATION_METHOD
                  : Routes.UPDATE_VERIFICATION_PHONE
              }&variant=${SCREEN_VARIANT.LOGIN}`,
              screenName: "UpdateVerificationMethod",
            }),
          );

          // Throw error so that any other code within the calling function does not execute.
          throw new Error("MFA_PHONE_NUMBER");
        }

        if (processedSignInError.type === "MFA_REQUIRED") {
          if (processedSignInError.mfaSupportedMethod === MFA_TYPE.SMS) {
            storeMFAMobilePhone(processedSignInError.verificationCodeSentTo);
          }

          router.push(
            `${
              authenticationType === SCREEN_VARIANT.LOGIN
                ? Routes.MFA_VERIFICATION_LOGIN
                : Routes.MFA_VERIFICATION_REGISTRATION
            }&mfaType=${processedSignInError.mfaSupportedMethod}`,
          );
          // Throw error so that any other code within the calling function does not execute.
          throw new Error("MFA_REQUIRED");
        }

        throw new Error(processedSignInError.error);
      }

      // Fallback to rethrow error if it fails the check for BFF error
      throw error;
    }
  };

  const authenticateUser = async (email: string, password: string) => {
    setIsSubmitting(true);
    dispatch(authenticationStateSlice.actions.storeMFACredentials({ email, password }));

    try {
      const signInToken = await signInWithFirstFactor({ email, password, authenticationType: SCREEN_VARIANT.LOGIN });

      /**
       * MFA not enabled, sign in user using custom token.
       * If the user is currently registering (they can end up here as a result of an error occuring during login), we need to
       * authenticate the user using the newly created user flow which will complete the user registeration (associate the user
       * with the registration code and time zone) and sign in the user.
       */
      if (isCurrentlyRegistering) {
        await authenticateNewlyCreatedUser(signInToken);

        dispatch(initializeFirebaseAuth());
        dispatch(signInSuccess());
      } else {
        await authenticateUserWithCustomToken(signInToken);
      }
    } catch (error) {
      if (error instanceof Error || error instanceof FirebaseError) {
        // If the error is MFA_REQUIRED, we do not need to display anything as the page should navigate to the code verification screen
        if (error.message === "MFA_REQUIRED") {
          return;
        }

        return handleLoginError(error.message);
      }

      // Neither instance of Error or FirebaseError, stringify error as we don't know the shape otherwise. This is a fallback
      handleLoginError(JSON.stringify(error));
    }
  };

  return {
    authenticateUser,
    authenticateUserWithCustomToken,
    isSubmitting,
    loginError,
    signInWithFirstFactor,
    storeMFAMobilePhone,
  } as const;
};
