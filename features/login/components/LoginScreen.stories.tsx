import type { Meta, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import { LoginScreen } from "./LoginScreen";

const meta: Meta<typeof LoginScreen> = {
  decorators: cylinderThemeDecorator,
  title: "@Features/login/LoginScreen",
  component: LoginScreen,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof LoginScreen>;

export const Primary: Story = {
  args: {
    // eslint-disable-next-line no-alert
    onSubmit: (data) => alert(JSON.stringify(data)),
    // eslint-disable-next-line no-alert
    onForgotPassword: () => alert("forgot password"),
    isLoading: false,
  },
};
