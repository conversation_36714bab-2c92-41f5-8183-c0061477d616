import { useSelector } from "react-redux";
import { SignInProps, SignInUseCase } from "@vivantehealth/vivante-core";
import { Box, Button, CircularProgress, Grid, Paper } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { AuthFormContainer } from "@Components/AuthFormContainer/AuthFormContainer";
import { ErrorText } from "@Components/ErrorText/ErrorText";
import { FormInput } from "@Components/form/Fields";
import { Form } from "@Components/form/Form";
import { authenticationStateSelector } from "@Features/authentication/store/authenticationStateSlice";
import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";
import { isOfType } from "@Utils/isOfType";

const SHARED_FORM_STRINGS = appStrings.sharedFormText;
const BUTTON_STRINGS = appStrings.buttonText;
const AUTHENTICATION_STRINGS = appStrings.features.authentication;

type LoginScreenProps = Readonly<{
  isLoading: boolean;
  onSubmit: (data: SignInProps, event?: React.BaseSyntheticEvent) => void;
  onForgotPassword: () => void;
  error?: string;
  errorMessageRef?: React.RefObject<HTMLDivElement | null>;
}>;

export const LoginScreen = ({ isLoading, onSubmit, onForgotPassword, error, errorMessageRef }: LoginScreenProps) => {
  const { formWidth } = useResponsiveStylesHook();
  const userCredentials = useSelector(authenticationStateSelector("mfaCredentials"));

  return (
    <AuthFormContainer>
      <Paper sx={{ width: formWidth }}>
        <Form
          defaultValues={{ email: userCredentials.email, password: userCredentials.password }}
          onSubmit={(data) => {
            if (isOfType<SignInProps>(data, ["email", "password"])) {
              onSubmit(data);
            }
          }}
          formHeader={AUTHENTICATION_STRINGS.signInFormTitle}
          formSubheader={AUTHENTICATION_STRINGS.signInPrompt}
        >
          <Grid item mt={5} mb={4}>
            <FormInput
              label={SHARED_FORM_STRINGS.email}
              name={SHARED_FORM_STRINGS.email}
              type="text"
              required
              rules={{
                required: {
                  value: true,
                  message: SHARED_FORM_STRINGS.requiredMessage,
                },
                pattern: {
                  value: SignInUseCase.EmailValidationRegex,
                  message: AUTHENTICATION_STRINGS.invalidEmailError,
                },
              }}
            />
          </Grid>

          <Grid item mb={5}>
            <FormInput
              label={SHARED_FORM_STRINGS.password}
              name={SHARED_FORM_STRINGS.password}
              type="password"
              required
              rules={{
                required: {
                  value: true,
                  message: SHARED_FORM_STRINGS.requiredMessage,
                },
              }}
            />
          </Grid>

          <Grid item>
            {error && (
              <Box mb={5}>
                <ErrorText errorMessage={error} errorTextRef={errorMessageRef} />
              </Box>
            )}

            <Grid item mb={5}>
              <Button
                type="submit"
                variant="primary"
                disabled={isLoading}
                aria-label={BUTTON_STRINGS.continue}
                fullWidth
              >
                {isLoading ? <CircularProgress size={24} color="inherit" /> : BUTTON_STRINGS.continue}
              </Button>
            </Grid>
          </Grid>
        </Form>

        <Button variant="tertiary" aria-label={BUTTON_STRINGS.forgotPassword} onClick={onForgotPassword}>
          {BUTTON_STRINGS.forgotPassword}
        </Button>
      </Paper>
    </AuthFormContainer>
  );
};
