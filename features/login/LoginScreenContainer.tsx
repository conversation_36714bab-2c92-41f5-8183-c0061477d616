import { useEffect, useRef } from "react";
import { useDispatch } from "react-redux";
import { SignInProps } from "@vivantehealth/vivante-core";

import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { Routes } from "@Types";

import { LoginScreen } from "./components/LoginScreen";
import { useLogin } from "./hooks/useLogin";

export const LoginScreenContainer = () => {
  const dispatch = useDispatch();
  const { authenticateUser, isSubmitting, loginError: errorMessage } = useLogin();

  const errorMessageRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (errorMessage && errorMessageRef?.current) {
      errorMessageRef?.current.focus();
    }
  }, [errorMessage]);

  const onSubmit = async (data: SignInProps) => {
    await authenticateUser(data.email, data.password);
  };

  const onForgotPassword = () => {
    dispatch(NavigationStateSlice.actions.navigateTo({ path: Routes.RESET_PASSWORD, screenName: "PasswordReset" }));
  };

  return (
    <LoginScreen
      onSubmit={onSubmit}
      onForgotPassword={onForgotPassword}
      isLoading={isSubmitting}
      error={errorMessage}
      errorMessageRef={errorMessageRef}
    />
  );
};
