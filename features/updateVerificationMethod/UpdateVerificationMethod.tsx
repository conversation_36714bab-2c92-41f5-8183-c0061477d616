import { useDispatch, useSelector } from "react-redux";
import { Paper, Button, CircularProgress, Box } from "@mui/material";
import { useSearchParams } from "next/navigation";

import { appStrings } from "@Assets/app_strings";
import { AuthFormContainer } from "@Components/AuthFormContainer/AuthFormContainer";
import { FormInput } from "@Components/form/Fields";
import { Form } from "@Components/form/Form";
import { HavingTrouble } from "@Components/HavingTrouble/HavingTrouble";
import { SmsUsageDisclaimer } from "@Components/SmsUsageDisclaimer/SmsUsageDisclaimer";
import { useLazyFirstFactorSignInQuery } from "@Features/authentication/api/authenticationApi";
import {
  authenticationStateSelector,
  authenticationStateSlice,
} from "@Features/authentication/store/authenticationStateSlice";
import { MFA_TYPE, SCREEN_VARIANT } from "@Features/multiFactorAuthentication/assets/constants";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";
import { Routes } from "@Types";
import { formatPhoneNumber } from "@Utils/formatPhoneNumber";
import { isOfType } from "@Utils/isOfType";

import { UPDATE_TYPE } from "./assets/constants";

const UPDATE_VERIFICATION_STRINGS = appStrings.features.updateVerificationMethod;
const SHARED_FORM_STRINGS = appStrings.sharedFormText;
const UPDATE_VERIFICATION_METHOD_HEADER_MAP = {
  [UPDATE_TYPE.METHOD]: {
    header: UPDATE_VERIFICATION_STRINGS.updatedVerificationMethodHeader,
    subHeader: UPDATE_VERIFICATION_STRINGS.updatedVerificationMethodSubheader,
  },
  [UPDATE_TYPE.PHONE]: {
    header: UPDATE_VERIFICATION_STRINGS.updatePhoneNumberHeader,
    subHeader: UPDATE_VERIFICATION_STRINGS.updatedPhoneNumberSubheader,
  },
} as const satisfies Record<(typeof UPDATE_TYPE)[keyof typeof UPDATE_TYPE], { header: string; subHeader: string }>;

export const UpdateVerificationMethod = () => {
  const dispatch = useDispatch();
  const searchParams = useSearchParams();
  const mfaCredentials = useSelector(authenticationStateSelector("mfaCredentials"));
  const { formWidth } = useResponsiveStylesHook();
  const [updatePhoneNumber, { isFetching }] = useLazyFirstFactorSignInQuery();
  /** If updateType is METHOD OR undefined, we fallback to METHOD */
  const updateType = searchParams.get("updateType") === UPDATE_TYPE.PHONE ? UPDATE_TYPE.PHONE : UPDATE_TYPE.METHOD;
  const { header, subHeader } = UPDATE_VERIFICATION_METHOD_HEADER_MAP[updateType];
  const variantSearchParam = searchParams.get("variant");

  /** Update the phone number, send new verification code and navigate to appropriate MFA verification screen */
  const onSubmit = async (data: Record<"mobilePhone", string>) => {
    dispatch(authenticationStateSlice.actions.storeMFAMobilePhone(data.mobilePhone));
    await updatePhoneNumber({ ...mfaCredentials, phone: data.mobilePhone });

    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path:
          variantSearchParam === SCREEN_VARIANT.LOGIN
            ? `${Routes.MFA_VERIFICATION_LOGIN}&mfaType=${MFA_TYPE.SMS}`
            : `${Routes.MFA_VERIFICATION_REGISTRATION}&mfaType=${MFA_TYPE.SMS}`,
      }),
    );
  };

  return (
    <AuthFormContainer>
      <Paper sx={{ width: formWidth }}>
        <Form
          defaultValues={{
            mobilePhone: "",
          }}
          formHeader={header}
          formSubheader={subHeader}
          onSubmit={(data) => {
            if (isOfType<Record<"mobilePhone", string>>(data, ["mobilePhone"])) {
              onSubmit(data);
            }
          }}
        >
          <Box display="flex" flexDirection="column" gap={5} mt={5}>
            <FormInput
              label={SHARED_FORM_STRINGS.mobilePhone}
              name="mobilePhone"
              type="telephone"
              required
              rules={{
                required: {
                  value: true,
                  message: SHARED_FORM_STRINGS.requiredMessage,
                },
                pattern: {
                  value: /^\d{3}-\d{3}-\d{4}$/,
                  message: SHARED_FORM_STRINGS.invalidMobilePhone,
                },
              }}
              onChange={formatPhoneNumber}
            />

            <Button type="submit" fullWidth variant="primary" disabled={isFetching}>
              {isFetching ? <CircularProgress thickness={4} size={24} color="inherit" /> : appStrings.buttonText.submit}
            </Button>

            <SmsUsageDisclaimer />

            <HavingTrouble />
          </Box>
        </Form>
      </Paper>
    </AuthFormContainer>
  );
};
