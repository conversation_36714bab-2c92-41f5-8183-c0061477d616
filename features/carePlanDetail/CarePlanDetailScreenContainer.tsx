import { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { useRouter } from "next/router";

import {
  ActionPlansStateSlice,
  actionPlansStateSelector,
  selectCarePlanDataById,
} from "@Features/carePlan/store/actionPlansStateSlice";
import { CarePlanDataById } from "@Features/carePlan/utils/actionPlanUtils";
import { CarePlanDetailHeader } from "@Features/carePlanDetail/components/CarePlanDetailHeader";
import { CarePlanTargetCards } from "@Features/carePlanDetail/components/CarePlanTargetCards";
import { NavOptions, setActiveNavOption } from "@Features/navigation/store/navigationStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { RootState } from "@Store/store";
import { Routes } from "@Types";

export type CarePlanFilterType = Record<string, boolean>;

const { loadActionPlanTargetStateChoices, loadActionPlanTargetStateFilters, loadActionPlans, getMemberHasTracking } =
  ActionPlansStateSlice.actions;

export const CarePlanDetailScreenContainer = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();

  const { carePlanDetailId } = router.query;
  const actionPlanModuleId = Array.isArray(carePlanDetailId) ? carePlanDetailId[0] : carePlanDetailId;

  const [carePlanFiltersState, setCarePlanFiltersState] = useState<CarePlanFilterType | null>(null);

  const carePlanData: CarePlanDataById = useSelector((state: RootState) =>
    selectCarePlanDataById(state, actionPlanModuleId ?? ""),
  );

  const carePlanFiltersData = useSelector(actionPlansStateSelector("actionPlanTargetStateFilters"));

  const carePlanTargetCardDropdownData = useSelector(actionPlansStateSelector("targetStateChoices"));

  const actionPlanId = useSelector(actionPlansStateSelector("actionPlanId"));

  const filteredTargets = useMemo(() => {
    if (carePlanFiltersState) {
      return carePlanData?.targets?.filter((target) => {
        const allFalse = Object.values(carePlanFiltersState)?.every((value) => !value);

        if (!allFalse) {
          return carePlanFiltersState[target.state] === true;
        }

        return true;
      });
    }

    return [];
  }, [carePlanData?.targets, carePlanFiltersState]);

  useEffect(() => {
    dispatch(setActiveNavOption(NavOptions.CARE_PLAN));
    dispatch(loadActionPlans());
    dispatch(loadActionPlanTargetStateFilters());
    dispatch(loadActionPlanTargetStateChoices());
    dispatch(getMemberHasTracking());
  }, [dispatch, actionPlanModuleId]);

  useEffect(() => {
    if (carePlanFiltersData?.length && !carePlanFiltersState) {
      const filters = carePlanFiltersData.reduce<CarePlanFilterType>((acc, data) => {
        acc[data.state] = false;
        return acc;
      }, {});

      setCarePlanFiltersState(filters);
    }
  }, [carePlanFiltersData, carePlanFiltersState]);

  const handleBackButtonClick = () => {
    sendEventAnalytics(ClickStreamActivityEventType.ACTION_PLAN_MODULE_CLOSED, {
      actionPlanId: actionPlanId ?? "",
      actionPlanModuleId: actionPlanModuleId ?? "",
    });
    router.push(Routes.CARE_PLAN);
  };

  const handleCarePlanFilterChange = (filterName: string) => {
    const newFilterState = {
      ...carePlanFiltersState,
      [filterName]: !carePlanFiltersState?.[filterName],
    };

    sendEventAnalytics(ClickStreamActivityEventType.ACTION_PLAN_MODULE_FILTER_SELECTED, {
      actionPlanId: actionPlanId ?? "",
      actionPlanModuleId: actionPlanModuleId ?? "",
      filters: JSON.stringify(newFilterState),
    });
    setCarePlanFiltersState(newFilterState);
  };

  return (
    <>
      <CarePlanDetailHeader
        title={carePlanData?.module?.title}
        onBackButtonClick={handleBackButtonClick}
        carePlanFiltersData={carePlanFiltersData}
        carePlanFiltersState={carePlanFiltersState}
        onCarePlanFilterChange={handleCarePlanFilterChange}
      />
      <CarePlanTargetCards
        actionPlanModuleId={carePlanData?.module?.id}
        actionPlanId={actionPlanModuleId}
        cardDropdownData={carePlanTargetCardDropdownData}
        carePlanTargets={filteredTargets}
        carePlanDrivers={carePlanData?.drivers}
      />
    </>
  );
};
