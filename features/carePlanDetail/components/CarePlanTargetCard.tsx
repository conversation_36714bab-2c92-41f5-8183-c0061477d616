import { useCallback } from "react";
import { useDispatch } from "react-redux";
import { ActionPlanDriver, ActionPlanTargetState, ActionPlanTargetStateChoice } from "@vivantehealth/vivante-core";
import { Box, Button, List, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { BaseModal } from "@Components/BaseModal/BaseModal";
import { useDrivers } from "@Features/carePlanDetail/utils/useDrivers";

import { CarePlanDriver } from "./CarePlanDriver";
import { VideoModal } from "./VideoModal";

type CarePlanTargetCardProps = Readonly<{
  title: string;
  subtitle?: string;
  cardState: ActionPlanTargetState;
  cardId: string;
  actionPlanId?: string;
  actionPlanModuleId: string;
  targetDriverIds?: string[];
  drivers: ActionPlanDriver[];
  dropdownData: ActionPlanTargetStateChoice[];
}>;

const CARE_PLAN_STRINGS = appStrings.features.carePlan;

export const CarePlanTargetCard = ({
  title,
  subtitle,
  cardState,
  cardId,
  targetDriverIds,
  drivers,
  dropdownData,
  actionPlanId,
  actionPlanModuleId,
}: CarePlanTargetCardProps) => {
  const dispatch = useDispatch();
  const { modalTitle, handleDriverClick, isModalOpen, closeModal, videoModalData, isVideoCompleted } = useDrivers();

  const cardStateAriaLabel = dropdownData.find((data) => data.state === cardState)?.label || CARE_PLAN_STRINGS.toDo;
  const ariaLabel = `${appStrings.a11y.carePlanCard}. ${appStrings.a11y.title(title)}. ${
    subtitle ? appStrings.a11y.subtitle(subtitle) : ""
  }. ${appStrings.a11y.carePlanCardState(cardStateAriaLabel)}`;

  const getVideoModalContent = useCallback(() => {
    if (videoModalData === null) return null;

    return (
      <VideoModal
        videoUrl={videoModalData.activeDriver.linkUri}
        dispatch={dispatch}
        onComplete={videoModalData.onComplete}
        activityContextExtra={{ driverId: videoModalData.activeDriver.id }}
      />
    );
  }, [videoModalData, dispatch]);

  const getVideoModalActions = useCallback(() => {
    if (videoModalData?.activeDriver?.state !== ActionPlanTargetState.COMPLETED) {
      if (isVideoCompleted) {
        return <AppIcon name="Completed" containerStyles={{ pt: 1 }} />;
      }

      return (
        <Button variant="secondary" size="small" onClick={videoModalData?.onComplete}>
          {appStrings.features.carePlan.markDone}
        </Button>
      );
    }

    return null;
  }, [isVideoCompleted, videoModalData]);

  return (
    <>
      <BaseModal
        maxWidth="md"
        title={modalTitle || ""}
        isModalOpen={isModalOpen}
        onClose={closeModal}
        bodyContent={getVideoModalContent()}
        isVideoModal // we only have modal for video content within careplan actions
        actions={getVideoModalActions()}
      />

      <Paper
        sx={{
          display: "flex",
          flexDirection: "column",
          width: "100%",
          height: "fit-content",
          padding: 4,
          justifyContent: "center",
          background: color.background.surface.primary,
        }}
        aria-label={ariaLabel}
      >
        <Box display="flex" gap={2} pb={4}>
          <Box flexGrow="2" width="100%">
            <Typography variant="h3" mb={1}>
              {title}
            </Typography>
            <Typography variant="body">{subtitle}</Typography>
          </Box>
        </Box>
        <Box flexShrink={0}>
          <List disablePadding>
            {targetDriverIds?.map((id, i, row) => {
              const driver = drivers.find((driver) => driver.id === id);

              return driver ? (
                <CarePlanDriver
                  key={id}
                  driver={driver}
                  actionPlanTargetId={cardId}
                  actionPlanId={actionPlanId}
                  actionPlanModuleId={actionPlanModuleId}
                  handleDriverClick={handleDriverClick}
                  actionState={cardState}
                  isLast={i + 1 === row.length}
                />
              ) : null;
            })}
          </List>
        </Box>
      </Paper>
    </>
  );
};
