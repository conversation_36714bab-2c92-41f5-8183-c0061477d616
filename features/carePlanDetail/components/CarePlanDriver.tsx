import { ActionPlanDriver, ActionPlanTargetState } from "@vivantehealth/vivante-core";
import { ListItem } from "@mui/material";

import { SPACING_8_PX } from "@Assets/style_constants";
import { ActionItemCard } from "@Components/ActionItemCard/ActionItemCard";
import { HandleDriveClickProps } from "@Features/carePlanDetail/utils/useDrivers";

import { CarePlanTrackingResolver } from "./CarePlanTrackingResolver";

type CarePlanDriverProps = {
  actionPlanTargetId: string;
  actionPlanId?: string;
  actionPlanModuleId: string;
  driver: ActionPlanDriver;
  handleDriverClick: (props: HandleDriveClickProps) => void;
  actionState?: ActionPlanTargetState;
  isLast?: boolean;
};

export const CarePlanDriver = ({
  actionPlanTargetId,
  actionPlanId,
  actionPlanModuleId,
  driver,
  handleDriverClick,
  actionState,
  isLast,
}: CarePlanDriverProps) => {
  const isAudio = driver.subtype === "audio";
  const isClickDisabled = isAudio;

  // get if the driver is a tracking driver, so we COMPLETE the driver and it's parent if not COMPLETED using the CarePlanTrackingResolver
  const shouldCheckForTrackingResolver =
    driver.subtype === "uri" && driver.linkUri === "Tracking" && driver.state !== ActionPlanTargetState.COMPLETED;

  return (
    <ListItem sx={{ padding: 0, paddingBottom: `${!isLast ? SPACING_8_PX : 0}` }}>
      {shouldCheckForTrackingResolver ? <CarePlanTrackingResolver driver={driver} /> : null}
      <ActionItemCard
        driver={driver}
        onClick={
          isClickDisabled
            ? undefined
            : () =>
                handleDriverClick({
                  driver,
                  actionPlanTargetId,
                  actionPlanId,
                  actionPlanModuleId,
                  actionState,
                })
        }
      />
    </ListItem>
  );
};
