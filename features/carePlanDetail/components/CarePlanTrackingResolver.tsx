import { useSelector } from "react-redux";
import { ActionPlanDriver, ActionPlanTargetState } from "@vivantehealth/vivante-core";

import { getMemberTrackingStatus } from "@Features/carePlan/store/actionPlansStateSlice";
import { setActionNewState } from "@Features/carePlan/utils/markAsDone.util";
import { useCheckAndUpdateParentActionState } from "@Hooks/useCheckAndUpdateParentActionState";

type CarePlanTrackingResolverProps = Readonly<{
  driver: ActionPlanDriver;
}>;

export const CarePlanTrackingResolver = ({ driver }: CarePlanTrackingResolverProps) => {
  const checkAndUpdateParentActionState = useCheckAndUpdateParentActionState();
  const memberTrackingStatus = useSelector(getMemberTrackingStatus);

  if (memberTrackingStatus?.food || memberTrackingStatus?.stool || memberTrackingStatus?.symptoms) {
    // mark parent/target item as completed if this is the last uncompleted driver
    checkAndUpdateParentActionState(driver.targetId ?? "", driver.id, ActionPlanTargetState.COMPLETED);

    // mark this action as completed
    setActionNewState(ActionPlanTargetState.COMPLETED, driver.state, driver.id);
  }

  return null;
};
