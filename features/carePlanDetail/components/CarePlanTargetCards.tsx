import { ActionPlanDriver, ActionPlanTargetStateChoice } from "@vivantehealth/vivante-core";
import { Box } from "@mui/material";

import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { TargetSchema } from "@Features/carePlan/types/types";
import { CarePlanTargetCard } from "@Features/carePlanDetail/components/CarePlanTargetCard";

type CarePlanTargetCardsProps = Readonly<{
  cardDropdownData: ActionPlanTargetStateChoice[];
  actionPlanId?: string;
  actionPlanModuleId: string;
  carePlanTargets: TargetSchema[];
  carePlanDrivers: ActionPlanDriver[];
}>;

export const CarePlanTargetCards = ({
  cardDropdownData,
  carePlanTargets,
  carePlanDrivers,
  actionPlanId,
  actionPlanModuleId,
}: CarePlanTargetCardsProps) => {
  if (!carePlanTargets) {
    return (
      <Box pb={7}>
        <LoadingSpinner open={!carePlanTargets} />
      </Box>
    );
  }

  return (
    <Box pb={7} display="grid" gap={4}>
      {carePlanTargets.map((target) => (
        <CarePlanTargetCard
          key={target.id}
          title={target.title}
          subtitle={target.description}
          cardId={target.id}
          cardState={target.state}
          actionPlanId={actionPlanId}
          actionPlanModuleId={actionPlanModuleId}
          targetDriverIds={target.drivers}
          drivers={carePlanDrivers}
          dropdownData={cardDropdownData}
        />
      ))}
    </Box>
  );
};
