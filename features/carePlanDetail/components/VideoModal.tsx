import { useCallback, useEffect, useRef } from "react";
import { ClickStreamActivityContextExtra, ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { Box } from "@mui/material";
import { Dispatch } from "@reduxjs/toolkit";
import ReactPlayer from "react-player";

import { RADIUS_16_PX } from "@Assets/style_constants";
import { sendAnalytics } from "@Features/analytics/store/analyticsEpics";

type VideoComponentProps = Readonly<{
  videoUrl: string;
  activityContextExtra?: ClickStreamActivityContextExtra;
  dispatch: Dispatch;
  onComplete?: () => void;
}>;

export const VideoModal = ({ videoUrl, activityContextExtra = {}, dispatch, onComplete }: VideoComponentProps) => {
  const playerRef = useRef<ReactPlayer | null>(null);
  const currentTimeRef = useRef<number | undefined>(0);
  const durationRef = useRef<number>(undefined);
  const timeRef = useRef<NodeJS.Timeout>(undefined);

  const dispatchAnalytics = useCallback(
    (eventType: ClickStreamActivityEventType) => {
      dispatch(
        sendAnalytics({
          eventType,
          activityContextExtra: {
            ...activityContextExtra,
            duration: durationRef.current,
            time: currentTimeRef.current,
          },
        }),
      );
    },
    [activityContextExtra, dispatch],
  );

  useEffect(() => {
    return () => {
      clearInterval(timeRef?.current);

      if (!durationRef.current) {
        return;
      }

      // dispatch the end analytics, if we've had the chance to setup for it
      dispatchAnalytics(ClickStreamActivityEventType.VIDEO_CLOSED);
    };
  }, [dispatchAnalytics]);

  const onPlay = () => {
    if (timeRef.current) {
      // we're already setup, exit out
      return;
    }

    // setup to start polling for video meta
    timeRef.current = setInterval(() => {
      const wasNoDuration = !durationRef.current;

      durationRef.current = playerRef?.current?.getDuration();

      if (wasNoDuration && !!durationRef.current) {
        // we hadn't computed duration before and now we have it,
        // go ahead and dispatch the start analytics
        dispatchAnalytics(ClickStreamActivityEventType.VIDEO_PLAYED);
      }

      currentTimeRef.current = playerRef?.current?.getCurrentTime();
    }, 1000);
  };

  return (
    <Box display="flex" justifyContent="center" borderRadius={4}>
      <ReactPlayer
        style={{ borderRadius: RADIUS_16_PX, overflow: "hidden" }}
        ref={playerRef}
        url={videoUrl}
        controls
        onPlay={onPlay}
        onSeek={() => {
          dispatchAnalytics(ClickStreamActivityEventType.VIDEO_SKIPPED);
        }}
        onPause={() => {
          dispatchAnalytics(ClickStreamActivityEventType.VIDEO_PAUSED);
        }}
        onEnded={() => {
          if (onComplete) onComplete();

          dispatchAnalytics(ClickStreamActivityEventType.VIDEO_ENDED);
        }}
      />
    </Box>
  );
};
