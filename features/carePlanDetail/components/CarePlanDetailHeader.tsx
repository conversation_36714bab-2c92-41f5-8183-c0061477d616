import { ActionPlanTargetStateFilter } from "@vivantehealth/vivante-core";
import Box from "@mui/material/Box";
import Chip from "@mui/material/Chip";
import Typography from "@mui/material/Typography";

import { appStrings } from "@Assets/app_strings";
import { BackButton } from "@Components/BackButton/BackButton";
import { CarePlanFilterType } from "@Features/carePlanDetail/CarePlanDetailScreenContainer";

type CarePlanDetailHeaderProps = Readonly<{
  title: string;
  onBackButtonClick: () => void;
  carePlanFiltersData: ActionPlanTargetStateFilter[];
  carePlanFiltersState: CarePlanFilterType | null;
  onCarePlanFilterChange: (state: string) => void;
}>;

export const CarePlanDetailHeader = ({
  title,
  onBackButtonClick,
  carePlanFiltersData,
  carePlanFiltersState,
  onCarePlanFilterChange,
}: CarePlanDetailHeaderProps) => {
  const filterDataLoaded = carePlanFiltersData?.length && carePlanFiltersState;

  return (
    <>
      <BackButton onClick={onBackButtonClick}>{appStrings.features.carePlan.backButtonText}</BackButton>
      <Typography variant="h1Serif" my={6} aria-label={`${appStrings.a11y.title(title)}`}>
        {title}
      </Typography>

      <Box display="flex" gap={2} mb={5}>
        {filterDataLoaded &&
          carePlanFiltersData.map((filter) => {
            const isFilterActive = carePlanFiltersState[filter.state];

            return (
              <Chip
                key={filter.label}
                label={filter.label}
                variant={isFilterActive ? "active" : "inactive"}
                onClick={() => onCarePlanFilterChange(filter.state)}
              />
            );
          })}
      </Box>
    </>
  );
};
