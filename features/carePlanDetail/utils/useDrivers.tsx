import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ActionPlanDriver, ActionPlanTargetState, ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { useRouter } from "next/router";

import { sendAnalytics } from "@Features/analytics/store/analyticsEpics";
import {
  actionPlansStateSelector,
  setActionPlanTargetState,
  setSelectedActionId,
} from "@Features/carePlan/store/actionPlansStateSlice";
import {
  markActionInProgressIfQualifying,
  setActionNewState,
  shouldMarkAsDone,
} from "@Features/carePlan/utils/markAsDone.util";
import { NavOptions, NavigationStateSlice, setActiveNavOption } from "@Features/navigation/store/navigationStateSlice";
import { handleUriNavigation } from "@Features/navigation/utils/navigation.util";
import { useCheckAndUpdateParentActionState } from "@Hooks/useCheckAndUpdateParentActionState";
import { Routes } from "@Types";

export type HandleDriveClickProps = Readonly<{
  driver: ActionPlanDriver;
  actionPlanId?: string;
  actionPlanModuleId: string;
  actionPlanTargetId: string;
  actionState?: ActionPlanTargetState;
}>;

type UseDriverReturnType = Readonly<{
  isModalOpen: boolean;
  modalTitle: string | null;
  handleDriverClick: (props: HandleDriveClickProps) => void;
  closeModal: () => void;
  videoModalData: VideoModalData | null;
  isVideoCompleted: boolean;
}>;
export type VideoModalData = Readonly<{
  activeDriver: ActionPlanDriver;
  onComplete?: () => void;
}>;
export const useDrivers = (): UseDriverReturnType => {
  const [activeDriverContext, setActiveDriverContext] = useState<HandleDriveClickProps | null>();
  const [modalTitle, setModalTitle] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const targetEntitiesState = useSelector(actionPlansStateSelector("targetEntities"));
  const [videoModalData, setVideoModalData] = useState<VideoModalData | null>(null);
  const [isVideoCompleted, setIsVideoCompleted] = useState(false);

  const router = useRouter();
  const dispatch = useDispatch();
  const handleDriverClick = (handleDriveClickProps: HandleDriveClickProps) =>
    setActiveDriverContext(handleDriveClickProps);
  const closeModal = () => setIsModalOpen(false);

  const checkAndUpdateParentActionState = useCheckAndUpdateParentActionState();

  const activeDriver = activeDriverContext?.driver;

  if (activeDriver) {
    // reset selected action id, it will be set again if this action is a survey
    dispatch(setSelectedActionId(null));

    // set target action (parent) state to in progress if this action is not yet started or completed (if mark as done is enabled)
    markActionInProgressIfQualifying(activeDriverContext?.actionPlanTargetId, activeDriverContext?.actionState);

    if (activeDriver.linkUri === "FoodLog" || activeDriver.linkUri === "ClinicalPathwayNutrition") {
      router.push(Routes.Food_Tracking);
      dispatch(setActiveNavOption(NavOptions.PROGRESS));
    } else if (activeDriver.linkUri === "chat" || activeDriver.title.toLowerCase() === "ask a question") {
      // this should only be executed if mark as done is enabled. otherwise we shouldn't navigate to chat screen for `ask a question` or `chat` uri
      if (shouldMarkAsDone(activeDriverContext?.driver?.id, activeDriverContext?.driver?.state)) {
        // mark parent/target item as completed if this is the last uncompleted driver
        checkAndUpdateParentActionState(
          activeDriverContext.actionPlanTargetId,
          activeDriverContext?.driver?.id,
          ActionPlanTargetState.COMPLETED,
        );

        // mark this action as completed
        dispatch(
          setActionPlanTargetState({
            targetId: activeDriverContext?.driver.id,
            newTargetState: ActionPlanTargetState.COMPLETED,
          }),
        );
      }

      router.push(Routes.CHAT);
      dispatch(setActiveNavOption(NavOptions.CARE_TEAM));
    } else if (activeDriver.linkUri === "Tracking") {
      router.push(Routes.PROGRESS);
      dispatch(setActiveNavOption(NavOptions.PROGRESS));
    } else if (activeDriver.linkUri === "care_team") {
      router.push(Routes.CARE_TEAM);
      dispatch(setActiveNavOption(NavOptions.CARE_TEAM));
    } else if (activeDriver.linkUri === "GutCheckResults") {
      router.push(Routes.GUT_CHECK);
      dispatch(setActiveNavOption(NavOptions.GUT_CHECK));
    } else if (activeDriver.subtype === "pdf" || activeDriver.subtype === "URL") {
      if (shouldMarkAsDone(activeDriverContext?.driver?.id, activeDriverContext?.driver?.state)) {
        // mark parent/target item as completed if this is the last uncompleted driver
        checkAndUpdateParentActionState(
          activeDriverContext.actionPlanTargetId,
          activeDriverContext?.driver?.id,
          ActionPlanTargetState.COMPLETED,
        );

        // mark this action as completed
        dispatch(
          setActionPlanTargetState({
            targetId: activeDriverContext?.driver.id,
            newTargetState: ActionPlanTargetState.COMPLETED,
          }),
        );
      }

      window.open(activeDriver.linkUri, "_blank", "noopener,noreferrer");
    } else if (activeDriver.subtype === "video_link") {
      setIsModalOpen(true);

      if (activeDriverContext.actionPlanTargetId) {
        setModalTitle(targetEntitiesState[activeDriverContext.actionPlanTargetId]?.title);
      }

      const markVideoAsDone = (actionId: string, targetId: string) => {
        // mark parent/target item as completed if this is the last uncompleted driver
        checkAndUpdateParentActionState(targetId, actionId, ActionPlanTargetState.COMPLETED);

        // mark this action as completed
        setActionNewState(ActionPlanTargetState.COMPLETED, activeDriverContext?.driver?.state, actionId);

        setIsVideoCompleted(true);
      };

      // mark action as in progress if this action is not yet started or completed
      markActionInProgressIfQualifying(activeDriverContext?.driver?.id, activeDriverContext?.driver?.state);

      setVideoModalData({
        ...(shouldMarkAsDone(activeDriverContext?.driver?.id, activeDriverContext?.driver?.state) && {
          onComplete: () => markVideoAsDone(activeDriverContext?.driver?.id, activeDriverContext.actionPlanTargetId),
        }),
        activeDriver: activeDriver,
      });
    } else if (activeDriver.linkUri.includes("slideshow")) {
      const slideShowId = activeDriver.linkUri.split("/")[1];

      // set current slide show action in progress if this action is not yet started or completed
      // the action will be marked as completed when the last slide is reached (in the SlideshowScreen component)
      // this action should only executed if mark as done is enabled
      markActionInProgressIfQualifying(activeDriverContext?.driver?.id, activeDriverContext?.driver?.state);

      dispatch(
        NavigationStateSlice.actions.navigateTo({
          path: `${Routes.SLIDESHOW}/${slideShowId}${
            activeDriverContext?.driver?.state !== ActionPlanTargetState.COMPLETED &&
            // apeend action id to url if this item is not yet completed, so we can identify the action in the SlideshowScreen component
            activeDriverContext?.driver?.id?.length > 0
              ? `?actionId=${activeDriverContext?.driver?.id}`
              : ""
          }`,
          screenName: "Slideshow",
          activityContextExtra: {
            slideshowId: slideShowId,
          },
        }),
      );
    } else if (activeDriver.linkUri === "course") {
      router.push(Routes.COURSES);
      dispatch(setActiveNavOption(NavOptions.COURSES));
    } else if (activeDriver.linkUri.includes("course") && activeDriver.subtype === "resource") {
      const courseUri = activeDriver.linkUri;
      const courseId = courseUri.split("/")[1];
      const redirectUri = `${Routes.COURSES}/${courseId}`;

      router.push(redirectUri);
      dispatch(setActiveNavOption(NavOptions.COURSES));
    } else if (activeDriver.linkUri.includes("witch") && activeDriver.subtype === "resource") {
      const witchId = activeDriver.linkUri.split("/")[1];

      router.push(`${Routes.SURVEY}/${witchId}`);
    } else if (activeDriver.linkUri.includes("survey") && activeDriver.subtype === "resource") {
      // mark action as in progress if this action is not yet started or completed
      markActionInProgressIfQualifying(activeDriverContext?.driver?.id, activeDriverContext?.driver?.state);

      dispatch(setSelectedActionId(activeDriver.id));

      const route = handleUriNavigation(activeDriver.linkUri);

      if (route) {
        router.push(route);
      }
    } else if (activeDriver.linkUri.includes("order") && activeDriver.subtype === "resource") {
      const orderUri = activeDriver.linkUri;
      const orderId = orderUri.split("/")[1];

      router.push(`${Routes.ORDER}/${orderId}`);
    } else if (activeDriver.linkUri === "set_reminder") {
      // no-op, similar to angular
      // TODO: set-reminder driver action not implemented
    } else if (activeDriver.linkUri) {
      // github issue https://github.com/vivantehealth/githrive-reactweb/issues/233
      alert(`ToDO: Integrate useUriParser to handle ${activeDriver.linkUri}`);
    }

    dispatch(
      sendAnalytics({
        eventType: ClickStreamActivityEventType.ACTION_PLAN_DRIVER_OPENED,
        activityContextExtra: {
          actionPlanId: activeDriverContext?.actionPlanId,
          actionPlanModuleId: activeDriverContext?.actionPlanModuleId,
          actionPlanTargetId: activeDriverContext?.actionPlanTargetId,
          actionPlanDriverId: activeDriver.id,
        },
      }),
    );

    setActiveDriverContext(null);
  }

  return {
    modalTitle,
    handleDriverClick,
    isModalOpen,
    closeModal,
    videoModalData,
    isVideoCompleted,
  };
};
