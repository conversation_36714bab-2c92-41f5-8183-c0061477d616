import { SetNotificationsPreferenceProps } from "@vivantehealth/vivante-core";
import { Action, PayloadAction } from "@reduxjs/toolkit";
import { Epic, ofType } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { catchError, map, switchMap } from "rxjs/operators";

import { vivanteCoreContainer } from "@Lib/vivanteCore";

import {
  loadMemberPreferences,
  loadMemberPreferencesFailure,
  loadMemberPreferencesSuccess,
  setNotificationSettings,
  setNotificationSettingsSuccess,
  setNotificationSettingsFailure,
} from "./memberPreferencesStateSlice";

export const loadMemberPreferencesEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(loadMemberPreferences.type),
    switchMap(() =>
      from(
        vivanteCoreContainer.getMemberPreferencesUseCaseFactory().createGetMemberPreferencesUseCase().execute(),
      ).pipe(
        map((memberPreferences) => loadMemberPreferencesSuccess(memberPreferences)),
        catchError((e) => of(loadMemberPreferencesFailure(e))),
      ),
    ),
  );
};

const setNotificationSettingsEpic: Epic = (actions$: Observable<PayloadAction<SetNotificationsPreferenceProps>>) => {
  return actions$.pipe(
    ofType(setNotificationSettings.type),
    switchMap((action: PayloadAction<SetNotificationsPreferenceProps>) =>
      from(
        vivanteCoreContainer
          .getMemberPreferencesUseCaseFactory()
          .createSetNotificationsPreferenceUseCase()
          .execute(action.payload),
      ).pipe(
        map(() => setNotificationSettingsSuccess()),
        catchError((e) => of(setNotificationSettingsFailure(e))),
      ),
    ),
  );
};

export const loadMemberPreferencesEpics = [loadMemberPreferencesEpic, setNotificationSettingsEpic];
