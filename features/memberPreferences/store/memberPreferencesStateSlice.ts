/* eslint-disable @typescript-eslint/no-unused-vars */
import { MemberPreferences, SetNotificationsPreferenceProps } from "@vivantehealth/vivante-core";
import { PayloadAction, createSlice } from "@reduxjs/toolkit";

import { LoadState } from "@Types";
import { processError } from "@Utils/slice.util";
import { buildSliceStateSelector } from "@Utils/slice.util";

export type MemberPreferencesState = Readonly<{
  memberPreferences?: MemberPreferences;
  loadState: LoadState;
}>;

export const initialState: MemberPreferencesState = {
  memberPreferences: undefined,
  loadState: null,
};

export const memberPreferencesStateSlice = createSlice({
  name: "memberPreferencesState",
  initialState,
  reducers: {
    loadMemberPreferences: (state) => ({ ...state, loadState: "loading" }),
    loadMemberPreferencesSuccess: (
      state: MemberPreferencesState,
      action: PayloadAction<MemberPreferences | undefined>,
    ) =>
      ({
        ...state,
        memberPreferences: action.payload,
        loadState: "loaded",
      }) as const,
    loadMemberPreferencesFailure: (state, action: PayloadAction<Error>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
    setNotificationSettings: (state, _: PayloadAction<SetNotificationsPreferenceProps>) => ({
      ...state,
      loadState: "loading",
    }),
    setNotificationSettingsSuccess: (state) => ({ ...state, loadState: "loaded" }),
    setNotificationSettingsFailure: (state, action: PayloadAction<Error>) => {
      processError({ error: action.payload });

      return { ...state, loadState: "failure" };
    },
  },
});

export const {
  loadMemberPreferences,
  loadMemberPreferencesSuccess,
  loadMemberPreferencesFailure,
  setNotificationSettings,
  setNotificationSettingsSuccess,
  setNotificationSettingsFailure,
} = memberPreferencesStateSlice.actions;

export const memberPreferencesStateSelector = buildSliceStateSelector("memberPreferencesState");

export const memberPreferencesStateReducer = memberPreferencesStateSlice.reducer;
