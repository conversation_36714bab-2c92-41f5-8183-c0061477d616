import type { Meta, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import { NurseTriage } from "./NurseTriage";

const meta: Meta<typeof NurseTriage> = {
  decorators: cylinderThemeDecorator,
  title: "@Features/nurseTriage/NurseTriage",
  component: NurseTriage,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof NurseTriage>;

export const Primary: Story = {
  args: {
    modalOpen: false,
    hasAcknowledged: false,
    setHasAcknowledged: () => alert("Has acknowldeged"),
    setModalOpen: () => alert("Opening modal!"),
  },
};
