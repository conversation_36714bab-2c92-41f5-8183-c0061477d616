import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, FormControlLabel, Checkbox } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import Router from "next/router";

import { appStrings } from "@Assets/app_strings";
import { SPACING_4_PX, SPACING_8_PX } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { BaseModal } from "@Components/BaseModal/BaseModal";
/** Value taken from Figma design https://www.figma.com/design/8tkrRyyha0TKlyV8ZUfICc/Rebrand-Phase-1---Product-UI?node-id=***********&m=dev */
const NURSE_TRIAGE_WIDTH = "684px";
const NURSE_TRIAGE_STRINGS = appStrings.features.nurseTriage;

type NurseTriageProps = Readonly<{
  modalOpen: boolean;
  hasAcknowledged: boolean;
  setHasAcknowledged: (hasAcknowledged: boolean) => void;
  setModalOpen: (isOpen: boolean) => void;
}>;

export const NurseTriage = ({ hasAcknowledged, modalOpen, setHasAcknowledged, setModalOpen }: NurseTriageProps) => {
  return (
    <>
      <BaseModal
        isModalOpen={modalOpen}
        displayCloseButton={false}
        onClose={() => {
          setModalOpen(false);
          setHasAcknowledged(false);
        }}
        bodyContent={
          <FormControlLabel
            checked={hasAcknowledged}
            onChange={(_, checked) => setHasAcknowledged(checked)}
            control={<Checkbox sx={{ p: 2 }} />}
            label={NURSE_TRIAGE_STRINGS.option}
            sx={{ m: 0, px: { xs: 3 } }}
          />
        }
        actions={
          <Button
            variant="primary"
            disabled={!hasAcknowledged}
            onClick={() => Router.back()}
            fullWidth
            sx={{ mx: { xs: 3 } }}
          >
            {appStrings.buttonText.continue}
          </Button>
        }
      />

      <Grid container justifyContent="center">
        <Box display="flex" flexDirection="column" gap={6} width={NURSE_TRIAGE_WIDTH} px={{ xs: 4 }}>
          <Alert
            color="warning"
            action={
              <AppIcon
                name="Warning"
                /** Had to manipulate the icon spacing here as targeting MUI classes would not work in the theme or in SX prop */
                containerStyles={{
                  width: "20px",
                  height: "20px",
                  marginTop: `-${SPACING_4_PX}`,
                  marginRight: SPACING_8_PX,
                }}
                color={color.icon.warning}
              />
            }
            icon={false}
            sx={{
              "&.MuiAlert-root": { alignItems: "center" },
            }}
          >
            {NURSE_TRIAGE_STRINGS.warning}
          </Alert>

          <Typography variant="h1Serif">{NURSE_TRIAGE_STRINGS.title}</Typography>

          <Box>
            <Typography variant="h4" mb={4}>
              {NURSE_TRIAGE_STRINGS.subtitle}
            </Typography>
            <Typography variant="body">{NURSE_TRIAGE_STRINGS.body}</Typography>
          </Box>

          <Box display="flex" gap={5} alignItems="center">
            <Button variant="secondary" startIcon={<AppIcon name="Phone" />} href={NURSE_TRIAGE_STRINGS.phoneNumber}>
              {NURSE_TRIAGE_STRINGS.phoneNumberDisplay}
            </Button>

            <Button variant="tertiary" onClick={() => setModalOpen(true)}>
              {appStrings.buttonText.dismiss}
            </Button>
          </Box>
        </Box>
      </Grid>
    </>
  );
};
