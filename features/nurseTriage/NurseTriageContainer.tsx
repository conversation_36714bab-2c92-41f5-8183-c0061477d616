import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

import { HeaderContainer } from "@Components/Header/HeaderContainer";
import { NavigationDrawer } from "@Components/Navigation/NavigationDrawer";
import { memberStateSelector } from "@Features/member/store/memberStateSlice";
import { navigationStateSelector, setNavDrawerOpen } from "@Features/navigation/store/navigationStateSlice";
import { updatePageTitle } from "@Features/navigation/utils/updatePageTitle";
import { useHeaderHook } from "@Hooks/useHeaderHook";

import { NurseTriage } from "./components/NurseTriage";

export const NurseTriageContainer = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [hasAcknowledged, setHasAcknowledged] = useState(false);
  const dispatch = useDispatch();

  const activeNavOption = useSelector(navigationStateSelector("activeNavOption"));
  const navDrawerOpen = useSelector(navigationStateSelector("navDrawerOpen"));
  const navDrawerOpenCallback = (open: boolean) => {
    dispatch(setNavDrawerOpen(open));
  };
  const member = useSelector(memberStateSelector("member"));
  const [isIntakeSurvey, setIsIntakeSurvey] = useState<boolean>();
  const { contentHeight } = useHeaderHook();

  useEffect(() => {
    if (isIntakeSurvey === null) {
      setIsIntakeSurvey(member?.setting?.onboardingPending);
    }
  }, [isIntakeSurvey, member]);

  return (
    <>
      <HeaderContainer isIntakeSurvey={isIntakeSurvey} />
      <NavigationDrawer
        activeNavOption={activeNavOption}
        navDrawerOpenCallback={navDrawerOpenCallback}
        navDrawerOpen={navDrawerOpen}
        hide={isIntakeSurvey}
        contentHeight={contentHeight}
        updatePageTitle={updatePageTitle}
      >
        <NurseTriage
          modalOpen={modalOpen}
          setModalOpen={setModalOpen}
          hasAcknowledged={hasAcknowledged}
          setHasAcknowledged={setHasAcknowledged}
        />
      </NavigationDrawer>
    </>
  );
};
