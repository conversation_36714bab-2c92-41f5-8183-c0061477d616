import { Paper, Typography } from "@mui/material";
import * as Sen<PERSON> from "@sentry/nextjs";
import Router from "next/router";

import { appStrings } from "@Assets/app_strings";
import { BackButton } from "@Components/BackButton/BackButton";
import { PostAuthContainer } from "@Components/PostAuthContainer/PostAuthContainer";
import { Routes } from "@Types";

type Error404ScreenProps = Readonly<{
  attemptedPath: string;
}>;

const { error404Header, error404Subheader, errorPageButtonText } = appStrings.errorPages;

export const Error404Screen = ({ attemptedPath }: Error404ScreenProps) => {
  Sentry.withScope((scope) => {
    scope.setLevel("log");
    scope.setTag("type", "404");
    Sentry.captureException(new Error(`User attempted to access: ${attemptedPath}`));
  });

  return (
    <PostAuthContainer>
      <BackButton onClick={() => Router.push(Routes.HOME)}>{errorPageButtonText}</BackButton>
      <Paper sx={{ mt: 5 }}>
        <Typography variant="h3" mb={3}>
          {error404Header}
        </Typography>
        <Typography variant="body">{error404Subheader}</Typography>
      </Paper>
    </PostAuthContainer>
  );
};
