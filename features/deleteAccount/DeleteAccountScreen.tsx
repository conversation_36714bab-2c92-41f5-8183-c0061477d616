import { CSSProperties } from "react";
import { Box, Button, Paper, Typography } from "@mui/material";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { SPACING_16_PX, SPACING_48_PX } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";

const DELETE_STRINGS = appStrings.features.deleteAccount;

export const DeleteAccountScreen = () => {
  const router = useRouter();

  return (
    <Box style={styles.screenContainer}>
      <Box style={{ ...styles.contentContainer, height: "100%" }}>
        <AppIcon name="CompanyLogo" size="logo" includeInTabIndex includeContainer={false} />
        <Typography variant="h1Serif" my={6}>
          {DELETE_STRINGS.header}
        </Typography>
        <Paper style={styles.body}>
          <Typography variant="h2Serif">{DELETE_STRINGS.subHeading}</Typography>
          <Typography variant="body" my={6}>
            {DELETE_STRINGS.body}
          </Typography>

          <div>
            <Typography variant="body1">{DELETE_STRINGS.details}</Typography>
            <Typography variant="body1">{DELETE_STRINGS.details2}</Typography>
          </div>

          <Typography variant="body" my={6}>
            {DELETE_STRINGS.post}
          </Typography>
          <Button variant="secondary" onClick={() => router.replace("/")} sx={styles.button}>
            Back
          </Button>
        </Paper>
      </Box>
    </Box>
  );
};

const styles = {
  body: {
    textAlign: "left",
  },
  screenContainer: {
    display: "flex",
    flexDirection: "column",
    flexWrap: "nowrap",
    justifyContent: "start",
    alignItems: "center",
    alignContent: "stretch",
  },
  contentContainer: {
    display: "block",
    flexBasis: "auto",
    alignSelf: "auto",
    order: 0,
    textAlign: "center",
    width: "390px",
    alignContent: "center",
    minHeight: "400px",
    zIndex: 20,
  },
  button: {
    margin: SPACING_16_PX,
    marginTop: 0,
    width: `calc(100% - ${SPACING_48_PX})`,
  },
  logoStyle: { height: "100%", width: "100%", maxHeight: "7vh", maxWidth: "204px" },
} as const satisfies Record<string, CSSProperties>;
