import { appStrings } from "@Assets/app_strings";

import { ScreenName } from "../types/screenName";

type PageTitleKey = keyof typeof appStrings.pageTitles;

/**
 * Maps screen names to page title keys. app_strings.ts contains the actual page titles.
 */
export const screenNamePageTitleKeyMapper: Record<ScreenName, PageTitleKey> = {
  ActionPlan: "carePlan",
  ActionPlanModule: "carePlanDetail",
  Appointments: "appointments",
  Activity: "cylinder",
  Article: "articleDetails",
  ArticleCategory: "articleCategory",
  Articles: "articleCatalog",
  Authentication: "cylinder",
  CareTeam: "team",
  CareTeamConversations: "cylinder",
  CareTeamUser: "cylinder",
  ChangePassword: "changePassword",
  Chat: "chat",
  ChatOverview: "chat",
  CourseCategory: "courseCategory",
  CourseLanding: "courseCatalog",
  Courses: "cylinder",
  DeleteAccount: "cylinder",
  DietTip: "cylinder",
  EditH2: "cylinder",
  EligibilityHelp: "cylinder",
  EligibilityHelpConfirmation: "cylinder",
  EligibilitySubmission: "cylinder",
  EligibilityStatus: "cylinder",
  FoodTracking: "foodTracking",
  FoodTrackingViewAddedFoods: "foodTrackingViewAddedFoods",
  FoodTrackingViewRecipes: "foodTrackingViewRecipes",
  GoalSettingWelcome: "goalSettingWelcome",
  GutCheckLanding: "gutCheck",
  GutCheckNew: "gutCheck",
  GutCheckNewIntro: "gutCheck",
  Home: "home",
  HistoryDayView: "progress",
  Landing: "cylinder",
  Onboarding: "cylinder",
  Ordering: "cylinder",
  PageNotFound: "pageNotFound",
  PasswordReset: "passwordReset",
  Profile: "cylinder",
  ProductShippingForm: "cylinder",
  ProviderScheduling: "cylinder",
  Referral: "cylinder",
  Settings: "settings",
  SignIn: "signIn",
  SignUp: "signUp",
  ScheduleSession: "cylinder",
  ScheduleSessionConfirm: "cylinder",
  Slideshow: "carePlanDetail",
  StoolLog: "cylinder",
  Survey: "cylinder",
  SymptomLogging: "cylinder",
  Ticket: "cylinder",
  ToDo: "cylinder",
  TypeOfEmployee: "cylinder",
  ViewPdf: "cylinder",
  Unmatched: "cylinder",
  Unspecified: "cylinder",
  Welcome: "welcome",
  WelcomeRegistrationCodeSubmission: "accessCode",
  EmailVerification: "welcome",
  UpdateVerificationMethod: "updateVerificationMethod",
};
