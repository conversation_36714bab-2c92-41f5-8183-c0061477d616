import { appStrings } from "@Assets/app_strings";
import { store } from "@Store/store";

import { screenNamePageTitleKeyMapper } from "./screenNamePageTitleKeyMapper";
import { NavigationStateSlice } from "../store/navigationStateSlice";
import { ScreenName } from "../types/screenName";

export const updatePageTitle = (screenName: ScreenName) => {
  store.dispatch(
    NavigationStateSlice.actions.pageTitleChanged(appStrings.pageTitles[screenNamePageTitleKeyMapper[screenName]]),
  );
};
