import {
  CourseItem,
  VivanteLink,
  VivanteLinkExternal,
  VivanteLinkInternal,
  VivanteLinkInternalLinkDestination,
  VivanteLinkMeta,
  VivanteLinkPlatform,
  VivanteLinkResource,
  VivanteLinkResourceType,
  VivanteLinkType,
} from "@vivantehealth/vivante-core";
import Router from "next/router";

import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { Routes } from "@Types";
import { logger } from "@Utils/logger";

import { calculateCoursePath } from "../../courses/utils/courseState.utils";

export type ResolvedNavigationPath = string;

const isVivanteLinkType = <T extends VivanteLink>(
  link: VivanteLink,
  linkType: (typeof VivanteLinkType)[keyof typeof VivanteLinkType],
): link is T => {
  return link.type === linkType;
};

export const handleCourseItemNavigation = (courseItem: CourseItem): ResolvedNavigationPath | undefined => {
  if (!courseItem) {
    return undefined;
  }

  const courseItemNavigation: VivanteLink = vivanteCoreContainer
    .getCoursesUseCaseFactory()
    .createGetCourseItemNavigationUseCase()
    .execute(courseItem);

  const routeToPath = (path: string) => {
    if (path) {
      Router.push(path);
    }
  };

  if (isVivanteLinkType<VivanteLinkResource>(courseItemNavigation, VivanteLinkType.RESOURCE)) {
    routeToPath(handleResourceNavigation(courseItemNavigation) ?? "");
    return;
  }

  if (isVivanteLinkType<VivanteLinkInternal>(courseItemNavigation, VivanteLinkType.INTERNAL)) {
    routeToPath(handleInternalNavigation(courseItemNavigation) ?? "");
    return;
  }

  if (isVivanteLinkType<VivanteLinkMeta>(courseItemNavigation, VivanteLinkType.META)) {
    routeToPath(handleMetaNavigation(courseItemNavigation) ?? "");
    return;
  }

  return undefined;
};

const uriWithoutQueryParams = (uri: string) => uri.split("?")[0];

const handleArticleNavigation = (link: VivanteLinkResource, uri?: string): ResolvedNavigationPath | undefined => {
  if (!link.id || link.id === "articles") {
    return undefined;
  }

  if (link?.queryParams?.originatingScreen && uri) {
    const articleId = uriWithoutQueryParams(uri).split("articles/")[1];

    return `${Routes.ARTICLES}/${articleId}?originatingScreen=${link.queryParams.originatingScreen}`;
  }

  if (uri?.includes("category")) {
    return `${Routes.ARTICLE_CATEGORY}/${link.id}`;
  }

  return `${Routes.ARTICLES}/${link.id}`;
};

export const handleResourceNavigation = (
  link: VivanteLinkResource,
  uri?: string,
): ResolvedNavigationPath | undefined => {
  const resourceType = link.resource;

  switch (resourceType) {
    case VivanteLinkResourceType.ARTICLE: {
      return handleArticleNavigation(link, uri);
    }
    case VivanteLinkResourceType.COURSE: {
      if (!link.id || link.id === "courses") {
        return undefined;
      }

      return calculateCoursePath(link.id);
    }
    case VivanteLinkResourceType.SLIDE_SHOW: {
      if (!link.id) {
        return undefined;
      }

      return `${Routes.SLIDESHOW}/${link.id}`;
    }
    case VivanteLinkResourceType.TO_DO: {
      if (!link.id) {
        return undefined;
      }

      return `${Routes.TODO}/${link.id}`;
    }
    case VivanteLinkResourceType.WITCH: {
      if (!link.id) {
        return undefined;
      }

      return `${Routes.SURVEY}/${link.id}`;
    }
    case VivanteLinkResourceType.ORDER: {
      if (!link.id) {
        return undefined;
      }

      return `${Routes.ORDER}/${link.id}`;
    }
    case VivanteLinkResourceType.SURVEY: {
      // If the link has an id that is not new_survey, it should use the classic survey route instead of new_survey route
      // classic survey route uses path parameters: /survey/:id
      if (link.id && link.id !== "new_survey") {
        return `${Routes.SURVEY}/${link.id}`;
      }

      // new_survey route uses query parameters: /new_survey?code=:id
      return buildRouteWithQueryParams(Routes.NEW_SURVEY, link);
    }
    default: {
      return undefined;
    }
  }
};

export const handleInternalNavigation = (link: VivanteLinkInternal): ResolvedNavigationPath | undefined => {
  const { destination } = link;

  switch (destination) {
    case VivanteLinkInternalLinkDestination.CARE_TEAM: {
      return buildRouteWithQueryParams(Routes.CARE_TEAM, link);
    }
    case VivanteLinkInternalLinkDestination.GUT_CHECK_RESULTS: {
      return Routes.GUT_CHECK;
    }
    case VivanteLinkInternalLinkDestination.GUTCHECK: {
      return Routes.GUT_CHECK_NEW;
    }
    case VivanteLinkInternalLinkDestination.TRACKING: {
      return Routes.PROGRESS;
    }
    case VivanteLinkInternalLinkDestination.COURSES: {
      return Routes.COURSES;
    }
    case VivanteLinkInternalLinkDestination.SHIPPING_ADDRESS_FORM: {
      logger.warn("not yet implemented! to do -- return route to ShippingAddressForm");
      break;
    }
    case VivanteLinkInternalLinkDestination.REGISTER: {
      if (!link.extra) {
        return Routes.ACCESS_CODE;
      }

      return `${Routes.REGISTER}/${link.extra}`;
    }
    case VivanteLinkInternalLinkDestination.NURSE_TRIAGE: {
      return Routes.NURSE_TRIAGE;
    }
    case VivanteLinkInternalLinkDestination.SCHEDULE_APPOINTMENT: {
      return Routes.CARE_TEAM;
    }
    case VivanteLinkInternalLinkDestination.PROVIDER_SCHEDULING: {
      return `${Routes.APPOINTMENTS}?role=${link.queryParams?.role}`;
    }
    case VivanteLinkInternalLinkDestination.HOME: {
      return Routes.HOME;
    }
    case VivanteLinkInternalLinkDestination.REFERRAL: {
      return `${Routes.CARE_PLAN}/intervention/${link.queryParams?.code}`;
    }
    case VivanteLinkInternalLinkDestination.CARE_PLAN: {
      return Routes.CARE_PLAN;
    }
    case VivanteLinkInternalLinkDestination.SURVEY_LOADING: {
      return `${Routes.SURVEY_LOADING}/${link.queryParams?.linkedIntervention}`;
    }
    case VivanteLinkInternalLinkDestination.TRACKING_WITH_MENU:
    case VivanteLinkInternalLinkDestination.SYMPTOM_TRACKING: {
      return `${Routes.PROGRESS}?displayTrackingMenu=true`;
    }
    default: {
      break;
    }
  }
  return undefined;
};

const handleMetaNavigation = (link: VivanteLinkMeta): ResolvedNavigationPath | undefined => {
  logger.warn(link, "NOT YET IMPLEMENTED! - handleMetaNavigation");
  // yield call(this.mergeState, { currentLinkMeta: link.meta })

  // if (link.metaType === VivanteLinkMetaType.DIET_TIP) {
  //   this.modules.navigation.handleNavigate("DietTip")
  //   return
  // }

  // this.handleUnsupportedNavigation(JSON.stringify(link))
  return undefined;
};

const handlePlatformNavigation = (link: VivanteLinkPlatform): ResolvedNavigationPath | undefined => {
  logger.warn(link, "NOT YET IMPLEMENTED! - handlePlatformNavigation");

  return undefined;
};

const handleExternalNavigation = (link: VivanteLinkExternal): ResolvedNavigationPath | undefined => {
  logger.warn(link, "NOT YET IMPLEMENTED! - handleExternalNavigation");

  return undefined;
};

const handleUnsupportedNavigation = (details: string) => {
  const message = `Member tried to open unsupported link: ${details}`;

  logger.warn(message);
};

export const handleUriNavigation = (uri?: string): ResolvedNavigationPath | undefined => {
  if (!uri) {
    // means we have no subpath ("url": "https://mygithrive.com/")
    return Routes.HOME;
  }

  // custom matching to avoid core logic which would match
  // this route to /home due to having the keyword in query params
  if (uri.includes("goal-setting/welcome")) {
    return undefined;
  }

  const vivanteLink: VivanteLink = vivanteCoreContainer.getUriParseUseCase().execute(uri) as VivanteLinkResource;
  const linkType = vivanteLink.type;

  switch (linkType) {
    // Beyond each switch case, we know the specific (narrowed) type of VivanteLink, hence casting them with `as`
    case VivanteLinkType.RESOURCE: {
      return handleResourceNavigation(vivanteLink as VivanteLinkResource, uri);
    }
    case VivanteLinkType.INTERNAL: {
      return handleInternalNavigation(vivanteLink as unknown as VivanteLinkInternal);
    }
    case VivanteLinkType.PLATFORM: {
      return handlePlatformNavigation(vivanteLink as unknown as VivanteLinkPlatform);
    }
    case VivanteLinkType.EXTERNAL: {
      return handleExternalNavigation(vivanteLink as unknown as VivanteLinkExternal);
    }
    case VivanteLinkType.UNSUPPORTED: {
      handleUnsupportedNavigation(JSON.stringify(vivanteLink));
      break;
    }
    default: {
      break;
    }
  }
  return undefined;
};

const buildRouteWithQueryParams = (route: Routes, link: VivanteLinkInternal | VivanteLinkResource) => {
  const searchParams = new URLSearchParams();

  if (link?.queryParams) {
    const key = Object.keys(link?.queryParams)[0];

    searchParams.append(key, link?.queryParams[key]);

    return `${route}?${searchParams}`;
  }

  return route;
};
