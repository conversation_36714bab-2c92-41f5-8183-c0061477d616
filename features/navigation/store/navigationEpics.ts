import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { PayloadAction } from "@reduxjs/toolkit";
import { Epic, ofType } from "redux-observable";
import { Observable } from "rxjs";
import { switchMap } from "rxjs/operators";

import { NavigateContext, NavigationStateSlice } from "./navigationStateSlice";
import { createSimpleAnalyticsEpic as createEnterScreenEpic } from "../../analytics/store/analyticsEpics";

/**
 * Syntactic sugar for creating an Epic which listens to action dispatches of the supplied
 * `actionType`, and provides its payload to the supplied processor function. The processor
 * function outputs a `NavigateContext` which is used by the `navigateTo` action for the
 * purposes of navigating to the route described by the `NavigateContext`.
 *
 * Additionally, attributes in the `NavigateContext` are used by the navigation module for emitting
 * analytics events.
 * @param actionType
 * @param processor
 * @returns
 */
export const createNavigateToEpic = <PAYLOAD>(
  actionType: string,
  processor: (payload: PAYLOAD) => NavigateContext,
): Epic => {
  const navigateToArticleCategoryEpic: Epic = (actions$: Observable<PayloadAction<PAYLOAD>>) =>
    actions$.pipe(
      ofType(actionType),
      switchMap((action) => [NavigationStateSlice.actions.navigateTo(processor(action.payload))]),
    );

  return navigateToArticleCategoryEpic;
};

const analyticsEpics: Epic[] = [
  createEnterScreenEpic<NavigateContext>(NavigationStateSlice.actions.navigateTo.type, (payload) => ({
    eventType: ClickStreamActivityEventType.ENTER_SCREEN,
    currentScreen: payload?.screenName,
    activityContextExtra: payload?.activityContextExtra,
  })),
];

export const navigationEpics = [...analyticsEpics];
