import { ClickStreamActivityContextExtra } from "@vivantehealth/vivante-core";
import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import Router from "next/router";

import { appStrings } from "@Assets/app_strings";
import { loadHomeScreen } from "@Store/actions";
import { buildSliceStateSelector } from "@Utils/slice.util";

import { ScreenName } from "../types/screenName";
import { screenNamePageTitleKeyMapper } from "../utils/screenNamePageTitleKeyMapper";

export enum NavOptions {
  HOME = "Home",
  CARE_PLAN = "Care Plan",
  COURSES = "Courses",
  ARTICLES = "Articles",
  PROGRESS = "Progress",
  GUT_CHECK = "GutCheck",
  CARE_TEAM = "Care Team",
}

/// //////////////////////////////////////////////////////
/// state

export type NavigationState = Readonly<{
  activeNavOption: NavOptions;
  navDrawerOpen: boolean;
  currentScreenName: ScreenName | undefined;
  pageTitle: string;
}>;

export const initialState: NavigationState = {
  activeNavOption: NavOptions.HOME,
  navDrawerOpen: true,
  currentScreenName: undefined,
  pageTitle: appStrings.pageTitles.cylinder,
};

export type NavigateContext = Readonly<{
  path: string;
  screenName?: ScreenName;
  activityContextExtra?: ClickStreamActivityContextExtra;
}>;

/// //////////////////////////////////////////////////////
/// slice

export const NavigationStateSlice = createSlice({
  name: "navigationState",
  initialState,
  reducers: {
    setActiveNavOption: (state, action: PayloadAction<NavOptions>) => ({
      ...state,
      activeNavOption: action.payload,
    }),
    setNavDrawerOpen: (state, action: PayloadAction<boolean>) => ({ ...state, navDrawerOpen: action.payload }),
    navigateTo: (state: NavigationState, action: PayloadAction<NavigateContext>) => {
      const { path, screenName } = action.payload;

      Router.push(path);
      return {
        ...state,
        currentScreenName: screenName,
        // Check `screeNamePageTitleKeyMapper` for the key to update the tab title of the page
        // We update here to ensure that the title is updated when the user navigates to a new page
        pageTitle: appStrings.pageTitles[screenNamePageTitleKeyMapper[screenName ?? "Unmatched"]],
      };
    },
    pageTitleChanged: (state, action: PayloadAction<string>) => ({ ...state, pageTitle: action.payload }),
  },
  extraReducers: (builder) => {
    builder.addCase(loadHomeScreen, (state) => {
      state.activeNavOption = NavOptions.HOME;
    });
  },
});

/// //////////////////////////////////////////////////////
/// actions

export const { setActiveNavOption, setNavDrawerOpen } = NavigationStateSlice.actions;

/// //////////////////////////////////////////////////////
/// selectors

export const navigationStateSelector = buildSliceStateSelector("navigationState");

export const navigationStateReducer = NavigationStateSlice.reducer;
