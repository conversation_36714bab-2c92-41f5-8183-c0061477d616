import { DateTemplate } from "@vivantehealth/vivante-core/dist/domain/value-objects/date/date.util";
import Router from "next/router";

import {
  AppointmentAvailability,
  AppointmentsScreenType,
  AppointmentsState,
  initialState,
} from "../store/appointmentsStateSlice";

export const computeGoBack = (state: AppointmentsState) => {
  if (state.appointmentScreen === AppointmentsScreenType.CONFIRM_APPOINTMENT) {
    return {
      ...state,
      appointmentScreen: AppointmentsScreenType.SCHEDULE_APPOINTMENT,
      appointmentSelection: initialState.appointmentSelection,
    };
  }
  if (state.appointmentScreen === AppointmentsScreenType.SCHEDULE_APPOINTMENT) {
    return {
      ...state,
      appointmentScreen: AppointmentsScreenType.USA_STATE_SELECTION,
      availability: initialState.availability,
    };
  }

  return Router.back();
};

const isDateTemplate = (key: string): key is DateTemplate => {
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;

  return dateRegex.test(key);
};

export const findIndexForKeyWithNonEmptyArray = (obj: AppointmentAvailability | null) => {
  if (!obj) {
    return null;
  }

  const keys = Object.keys(obj);
  const foundIndex = keys.findIndex((key) => isDateTemplate(key) && obj[key].length > 0);

  return foundIndex;
};
