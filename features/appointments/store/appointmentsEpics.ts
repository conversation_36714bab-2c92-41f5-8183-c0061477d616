/* eslint-disable @typescript-eslint/no-unused-vars */
import { AppointmentUSState, PractitionerWithSlots } from "@vivantehealth/vivante-core";
import { Action, PayloadAction } from "@reduxjs/toolkit";
import { DateTemplate } from "@vivantehealth/vivante-core/dist/domain/value-objects/date/date.util";
import { Epic, StateObservable, ofType } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { catchError, map, switchMap, withLatestFrom } from "rxjs/operators";

import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { RootState } from "@Store/store";
import { Routes } from "@Types";

import { AppointmentAvailability, AppointmentsScreenType, AppointmentsStateSlice } from "./appointmentsStateSlice";
import { createNavigateToEpic } from "../../navigation/store/navigationEpics";

const {
  confirmAppointmentSelectionSuccess,
  confirmAppointmentSelectionFailure,
  loadPractitionersAvailability,
  loadPractitionersAvailabilitySuccess,
  loadPractitionersAvailabilityFailure,
  confirmAppointmentSelection,
  updateAppointmentScreen,
  updateAppointmentSelection,
  rescheduleAppointmentSelection,
  rescheduleAppointmentSelectionSuccess,
} = AppointmentsStateSlice.actions;

const isUsState = (key: string): key is keyof typeof AppointmentUSState => {
  return key in AppointmentUSState;
};

const loadPractitionersAvailabilityEpic: Epic = (
  actions$: Observable<PayloadAction<string>>,
  state$: StateObservable<RootState>,
) => {
  return actions$.pipe(
    ofType(loadPractitionersAvailability.type),

    withLatestFrom(state$),

    switchMap(([action, state]: [PayloadAction<string>, RootState]) => {
      const usState = isUsState(action.payload) ? AppointmentUSState[action.payload] : "";
      const { practitionerRole } = state.appointmentsState;

      if (usState === "") {
        throw new Error("Invalid US State");
      }

      return from(
        vivanteCoreContainer.getAppointmentsUseCaseFactory().createGetPractitionerAvailabilityUseCase().execute({
          usState,
          practitionerRole,
        }),
      ).pipe(
        switchMap((availability: Map<DateTemplate, PractitionerWithSlots[]>) => {
          const toSend: AppointmentAvailability = {};

          for (const [key, value] of availability) {
            toSend[key] = value;
          }

          return [
            loadPractitionersAvailabilitySuccess(toSend),
            updateAppointmentScreen(AppointmentsScreenType.SCHEDULE_APPOINTMENT),
          ];
        }),
        catchError((error) => {
          return of(loadPractitionersAvailabilityFailure(error));
        }),
      );
    }),
  );
};

const updateAppointmentSelectionEpic: Epic = (actions$: Observable<Action>) =>
  actions$.pipe(
    ofType(updateAppointmentSelection.type),
    map(() => updateAppointmentScreen(AppointmentsScreenType.CONFIRM_APPOINTMENT)),
  );

const confirmAppointmentSelectionEpic: Epic = (actions$: Observable<Action>, state$: StateObservable<RootState>) => {
  return actions$.pipe(
    ofType(confirmAppointmentSelection.type),
    withLatestFrom(state$),
    switchMap(([_, state]) => {
      const appointment = state?.appointmentsState.appointmentSelection;

      if (!appointment) {
        throw new Error("No appointment selected");
      }

      return from(
        vivanteCoreContainer
          .getAppointmentsUseCaseFactory()
          .createScheduleAppointmentUseCase()
          .execute({
            slot: {
              start: new Date(appointment.slot.start),
              end: new Date(appointment.slot.end),
            },
            practitionerId: appointment.practitioner.id,
            practitionerType: appointment.practitioner.type,
            communicationMethod: appointment.communicationMethod,
            requestedRole: state?.appointmentsState.practitionerRole,
          }),
      ).pipe(
        map(() => confirmAppointmentSelectionSuccess()),
        catchError((error) => {
          return of(confirmAppointmentSelectionFailure(error));
        }),
      );
    }),
  );
};

const rescheduleAppointmentSelectionEpic: Epic = (actions$: Observable<Action>, state$: StateObservable<RootState>) => {
  return actions$.pipe(
    ofType(rescheduleAppointmentSelection.type),
    withLatestFrom(state$),
    switchMap(([_, state]) => {
      const appointment = state?.appointmentsState.appointmentSelection;
      const { appointmentToReschedule } = state.appointmentsState;

      if (!appointment) {
        throw new Error("No appointment selected");
      }

      if (!appointmentToReschedule) {
        throw new Error("No appointment to reschedule");
      }

      return from(
        vivanteCoreContainer
          .getAppointmentsUseCaseFactory()
          .createRescheduleAppointmentUseCase()
          .execute({
            originalAppointment: appointmentToReschedule,
            newSlot: {
              start: new Date(appointment.slot.start),
              end: new Date(appointment.slot.end),
            },
            newPractitionerId: appointment.practitioner.id,
            newPractitionerType: appointment.practitioner.type,
            newCommunicationMethod: appointment.communicationMethod,
          }),
      ).pipe(
        map(() => rescheduleAppointmentSelectionSuccess()),
        catchError((error) => {
          return of(confirmAppointmentSelectionFailure(error));
        }),
      );
    }),
  );
};

const confirmAppointmentSelectionSuccessEpic = createNavigateToEpic(confirmAppointmentSelectionSuccess.type, () => ({
  path: Routes.CARE_TEAM,
  screenName: "CareTeam",
}));

const rescheduleAppointmentSelectionSuccessEpic = createNavigateToEpic(
  rescheduleAppointmentSelectionSuccess.type,
  () => ({
    path: Routes.CARE_TEAM,
    screenName: "CareTeam",
  }),
);

export const appointmentsEpics = [
  updateAppointmentSelectionEpic,
  loadPractitionersAvailabilityEpic,
  confirmAppointmentSelectionEpic,
  confirmAppointmentSelectionSuccessEpic,
  rescheduleAppointmentSelectionEpic,
  rescheduleAppointmentSelectionSuccessEpic,
];
