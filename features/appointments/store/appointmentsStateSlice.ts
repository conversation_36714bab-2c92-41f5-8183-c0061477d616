/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  Appointment,
  AppointmentCommunicationMethod,
  PractitionerWithSlots,
  SessionSlot,
  VivanteApiError,
} from "@vivantehealth/vivante-core";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { DateTemplate } from "@vivantehealth/vivante-core/dist/domain/value-objects/date/date.util";

import { LoadState } from "@Types";
import { buildSliceStateSelector, processError } from "@Utils/slice.util";

import { computeGoBack } from "../utils/appointments.util";

export enum AppointmentsScreenType {
  USA_STATE_SELECTION = "usaStateSelection",
  SCHEDULE_APPOINTMENT = "scheduleAppointment",
  CONFIRM_APPOINTMENT = "confirmAppointment",
}

export type AppointmentAvailability = Record<DateTemplate, PractitionerWithSlots[]>;

export type AppointmentSelection = Readonly<{
  slot: SessionSlot;
  practitioner: PractitionerWithSlots;
  communicationMethod: AppointmentCommunicationMethod;
  requestedRole?: string;
}>;

/// //////////////////////////////////////////////////////
/// state

export type AppointmentsState = Readonly<{
  loadState: LoadState;
  appointmentScreen: AppointmentsScreenType;
  practitionerRole: string;
  availability: AppointmentAvailability | null;
  appointmentSelection: AppointmentSelection | null;
  appointmentToReschedule: Appointment | null;
}>;

export const initialState: AppointmentsState = {
  loadState: null,
  appointmentScreen: AppointmentsScreenType.USA_STATE_SELECTION,
  practitionerRole: "gi",
  availability: null,
  appointmentSelection: null,
  appointmentToReschedule: null,
};

/// //////////////////////////////////////////////////////
/// slice

export const AppointmentsStateSlice = createSlice({
  name: "appointmentsState",
  initialState,
  reducers: {
    loadUsaStateSelection: (state, action: PayloadAction<string>) => ({
      ...state,
      loadState: "loaded",
      practitionerRole: action.payload,
    }),
    loadPractitionersAvailability: (state, _: PayloadAction<string>) => ({ ...state, loadState: "loading" }),
    updateAppointmentScreen: (state, action: PayloadAction<AppointmentsScreenType>) => ({
      ...state,
      appointmentScreen: action.payload,
    }),
    loadPractitionersAvailabilitySuccess: (state, action: PayloadAction<AppointmentAvailability>) => ({
      ...state,
      loadState: "loaded",
      availability: action.payload,
    }),
    loadPractitionersAvailabilityFailure: (state, action: PayloadAction<Error | VivanteApiError>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
    updateAppointmentSelection: (state, action: PayloadAction<AppointmentSelection>) => ({
      ...state,
      appointmentSelection: action.payload,
    }),
    confirmAppointmentSelection: (state) => ({ ...state, loadState: "loading" }),
    confirmAppointmentSelectionSuccess: (state) => state,
    confirmAppointmentSelectionFailure: (state, action: PayloadAction<Error | VivanteApiError>) => {
      processError({ error: action.payload });

      return { ...state, loadState: "failure" };
    },
    setAppointmentToReschedule: (state, action: PayloadAction<Appointment>) => ({
      ...state,
      appointmentToReschedule: action.payload,
    }),
    rescheduleAppointmentSelection: (state) => ({ ...state, loadState: "loading" }),
    rescheduleAppointmentSelectionSuccess: (state) => ({
      ...state,
      appointmentToReschedule: initialState.appointmentToReschedule,
    }),
    goBack: (state: AppointmentsState) => {
      const updatedState = computeGoBack(state);

      return updatedState;
    },
    teardown: () => ({ ...initialState }),
  },
});

/// //////////////////////////////////////////////////////
/// actions

/// //////////////////////////////////////////////////////
/// selectors

export const appointmentsSelector = buildSliceStateSelector("appointmentsState");

export const appointmentsStateReducer = AppointmentsStateSlice.reducer;
