import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { PractitionerWithSlots, SessionSlot } from "@vivantehealth/vivante-core";

import { AppointmentsStateSlice, appointmentsSelector } from "@Features/appointments/store/appointmentsStateSlice";
import { findIndexForKeyWithNonEmptyArray } from "@Features/appointments/utils/appointments.util";
import { memberStateSelector } from "@Features/member/store/memberStateSlice";

import { AppointmentsScreen } from "./components/AppointmentsScreen";

const {
  teardown,
  loadUsaStateSelection,
  loadPractitionersAvailability,
  updateAppointmentSelection,
  confirmAppointmentSelection,
  rescheduleAppointmentSelection,
  goBack,
} = AppointmentsStateSlice.actions;

type AppointmentsScreenContainerProps = Readonly<{
  practitionerRole: string;
}>;

export const AppointmentsScreenContainer = ({ practitionerRole }: AppointmentsScreenContainerProps) => {
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(loadUsaStateSelection(practitionerRole));
  }, [dispatch, practitionerRole]);

  useEffect(() => {
    return () => {
      dispatch(teardown());
    };
  }, [dispatch]);

  const appointmentScreen = useSelector(appointmentsSelector("appointmentScreen"));

  const memberUsState = useSelector(memberStateSelector("member"))?.usState || "AL";
  const loading = useSelector(appointmentsSelector("loadState")) === "loading";
  const availability = useSelector(appointmentsSelector("availability"));
  const appointmentToReschedule = useSelector(appointmentsSelector("appointmentToReschedule"));
  const defaultPosition = findIndexForKeyWithNonEmptyArray(availability);

  const appointmentSelection = useSelector(appointmentsSelector("appointmentSelection"));
  const submitStateCallback = (selectedState: string) => {
    dispatch(loadPractitionersAvailability(selectedState));
  };

  const onSlotSelectionCallback = (appointment: { slot: SessionSlot; practitioner: PractitionerWithSlots }) => {
    dispatch(
      updateAppointmentSelection({
        slot: appointment.slot,
        practitioner: appointment.practitioner,
        communicationMethod: "VIDEO",
      }),
    );
  };

  const onConfirmationCallback = () => {
    if (appointmentToReschedule) {
      dispatch(rescheduleAppointmentSelection());
      return;
    }

    dispatch(confirmAppointmentSelection());
  };
  const onBackCallback = () => {
    dispatch(goBack());
  };

  return (
    <AppointmentsScreen
      loading={loading}
      appointmentScreen={appointmentScreen}
      memberUsState={memberUsState}
      submitStateCallback={submitStateCallback}
      availability={availability}
      onSlotSelectionCallback={onSlotSelectionCallback}
      appointmentSelection={appointmentSelection}
      onConfirmationCallback={onConfirmationCallback}
      onBackCallback={onBackCallback}
      defaultPosition={defaultPosition}
    />
  );
};
