import { Box, Paper } from "@mui/material";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { SPACING_32_PX, SPACING_16_PX } from "@Assets/style_constants";
import { AppointmentDuration } from "@Components/AppointmentDuration/AppointmentDuration";
import { AppointmentGetReady } from "@Components/AppointmentGetReady/AppointmentGetReady";
import { AppointmentInformation } from "@Components/AppointmentInformation/AppointmentInformation";
import { GutCheckResultsNote } from "@Components/GutCheckResultsNote/GutCheckResultsNote";
import { PractitionerInformation } from "@Components/PractitionerInformation/PractitionerInformation";
import { AppointmentSelection } from "@Features/appointments/store/appointmentsStateSlice";

import { AppointmentsFooter } from "./AppointmentsFooter";
import { AppointmentsHeader } from "./AppointmentsHeader";

const SCHEDULING_STRINGS = appStrings.features.scheduling;

type AppointmentsConfirmProps = Readonly<{
  appointmentSelection: AppointmentSelection;
  onConfirmationCallback: () => void;
  onBackCallback: () => void;
}>;
export const AppointmentsConfirm = ({
  appointmentSelection,
  onConfirmationCallback,
  onBackCallback,
}: AppointmentsConfirmProps) => {
  const { practitioner } = appointmentSelection;
  const slotDuration = dayjs(appointmentSelection.slot.end).diff(dayjs(appointmentSelection.slot.start), "minute");

  return (
    <>
      <AppointmentsHeader header={SCHEDULING_STRINGS.confirmation.header} onBackClick={onBackCallback} />
      <Paper sx={{ mb: SPACING_32_PX }}>
        <Box display="flex" justifyContent="space-between" alignItems="start">
          <PractitionerInformation practitioner={practitioner} />
          <AppointmentDuration duration={slotDuration} />
        </Box>
        <Box mt={SPACING_16_PX}>
          <AppointmentInformation
            startTime={appointmentSelection.slot.start}
            communicationMethod={appointmentSelection.communicationMethod}
          />
        </Box>
      </Paper>

      <AppointmentGetReady headingSize="h3" />

      <GutCheckResultsNote />

      <AppointmentsFooter
        shouldDisplayDisclaimer
        buttonText={appStrings.buttonText.confirmAppointment}
        onSubmitCallback={onConfirmationCallback}
        minWidth={224}
      />
    </>
  );
};
