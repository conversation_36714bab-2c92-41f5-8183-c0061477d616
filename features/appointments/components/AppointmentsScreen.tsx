import { PractitionerWithSlots, SessionSlot } from "@vivantehealth/vivante-core";

import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import {
  AppointmentAvailability,
  AppointmentSelection,
  AppointmentsScreenType,
} from "@Features/appointments/store/appointmentsStateSlice";

import { AppointmentsConfirm } from "./AppointmentsConfirm";
import { AppointmentsSchedule } from "./AppointmentsSchedule";
import { AppointmentsStateSelection } from "./AppointmentsStateSelection";

type AppointmentsScreenProps = Readonly<{
  appointmentScreen: AppointmentsScreenType;
  memberUsState: string;
  loading: boolean;
  availability: AppointmentAvailability | null;
  appointmentSelection: AppointmentSelection | null;
  defaultPosition: number | null;
  submitStateCallback: (selectedState: string) => void;
  onSlotSelectionCallback: (appointment: { slot: SessionSlot; practitioner: PractitionerWithSlots }) => void;
  onConfirmationCallback: () => void;
  onBackCallback: () => void;
}>;

export const AppointmentsScreen = ({
  appointmentScreen,
  memberUsState,
  loading,
  availability,
  appointmentSelection,
  defaultPosition,
  submitStateCallback,
  onSlotSelectionCallback,
  onConfirmationCallback,
  onBackCallback,
}: AppointmentsScreenProps) => {
  const isScheduleAppointment =
    appointmentScreen === AppointmentsScreenType.SCHEDULE_APPOINTMENT &&
    availability &&
    defaultPosition != null &&
    defaultPosition >= 0;

  return (
    <>
      <LoadingSpinner open={loading} />
      {appointmentScreen === AppointmentsScreenType.USA_STATE_SELECTION && memberUsState && (
        <AppointmentsStateSelection
          onBackCallback={onBackCallback}
          submitStateCallback={submitStateCallback}
          memberUsState={memberUsState}
        />
      )}
      {isScheduleAppointment && (
        <AppointmentsSchedule
          onBackCallback={onBackCallback}
          availability={availability}
          onSlotSelectionCallback={onSlotSelectionCallback}
          defaultPosition={defaultPosition}
        />
      )}
      {appointmentScreen === AppointmentsScreenType.CONFIRM_APPOINTMENT && appointmentSelection && (
        <AppointmentsConfirm
          onBackCallback={onBackCallback}
          appointmentSelection={appointmentSelection}
          onConfirmationCallback={onConfirmationCallback}
        />
      )}
    </>
  );
};
