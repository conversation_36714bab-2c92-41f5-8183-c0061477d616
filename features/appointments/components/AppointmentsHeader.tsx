import { Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { SPACING_56_PX, SPACING_32_PX } from "@Assets/style_constants";
import { BackButton } from "@Components/BackButton/BackButton";

export const AppointmentsHeader = ({ header, onBackClick }: { header: string; onBackClick: () => void }) => {
  return (
    <>
      <BackButton onClick={onBackClick}>{appStrings.buttonText.back}</BackButton>
      <Typography variant="h2Serif" mt={SPACING_56_PX} mb={SPACING_32_PX}>
        {header}
      </Typography>
    </>
  );
};
