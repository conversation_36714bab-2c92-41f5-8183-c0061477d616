import { Box, Typography, Button } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { SPACING_24_PX, SPACING_40_PX } from "@Assets/style_constants";

const SCHEDULING_STRINGS = appStrings.features.scheduling;

type AppointmentsFooterProps = Readonly<{
  shouldDisplayDisclaimer: boolean;
  buttonText?: string;
  minWidth?: number;
  onSubmitCallback?: () => void;
}>;

export const AppointmentsFooter = ({
  shouldDisplayDisclaimer,
  buttonText,
  minWidth,
  onSubmitCallback,
}: AppointmentsFooterProps) => {
  return (
    <Box
      display="flex"
      position="absolute"
      alignItems="center"
      justifyContent="center"
      bottom={0}
      left={0}
      py={SPACING_24_PX}
      bgcolor={color.background.surface.primary}
      width="100%"
    >
      {shouldDisplayDisclaimer ? (
        <Typography variant="bodyDense" mr={SPACING_40_PX}>
          {SCHEDULING_STRINGS.disclaimer}
        </Typography>
      ) : null}
      {onSubmitCallback ? (
        <Button variant="primary" onClick={onSubmitCallback} sx={{ minWidth: minWidth || "200px" }}>
          {buttonText ?? ""}
        </Button>
      ) : null}
    </Box>
  );
};
