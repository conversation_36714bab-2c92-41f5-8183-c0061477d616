import { useState } from "react";
import { AppointmentUSState } from "@vivantehealth/vivante-core";
import { KeyboardArrowDown } from "@mui/icons-material";
import { Select, MenuItem, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { SPACING_32_PX, SPACING_24_PX } from "@Assets/style_constants";

import { AppointmentsFooter } from "./AppointmentsFooter";
import { AppointmentsHeader } from "./AppointmentsHeader";

const SCHEDULING_STRINGS = appStrings.features.scheduling;

type AppointmentsStateSelectionProps = Readonly<{
  memberUsState: string;
  submitStateCallback: (selectedState: string) => void;
  onBackCallback: () => void;
}>;

export const AppointmentsStateSelection = ({
  memberUsState,
  submitStateCallback,
  onBackCallback,
}: AppointmentsStateSelectionProps) => {
  const [selectedState, setSelectedState] = useState<string>(memberUsState);

  const stateKeys = Object.keys(AppointmentUSState);

  return (
    <>
      <AppointmentsHeader header={SCHEDULING_STRINGS.stateConfirmationHeader} onBackClick={onBackCallback} />
      <Select
        onChange={(e) => setSelectedState(e.target.value)}
        IconComponent={KeyboardArrowDown}
        fullWidth
        defaultValue={memberUsState}
      >
        {Object.values(AppointmentUSState).map((stateName, stateIndex) => {
          return (
            <MenuItem value={stateKeys[stateIndex]} key={stateName}>
              {stateName}
            </MenuItem>
          );
        })}
      </Select>
      <Typography variant="body" mt={SPACING_32_PX} mb={SPACING_24_PX}>
        {SCHEDULING_STRINGS.locationNote}
      </Typography>
      <Typography variant="body">{SCHEDULING_STRINGS.medicalTeamNote}</Typography>
      <AppointmentsFooter
        shouldDisplayDisclaimer={false}
        buttonText={appStrings.buttonText.next}
        onSubmitCallback={() => submitStateCallback(selectedState)}
      />
    </>
  );
};
