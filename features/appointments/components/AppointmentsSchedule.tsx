import { useState } from "react";
import { SessionSlot, PractitionerWithSlots } from "@vivantehealth/vivante-core";
import { Box, Typography, Paper, Chip } from "@mui/material";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { SPACING_12_PX, SPACING_16_PX, SPACING_32_PX, SPACING_8_PX } from "@Assets/style_constants";
import { AppointmentDuration } from "@Components/AppointmentDuration/AppointmentDuration";
import { OutlinedIconButton } from "@Components/OutlinedIconButton/OutlinedIconButton";
import { PractitionerInformation } from "@Components/PractitionerInformation/PractitionerInformation";
import { AppointmentAvailability } from "@Features/appointments/store/appointmentsStateSlice";

import { AppointmentsHeader } from "./AppointmentsHeader";

const SCHEDULING_STRINGS = appStrings.features.scheduling;

type AppointmentsScheduleProps = Readonly<{
  availability: AppointmentAvailability;
  defaultPosition: number;
  onSlotSelectionCallback: (appointment: { slot: SessionSlot; practitioner: PractitionerWithSlots }) => void;
  onBackCallback: () => void;
}>;

export const AppointmentsSchedule = ({
  availability,
  defaultPosition,
  onSlotSelectionCallback,
  onBackCallback,
}: AppointmentsScheduleProps) => {
  const [pos, setPos] = useState(defaultPosition);
  const size = Object.keys(availability).length;
  const next = () => {
    setPos(Math.min(size, pos + 1));
  };
  const previous = () => {
    setPos(Math.max(0, pos - 1));
  };
  const currentDate = Object.keys(availability)[pos];
  const currentDateBlock: PractitionerWithSlots[] = Object.values(availability)[pos];

  return (
    <>
      <AppointmentsHeader header={SCHEDULING_STRINGS.chooseAppointmentTime} onBackClick={onBackCallback} />

      <Box mb={SPACING_32_PX} display="flex" alignItems="center" justifyContent="space-between">
        <OutlinedIconButton
          icon="LeftChevron"
          onClick={previous}
          ariaLabel={SCHEDULING_STRINGS.ariaLabels.previousDay}
        />

        <Box width="254px" textAlign="center">
          <Typography variant="h4">{dayjs(currentDate).format("dddd, MMMM D")}</Typography>
        </Box>

        <OutlinedIconButton icon="RightChevron" onClick={next} ariaLabel={SCHEDULING_STRINGS.ariaLabels.nextDay} />
      </Box>

      {!currentDateBlock || currentDateBlock?.length < 1 ? (
        <Box textAlign="center">
          <Typography variant="body">{SCHEDULING_STRINGS.noAvailableAppointments}</Typography>
        </Box>
      ) : (
        currentDateBlock?.map((practitioner) => {
          const slots = practitioner.availableSlots.map((n) => ({
            start: n.start.getTime(),
            end: n.end.getTime(),
          }));

          const appointmentDuration = Math.abs(
            practitioner.availableSlots[0].start.getMinutes() - practitioner.availableSlots[0].end.getMinutes(),
          );

          return (
            <Paper key={practitioner.id}>
              <Box display="flex" justifyContent="space-between" alignItems="start" mb={SPACING_16_PX}>
                <PractitionerInformation practitioner={practitioner} />
                <AppointmentDuration duration={appointmentDuration} />
              </Box>

              <Box display="flex" rowGap={SPACING_8_PX} columnGap={SPACING_12_PX} flexWrap="wrap">
                {slots.map((slot) => (
                  <Chip
                    variant="inactive"
                    label={dayjs(slot.start).format("h:mm a")}
                    onClick={() => onSlotSelectionCallback({ slot, practitioner })}
                    key={slot.start}
                  />
                ))}
              </Box>
            </Paper>
          );
        })
      )}
    </>
  );
};
