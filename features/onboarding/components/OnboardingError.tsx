import { Paper, Typography, Box, Button } from "@mui/material";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { HavingTrouble } from "@Components/HavingTrouble/HavingTrouble";
import { AuthenticationLayout } from "@Features/authentication/components/AuthenticationLayout";
import { useSignOutHook } from "@Hooks/signOutHook";
import { Routes } from "@Types";
// Matches Figma design from SSO error screen
const ONBOARDING_ERROR_WIDTH = "448px";
const ONBOARDING_ERROR_STRINGS = appStrings.features.onboarding;

export const OnboardingError = () => {
  const { signOutOfApp } = useSignOutHook();
  const router = useRouter();

  const handleLogoutAndRedirect = () => {
    signOutOfApp();
    /**
     * Add in a timeout to ensure the sign out action completes before redirecting
     * as the sign out action typically redirects to Welcome screen
     */
    setTimeout(() => router.push(Routes.LOGIN), 1);
  };

  return (
    <AuthenticationLayout>
      <Paper sx={{ width: ONBOARDING_ERROR_WIDTH, display: "flex", flexDirection: "column", gap: 2, mt: 6 }}>
        <Typography variant="h3">{ONBOARDING_ERROR_STRINGS.errorHeader}</Typography>

        <Typography variant="body">{ONBOARDING_ERROR_STRINGS.errorSubHeader}</Typography>

        <HavingTrouble />

        <Box mt={4}>
          <Button variant="primary" onClick={handleLogoutAndRedirect} fullWidth>
            {appStrings.buttonText.logOut}
          </Button>
        </Box>
      </Paper>
    </AuthenticationLayout>
  );
};
