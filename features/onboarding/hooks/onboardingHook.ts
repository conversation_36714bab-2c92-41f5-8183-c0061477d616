import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";

import { useLazyGetUserStatusQuery } from "@Features/authentication/api/registrationApi";
import {
  isEligibilityProcessLoading,
  selectEligibilityProcessState,
  selectEligibilityProcessSubmitFormLoadState,
  selectIsEligible,
  loadEligibilityProcessState,
  updateEligibilityDataV2,
  selectRegistrationVersion,
  selectIsRegistered,
  updateRegistrationConfig,
} from "@Features/eligibility/store/eligibilityProcessStateSlice";
import { memberStateSelector } from "@Features/member/store/memberStateSlice";
import { Routes } from "@Types";
import { getReturnUrl } from "@Utils/getReturnUrl";

import { useFirebaseAuth } from "../../../hooks/firebaseAuthHook";

const eligibilityPaths: string[] = [Routes.ELIGIBILITY, Routes.ELIGIBILITY_HELP];

/**
 *
 * @param unauthenticatedUserRedirectRoute
 * Optional: Used to redirect to a route other than Welcome for an unauthenticated user
 * The main use case for this is when a user is trying to create an account but after filling in the eligibility form,
 * they are informed they have an account and are given the Log in CTA.
 */
export const useOnboardingHook = (unauthenticatedUserRedirectRoute?: Routes) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [displayUserStatusError, setDisplayUserStatusError] = useState(false);
  const isMemberLoading = useSelector(memberStateSelector("loadState")) === "loading";
  const member = useSelector(memberStateSelector("member"));
  const needsOnboarding = member?.setting?.onboardingPending;
  // for registration v2 flow
  const isRegistrationV2 = useSelector(selectRegistrationVersion);
  const [invokeGetUserEligibilityStatus] = useLazyGetUserStatusQuery();

  const isEligibilityLoading = useSelector(isEligibilityProcessLoading);
  const eligibilityState = useSelector(selectEligibilityProcessState);
  const submissionLoadState = useSelector(selectEligibilityProcessSubmitFormLoadState);
  const isEligible = useSelector(selectIsEligible);
  const isRegistered = useSelector(selectIsRegistered);
  const { isAuthenticated, isFirebaseAuthLoading } = useFirebaseAuth();
  const isSubmissionLoading = submissionLoadState === "loading";
  const [authCheckDone, setAuthCheckDone] = useState(false);
  const eligibilityCheckLoading = [
    isMemberLoading,
    isEligibilityLoading,
    isFirebaseAuthLoading,
    !authCheckDone,
    !eligibilityState && isAuthenticated,
  ].includes(true);

  // check authentication
  useEffect(() => {
    if (!isFirebaseAuthLoading) {
      if (!isAuthenticated) {
        router.push(unauthenticatedUserRedirectRoute ?? Routes.WELCOME);
      } else {
        setAuthCheckDone(true);
      }
    }
  }, [isAuthenticated, isFirebaseAuthLoading, unauthenticatedUserRedirectRoute, router]);

  useEffect(() => {
    // When integrating feature flag for the new API, we can uncomment this.
    async function getUserStatus() {
      const response = await invokeGetUserEligibilityStatus();

      if (!response.isSuccess) {
        return setDisplayUserStatusError(true);
      }

      const {
        registration_v2: isRegistrationV2,
        registration_details,
        registration_status,
      } = response.data.data.attributes;
      const passiveV2EligibilityCheck = !!registration_details?.passive_v2_eligibility_check;

      /**
       * We should follow the v1 registration flow going through the eligibility process api instead of BFF
       * If registrationV2 === false, always go v1 registration flow OR registrationV2 === true && passiveV2EligibilityCheck === true
       * This is a result of having to use a single client access code for both v1 and v2 registration flows
       */
      if (!isRegistrationV2 || (isRegistrationV2 && passiveV2EligibilityCheck)) {
        /**
         * Update the registration config to ensure we have the passive_v2_eligibility_check flag to use in
         * the submission of the eligibility form even if the user has left the registration flow and returned to it
         */
        dispatch(
          updateRegistrationConfig({
            accessCode: registration_details?.registration_code,
            registration_v2: isRegistrationV2,
            passiveV2EligibilityCheck,
          }),
        );
        dispatch(loadEligibilityProcessState());
        return;
      }

      dispatch(
        updateEligibilityDataV2({
          accessCode: registration_details?.registration_code,
          submissionForm: registration_details?.registration_form_schema,
          registration_v2: isRegistrationV2,
          isRegistered: registration_status === "registered",
        }),
      );
    }

    if (isAuthenticated) {
      if (!eligibilityState && !isSubmissionLoading) {
        getUserStatus();
      }
    }
  }, [
    dispatch,
    eligibilityState,
    isSubmissionLoading,
    isAuthenticated,
    invokeGetUserEligibilityStatus,
    isRegistrationV2,
    router,
  ]);

  // handle routing
  useEffect(() => {
    const isOnboardingPath = router.pathname === Routes.ONBOARDING;
    const isEligibilityPath = eligibilityPaths.includes(router.pathname);

    if (eligibilityState && !eligibilityCheckLoading) {
      if (isEligible && !needsOnboarding) {
        getReturnUrl(router);
        return;
      }
      // To support both v1 and v2 flows, we check the isEligible flag (v1) and isRegistered flag (v2)
      if (!isEligible && !isRegistered) {
        if (!isEligibilityPath) {
          router.push(Routes.ELIGIBILITY);
        }

        return;
      }

      if (needsOnboarding && !isOnboardingPath) {
        router.push(Routes.ONBOARDING);
      }
    }
  }, [eligibilityCheckLoading, eligibilityState, isEligible, isRegistered, needsOnboarding, router]);

  return {
    eligibilityCheckLoading,
    isEligible,
    needsOnboarding,
    eligibilityState,
    displayUserStatusError,
  };
};
