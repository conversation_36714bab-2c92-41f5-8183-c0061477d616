import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";

import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { ActionPlansStateSlice, selectInterventions } from "@Features/carePlan/store/actionPlansStateSlice";
import { handleUriNavigation } from "@Features/navigation/utils/navigation.util";
import { useOnboardingHook } from "@Features/onboarding/hooks/onboardingHook";
import { ToDosStateSlice, toDosSelectors } from "@Features/toDos/store/toDosStateSlice";
import { Routes } from "@Types";

import { OnboardingError } from "./components/OnboardingError";

export const OnboardingContainer = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const interventions = useSelector(selectInterventions);
  const todos = useSelector(toDosSelectors("todos"));
  const { eligibilityCheckLoading, displayUserStatusError } = useOnboardingHook();
  const [loadingToDos, setLoadingToDos] = useState(false);
  const [loadingData, setLoadingData] = useState(false);
  const [loadingUrl, setLoadingUrl] = useState(false);

  if (!todos && !loadingToDos && !eligibilityCheckLoading) {
    setLoadingToDos(true);
    dispatch(ToDosStateSlice.actions.loadToDos());
  }

  if (todos) {
    if (todos.length > 0 && todos[0]?.witchId) {
      router.push(`${Routes.TODO}/${todos[0]?.id}`);
      return null;
    }

    if (loadingToDos) {
      setLoadingToDos(false);
    }
  }

  if (todos?.length === 0 && !loadingData && interventions?.length === 0 && !eligibilityCheckLoading) {
    setLoadingData(true);
    dispatch(ActionPlansStateSlice.actions.loadActionPlans());
  }

  if (interventions && interventions.length > 0 && !loadingUrl) {
    setLoadingUrl(true);
    const url = handleUriNavigation(interventions[0]?.action?.uri);

    if (url) {
      router.push(url);
    }

    return null;
  }

  if (displayUserStatusError) {
    return <OnboardingError />;
  }

  return <LoadingSpinner open overlayDrawer overlayHeader />;
};
