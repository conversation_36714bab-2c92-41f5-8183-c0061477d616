import {
  Course,
  DisplayableVivanteException,
  Enrollment,
  EnrollmentState,
  VivanteApiError,
  VivanteException,
} from "@vivantehealth/vivante-core";
import { Action, PayloadAction } from "@reduxjs/toolkit";
import { Epic, StateObservable, ofType } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { switchMap, map, catchError, concatMap, withLatestFrom, filter } from "rxjs/operators";

import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { RootState } from "@Store/store";
import { Routes } from "@Types";

import { CoursesStateSlice, DisableCourseReroute, UnenrollRequest } from "./coursesStateSlice";
import { createNavigateToEpic } from "../../navigation/store/navigationEpics";
import { CategorizedCourses, calculateCoursesListPath } from "../utils/courseState.utils";

const {
  completeCourseItemFailure,
  completeCourseItemSuccess,
  completeCourseItem,
  enrollCourse,
  enrollCourseFailure,
  loadCourse,
  loadCourseFailure,
  loadCourseSuccess,
  loadCourses,
  loadCoursesFailure,
  loadCoursesSuccess,
  loadEnrollment,
  loadEnrollmentSuccess,
  loadEnrollmentFailed,
  navigateToCourse,
  navigateToCourseCategory,
  skipCourseItem,
  skipCourseItemFailure,
  skipCourseItemSuccess,
  unenrollCourse,
  unenrollCourseFailure,
} = CoursesStateSlice.actions;

const loadCoursesEpic: Epic = (actions$: Observable<PayloadAction<string>>) => {
  return actions$.pipe(
    ofType(loadCourses.type),

    switchMap((action: PayloadAction<string>) => {
      return from(vivanteCoreContainer.getCoursesUseCaseFactory().createGetCoursesUseCase().execute()).pipe(
        map((courses: Course[]) =>
          loadCoursesSuccess({
            courses,
            courseCategoryId: action?.payload,
          }),
        ),
        catchError((error) => {
          return of(loadCoursesFailure(error));
        }),
      );
    }),
  );
};

const loadCourseEpic: Epic = (actions$: Observable<PayloadAction<string>>) => {
  return actions$.pipe(
    ofType(loadCourse.type),

    // load the course
    switchMap((action: PayloadAction<string>) => {
      const courseId = action.payload;

      return from(
        vivanteCoreContainer
          .getCoursesUseCaseFactory()
          .createGetCourseByIdUseCase()
          .execute(courseId ?? ""),
      ).pipe(
        // load corresponding enrollment
        switchMap((course: Course) => {
          if (!course.id) {
            return of(loadCourseFailure(new Error("Failed to load course")));
          }
          if (!course.currentEnrollmentId) {
            return of(
              loadCourseSuccess({
                course,
              }),
            );
          }

          return from(
            vivanteCoreContainer
              .getCoursesUseCaseFactory()
              .createGetEnrollmentByIdUseCase()
              .execute(course.currentEnrollmentId),
          ).pipe(
            map((enrollment: Enrollment) =>
              loadCourseSuccess({
                course,
                enrollment,
              }),
            ),
            catchError((error: Error | VivanteApiError) => {
              return of(loadCourseFailure(error));
            }),
          );
        }),
      );
    }),
  );
};

const enrollCourseEpic: Epic = (actions$: Observable<PayloadAction<Course>>) => {
  return actions$.pipe(
    ofType(enrollCourse.type),

    // enroll the course
    concatMap((action: PayloadAction<Course>) => {
      const course = action.payload;

      return from(
        vivanteCoreContainer.getCoursesUseCaseFactory().createEnrollToCourseUseCase().execute(course.id),
      ).pipe(
        map(() => {
          return loadCourse(course.id);
        }),
        catchError((error: VivanteException | DisplayableVivanteException) => {
          return of(enrollCourseFailure(error));
        }),
      );
    }),
  );
};

const unenrollCourseEpic: Epic = (actions$: Observable<PayloadAction<UnenrollRequest>>) => {
  return actions$.pipe(
    ofType(unenrollCourse.type),

    // unenroll the course
    concatMap((action: PayloadAction<UnenrollRequest>) => {
      const { enrollment } = action.payload;

      return from(
        vivanteCoreContainer
          .getCoursesUseCaseFactory()
          .createUnenrollFromEnrollmentUseCase()
          .execute(enrollment?.id ?? ""),
      ).pipe(
        map(() => loadCourse(action.payload.course?.id ?? "")),
        catchError((error) => {
          return of(unenrollCourseFailure(error));
        }),
      );
    }),
  );
};

const skipCourseItemEpic: Epic = (actions$: Observable<Action>, state$: StateObservable<RootState>) => {
  return actions$.pipe(
    ofType(skipCourseItem.type),
    withLatestFrom(state$),
    concatMap(([, state]) => {
      const { currentCourseItem } = state.coursesState;

      return from(
        vivanteCoreContainer
          .getCoursesUseCaseFactory()
          .createSkipCourseItemUseCase()
          .execute(currentCourseItem?.id ?? ""),
      ).pipe(
        switchMap(() => [skipCourseItemSuccess()]),
        catchError(() => of(skipCourseItemFailure())),
      );
    }),
  );
};

const completeCourseItemEpic: Epic = (
  actions$: Observable<PayloadAction<DisableCourseReroute>>,
  state$: StateObservable<RootState>,
) => {
  return actions$.pipe(
    ofType(completeCourseItem.type),
    withLatestFrom(state$),
    concatMap(([action, state]: [PayloadAction<DisableCourseReroute>, RootState]) => {
      const { currentCourseItem } = state.coursesState;

      return from(
        vivanteCoreContainer
          .getCoursesUseCaseFactory()
          .createCompleteCourseItemUseCase()
          .execute(currentCourseItem?.id ?? ""),
      ).pipe(
        switchMap(() => [
          completeCourseItemSuccess({
            disableCourseReroute: action?.payload?.disableCourseReroute,
          }),
        ]),
        catchError(() => {
          return of(completeCourseItemFailure());
        }),
      );
    }),
  );
};

const loadEnrollmentEpic: Epic = (actions$: Observable<PayloadAction<string>>) => {
  return actions$.pipe(
    ofType(loadEnrollment.type),
    switchMap((action: PayloadAction<string>) =>
      vivanteCoreContainer
        .getCoursesUseCaseFactory()
        .createGetEnrollmentByIdUseCase()
        .execute(action.payload)
        .pipe(
          filter((enrollment: Enrollment) => enrollment.state !== EnrollmentState.CANCELED),
          map((enrollment: Enrollment) => {
            return loadEnrollmentSuccess(enrollment);
          }),
          catchError((error) => {
            return of(loadEnrollmentFailed(error));
          }),
        ),
    ),
  );
};

const navigateToCourseEpic = createNavigateToEpic<string>(navigateToCourse.type, (payload) => ({
  path: `${Routes.COURSES}/${payload}`,
  screenName: "CourseLanding",
  activityContextExtra: {
    courseId: payload,
  },
}));

const navigateToCourseCategoryEpic = createNavigateToEpic<CategorizedCourses>(
  navigateToCourseCategory.type,
  (payload) => ({
    path: calculateCoursesListPath(payload),
    screenName: "CourseCategory",
    activityContextExtra: {
      courseCategoryId: payload?.courseCategory?.id,
    },
  }),
);

export const courseEpics = [
  enrollCourseEpic,
  loadCourseEpic,
  loadCoursesEpic,
  loadEnrollmentEpic,
  navigateToCourseEpic,
  navigateToCourseCategoryEpic,
  skipCourseItemEpic,
  unenrollCourseEpic,
  completeCourseItemEpic,
];
