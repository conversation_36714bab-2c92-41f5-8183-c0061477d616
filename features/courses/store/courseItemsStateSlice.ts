import { Activity, CourseItem, VivanteApiError } from "@vivantehealth/vivante-core";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

import { RootState } from "@Store/store";
import { LoadState } from "@Types";
import { buildSliceStateSelector, processError } from "@Utils/slice.util";

export type CourseItemsState = Readonly<{
  currentCourseItems: CourseItem[];
  currentCourseItemsLoadState: LoadState;
  currentCourseItem: CourseItem | null;
  // also handle shippingAddress and DietTip
  currentActivity: Activity | null;
  loadState: LoadState;
  currentActivityLoadState: LoadState;
}>;

export const initialState: CourseItemsState = {
  currentCourseItems: [],
  currentCourseItemsLoadState: null,
  currentCourseItem: null,
  currentActivity: null,
  loadState: null,
  currentActivityLoadState: null,
};

export const CourseItemsStateSlice = createSlice({
  name: "courseItemsState",
  initialState,
  reducers: {
    loadCourseItems: (state) => ({ ...state, loadState: "loading" }),
    loadCourseItemsSuccess: (state, action: PayloadAction<CourseItem[]>) => ({
      ...state,
      loadState: "loaded",
      currentCourseItems: action.payload,
    }),
    loadCourseItemsFailure: (state, action: PayloadAction<Error | VivanteApiError>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
  },
});

export const { loadCourseItems, loadCourseItemsSuccess, loadCourseItemsFailure } = CourseItemsStateSlice.actions;

export const selectCourseItems = (state: RootState) => state.courseItemsState.currentCourseItems;

export const courseItemsStateSelector = buildSliceStateSelector("courseItemsState");

export const courseItemsStateReducer = CourseItemsStateSlice.reducer;
