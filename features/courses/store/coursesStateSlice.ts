// Expect this error as the action is actually used in epic and not in the reducer
/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  Course,
  CourseItem,
  DisplayableVivanteException,
  Enrollment,
  VivanteApiError,
  VivanteException,
} from "@vivantehealth/vivante-core";
import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import Router from "next/router";

import { LoadState } from "@Types";
import { buildSliceStateSelector, processError } from "@Utils/slice.util";

import { CategorizedCourses, loadCoursesSuccess, calculateCoursePath } from "../utils/courseState.utils";

export type UnenrollRequest = Readonly<{
  course: Course | null;
  enrollment: Enrollment | null;
}>;

export type DisableCourseReroute = Readonly<{
  disableCourseReroute?: boolean;
}>;
/// //////////////////////////////////////////////////////
/// state

export type CoursesState = Readonly<{
  loadState: LoadState;
  enrollmentLoadState: LoadState;
  courses: Course[];
  categorizedCourses: CategorizedCourses[];
  currentCourseList: Course[];
  currentCategoryId: string | null;
  currentCourse: Course | null;
  currentEnrollment: Enrollment | null;
  currentCourseItem: CourseItem | null;
  enrollmentError: VivanteException | null;
}>;

export const initialState: CoursesState = {
  loadState: null,
  enrollmentLoadState: null,
  courses: [],
  categorizedCourses: [],
  currentCourseList: [],
  currentCategoryId: null,
  currentCourse: null,
  currentEnrollment: null,
  currentCourseItem: null,
  enrollmentError: null,
};

/// //////////////////////////////////////////////////////
/// slice

export const CoursesStateSlice = createSlice({
  name: "coursesState",
  initialState,
  reducers: {
    loadCourses: (state, _: PayloadAction<string | undefined>) => {
      return {
        ...state,
        loadState: "loading",
      };
    },
    loadCourse: (state, _: PayloadAction<string>) => ({ ...state, loadState: "loading" }),
    enrollCourse: (state, _: PayloadAction<Course>) => ({ ...state, enrollmentLoadState: "loading" }),
    unenrollCourse: (state, _: PayloadAction<UnenrollRequest>) => {
      return {
        ...state,
        loadState: "loading",
      };
    },
    unenrollCourseFailure: (state, action: PayloadAction<Error | VivanteApiError>) => {
      processError({ error: action.payload });

      return {
        ...state,
        loadState: "failure",
      };
    },
    loadCoursesSuccess,
    loadCoursesFailure: (state, action: PayloadAction<Error | VivanteApiError>) => {
      processError({ error: action.payload });

      return {
        ...state,
        loadState: "failure",
      };
    },
    loadCourseSuccess: (
      state,
      action: PayloadAction<{
        course: Course | null;
        enrollment?: Enrollment | null;
      }>,
    ) => {
      return {
        ...state,
        currentCourse: action?.payload.course ?? null,
        currentEnrollment: action?.payload.enrollment ?? null,
        // update enrollment loading state too as when enrollment succeeds it reloads the course
        enrollmentLoadState: "loaded",
      };
    },
    loadCourseFailure: (state, action: PayloadAction<Error | VivanteApiError>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
    loadEnrollment: (state, _: PayloadAction<string>) => ({ ...state, enrollmentLoadState: "loading" }),
    loadEnrollmentSuccess: (state, action: PayloadAction<Enrollment>) => ({
      ...state,
      enrollmentLoadState: "loaded",
      currentEnrollment: action.payload,
    }),
    loadEnrollmentFailed: (state, action: PayloadAction<Error | VivanteApiError>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
    enrollCourseSuccess: (state) => state,
    enrollCourseFailure: (state, action: PayloadAction<VivanteException | DisplayableVivanteException>) => {
      processError({ error: action.payload });

      return { ...state, enrollmentError: action.payload, enrollmentLoadState: "failure" };
    },
    clearEnrollmentError: (state) => ({ ...state, enrollmentError: null }),
    navigateToCourseCategory: (state, _: PayloadAction<CategorizedCourses>) => ({
      ...state,
      loadState: null,
      currentCourseList: [],
    }),
    navigateToCourse: (state, _: PayloadAction<string>) => ({ ...state, loadState: null, currentCourse: null }),
    setCurrentCourseItem: (state, action: PayloadAction<CourseItem>) => ({
      ...state,
      loadState: null,
      currentCourseItem: action.payload,
    }),
    skipCourseItem: (state) => state,
    skipCourseItemSuccess: (state) => {
      Router.push(calculateCoursePath(state?.currentCourse?.id ?? ""));

      return { ...state };
    },
    skipCourseItemFailure: (state) => state,
    completeCourseItem: (state, _: PayloadAction<DisableCourseReroute>) => state,
    completeCourseItemSuccess: (state, action: PayloadAction<DisableCourseReroute>) => {
      if (!action.payload.disableCourseReroute) {
        Router.push(calculateCoursePath((state?.currentCourse?.id || state?.currentEnrollment?.course?.id) ?? ""));
      }

      return { ...state };
    },
    completeCourseItemFailure: (state) => state,
  },
});

/// //////////////////////////////////////////////////////
/// selectors

export const coursesStateSelector = buildSliceStateSelector("coursesState");

export const coursesStateReducer = CoursesStateSlice.reducer;
