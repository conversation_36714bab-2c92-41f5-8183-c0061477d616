import { Action } from "@reduxjs/toolkit";
import { ofType } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { switchMap, map, catchError } from "rxjs/operators";

import { vivanteCoreContainer } from "@Lib/vivanteCore";

import { loadCourseItems, loadCourseItemsFailure, loadCourseItemsSuccess } from "./courseItemsStateSlice";

export const loadCourseItemsEpic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(loadCourseItems.type),
    switchMap(() =>
      from(vivanteCoreContainer.getCoursesUseCaseFactory().createGetCurrentCourseItemsUseCase().execute()).pipe(
        map((courseItems) => loadCourseItemsSuccess(courseItems)),
        catchError((error) => of(loadCourseItemsFailure(error))),
      ),
    ),
  );
};

export const courseItemsEpics = [loadCourseItemsEpic];
