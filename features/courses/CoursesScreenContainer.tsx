import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { Course } from "@vivantehealth/vivante-core";

import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { coursesStateSelector, CoursesStateSlice } from "@Features/courses/store/coursesStateSlice";
import { NavigationStateSlice, NavOptions } from "@Features/navigation/store/navigationStateSlice";
import { Routes } from "@Types";

import { CoursesScreen } from "./components/CoursesScreen";
import { CategorizedCourses } from "./utils/course.utils";

export const { loadCourses, navigateToCourseCategory, navigateToCourse } = CoursesStateSlice.actions;

export const CoursesScreenContainer = () => {
  const dispatch = useDispatch();

  const categorizedCourses = useSelector(coursesStateSelector("categorizedCourses"));

  const courseLoadingState = useSelector(coursesStateSelector("loadState"));
  const isLoading = courseLoadingState === "loading" && !categorizedCourses?.length;

  useEffect(() => {
    dispatch(loadCourses());
    dispatch(NavigationStateSlice.actions.setActiveNavOption(NavOptions.COURSES));
  }, [dispatch]);

  const viewAllOnClick = (categorizedCourses: CategorizedCourses) => {
    dispatch(navigateToCourseCategory(categorizedCourses));
  };

  const courseOnClick = (course: Course) => {
    dispatch(navigateToCourse(course?.id));
  };

  const handleHomeClick = () => {
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: Routes.HOME,
        screenName: "Home",
      }),
    );
  };

  return (
    <>
      <LoadingSpinner open={isLoading} />
      <CoursesScreen
        categorizedCourses={categorizedCourses}
        courseOnClick={courseOnClick}
        viewAllOnClick={viewAllOnClick}
        handleHomeClick={handleHomeClick}
      />
    </>
  );
};
