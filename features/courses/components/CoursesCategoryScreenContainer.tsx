import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { Course } from "@vivantehealth/vivante-core";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { CoursesStateSlice, coursesStateSelector } from "@Features/courses/store/coursesStateSlice";
import { NavOptions, NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { Routes } from "@Types";

import { CoursesCategoryScreen } from "./CoursesCategoryScreen";
import { SpecialCourseCategoriesPaths } from "../utils/course.utils";

export const { loadCourses, navigateToCourse } = CoursesStateSlice.actions;

type CoursesCategoryScreenContainer = Readonly<{
  category: string | undefined;
}>;

export const CoursesCategoryScreenContainer = ({ category }: CoursesCategoryScreenContainer) => {
  const router = useRouter();
  const { category: routerCategory } = router.query;
  const dispatch = useDispatch();
  const [categoryTitle, setCategoryTitle] = useState<string>("");

  const courses = useSelector(coursesStateSelector("currentCourseList"));
  const isLoading = useSelector(coursesStateSelector("loadState")) === "loading" && !courses;

  useEffect(() => {
    dispatch(loadCourses(category));
    dispatch(NavigationStateSlice.actions.setActiveNavOption(NavOptions.COURSES));
  }, [dispatch, category]);

  useEffect(() => {
    if (courses?.length) {
      let title = "";

      if (routerCategory === SpecialCourseCategoriesPaths.COMPLETED_COURSES) {
        title = appStrings.features.courses.completed;
      } else if (routerCategory === SpecialCourseCategoriesPaths.ENROLLED_COURSES) {
        title = appStrings.features.courses.inProgress;
      } else if (courses[0]?.category?.title) {
        title = courses[0].category.title;
      }

      setCategoryTitle(title);
    }
  }, [courses, routerCategory]);

  const backOnClick = () => {
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: Routes.COURSES,
        screenName: "CourseLanding",
      }),
    );
  };

  const courseOnClick = (course: Course) => {
    dispatch(navigateToCourse(course.id));
  };

  return (
    <>
      <LoadingSpinner open={isLoading} />
      <CoursesCategoryScreen
        categoryTitle={categoryTitle}
        courses={courses}
        courseOnClick={courseOnClick}
        backOnClick={backOnClick}
      />
    </>
  );
};
