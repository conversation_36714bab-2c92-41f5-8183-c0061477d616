import { Course } from "@vivantehealth/vivante-core";
import { Box, Typography, Button } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

import { CourseGrid } from "./CourseGrid";
import { CategorizedCourses } from "../utils/course.utils";

type CoursesScreenProps = Readonly<{
  categorizedCourses?: CategorizedCourses[];
  viewAllOnClick: (categorizedCourses: CategorizedCourses) => void;
  courseOnClick: (course: Course) => void;
  handleHomeClick: () => void;
}>;

export const CoursesScreen = ({
  categorizedCourses,
  courseOnClick,
  viewAllOnClick,
  handleHomeClick,
}: CoursesScreenProps) => (
  <Box display="grid" gap={6}>
    <Typography variant="h1Serif">{appStrings.features.courses.listHeader}</Typography>
    {!categorizedCourses?.length ? (
      <Box display="flex" flexDirection="column" alignItems="center" textAlign="center" gap={5}>
        <Typography variant="body">{appStrings.features.courses.noCourses}</Typography>
        <Button variant="secondary" onClick={handleHomeClick}>
          {appStrings.features.home.backToHome}
        </Button>
      </Box>
    ) : (
      categorizedCourses.map((categorizedCourse) => {
        const sectionLabel = categorizedCourse?.label || categorizedCourse.courseCategory?.title;

        return (
          <Box key={sectionLabel} display="grid" gap={4}>
            <Box display="flex" justifyContent="space-between">
              <Typography variant="h2Serif">{sectionLabel ?? ""}</Typography>
              {categorizedCourse.courses?.length > 2 && (
                <Button
                  variant="secondary"
                  size="small"
                  aria-label={`${appStrings.a11y.callToAction(appStrings.buttonText.viewAll)}.`}
                  onClick={() => viewAllOnClick(categorizedCourse)}
                >
                  {appStrings.buttonText.viewAll}
                </Button>
              )}
            </Box>
            <CourseGrid courses={categorizedCourse.courses?.slice(0, 2)} onCourseClick={courseOnClick} />
          </Box>
        );
      })
    )}
  </Box>
);
