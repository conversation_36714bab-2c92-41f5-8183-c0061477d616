import { ChangeEvent, useState } from "react";
import { Course } from "@vivantehealth/vivante-core";
import { TextField, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { BackButton } from "@Components/BackButton/BackButton";

import { CourseGrid } from "./CourseGrid";

type CoursesCategoryScreenProps = Readonly<{
  categoryTitle?: string;
  courses: Course[];
  backOnClick: () => void;
  courseOnClick: (course: Course) => void;
}>;

export const CoursesCategoryScreen = ({
  categoryTitle,
  courses,
  backOnClick,
  courseOnClick,
}: CoursesCategoryScreenProps) => {
  const [courseFilter, setCourseFilter] = useState("");
  const onFilterChange = (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setCourseFilter(event.target.value);
  };

  const coursesToUse =
    courseFilter.length < 2
      ? courses
      : courses.filter((n) => n.title.toLowerCase()?.includes(courseFilter.toLocaleLowerCase()));

  return (
    <>
      <BackButton onClick={backOnClick}>{appStrings.features.courses.backToCourses}</BackButton>
      <Typography variant="h1Serif" my={5}>
        {categoryTitle}
      </Typography>
      <TextField
        type="text"
        fullWidth
        onChange={onFilterChange}
        placeholder={appStrings.features.courses.searchCourses}
        InputProps={{
          startAdornment: <AppIcon name="Search" />,
        }}
        sx={{ mb: 6 }}
      />
      <CourseGrid courses={coursesToUse} onCourseClick={courseOnClick} />
    </>
  );
};
