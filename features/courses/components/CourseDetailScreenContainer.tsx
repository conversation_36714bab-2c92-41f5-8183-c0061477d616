import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { Course, CourseItem, CourseItemResourceType, CourseItemSubType } from "@vivantehealth/vivante-core";
import { ShippingAddress } from "@vivantehealth/vivante-core";

import { CannotLoadContainer } from "@Components/CannotLoad/CannotLoadContainer";
import { ErrorModal } from "@Components/ErrorModal/ErrorModal";
import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { ActivityScreenContainer } from "@Features/activity/ActivityScreenContainer";
import { coursesStateSelector, CoursesStateSlice } from "@Features/courses/store/coursesStateSlice";
import { NavigationStateSlice, NavOptions } from "@Features/navigation/store/navigationStateSlice";
import { handleCourseItemNavigation } from "@Features/navigation/utils/navigation.util";
import { ShippingFormContainer } from "@Features/shippingForm/ShippingFormContainer";
import { ShippingFormStateSlice, shippingSelector } from "@Features/shippingForm/store/shippingFormStateSlice";
import { Routes } from "@Types";
import { isOfType } from "@Utils/isOfType";

import { CourseDetailScreen } from "./CourseDetailScreen";

const { loadCourse, enrollCourse, unenrollCourse, setCurrentCourseItem, clearEnrollmentError } =
  CoursesStateSlice.actions;

type CourseDetailScreenContainerProps = Readonly<{
  courseId: string;
}>;

export const CourseDetailScreenContainer = ({ courseId }: CourseDetailScreenContainerProps) => {
  const [showActivityScreen, setShowActivityScreen] = useState<boolean>(false);
  const [showShippingScreen, setShowShippingScreen] = useState<boolean>(false);
  const dispatch = useDispatch();

  const course = useSelector(coursesStateSelector("currentCourse"));
  const enrollment = useSelector(coursesStateSelector("currentEnrollment"));
  const enrollmentLoadState = useSelector(coursesStateSelector("enrollmentLoadState"));

  useEffect(() => {
    dispatch(loadCourse(courseId));
    dispatch(NavigationStateSlice.actions.setActiveNavOption(NavOptions.COURSES));
  }, [dispatch, courseId]);

  const backOnClick = () => {
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: Routes.COURSES,
        screenName: "CourseLanding",
      }),
    );
  };
  const enrollOnClick = (course: Course) => dispatch(enrollCourse(course));
  const unenrollOnClick = () => dispatch(unenrollCourse({ enrollment, course }));
  const onCourseItemClick = (courseItem: CourseItem) => {
    dispatch(setCurrentCourseItem(courseItem));
    if (courseItem.resource?.type === CourseItemResourceType.ACTIVITY) {
      setShowActivityScreen(true);
      return;
    }
    if (courseItem?.subtype === CourseItemSubType.SHIPPING_ADDRESS) {
      setShowShippingScreen(true);
      return;
    }

    handleCourseItemNavigation(courseItem);
  };

  const courseLoadState = useSelector(coursesStateSelector("loadState"));
  const loading =
    useSelector(shippingSelector("loadState")) === "loading" ||
    (!course && courseLoadState !== "failure" && !showActivityScreen && !showShippingScreen);

  if (loading) {
    return <LoadingSpinner open />;
  }

  if (showActivityScreen) {
    return <ActivityScreenContainer onClose={() => setShowActivityScreen(false)} />;
  }

  if (showShippingScreen) {
    return (
      <ShippingFormContainer
        onBackClick={() => {
          setShowShippingScreen(false);
        }}
        onSubmit={(shippingAddress) => {
          if (isOfType<ShippingAddress>(shippingAddress, ["address", "city", "state", "zipCode"])) {
            dispatch(ShippingFormStateSlice.actions.submitShippingForm(shippingAddress));
          }
        }}
        onContinue={() => {
          dispatch(CoursesStateSlice.actions.completeCourseItem({}));
          setShowShippingScreen(false);
        }}
      />
    );
  }

  return (
    <>
      <ErrorModal
        errorSelector={coursesStateSelector("enrollmentError")}
        onClose={() => dispatch(clearEnrollmentError())}
      />

      <CannotLoadContainer cannotLoad={!course}>
        {course ? (
          <CourseDetailScreen
            course={course}
            enrollment={enrollment}
            backOnClick={backOnClick}
            enrollOnClick={enrollOnClick}
            unenrollOnClick={unenrollOnClick}
            enrollmentLoadState={enrollmentLoadState}
            onCourseItemClick={onCourseItemClick}
          />
        ) : null}
      </CannotLoadContainer>
    </>
  );
};
