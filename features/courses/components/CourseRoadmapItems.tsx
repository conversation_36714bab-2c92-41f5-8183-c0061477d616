import React, { Fragment } from "react";
import { CourseItem, CourseRoadMapItem } from "@vivantehealth/vivante-core";
import { Box, Button, Divider, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { AppIcon, IconVariant } from "@Components/AppIcon/AppIcon";

type CourseItemProps = {
  item: CourseItem;
  icon?: IconVariant;
  onClick?: (courseItem: CourseItem) => void;
};

const CourseItemComponent = ({ item, icon, onClick }: CourseItemProps) => (
  <Box display="flex" justifyContent="space-between">
    <Box display="flex" alignItems="center" height="38px">
      <Box display="flex">
        <Typography variant="body" color={onClick || item.dateDone ? color.text.strong : color.text.disabled}>
          {dayjs(item.dateDone).format("MMM DD")}
        </Typography>
        <Divider orientation="vertical" flexItem sx={{ ml: 4, mr: 3 }} />
      </Box>
      {icon ? <AppIcon name={icon} containerStyles={{ mr: 2 }} color={color.icon.disabled} /> : null}
      <Typography variant="body" color={onClick || item.dateDone ? color.text.strong : color.text.disabled}>
        {item.title}
      </Typography>
    </Box>
    {onClick ? (
      <Box display="flex" alignItems="right">
        <Button variant="secondary" size="small" onClick={() => onClick(item)}>
          {appStrings.buttonText.start}
        </Button>
      </Box>
    ) : null}
  </Box>
);

type CourseRoadMapItemProps = {
  item: CourseRoadMapItem;
  icon?: IconVariant;
};

const CourseRoadMapItemComponent = ({ item, icon }: CourseRoadMapItemProps) => (
  <Box display="flex" justifyContent="space-between">
    <Box display="flex" alignItems="center" height="38px">
      <Box display="flex">
        <Typography variant="body" color={color.text.disabled}>
          {dayjs(item.date).format("MMM DD")}
        </Typography>
        <Divider orientation="vertical" flexItem sx={{ ml: 4, mr: 3 }} />
      </Box>
      {icon ? <AppIcon name={icon} containerStyles={{ mr: 2 }} color={color.icon.disabled} /> : null}
      <Typography variant="body" color={color.text.disabled}>
        {item.title}
      </Typography>
    </Box>
  </Box>
);

type CourseRoadmapItemsProps = Readonly<{
  data?: CourseItem[] | CourseRoadMapItem[];
  icon?: IconVariant;
  onClick?: (courseItem: CourseItem) => void;
  removeDivider?: boolean;
}>;

export const CourseRoadmapItems = ({ data, icon, onClick, removeDivider }: CourseRoadmapItemsProps) => {
  const isLastItem = data && data.length - 1;

  return data
    ? data.map((item: CourseItem | CourseRoadMapItem, i: number) => (
        <Fragment key={item.title}>
          {"date" in item ? (
            <CourseRoadMapItemComponent item={item} icon={icon} />
          ) : (
            <CourseItemComponent item={item} icon={icon} onClick={onClick} />
          )}
          {i === isLastItem && removeDivider ? null : <Divider flexItem />}
        </Fragment>
      ))
    : null;
};
