import { useSelector } from "react-redux";
import { ActionPlanTargetState, Course, CourseItem, Enrollment } from "@vivantehealth/vivante-core";
import { Box, Button, Card, CardMedia, Typography } from "@mui/material";
import { CircularProgress } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { RADIUS_16_PX } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { BackButton } from "@Components/BackButton/BackButton";
import { actionPlansStateSelector } from "@Features/carePlan/store/actionPlansStateSlice";
import {
  GUTCHECK_ACTION_TITLE,
  identifyCarePlanActionByTitle,
  markActionInProgressIfQualifying,
  markActionNotStarted,
} from "@Features/carePlan/utils/markAsDone.util";
import { useCheckAndUpdateParentActionState } from "@Hooks/useCheckAndUpdateParentActionState";
import { LoadState } from "@Types";

import { CourseRoadmapItems } from "./CourseRoadmapItems";
import { getEnrollText } from "../utils/course.utils";

const COURSES_STRINGS = appStrings.features.courses;

type CourseDetailScreenProps = Readonly<{
  course: Course;
  enrollment: Enrollment | null;
  backOnClick: () => void;
  enrollOnClick: (course: Course) => void;
  unenrollOnClick: () => void;
  onCourseItemClick: (courseItem: CourseItem) => void;
  enrollmentLoadState: LoadState;
}>;

// the course for gut check can only be identified by using the title string
const GUTCHECK_COURSE_TITLE = "GutCheck: My Microbiome";

export const CourseDetailScreen = ({
  course,
  enrollment,
  backOnClick,
  unenrollOnClick,
  enrollOnClick,
  onCourseItemClick,
  enrollmentLoadState,
}: CourseDetailScreenProps) => {
  const driverEntitiesState = useSelector(actionPlansStateSelector("driverEntities"));
  const targetEntitiesState = useSelector(actionPlansStateSelector("targetEntities"));
  const checkAndUpdateParentActionState = useCheckAndUpdateParentActionState();

  const isMarkAsDoneForGutcheck = course.title === GUTCHECK_COURSE_TITLE;

  const gutCheckAction =
    isMarkAsDoneForGutcheck && driverEntitiesState
      ? identifyCarePlanActionByTitle(GUTCHECK_ACTION_TITLE, driverEntitiesState)
      : null;

  const onEnrollUnenrollClick = () => {
    if (enrollment) {
      unenrollOnClick();

      if (gutCheckAction) {
        // mark parent/target item back as not started (SPECIFIED)
        checkAndUpdateParentActionState(
          gutCheckAction?.targetId ?? "",
          gutCheckAction?.id,
          ActionPlanTargetState.SPECIFIED,
        );

        // remove action in progress/STARTED state
        markActionNotStarted(gutCheckAction?.id, gutCheckAction?.state);
      }
    } else {
      enrollOnClick(course);

      if (gutCheckAction) {
        // Mark parent as in progress
        if (gutCheckAction?.targetId) {
          markActionInProgressIfQualifying(
            gutCheckAction.targetId,
            targetEntitiesState[gutCheckAction.targetId]?.state,
          );
        }

        // Mark action as in progress
        markActionInProgressIfQualifying(gutCheckAction?.id, gutCheckAction?.state);
      }
    }
  };
  const isLoading = enrollmentLoadState === "loading";
  const progress = Math.ceil((enrollment?.progress || 0) * 100);
  const btnText = getEnrollText(enrollment, course.isPastCompleted);

  return (
    <>
      <BackButton onClick={backOnClick}>{COURSES_STRINGS.backToCourses}</BackButton>
      <Card sx={{ p: 5, mt: 5 }}>
        <Box display="flex" flexDirection="column" gap={3}>
          <CardMedia image={course.image} title={course.title} sx={{ height: "312px", borderRadius: RADIUS_16_PX }} />
          <Box display="flex" gap={4} pt={1}>
            <Typography variant="h4" color={color.text.subtle}>
              {course.category?.title}
            </Typography>
            <Box display="flex" gap={1}>
              <AppIcon name="Clock" size="sm" color={color.icon.subtle} />
              <Typography variant="h4" color={color.text.subtle}>
                {course.estimatedDuration}
              </Typography>
            </Box>
          </Box>
          <Typography variant="h1Serif">{course.title}</Typography>
          <Typography variant="body">{course.description}</Typography>
          <Box pb={4}>
            <Button
              variant={btnText === COURSES_STRINGS.enroll ? "primary" : "secondary"}
              onClick={onEnrollUnenrollClick}
              disabled={isLoading}
            >
              {isLoading ? <CircularProgress thickness={4} size={24} color="inherit" /> : btnText}
            </Button>
          </Box>
          <Box display="grid" gap={5}>
            {enrollment ? (
              <>
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="h4" color={color.text.strong}>
                    {COURSES_STRINGS.courseRoadmap}
                  </Typography>
                  <Typography
                    variant="h4"
                    color={color.text.strong}
                  >{`${COURSES_STRINGS.progress} ${progress}%`}</Typography>
                </Box>
                <CourseRoadmapItems
                  data={enrollment.pastCourseItems}
                  icon="Completed"
                  removeDivider={!enrollment.currentCourseItems.length && !enrollment.projectedCourseItems.length}
                />
                <CourseRoadmapItems
                  data={enrollment.currentCourseItems}
                  onClick={onCourseItemClick}
                  removeDivider={!enrollment.projectedCourseItems.length}
                />
                <CourseRoadmapItems data={enrollment.projectedCourseItems} icon="Lock" removeDivider />
              </>
            ) : (
              <CourseRoadmapItems data={course.roadMap} icon="Lock" removeDivider />
            )}
          </Box>
        </Box>
      </Card>
    </>
  );
};
