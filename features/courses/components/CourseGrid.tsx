import { Course } from "@vivantehealth/vivante-core";
import { Grid } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { EducationCard } from "@Components/EducationCard/EducationCard";

type CourseGridProps = Readonly<{
  courses: Course[];
  onCourseClick: (course: Course) => void;
}>;

const ARIA_STRINGS = appStrings.a11y;
const COURSES_STRINGS = appStrings.features.courses;

export const CourseGrid = ({ courses, onCourseClick }: CourseGridProps) => (
  <Grid container spacing={4}>
    {courses?.map((course) => {
      let chipText = "";

      if (course.isRecommended) {
        chipText = COURSES_STRINGS.suggested;
      } else if (course.currentEnrollmentId) {
        chipText = COURSES_STRINGS.enrolled;
      }

      const ariaCategoryTitle = course.category?.title ? `${ARIA_STRINGS.category(course.category.title)}. ` : "";
      const ariaTitle = `${ARIA_STRINGS.title(course.title)}. `;
      const ariaChipText = chipText ? `${chipText}. ` : "";
      const ariaIsExpress = course.isExpress ? `${COURSES_STRINGS.express}. ` : "";
      const ariaCallToAction = `${ARIA_STRINGS.callToAction(COURSES_STRINGS.takeCourse)}.`;

      return (
        <EducationCard
          key={course.id}
          ariaLabel={`${ARIA_STRINGS.course}. ${ariaCategoryTitle}${ariaTitle}${ariaChipText}${ariaIsExpress}${ariaCallToAction}`}
          title={course.title}
          imageSrc={course.image}
          category={course.category?.title}
          buttonText={course.currentEnrollmentId ? appStrings.buttonText.continue : COURSES_STRINGS.takeCourse}
          chipText={chipText}
          onClick={() => onCourseClick(course)}
          icon={course.isExpress ? "Clock" : undefined}
          iconText={course.isExpress ? COURSES_STRINGS.express : undefined}
        />
      );
    })}
  </Grid>
);
