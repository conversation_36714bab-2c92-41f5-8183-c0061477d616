import { Course, Enrollment } from "@vivantehealth/vivante-core";
import { CourseCategory } from "@vivantehealth/vivante-core";
import { PayloadAction } from "@reduxjs/toolkit";

import { appStrings } from "@Assets/app_strings";
import { CoursesState } from "@Features/courses/store/coursesStateSlice";
import { LoadState, Routes } from "@Types";

const COURSES_STRINGS = appStrings.features.courses;

export type CategorizedCourses = Readonly<{
  label?: string;
  courseCategory?: CourseCategory;
  courses: Course[];
  inProgress?: boolean;
}>;

export enum SpecialCourseCategoriesPaths {
  COMPLETED_COURSES = "completedCourses",
  ENROLLED_COURSES = "enrolledCourses",
}

export const createCatagorizedCourses = (courses: Course[]): CategorizedCourses[] => {
  // bucket these courses
  const bucketed = courses.reduce(
    (acc: Record<string, CategorizedCourses>, next: Course): Record<string, CategorizedCourses> => {
      const { category } = next;

      if (!category) {
        return acc;
      }

      const targetCategory: CategorizedCourses = acc[category.id] || {
        courseCategory: category,
        courses: [],
      };
      const updatedCategory: CategorizedCourses = {
        ...targetCategory,
        courses: [...targetCategory.courses, next],
      };

      if (!updatedCategory.courseCategory) {
        return acc;
      }

      return {
        ...acc,
        [updatedCategory.courseCategory.id]: updatedCategory,
      };
    },
    {},
  );

  return Object.values(bucketed);
};

export const createCatagorizedCoursesList = (courses: Course[]): CategorizedCourses[] => {
  const enrolledCourses: Course[] = [];
  const completedCourses: Course[] = [];
  const otherCourses: Course[] = [];

  courses.forEach((course: Course) => {
    if (course.isPastCompleted) {
      completedCourses.push(course);
    }
    if (course.currentEnrollmentId) {
      enrolledCourses.push(course);
    }

    otherCourses.push(course);
  });

  if (courses.length < 1) {
    return [];
  }

  const categorizedCourses: CategorizedCourses[] = [
    {
      label: COURSES_STRINGS.inProgress,
      courses: enrolledCourses,
      inProgress: true,
    },
    ...createCatagorizedCourses(otherCourses),
    {
      label: COURSES_STRINGS.completed,
      courses: completedCourses,
    },
  ].filter((next) => next.courses.length > 0);

  return categorizedCourses;
};

export const calculateCoursesListPath = (selectedCategory: CategorizedCourses) => {
  let categoryPath = "";

  if (selectedCategory.label === COURSES_STRINGS.completed) {
    categoryPath = SpecialCourseCategoriesPaths.COMPLETED_COURSES;
  } else if (selectedCategory?.inProgress) {
    categoryPath = SpecialCourseCategoriesPaths.ENROLLED_COURSES;
  } else if (selectedCategory?.courseCategory?.id) {
    categoryPath = selectedCategory.courseCategory.id;
  }

  const fullPath = `${Routes.COURSES}/category/${categoryPath}`;

  return fullPath;
};

export const calculateCoursePath = (courseId: string) => {
  return `${Routes.COURSES}/${courseId}`;
};

export const extractCurrentList = (
  categorizedCoursesList: CategorizedCourses[],
  currentCategoryId?: string,
): Course[] => {
  const currentCourseList =
    categorizedCoursesList.find(
      (categorizedCourseList) =>
        (currentCategoryId === SpecialCourseCategoriesPaths.ENROLLED_COURSES && categorizedCourseList.inProgress) ||
        (currentCategoryId === SpecialCourseCategoriesPaths.COMPLETED_COURSES &&
          categorizedCourseList.label === COURSES_STRINGS.completed) ||
        categorizedCourseList.courseCategory?.id === currentCategoryId,
    )?.courses || [];

  return currentCourseList;
};

export const loadCoursesSuccess = (
  state: CoursesState,
  action: PayloadAction<{
    courses: Course[];
    courseCategoryId?: string;
  }>,
) => {
  const { courses } = action.payload;
  const { courseCategoryId } = action.payload;
  const categorizedCourses = createCatagorizedCoursesList(courses);
  const currentCourseList = extractCurrentList(categorizedCourses, courseCategoryId);

  return {
    ...state,
    courses,
    categorizedCourses,
    currentCourseList,
    loadState: "loaded" as LoadState,
  };
};

export const loadCourseSuccess = (
  state: CoursesState,
  action: PayloadAction<{
    enrollment?: Enrollment;
    course: Course;
  }>,
) => {
  return {
    ...state,
    loadState: "loaded" as LoadState,
    enrollmentLoadState: "loaded" as LoadState,
    currentCourse: action.payload.course,
    currentEnrollment: action.payload.enrollment,
  };
};

export const getEnrollText = (enrollment: Enrollment | null, isPastCompleted: boolean): string => {
  if (enrollment) {
    return appStrings.features.courses.unEnroll;
  }
  if (isPastCompleted) {
    return appStrings.features.courses.reEnroll;
  }

  return appStrings.features.courses.enroll;
};
