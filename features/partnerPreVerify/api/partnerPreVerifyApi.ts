import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import qs from "qs";

import { mergePartnerPreVerifyData } from "@Features/eligibility/store/eligibilityProcessStateSlice";
import { getBaseApiUrl } from "@Utils/getBaseApiUrl";

import { PARTNERS } from "../assets/constants";
import {
  PartnerPreVerifyAuthorizationRequest,
  PartnerPreVerifyAuthorizationResponse,
} from "../types/partnerPreVerify.types";

type PreTransformPartnerPreVerifyAuthorizationResponse = {
  data: {
    type: "eligibility_data";
    id: string;
    attributes: {
      registrationCode: string;
      fields: Record<string, string | number>;
    };
  };
};

export const partnerPreVerifyApi = createApi({
  reducerPath: "partnerPreVerifyApi",
  baseQuery: fetchBaseQuery({
    baseUrl: getBaseApiUrl("PARTNER_PREVERIFY_URL"),
    prepareHeaders: async (headers) => {
      headers.set("content-type", "application/x-www-form-urlencoded");
      headers.set("accept", "application/vnd.api+json");

      return headers;
    },
  }),
  endpoints: (builder) => ({
    partnerPreVerifyAuthorization: builder.query<
      PartnerPreVerifyAuthorizationResponse,
      PartnerPreVerifyAuthorizationRequest
    >({
      query: (body) => {
        return {
          url: `/${PARTNERS[body.partner]}/eligibilityData`,
          method: "POST",
          body: qs.stringify({
            lookup_key: body.code,
            redirect_uri: body.redirectUri,
          }),
        };
      },
      transformResponse: (
        response: PreTransformPartnerPreVerifyAuthorizationResponse,
      ): PartnerPreVerifyAuthorizationResponse => {
        return {
          registrationCode: response.data.attributes.registrationCode,
          fields: response.data.attributes.fields,
        };
      },
      onQueryStarted: async (_, { dispatch, queryFulfilled }) => {
        const { data } = await queryFulfilled;

        dispatch(mergePartnerPreVerifyData({ eligibilityData: data.fields, registrationCode: data.registrationCode }));
      },
    }),
  }),
});

export const { usePartnerPreVerifyAuthorizationQuery } = partnerPreVerifyApi;
