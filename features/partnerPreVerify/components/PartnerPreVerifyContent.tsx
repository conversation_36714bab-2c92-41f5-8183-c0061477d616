import { Typography, Paper, Box, Button } from "@mui/material";
import parse from "html-react-parser";

import { appStrings } from "@Assets/app_strings";

import { Partner } from "../types/partnerPreVerify.types";

const PARTNER_PREVERIFY_STRINGS = appStrings.features.partnerPreVerify;
const BUTTON_STRINGS = appStrings.buttonText;

type PartnerPreVerifyContentProps = Readonly<{
  isError: boolean;
  partner: Partner;
  onAlreadySignedUpClick: (alreadySignedUp: boolean) => void;
}>;

export const PartnerPreVerifyContent = ({ isError, partner, onAlreadySignedUpClick }: PartnerPreVerifyContentProps) => {
  return isError ? (
    <Typography variant="bodyDense">
      {parse(
        `${PARTNER_PREVERIFY_STRINGS.error.partOne} ${PARTNER_PREVERIFY_STRINGS.partnerTo<PERSON>abel[partner]} ${PARTNER_PREVERIFY_STRINGS.error.partTwo} ${PARTNER_PREVERIFY_STRINGS.emailLink} ${PARTNER_PREVERIFY_STRINGS.error.partThree}`,
      )}
    </Typography>
  ) : (
    <Paper>
      <Typography variant="h3" mb={5}>
        {PARTNER_PREVERIFY_STRINGS.signedUpPrompt}
      </Typography>

      <Box display="flex" gap={4}>
        <Button variant="secondary" fullWidth onClick={() => onAlreadySignedUpClick(false)}>
          {BUTTON_STRINGS.no}
        </Button>

        <Button variant="primary" fullWidth onClick={() => onAlreadySignedUpClick(true)}>
          {BUTTON_STRINGS.yes}
        </Button>
      </Box>
    </Paper>
  );
};
