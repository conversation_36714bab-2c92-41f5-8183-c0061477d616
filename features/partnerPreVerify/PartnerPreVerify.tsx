import { useDispatch } from "react-redux";
import { CircularProgress, Grid, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { Routes } from "@Types";

import { usePartnerPreVerifyAuthorizationQuery } from "./api/partnerPreVerifyApi";
import { PartnerPreVerifyContent } from "./components/PartnerPreVerifyContent";
import { Partner } from "./types/partnerPreVerify.types";
import { NavigationStateSlice } from "../navigation/store/navigationStateSlice";

const PARTNER_PREVERIFY_STRINGS = appStrings.features.partnerPreVerify;

type PartnerPreVerifyProps = Readonly<{
  code: string;
  partner: Partner;
}>;

export const PartnerPreVerify = ({ code, partner }: PartnerPreVerifyProps) => {
  const dispatch = useDispatch();
  const redirectUri = `${window.location.origin}${window.location.pathname}`;
  const { isError, isLoading } = usePartnerPreVerifyAuthorizationQuery({ code, redirectUri, partner });

  const onAlreadySignedUpClick = (alreadySignedUp: boolean) => {
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: alreadySignedUp ? Routes.LOGIN : Routes.SIGN_UP,
        screenName: alreadySignedUp ? "SignIn" : "SignUp",
      }),
    );
  };

  return (
    <Grid
      container
      direction="column"
      alignItems="center"
      pt={11}
      mx="auto"
      sx={{ textAlign: "center", width: "448px" }}
    >
      <AppIcon name="CompanyLogo" size="logo" />

      <Typography variant="h3" my={6}>
        {PARTNER_PREVERIFY_STRINGS.pageHeader}
      </Typography>

      {isLoading ? (
        <CircularProgress />
      ) : (
        <PartnerPreVerifyContent isError={isError} partner={partner} onAlreadySignedUpClick={onAlreadySignedUpClick} />
      )}
    </Grid>
  );
};
