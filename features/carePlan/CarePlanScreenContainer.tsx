import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Tab from "@mui/material/Tab";
import Tabs from "@mui/material/Tabs";

import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { TabsPanel } from "@Components/TabsPanel/TabsPanel";
import { tabsA11yProps } from "@Components/TabsPanel/utils/tabsA11yProps";
import { CarePlanHistoryTab } from "@Features/carePlan/components/CarePlanHistoryTab";
import { CarePlanToDoTab } from "@Features/carePlan/components/CarePlanToDoTab";
import {
  ActionPlansStateSlice,
  actionPlansStateSelector,
  selectCarePlanModulesGroupedByPhase,
  selectCareplanUngroupedModules,
  selectCompletedActionsHistory,
  selectInterventions,
} from "@Features/carePlan/store/actionPlansStateSlice";
import { NavOptions, setActiveNavOption } from "@Features/navigation/store/navigationStateSlice";

const { loadActionPlans } = ActionPlansStateSlice.actions;

type TabValues = "todo" | "history";

export const CarePlanScreenContainer = () => {
  const [activeTabName, setActiveTabName] = useState<TabValues>("todo");
  const [showCompletedByPhase, setShowCompletedByPhase] = useState<Record<string, boolean>>({});

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(loadActionPlans());
    dispatch(setActiveNavOption(NavOptions.CARE_PLAN));
  }, [dispatch]);

  const carePlanLoadState = useSelector(actionPlansStateSelector("loadState"));
  const interventions = useSelector(selectInterventions)?.filter((i) => i.state !== "COMPLETED");
  const completedInterventions = useSelector(selectInterventions)?.filter((i) => i.state === "COMPLETED");

  const carePlanModulesGroupedByPhase = useSelector(selectCarePlanModulesGroupedByPhase);
  const carePlanModulesUngrouped = useSelector(selectCareplanUngroupedModules);

  const completedActionsHistory = useSelector(selectCompletedActionsHistory);

  useEffect(() => {
    if (carePlanModulesGroupedByPhase) {
      const carePlanModules = Object.entries(carePlanModulesGroupedByPhase);
      const completedByPhase = carePlanModules.reduce<Record<string, boolean>>((acc, [phaseName, phaseData]) => {
        if (phaseData?.completed?.length) {
          acc[phaseName] = false;
        }

        return acc;
      }, {});

      setShowCompletedByPhase(completedByPhase);
    }
  }, [carePlanModulesGroupedByPhase]);

  const isCarePlanLoading = carePlanLoadState === "loading" && !carePlanModulesUngrouped;

  return (
    <>
      <Tabs
        onChange={(event: React.SyntheticEvent, newValue: TabValues) => {
          setActiveTabName(newValue);
        }}
        value={activeTabName}
      >
        <Tab key="value-todo" value="todo" label="To do" {...tabsA11yProps("todo")} />
        <Tab key="value-history" value="history" label="History" {...tabsA11yProps("history")} />
      </Tabs>

      <TabsPanel tabName={"todo"} currentSelectedTabName={activeTabName} sx={{ marginTop: 7 }}>
        <CarePlanToDoTab
          interventions={interventions}
          carePlanModulesGroupedByPhase={carePlanModulesGroupedByPhase}
          carePlanModulesUngrouped={carePlanModulesUngrouped}
          showCompletedByPhase={showCompletedByPhase}
          setShowCompletedByPhase={setShowCompletedByPhase}
        />
      </TabsPanel>
      <TabsPanel tabName={"history"} currentSelectedTabName={activeTabName} sx={{ marginTop: 7 }}>
        <CarePlanHistoryTab
          completedActionsHistory={completedActionsHistory}
          completedInterventions={completedInterventions}
        />
      </TabsPanel>
      <LoadingSpinner open={isCarePlanLoading} />
    </>
  );
};
