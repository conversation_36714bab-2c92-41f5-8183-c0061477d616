import { ActionPlanDriver, ActionPlanModule, CarePlanIntervention } from "@vivantehealth/vivante-core";

import { appStrings } from "@Assets/app_strings";
import { RootState } from "@Store/store";

import {
  ActionPlanState,
  ActionsHistoryType,
  ModulesGroupedByPhaseType,
  SetCarePlanInterventionProps,
  UngroupedModulesByStatus,
} from "../store/actionPlansStateSlice";
import { TargetSchema } from "../types/types";

export const getGroupedModulesByPhases = (state: RootState): ModulesGroupedByPhaseType | null => {
  const modules = state.actionPlansState?.moduleEntities;

  if (!modules) {
    return null;
  }

  const modulesArray = Object.values(modules);
  const groupedModulesByPhases: ModulesGroupedByPhaseType = modulesArray.reduce<ModulesGroupedByPhaseType>(
    (acc: ModulesGroupedByPhaseType, module: ActionPlanModule) => {
      if (!module.phase) {
        return acc;
      }

      const arrayKey = module.progress.completed === module.progress.total ? "completed" : "incomplete";

      if (acc[module.phase.tag]) {
        return {
          ...acc,
          [module.phase.tag]: {
            ...acc[module.phase.tag],
            [arrayKey]: [...(acc[module.phase.tag][arrayKey] || []), module],
          },
        };
      }

      return {
        ...acc,
        [module.phase.tag]: {
          title: module.phase.title,
          subtitle: module.phase.subtitle,
          description: module.phase.description,
          [arrayKey]: [module],
        },
      };
    },
    {},
  );

  return groupedModulesByPhases;
};

export const getUngroupedModulesByStatus = (state: RootState) => {
  const modules = state.actionPlansState?.moduleEntities;

  if (!modules) {
    return null;
  }

  const modulesArray = Object.values(modules);
  const ungroupedModulesByStatus = modulesArray.reduce<UngroupedModulesByStatus>(
    (acc, module) => {
      if (!module.phase) {
        const isCompleted = module.progress.completed === module.progress.total;

        if (isCompleted) {
          acc.completedModules.push(module);
        } else {
          acc.incompleteModules.push(module);
        }
      }

      return acc;
    },
    { completedModules: [], incompleteModules: [] },
  );

  return ungroupedModulesByStatus;
};

export const reduceModulesIntoGroupsByDate = (
  acc: ActionsHistoryType,
  module: ActionPlanModule | CarePlanIntervention,
  isIntervention: boolean = false,
) => {
  const { earlierItems: earlierItemsString, otherItems: otherItemsString } = appStrings.features.carePlan;
  const returnModule = {
    ...module,
    isIntervention,
  };

  if (module.completedAt) {
    const completedDate = new Date(module.completedAt);
    const today = new Date();
    const oneYearAgo = new Date(today);

    oneYearAgo.setFullYear(today.getFullYear() - 1);

    if (completedDate < oneYearAgo) {
      return {
        ...acc,
        [earlierItemsString]: {
          order: 13,
          modules: [...(acc?.earlierItems?.modules || []), returnModule],
        },
      };
    }

    const month = completedDate.toLocaleString("default", { month: "long" });
    const monthNumber = completedDate.getMonth() + 1;

    return {
      ...acc,
      [month]: {
        order: monthNumber,
        modules: [...(acc[month]?.modules || []), returnModule],
      },
    };
  }

  return {
    ...acc,
    [otherItemsString]: {
      order: 14,
      modules: [...(acc?.otherItems?.modules || []), returnModule],
    },
  };
};

export const getCompletedGroupedByDate = (state: RootState) => {
  const modules = state.actionPlansState?.moduleEntities;

  if (!modules) {
    return null;
  }

  const completedGroupedByDate = Object.values(modules).reduce<ActionsHistoryType>((acc, moduleEntity) => {
    if (!moduleEntity.phase) {
      return acc;
    }

    const isCompleted = moduleEntity.progress.completed === moduleEntity.progress.total;

    if (!isCompleted) {
      return acc;
    }

    return reduceModulesIntoGroupsByDate(acc, moduleEntity);
  }, {});

  // sorts modules in descending order.
  Object.keys(completedGroupedByDate).forEach((group) => {
    completedGroupedByDate[group].modules.sort(
      (a, b) =>
        (b.completedAt ? new Date(b.completedAt).getTime() : 0) -
        (a.completedAt ? new Date(a.completedAt).getTime() : 0),
    );
  });
  return completedGroupedByDate as ActionsHistoryType;
};

export type CarePlanDataById = Readonly<{
  module: ActionPlanModule;
  targets: TargetSchema[];
  drivers: ActionPlanDriver[];
}>;
export const getCarePlanDataById = (state: RootState, carePlanId: string): CarePlanDataById => {
  const { moduleEntities, targetEntities, driverEntities } = state.actionPlansState || {};

  const actionPlanModule = moduleEntities?.[carePlanId];

  // type coming from Vivante core asserts module.targets is an ActionPlanTarget[]
  // when in reality it is an string[] containing IDs of the Action Plan Targets
  const targets = actionPlanModule?.targets?.map(
    (target) => targetEntities[target as unknown as string],
  ) as TargetSchema[];

  const drivers = targets?.flatMap((target) =>
    target?.drivers?.map((driver) => driverEntities[driver]),
  ) as ActionPlanDriver[];

  return { module: actionPlanModule, targets, drivers };
};

type HomePageTopCarePlanItems = Readonly<{
  modules: { [id: string]: ActionPlanModule };
  interventions: { [id: string]: CarePlanIntervention };
}>;

export const getHomePageTopCarePlanItems = ({ modules, interventions }: HomePageTopCarePlanItems) => {
  const topCompletedInterventions = interventions
    ? Object.values(interventions)
        ?.filter((m: CarePlanIntervention) => m.state !== "COMPLETED")
        .slice(0, 3)
    : [];
  const remainingModulesNumber = 3 - topCompletedInterventions.length;

  const topCompletedActionPlans = modules
    ? Object.values(modules)
        ?.filter((m: ActionPlanModule) => m.state !== "COMPLETED")
        .slice(0, remainingModulesNumber)
    : [];

  return { topCompletedInterventions, topCompletedActionPlans };
};

export const updateCarePlanIntervention = (
  state: ActionPlanState,
  payload: SetCarePlanInterventionProps,
): ActionPlanState => ({
  ...state,
  interventionEntities: {
    ...state.interventionEntities,
    [payload.intervention.id]: { ...payload.intervention, state: payload.newState },
  },
});
