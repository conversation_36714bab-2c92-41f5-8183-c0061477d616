import { CarePlanInterventionAction } from "@vivantehealth/vivante-core";

import { handleUriNavigation } from "@Features/navigation/utils/navigation.util";

export const handleInterventionSelected = (interventionId: string, action: CarePlanInterventionAction) => {
  switch (action.type) {
    case "pdf":
    case "URL":
      return action.uri;
    case "referral":
      return `care-plan/intervention/${interventionId}`;
    default:
      return handleUriNavigation(action.uri);
  }
};
