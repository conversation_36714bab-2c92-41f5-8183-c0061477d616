import { ActionPlanDriver, ActionPlanTargetState } from "@vivantehealth/vivante-core";

import { store } from "@Store/store";

import { setActionPlanTargetState } from "../store/actionPlansStateSlice";

export const GUTCHECK_ACTION_TITLE = "Order your GutCheck at-home kit";

export const shouldMarkAsDone = (actionId: string, actionState: ActionPlanTargetState | undefined) => {
  return actionState !== ActionPlanTargetState.COMPLETED && actionId?.length > 0;
};

export const markActionInProgressIfQualifying = (actionId: string, actionState: ActionPlanTargetState | undefined) => {
  // mark action as in progress if it's not already completed
  if (actionState !== ActionPlanTargetState.COMPLETED) {
    setActionNewState(ActionPlanTargetState.STARTED, actionState, actionId);
  }
};

export const markActionNotStarted = (actionId: string, actionState?: ActionPlanTargetState) => {
  // mark action as not started if it's not already completed
  if (actionState !== ActionPlanTargetState.COMPLETED) {
    setActionNewState(ActionPlanTargetState.SPECIFIED, actionState, actionId);
  }
};

export const setActionNewState = (
  newState: ActionPlanTargetState,
  currentState: ActionPlanTargetState | undefined,
  actionId: string,
) => {
  // exit if targetId is not provided or if the target state is the same as the new state (same state update will trigger an error)
  if (!actionId || currentState === newState) {
    return;
  }

  store.dispatch(
    setActionPlanTargetState({
      targetId: actionId,
      newTargetState: newState,
    }),
  );
};

export const identifyCarePlanActionByTitle = (title: string, actions: Record<string, ActionPlanDriver>) => {
  if (!actions) return null;

  return Object.values(actions).find((action) => action.title === title);
};
