import { schema } from "normalizr";

export const drivers = new schema.Entity("drivers");

export const targets = new schema.Entity("targets", {
  drivers: [drivers],
});

export const modules = new schema.Entity("modules", {
  targets: [targets],
});

export const interventions = new schema.Entity("interventions");

export const actionPlanSchema = new schema.Entity("actionPlan", {
  modules: [modules],
  interventions: [interventions],
});
