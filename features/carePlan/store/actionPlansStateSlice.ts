/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  ActionPlanModule,
  ActionPlanDriver,
  ActionPlanTargetStateChoice,
  ActionPlanTargetStateFilter,
  CarePlanIntervention,
  ActionPlanTargetState,
  CarePlanInterventionState,
  MemberTrackingStatus,
  VivanteException,
} from "@vivantehealth/vivante-core";
import { PayloadAction, createSelector, createAction, createSlice } from "@reduxjs/toolkit";

import { RootState } from "@Store/store";
import { LoadState } from "@Types";
import { buildSliceStateSelector, processError } from "@Utils/slice.util";

import { ActionPlanNormalized, ActionPlanSchema, TargetSchema } from "../types/types";
import {
  getCarePlanDataById,
  getCompletedGroupedByDate,
  getGroupedModulesByPhases,
  getHomePageTopCarePlanItems,
  getUngroupedModulesByStatus,
  updateCarePlanIntervention,
} from "../utils/actionPlanUtils";

export type SetCarePlanInterventionProps = Readonly<{
  intervention: CarePlanIntervention;
  newState: CarePlanInterventionState;
}>;

export type ActionPlanState = Readonly<{
  actionPlanId: string | null;
  actionPlanEntities: { [id: string]: ActionPlanSchema };
  interventionEntities: { [id: string]: CarePlanIntervention };
  moduleEntities: { [id: string]: ActionPlanModule };
  targetEntities: { [id: string]: TargetSchema };
  driverEntities: { [id: string]: ActionPlanDriver };
  targetStateChoices: ActionPlanTargetStateChoice[];
  actionPlanTargetStateFilters: ActionPlanTargetStateFilter[];
  selectedActionPlanModuleId: string | null;
  currentTargetStateFilterLabels: string[];
  actionPlanPhaseAnchor: string | null;
  loadState: LoadState;
  targetFiltersLoadState: LoadState;
  targetFilterChoicesLoadState: LoadState;
  memberHasSymptomsLogged: MemberTrackingStatus | null;
  selectedActionId: string | null;
}>;

export const initialState: ActionPlanState = {
  actionPlanId: null,
  actionPlanEntities: {},
  interventionEntities: {},
  moduleEntities: {},
  targetEntities: {},
  driverEntities: {},
  targetStateChoices: [],
  actionPlanTargetStateFilters: [],
  selectedActionPlanModuleId: null,
  currentTargetStateFilterLabels: [],
  actionPlanPhaseAnchor: null,
  loadState: null,
  targetFiltersLoadState: null,
  targetFilterChoicesLoadState: null,
  memberHasSymptomsLogged: null,
  selectedActionId: null,
};

export const ActionPlansStateSlice = createSlice({
  name: "actionPlansState",
  initialState,
  reducers: {
    getMemberHasTracking: (state) => {
      return {
        ...state,
        loadState: "loading",
      };
    },
    getMemberHasTrackingSuccess: (state, action: PayloadAction<MemberTrackingStatus>) => {
      return {
        ...state,
        loadState: "loaded",
        memberHasSymptomsLogged: action?.payload,
      };
    },
    loadActionPlans: (state) => ({ ...state, loadState: "loading" }),
    loadActionPlansSuccess: (state, action: PayloadAction<ActionPlanNormalized>) => {
      const { actionPlan, interventions, modules, targets, drivers } = action?.payload?.entities || {};

      return {
        ...state,
        actionPlanId: action?.payload?.result,
        actionPlanEntities: actionPlan,
        interventionEntities: interventions,
        moduleEntities: modules,
        targetEntities: targets,
        driverEntities: drivers,
        loadState: "loaded",
      };
    },
    loadActionPlanTargetStateFilters: (state) => ({ ...state, targetFiltersLoadState: "loading" }),
    loadActionPlanTargetStateFiltersSuccess: (state, action: PayloadAction<ActionPlanTargetStateFilter[]>) => ({
      ...state,
      actionPlanTargetStateFilters: action.payload,
      targetFiltersLoadState: "loaded",
    }),
    loadActionPlanTargetStateFiltersFail: (state) => ({ ...state, targetFiltersLoadState: "failure" }),
    loadActionPlanTargetStateChoices: (state) => ({ ...state, targetFilterChoicesLoadState: "loading" }),
    loadActionPlanTargetStateChoicesSuccess: (state, action: PayloadAction<ActionPlanTargetStateChoice[]>) => ({
      ...state,
      targetStateChoices: action.payload,
      targetFilterChoicesLoadState: "loaded",
    }),
    setActionPlanTargetStateFail: (state) => ({ ...state, loadState: "failure" }),
    setInterventionState: (state, _: PayloadAction<SetCarePlanInterventionProps>) => ({
      ...state,
      loadState: "loading",
    }),
    setInterventionStateSuccess: (state: ActionPlanState, action: PayloadAction<SetCarePlanInterventionProps>) =>
      updateCarePlanIntervention(state, action.payload),
    setInterventionStateFail: (state, action: PayloadAction<VivanteException>) => {
      processError({ error: action.payload });

      return { ...state, loadState: "failure" };
    },
    linkingInterventionTriggered: (state, _: PayloadAction<string[]>) => ({ ...state, loadState: "loading" }),
    // When linking intervention is not yet found
    // Leave loadState as loading and keep observing for the update
    linkingInterventionNotFound: (state) => ({ ...state, loadState: "loading" }),
    linkingInterventionSuccess: (state) => ({ ...state, loadState: "loaded" }),
    setSelectedActionId(state, action: PayloadAction<string | null>) {
      state.selectedActionId = action.payload;
    },
  },
});

export type SetActionPlanTargetStatePayload = {
  targetId: string;
  newTargetState: ActionPlanTargetState;
};
export const setActionPlanTargetState = createAction<SetActionPlanTargetStatePayload>("setActionPlanTargetState");
export const setActionPlanTargetStateSuccess = createAction<SetActionPlanTargetStatePayload>(
  "setActionPlanTargetStateSuccess",
);

export type SetActionPlanTargetStateType = ReturnType<typeof setActionPlanTargetState>;

export const selectModulesSlice = createSelector(
  (state: RootState) => ({
    modules: state.actionPlansState.moduleEntities,
    interventions: state.actionPlansState.interventionEntities,
  }),
  (data) => getHomePageTopCarePlanItems(data),
);

export const selectInterventions = (state: RootState) =>
  state.actionPlansState.interventionEntities &&
  (Object.values(state.actionPlansState.interventionEntities) as CarePlanIntervention[]);

export type ModulesGroupedByPhaseType = Readonly<{
  [tag: string]: {
    title: string;
    subtitle: string;
    description: string;
    completed?: ActionPlanModule[];
    incomplete?: ActionPlanModule[];
  };
}>;

export const selectCarePlanModulesGroupedByPhase = (state: RootState) => {
  return getGroupedModulesByPhases(state);
};

export type UngroupedModulesByStatus = Readonly<{
  completedModules: ActionPlanModule[];
  incompleteModules: ActionPlanModule[];
}>;

export const selectCareplanUngroupedModules = (state: RootState) => {
  return getUngroupedModulesByStatus(state);
};

export type ActionsHistoryType = Readonly<{
  [date: string]: {
    order: number;
    modules: ((ActionPlanModule | CarePlanIntervention) & { isIntervention: boolean })[];
  };
}>;

export const selectCompletedActionsHistory = (state: RootState) => {
  return getCompletedGroupedByDate(state);
};

export const selectCarePlanDataById = (state: RootState, carePlanId: string) => {
  return getCarePlanDataById(state, carePlanId);
};

export const getMemberTrackingStatus = (state: RootState) => state.actionPlansState.memberHasSymptomsLogged;

export const actionPlansStateSelector = buildSliceStateSelector("actionPlansState");

export const actionPlansStateReducer = ActionPlansStateSlice.reducer;

export const { setSelectedActionId } = ActionPlansStateSlice.actions;
