import {
  ActionPlan,
  ActionPlanTargetStateFilter,
  ClickStreamActivityEventType,
  SetTargetStateProps,
} from "@vivantehealth/vivante-core";
import { Action, ActionCreatorWithoutPayload, PayloadAction } from "@reduxjs/toolkit";
import Router from "next/router";
import { normalize } from "normalizr";
import { Epic, ofType, StateObservable } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { map, switchMap, catchError, mergeMap, withLatestFrom, takeWhile } from "rxjs/operators";

import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { RootState } from "@Store/store";
import { Routes } from "@Types";

import {
  ActionPlansStateSlice,
  SetActionPlanTargetStatePayload,
  SetActionPlanTargetStateType,
  SetCarePlanInterventionProps,
  setActionPlanTargetState,
  setActionPlanTargetStateSuccess,
} from "./actionPlansStateSlice";
import { createSimpleAnalyticsEpic } from "../../analytics/store/analyticsEpics";
import { createNavigateToEpic } from "../../navigation/store/navigationEpics";
import { ActionPlanNormalized } from "../types/types";
import { handleInterventionSelected } from "../utils/carePlan.util";
import { actionPlanSchema } from "../utils/schemas";

const {
  getMemberHasTracking,
  getMemberHasTrackingSuccess,
  loadActionPlans,
  loadActionPlansSuccess,
  loadActionPlanTargetStateChoices,
  loadActionPlanTargetStateChoicesSuccess,
  loadActionPlanTargetStateFilters,
  loadActionPlanTargetStateFiltersSuccess,
  loadActionPlanTargetStateFiltersFail,
  setActionPlanTargetStateFail,
  setInterventionState,
  setInterventionStateSuccess,
  setInterventionStateFail,
  linkingInterventionTriggered,
  linkingInterventionNotFound,
  linkingInterventionSuccess,
} = ActionPlansStateSlice.actions;

const loadActionPlansEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(loadActionPlans.type),
    switchMap(() => {
      return vivanteCoreContainer
        .getActionPlanUseCaseFactory()
        .createGetActionPlanUseCase()
        .execute()
        .pipe(
          map((actionPlanArray: ActionPlan[]) => {
            if (actionPlanArray.length > 0) {
              const actionPlan = actionPlanArray[0];
              const actionPlanNormalized: ActionPlanNormalized = normalize(actionPlan, actionPlanSchema);

              return loadActionPlansSuccess(actionPlanNormalized);
            }

            return loadActionPlansSuccess({} as ActionPlanNormalized);
          }),
        );
    }),
  );
};

const getMemberHasTrackingEpic: Epic = (actions$: Observable<ActionCreatorWithoutPayload<string>>) => {
  return actions$.pipe(
    ofType(getMemberHasTracking.type),
    mergeMap(() => {
      const promise = vivanteCoreContainer.getActionPlanUseCaseFactory().createGetTrackingUsecase().execute();

      return from(promise).pipe(map((result) => getMemberHasTrackingSuccess(result)));
    }),
  );
};

const loadActionPlanTargetStateFiltersEpic: Epic = (actions$: Observable<ActionCreatorWithoutPayload<string>>) => {
  return actions$.pipe(
    ofType(loadActionPlanTargetStateFilters.type),
    map(() => {
      const actionPlanTargetStateFilters: ActionPlanTargetStateFilter[] = vivanteCoreContainer
        .getActionPlanUseCaseFactory()
        .createGetActionPlanTargetStateFilters()
        .execute();

      return loadActionPlanTargetStateFiltersSuccess(actionPlanTargetStateFilters);
    }),
    catchError(() => of(loadActionPlanTargetStateFiltersFail())),
  );
};

const loadActionPlanTargetStateChoicesEpic: Epic = (actions$: Observable<ActionCreatorWithoutPayload<string>>) => {
  return actions$.pipe(
    ofType(loadActionPlanTargetStateChoices.type),
    mergeMap(() => {
      const targetStateChoices = vivanteCoreContainer
        .getActionPlanUseCaseFactory()
        .createGetActionPlanTargetStateChoices()
        .execute();

      return of(loadActionPlanTargetStateChoicesSuccess(targetStateChoices));
    }),
  );
};

const setActionPlanTargetStateEpic: Epic = (
  actions$: Observable<SetActionPlanTargetStateType>,
  state$: StateObservable<RootState>,
) => {
  return actions$.pipe(
    ofType(setActionPlanTargetState.type),
    switchMap((action: SetActionPlanTargetStateType) => {
      return of(action).pipe(
        withLatestFrom(state$),
        map(
          ([action, state]) =>
            ({
              target:
                state.actionPlansState?.targetEntities[action.payload.targetId] ||
                state.actionPlansState?.driverEntities[action.payload.targetId] ||
                state.actionPlansState?.interventionEntities[action.payload.targetId],
              newState: action.payload.newTargetState,
            }) as unknown as SetTargetStateProps,
        ),
        switchMap((data) => {
          return from(
            vivanteCoreContainer
              .getActionPlanUseCaseFactory()
              .createSetTargetStateUseCase()
              .execute({ target: data.target, newState: data.newState }),
          ).pipe(
            map(() => {
              return setActionPlanTargetStateSuccess(action.payload);
            }),
            catchError(() => {
              // to do handle
              return of(setActionPlanTargetStateFail());
            }),
          );
        }),
      );
    }),
  );
};

const setInterventionStateEpic: Epic = (actions$: Observable<PayloadAction<SetCarePlanInterventionProps>>) => {
  return actions$.pipe(
    ofType(setInterventionState.type),
    switchMap((action: PayloadAction<SetCarePlanInterventionProps>) => {
      return from(
        vivanteCoreContainer.getActionPlanUseCaseFactory().createSetInterventionStateUseCase().execute(action.payload),
      ).pipe(
        map(() => {
          return setInterventionStateSuccess(action.payload);
        }),
        catchError((e) => {
          return of(setInterventionStateFail(e));
        }),
      );
    }),
  );
};

const navigateToHomeEpic = createNavigateToEpic<string>(setInterventionStateSuccess.type, () => ({
  path: `${Routes.HOME}`,
  screenName: "Home",
}));

/**
 * Retrieves actionPlan and check for an intervention that matches given `linkedInterventionIds`.
 * the ids are consist of all possible linked intervention ids.
 *
 * Check `linking-survey-document.md` for more details of linking intervention logic
 * */
const linkingInterventionTriggeredEpic: Epic = (actions$: Observable<PayloadAction<string[]>>) => {
  return actions$.pipe(
    ofType(linkingInterventionTriggered.type),
    switchMap((action: PayloadAction<string[]>) => {
      return vivanteCoreContainer
        .getActionPlanUseCaseFactory()
        .createGetActionPlanUseCase()
        .execute()
        .pipe(
          map((actionPlanArray) => {
            if (actionPlanArray.length > 0) {
              const actionPlan = actionPlanArray[0];
              const linkedInterventionIds = action.payload;

              /**
               * Find the intervention that matches given linkedInterventionIds
               * If that exists, open the intervention right away and unsubscribe this data stream
               * */
              const linkedIntervention = actionPlan.interventions?.find((intervention) =>
                linkedInterventionIds.find((id) => id === intervention.id),
              );

              if (!linkedIntervention || linkedIntervention.action === undefined) {
                return linkingInterventionNotFound();
              }

              // Update linked intervention's state to be opened with link
              // This way, the intervention won't be opened by different survey accidentally
              vivanteCoreContainer.getActionPlanUseCaseFactory().createSetInterventionStateUseCase().execute({
                intervention: linkedIntervention,
                newState: "OPENED_WITH_LINK",
              });

              // Extract url to navigate a screen specified by the linked intervention
              const selectedInterventionAction = handleInterventionSelected(
                linkedIntervention.id,
                linkedIntervention.action,
              );

              if (selectedInterventionAction === undefined) {
                return linkingInterventionNotFound();
              }

              // Use replace here not to go back to the survey after landing on linked intervention
              Router.replace(selectedInterventionAction);

              return linkingInterventionSuccess();
            }

            return linkingInterventionNotFound();
          }),
          catchError(() => {
            Router.replace(Routes.HOME);
            return of(linkingInterventionNotFound());
          }),
          takeWhile((action) => action.type !== linkingInterventionSuccess.type), // kills stream when linking intervention is found
        );
    }),
  );
};

export const analyticsEpics: Epic[] = [
  createSimpleAnalyticsEpic<SetActionPlanTargetStatePayload>(
    setActionPlanTargetStateSuccess.type,
    (payload, state) => ({
      eventType: ClickStreamActivityEventType.ACTION_PLAN_TARGET_STATUS_UPDATED,
      activityContextExtra: {
        actionPlanId: state ? Object.keys(state.actionPlansState.actionPlanEntities)[0] : undefined,
        actionPlanModuleId: state?.actionPlansState.selectedActionPlanModuleId ?? undefined,
        actionPlanTargetId: payload?.targetId,
        targetState: payload?.newTargetState,
      },
    }),
  ),
];

export const actionPlanEpics = [
  getMemberHasTrackingEpic,
  loadActionPlansEpic,
  loadActionPlanTargetStateChoicesEpic,
  loadActionPlanTargetStateFiltersEpic,
  setActionPlanTargetStateEpic,
  setInterventionStateEpic,
  navigateToHomeEpic,
  linkingInterventionTriggeredEpic,
  ...analyticsEpics,
];
