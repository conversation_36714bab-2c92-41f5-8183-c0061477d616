import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";

import { appStrings } from "@Assets/app_strings";
import { CarePlanCard } from "@Components/CarePlanCard/CarePlanCard";
import { ModulesGroupedByPhaseType } from "@Features/carePlan/store/actionPlansStateSlice";
import { Routes } from "@Types";

type GroupedByPhaseProps = Readonly<{
  carePlanModulesGroupedByPhase?: ModulesGroupedByPhaseType;
  showCompletedByPhase?: Record<string, boolean>;
  setShowCompletedByPhase: (values: Record<string, boolean>) => void;
}>;

const CARE_PLAN_STRINGS = appStrings.features.carePlan;

export const GroupedByPhaseSection = ({
  carePlanModulesGroupedByPhase,
  showCompletedByPhase,
  setShowCompletedByPhase,
}: GroupedByPhaseProps) =>
  Object.entries(carePlanModulesGroupedByPhase ?? {}).map(([phaseName, details]) => {
    const showCompleted = showCompletedByPhase?.[phaseName];
    const showCompletedText = showCompleted
      ? CARE_PLAN_STRINGS.hideCompleted
      : `${CARE_PLAN_STRINGS.showCompleted} (${details?.completed?.length})`;

    return (
      <Box mb={7} key={phaseName}>
        <Typography variant="h2Serif">{`${details.title}: ${details.subtitle}`}</Typography>
        <Typography variant="body" mb={4}>
          {details.description}
        </Typography>
        <Box display="grid" gap={4}>
          {details?.incomplete && details?.incomplete?.length > 0
            ? details.incomplete.map((incompleteCard) => {
                const chipText = `${incompleteCard.progress.completed}/${incompleteCard.progress.total}`;
                const ctaText = incompleteCard.progress.completed
                  ? appStrings.buttonText.resume
                  : appStrings.buttonText.start;

                return (
                  <CarePlanCard
                    key={incompleteCard.id}
                    title={incompleteCard.title}
                    subtitle={incompleteCard.subtitle}
                    chipText={chipText}
                    isContentCompleted={false}
                    ctaText={ctaText}
                    href={`${Routes.CARE_PLAN}/${incompleteCard.id}`}
                    imageSrc={incompleteCard.isometricIcon}
                    imageAltText={CARE_PLAN_STRINGS.phaseIncompleteCardIconAltText}
                  />
                );
              })
            : null}
        </Box>

        {details?.completed && details?.completed?.length > 0 ? (
          <>
            <Button
              variant="tertiary"
              sx={{ mt: 4, mb: showCompleted ? 4 : 0 }}
              aria-label={`${showCompletedText}.`}
              onClick={() =>
                setShowCompletedByPhase({
                  ...showCompletedByPhase,
                  [phaseName]: !showCompleted,
                })
              }
            >
              {showCompletedText}
            </Button>
            <Box display="grid" gap={4}>
              {showCompleted &&
                details.completed.map((completedCard) => {
                  return (
                    <CarePlanCard
                      key={completedCard.id}
                      title={completedCard.title}
                      subtitle={completedCard.subtitle}
                      isContentCompleted
                      ctaText={appStrings.buttonText.complete}
                      href={`${Routes.CARE_PLAN}/${completedCard.id}`}
                      imageSrc={completedCard.isometricIcon}
                      imageAltText={CARE_PLAN_STRINGS.phaseCompleteCardIconAltText}
                    />
                  );
                })}
            </Box>
          </>
        ) : null}
      </Box>
    );
  });
