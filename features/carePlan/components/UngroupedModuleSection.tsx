import { useState } from "react";
import { Box, Button } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { CarePlanCard } from "@Components/CarePlanCard/CarePlanCard";
import { UngroupedModulesByStatus } from "@Features/carePlan/store/actionPlansStateSlice";
import { Routes } from "@Types";

const CARE_PLAN_STRINGS = appStrings.features.carePlan;

type UngroupedModuleSectionProps = Readonly<{
  carePlanModulesUngrouped: UngroupedModulesByStatus;
}>;

export const UngroupedModuleSection = ({ carePlanModulesUngrouped }: UngroupedModuleSectionProps) => {
  const [showCompleted, setShowCompleted] = useState(false);
  const { completedModules, incompleteModules } = carePlanModulesUngrouped;
  const showCompletedText = showCompleted
    ? CARE_PLAN_STRINGS.hideCompleted
    : `${CARE_PLAN_STRINGS.showCompleted} (${completedModules.length})`;

  return (
    <Box display="grid" gap={4}>
      {incompleteModules.map((module) => {
        const chipText = `${module.progress.completed}/${module.progress.total}`;
        const ctaText = module.progress.completed ? appStrings.buttonText.resume : appStrings.buttonText.start;

        return (
          <CarePlanCard
            key={module.id}
            title={module.title}
            subtitle={module.subtitle}
            chipText={chipText}
            isContentCompleted={false}
            ctaText={ctaText}
            href={`${Routes.CARE_PLAN}/${module.id}`}
            imageSrc={module.isometricIcon}
            imageAltText={CARE_PLAN_STRINGS.phaseIncompleteCardIconAltText}
          />
        );
      })}
      {completedModules?.length > 0 ? (
        <>
          <Button
            variant="tertiary"
            aria-label={`${showCompletedText}.`}
            onClick={() => setShowCompleted((prevShowCompleted) => !prevShowCompleted)}
          >
            {showCompletedText}
          </Button>
          {showCompleted &&
            completedModules.map((module) => {
              const chipText = `${module.progress.completed}/${module.progress.total}`;

              return (
                <CarePlanCard
                  key={module.id}
                  title={module.title}
                  subtitle={module.subtitle}
                  chipText={chipText}
                  isContentCompleted
                  ctaText={appStrings.buttonText.complete}
                  href={`${Routes.CARE_PLAN}/${module.id}`}
                  imageSrc={module.isometricIcon}
                  imageAltText={CARE_PLAN_STRINGS.phaseCompleteCardIconAltText}
                />
              );
            })}
        </>
      ) : null}
    </Box>
  );
};
