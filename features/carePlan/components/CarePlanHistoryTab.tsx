import { useState } from "react";
import { CarePlanIntervention } from "@vivantehealth/vivante-core";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import { color } from "@vivantehealth/design-tokens";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { BaseModal } from "@Components/BaseModal/BaseModal";
import { CarePlanCard } from "@Components/CarePlanCard/CarePlanCard";
import { ActionsHistoryType } from "@Features/carePlan/store/actionPlansStateSlice";
import { reduceModulesIntoGroupsByDate } from "@Features/carePlan/utils/actionPlanUtils";
import { Routes } from "@Types";

const CARE_PLAN_STRINGS = appStrings.features.carePlan;

type CarePlanHistoryTabProps = Readonly<{
  completedActionsHistory: ActionsHistoryType | null;
  completedInterventions?: CarePlanIntervention[];
}>;

export const CarePlanHistoryTab = ({ completedActionsHistory, completedInterventions }: CarePlanHistoryTabProps) => {
  const completedHistoryItemsLength = completedActionsHistory ? Object.entries(completedActionsHistory).length : 0;
  const showHistorySection = completedHistoryItemsLength > 0 || completedInterventions?.length;
  const [showAlert, setShowAlert] = useState<boolean>(false);
  const combinedHistory =
    completedInterventions?.reduce((history, intervention) => {
      return reduceModulesIntoGroupsByDate(history ?? {}, intervention, true);
    }, completedActionsHistory) || completedActionsHistory;

  if (!showHistorySection) {
    return (
      <Box display="flex" flexDirection="column" textAlign="center" alignItems="center" gap={4} maxWidth="564px">
        <AppIcon name="Completed" size="xl" />
        <Typography variant="h2Serif" color={color.text.default}>
          {CARE_PLAN_STRINGS.noHistoryMessage}
        </Typography>
      </Box>
    );
  }

  return (
    <>
      <BaseModal
        isModalOpen={showAlert}
        title={CARE_PLAN_STRINGS.alertTitle}
        onClose={() => setShowAlert(false)}
        bodyContent={
          <>
            <Typography variant="body">{CARE_PLAN_STRINGS.alertSubtitle}</Typography>
          </>
        }
      />

      {combinedHistory
        ? Object.entries(combinedHistory)
            .sort((a, b) => a[1].order - b[1].order)
            .map(([sectionTitle, { modules }]) => (
              <Box key={sectionTitle} mb={7}>
                <Typography variant="h2Serif" mb={4}>
                  {sectionTitle}
                </Typography>
                <Box display="grid" gap={4}>
                  {modules.map((card) => {
                    const formattedDate = dayjs(card.completedAt).format("MMMM DD");
                    const dateString = card.completedAt ? `${CARE_PLAN_STRINGS.completedOn} ${formattedDate}` : "";
                    const carePlanCardProps = {
                      dateString,
                      title: card.title,
                      isContentCompleted: true,
                      imageSrc: card.isometricIcon,
                      imageAltText: CARE_PLAN_STRINGS.phaseCompleteCardIconAltText,
                    };

                    return card.isIntervention ? (
                      <CarePlanCard {...carePlanCardProps} key={card.id} onClick={() => setShowAlert(true)} />
                    ) : (
                      <CarePlanCard {...carePlanCardProps} key={card.id} href={`${Routes.CARE_PLAN}/${card.id}`} />
                    );
                  })}
                </Box>
              </Box>
            ))
        : null}
    </>
  );
};
