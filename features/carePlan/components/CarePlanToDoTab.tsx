import { CarePlanIntervention } from "@vivantehealth/vivante-core";

import { ModulesGroupedByPhaseType, UngroupedModulesByStatus } from "@Features/carePlan/store/actionPlansStateSlice";

import { GroupedByPhaseSection } from "./GroupedByPhaseSection";
import { InterventionsSection } from "./InterventionCardsSection";
import { UngroupedModuleSection } from "./UngroupedModuleSection";

type CarePlanToDoProps = Readonly<{
  interventions?: CarePlanIntervention[];
  carePlanModulesGroupedByPhase: ModulesGroupedByPhaseType | null;
  carePlanModulesUngrouped: UngroupedModulesByStatus | null;
  showCompletedByPhase?: Record<string, boolean>;
  setShowCompletedByPhase: (values: Record<string, boolean>) => void;
}>;

export const CarePlanToDoTab = ({
  interventions,
  carePlanModulesGroupedByPhase,
  carePlanModulesUngrouped,
  showCompletedByPhase,
  setShowCompletedByPhase,
}: CarePlanToDoProps) => {
  const showUngroupedModules =
    !!carePlanModulesUngrouped?.completedModules.length || !!carePlanModulesUngrouped?.incompleteModules.length;

  return (
    <>
      {interventions && interventions?.length > 0 ? <InterventionsSection cards={interventions} /> : null}
      {showUngroupedModules ? <UngroupedModuleSection carePlanModulesUngrouped={carePlanModulesUngrouped} /> : null}
      {carePlanModulesGroupedByPhase ? (
        <GroupedByPhaseSection
          carePlanModulesGroupedByPhase={carePlanModulesGroupedByPhase}
          showCompletedByPhase={showCompletedByPhase}
          setShowCompletedByPhase={setShowCompletedByPhase}
        />
      ) : null}
    </>
  );
};
