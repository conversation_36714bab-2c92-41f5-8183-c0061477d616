import {
  ActionPlanDriver,
  ActionPlanModule,
  ActionPlanTargetState,
  CarePlanIntervention,
} from "@vivantehealth/vivante-core";

export type TargetSchema = Readonly<{
  id: string;
  state: ActionPlanTargetState;
  title: string;
  description: string;
  lockedReason?: string;
  drivers?: string[];
}>;

export type ActionPlanSchema = Readonly<{
  id: string;
  state: string;
  title: string;
  modules: string[];
  interventions: string[];
}>;

export type ActionPlanNormalized = Readonly<{
  entities: {
    actionPlan: { [id: string]: ActionPlanSchema };
    interventions: { [id: string]: CarePlanIntervention };
    modules: { [id: string]: ActionPlanModule };
    targets: { [id: string]: TargetSchema };
    drivers: { [id: string]: ActionPlanDriver };
  };
  result: string;
}>;
