import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import { ResetPasswordScreen } from "./ResetPasswordScreen";

const meta: Meta<typeof ResetPasswordScreen> = {
  decorators: cylinderThemeDecorator,
  title: "@Features/resetPassword/ResetPasswordScreen",
  component: ResetPasswordScreen,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof ResetPasswordScreen>;

export const Primary: Story = {
  args: {
    // eslint-disable-next-line no-alert
    onSubmit: (data) => alert(JSON.stringify(data)),
    // eslint-disable-next-line no-alert
    onBackToLogin: () => alert("back to login"),
  },
};
