import { SignInUseCase } from "@vivantehealth/vivante-core";
import { Box, Button, Grid, Paper, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { SPACING_16_PX, SPACING_24_PX } from "@Assets/style_constants";
import { AuthFormContainer } from "@Components/AuthFormContainer/AuthFormContainer";
import { FormInput } from "@Components/form/Fields";
import { Form } from "@Components/form/Form";
import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";

const BUTTON_STRINGS = appStrings.buttonText;
const PASSWORD_RESET_STRINGS = appStrings.features.resetPassword;
const AUTHENTICATION_STRINGS = appStrings.features.authentication;

type ResetPasswordScreenProps = Readonly<{
  onSubmit: React.ComponentProps<typeof Form>["onSubmit"];
  onBackToLogin: () => void;
  error?: string;
  showSuccessMessage: boolean;
  successMessageRef?: React.RefObject<HTMLDivElement | null>;
}>;

export const ResetPasswordScreen = ({
  onSubmit,
  onBackToLogin,
  error,
  showSuccessMessage,
  successMessageRef,
}: ResetPasswordScreenProps) => {
  const { formWidth } = useResponsiveStylesHook();

  return (
    <AuthFormContainer>
      <Paper sx={{ width: formWidth }}>
        <Form
          defaultValues={{ email: "" }}
          onSubmit={onSubmit}
          formHeader={PASSWORD_RESET_STRINGS.formHeader}
          formSubheader={PASSWORD_RESET_STRINGS.formSubtitle}
        >
          <Grid item mt={SPACING_24_PX} mb={SPACING_24_PX}>
            <FormInput
              label={appStrings.sharedFormText.email}
              name="email"
              type="text"
              required
              rules={{
                required: {
                  value: true,
                  message: appStrings.sharedFormText.requiredMessage,
                },
                pattern: {
                  value: SignInUseCase.EmailValidationRegex,
                  message: AUTHENTICATION_STRINGS.invalidEmailError,
                },
              }}
            />
          </Grid>
          <Grid item mt={SPACING_24_PX}>
            {(showSuccessMessage || error) && (
              <Box mb={SPACING_24_PX}>
                <Typography
                  ref={successMessageRef}
                  variant="caption"
                  aria-label={`Success: ${PASSWORD_RESET_STRINGS.sentEmailText}`}
                >
                  {PASSWORD_RESET_STRINGS.sentEmailText}
                </Typography>
              </Box>
            )}
            <Button type="submit" variant="primary" fullWidth>
              {BUTTON_STRINGS.reset}
            </Button>
          </Grid>
        </Form>
        <Button variant="tertiary" onClick={onBackToLogin} sx={{ mt: SPACING_16_PX }}>
          {BUTTON_STRINGS.backToLogin}
        </Button>
      </Paper>
    </AuthFormContainer>
  );
};
