import { useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";

import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import {
  authenticationStateSlice,
  authenticationStateSelector,
} from "@Features/authentication/store/authenticationStateSlice";
import { loginNow } from "@Features/landing/store/onboardingEpics";
import { isOfType } from "@Utils/isOfType";

import { ResetPasswordScreen } from "./components/ResetPasswordScreen";

const { resetPasswordResetStatus, resetUserPassword } = authenticationStateSlice.actions;

export const ResetPasswordScreenContainer = () => {
  const errorMessage = useSelector(authenticationStateSelector("resetPasswordError"))?.message;
  const emailSent = useSelector(authenticationStateSelector("passwordResetEmailSent"));
  const isLoading = useSelector(authenticationStateSelector("loadState")) === "loading";
  const dispatch = useDispatch();
  const successMessageRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if ((emailSent || errorMessage) && successMessageRef?.current) {
      successMessageRef?.current.focus();
    }
  }, [emailSent, errorMessage]);

  useEffect(() => {
    return () => {
      dispatch(resetPasswordResetStatus());
    };
  }, [dispatch]);

  const handleSubmit = (data: unknown) => {
    if (isOfType<{ email: string }>(data, ["email"])) {
      dispatch(resetUserPassword({ email: data.email }));
    }
  };

  const onBackToLogin = () => {
    dispatch(loginNow());
  };

  return (
    <>
      <ResetPasswordScreen
        onSubmit={handleSubmit}
        onBackToLogin={onBackToLogin}
        error={errorMessage}
        showSuccessMessage={emailSent}
        successMessageRef={successMessageRef}
      />
      <LoadingSpinner open={isLoading} overlayDrawer overlayHeader />
    </>
  );
};
