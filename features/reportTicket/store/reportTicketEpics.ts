import { PayloadAction } from "@reduxjs/toolkit";
import { Epic, ofType } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { catchError, map, switchMap } from "rxjs/operators";

import { vivanteCoreContainer } from "@Lib/vivanteCore";

import { submitReportTicket, submitReportTicketFail, submitReportTicketSuccess } from "./reportTicketSlice";
import { ReportTicket } from "../types/reportTicket.types";

const submitReportTicketEpic: Epic = (actions$: Observable<PayloadAction<ReportTicket>>) => {
  return actions$.pipe(
    ofType(submitReportTicket.type),
    switchMap((action: PayloadAction<ReportTicket>) =>
      from(
        vivanteCoreContainer.getTicketUseCaseFactory().createPostTicketUseCase().execute(action.payload.ticket),
      ).pipe(
        map(() => submitReportTicketSuccess()),
        catchError(() => of(submitReportTicketFail())),
      ),
    ),
  );
};

export const reportTicketEpics = [submitReportTicketEpic];
