// Expect this error as the action is actually used in epic and not in the reducer
/* eslint-disable @typescript-eslint/no-unused-vars */
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

import { RootState } from "@Store/store";
import { LoadState } from "@Types";

import { ReportTicket } from "../types/reportTicket.types";

export type ReportTicketState = Readonly<{
  loadState: LoadState;
}>;

export const initialState: ReportTicketState = {
  loadState: null,
};

export const reportTicketSlice = createSlice({
  name: "reportTicketState",
  initialState,
  reducers: {
    submitReportTicket: (state, _: PayloadAction<ReportTicket>) => ({
      ...state,
      loadState: "loading",
    }),
    submitReportTicketFail: (state) => ({
      ...state,
      loadState: "failure",
    }),
    submitReportTicketSuccess: (state) => ({
      ...state,
      loadState: "loaded",
    }),
    resetTicketReportState: (state) => ({
      ...state,
      ...initialState,
    }),
  },
});

export const { submitReportTicket, submitReportTicketFail, submitReportTicketSuccess, resetTicketReportState } =
  reportTicketSlice.actions;

export const getReportTicketLoadState = (state: RootState) => state.reportTicketState.loadState;

export const reportTicketStateReducer = reportTicketSlice.reducer;
