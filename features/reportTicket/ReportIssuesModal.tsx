import { Button } from "@mui/material";
import { FormProvider, useForm } from "react-hook-form";

import { appStrings } from "@Assets/app_strings";

import { BaseModal } from "../../components/BaseModal/BaseModal";
import { FormInput } from "../../components/form/Fields";

const REPORT_ISSUES_STRINGS = appStrings.features.reportIssuesModal;
const REPORT_ISSUES_DEFAULT_VALUES = { message: "" };
const MIN_MESSAGE_LENGTH = 8;
const MAX_MESSAGE_LENGTH = 65000;

export type OnIssueModalSendArgs = Readonly<{
  message: string;
}>;

type ReportIssuesModalProps = Readonly<{
  isOpen: boolean;
  onExitClick: () => void;
  onSend: ({ message }: OnIssueModalSendArgs) => void;
}>;

export const ReportIssuesModal = ({ isOpen, onExitClick, onSend }: ReportIssuesModalProps) => {
  const methods = useForm({ defaultValues: REPORT_ISSUES_DEFAULT_VALUES });
  const { handleSubmit, watch } = methods;
  const isSendDisabled = watch("message")?.trim().length === 0;

  return (
    <BaseModal
      isModalOpen={isOpen}
      title={REPORT_ISSUES_STRINGS.header}
      onClose={onExitClick}
      bodyContent={
        <FormProvider {...methods}>
          <form
            onSubmit={handleSubmit((data: typeof REPORT_ISSUES_DEFAULT_VALUES) => {
              onSend(data);
            })}
            style={{ height: "100%" }}
          >
            <FormInput
              name="message"
              label={REPORT_ISSUES_STRINGS.formLabel}
              required
              type="text"
              numberOfRows={8}
              rules={{
                required: {
                  value: true,
                  message: appStrings.sharedFormText.requiredMessage,
                },
                minLength: {
                  value: MIN_MESSAGE_LENGTH,
                  message: REPORT_ISSUES_STRINGS.messageLengthError,
                },
                maxLength: {
                  value: MAX_MESSAGE_LENGTH,
                  message: REPORT_ISSUES_STRINGS.messageLengthError,
                },
              }}
            />

            <Button variant="primary" type="submit" disabled={isSendDisabled} fullWidth sx={{ mt: 5 }}>
              {appStrings.buttonText.send}
            </Button>
          </form>
        </FormProvider>
      }
      alignTitleSection="flex-start"
    />
  );
};
