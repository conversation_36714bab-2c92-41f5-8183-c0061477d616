import { Action } from "@reduxjs/toolkit";
import { Epic, ofType } from "redux-observable";
import { Observable } from "rxjs";
import { switchMap } from "rxjs/operators";

import { BootstrapStateSlice } from "./bootstrapStateSlice";
import { ActionPlansStateSlice } from "../../carePlan/store/actionPlansStateSlice";
import { CareTeamStateSlice } from "../../careTeam/store/careTeamStateSlice";
import { CourseItemsStateSlice } from "../../courses/store/courseItemsStateSlice";
import { MemberConvertedToVirtualClinicStateSlice } from "../../memberConvertedToVirtualClinic/store/memberConvertedToVirtualClinicStateSlice";
import { SymptomLoggingStateSlice } from "../../symptomLogging/store/symptomLoggingStateSlice";
import { ToDosStateSlice } from "../../toDos/store/toDosStateSlice";

const bootstrapEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(BootstrapStateSlice.actions.bootstrap.type),

    switchMap(() => [
      SymptomLoggingStateSlice.actions.loadSymptomLogQuestion(),
      CareTeamStateSlice.actions.loadCareTeam(),
      ToDosStateSlice.actions.loadToDos(),
      CourseItemsStateSlice.actions.loadCourseItems(),
      ActionPlansStateSlice.actions.loadActionPlans(),
      MemberConvertedToVirtualClinicStateSlice.actions.checkConvertedStatus(),
    ]),
  );
};

export const bootstrapEpics = [bootstrapEpic];
