import { createSlice } from "@reduxjs/toolkit";

import { LoadState } from "@Types";
import { buildSliceStateSelector } from "@Utils/slice.util";

/// //////////////////////////////////////////////////////
/// state

export type BootstrapState = Readonly<{
  bootstrapped: boolean;
  loadState: LoadState;
}>;

export const initialState: BootstrapState = {
  bootstrapped: false,
  loadState: "loading",
};

/// //////////////////////////////////////////////////////
/// slice

export const BootstrapStateSlice = createSlice({
  name: "bootstrapState",
  initialState,
  reducers: {
    bootstrap: (state: BootstrapState) => ({
      ...state,
      bootstrapped: true,
    }),
    unmount: (state) => state,
  },
});

/// //////////////////////////////////////////////////////
/// selectors

export const bootstrapStateSelector = buildSliceStateSelector("bootstrapState");

export const bootstrapStateReducer = BootstrapStateSlice.reducer;
