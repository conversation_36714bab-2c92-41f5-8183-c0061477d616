import { Appointment, ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { vi, describe, beforeEach, test, expect, afterEach } from "vitest";

import { appStrings } from "@Assets/app_strings";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";
import { mockedNextRouter } from "@TestUtils/mockedNextRouter.util";
import { TestWrapper } from "@TestUtils/TestWrapper";
import { Routes } from "@Types";

import { LOCAL_STORAGE_CARE_GUIDE_ORIGINATING_SCREEN, LOCAL_STORAGE_CARE_GUIDE_SCREEN } from "./assets/constants";
import { CareGuide } from "./CareGuide";
import { CareGuideReschedulingModal } from "./components/CareGuideReschedulingModal";
import { GET_CARE_GUIDE_TIME_SLOTS_MOCK } from "./mocks/careGuide.mocks";
import { transformCareGuideTimeSlots } from "./utils/transformCareGuideTimeSlots";
import {
  AppointmentReschedulingErrorDetail,
  AppointmentSchedulingError,
} from "../appointmentsNew/utils/processAppointmentSchedulingErrors";

const CARE_GUIDE_STRINGS = appStrings.features.careGuide;
const APPOINTMENT_SCHEDULING_STRINGS = appStrings.features.scheduling;
const CARE_TEAM_STRINGS = appStrings.features.careTeam;
const BUTTON_STRINGS = appStrings.buttonText;

const { mockedRouterReplace, mockedRouterPush } = mockedNextRouter;

vi.mock("./hooks/useCareGuideStatus", () => ({
  useCareGuideStatus: vi
    .fn()
    .mockReturnValue({ careGuideStatus: "in_progress", updateCareGuideStatus: vi.fn() })
    .mockReturnValueOnce({ careGuideStatus: "loading", updateCareGuideStatus: vi.fn() })
    .mockReturnValueOnce({ careGuideStatus: "completed", updateCareGuideStatus: vi.fn() })
    .mockReturnValueOnce({ careGuideStatus: "not_applicable", updateCareGuideStatus: vi.fn() }),
}));

const mockedAnalytics = vi.fn();

vi.mock("@Hooks/analyticsHook", () => ({
  useAnalyticsHook: () => ({
    sendEventAnalytics: mockedAnalytics,
  }),
}));

const mockedGetCareGuideTimeSlotsQuery = vi.fn();
const mockedCreateCareGuideAppointmentMutation = vi.fn().mockReturnValue([vi.fn(), { isLoading: false }]);
const mockedRescheduleCareGuideAppointmentMutation = vi.fn().mockReturnValue([vi.fn(), { isLoading: false }]);

vi.mock("./api/careGuideApi", async () => {
  const actual = await vi.importActual("./api/careGuideApi");

  return {
    ...actual,
    useGetCareGuideTimeSlotsQuery: () => mockedGetCareGuideTimeSlotsQuery(),
    useCreateCareGuideAppointmentMutation: () => mockedCreateCareGuideAppointmentMutation(),
    useRescheduleCareGuideAppointmentMutation: () => mockedRescheduleCareGuideAppointmentMutation(),
  };
});

describe("CareGuide", () => {
  const localStorageRemoveItemSpy = vi.spyOn(Storage.prototype, "removeItem");
  const snackbarSpy = vi.spyOn(SnackbarStateSlice.actions, "toggleSnackbar");

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => localStorage.clear());

  test.skip("Renders the loading spinner when careGuideStatus is 'loading'", () => {
    render(<CareGuide />, { wrapper: TestWrapper });

    expect(screen.getByRole("progressbar", { hidden: true })).toBeInTheDocument();
  });

  test.skip("Redirects to home when careGuideStatus is 'completed'", () => {
    render(<CareGuide />, { wrapper: TestWrapper });

    expect(mockedRouterReplace).toHaveBeenCalledWith(Routes.HOME);
  });

  test.skip("Redirects to home when careGuideStatus is 'not_applicable'", () => {
    render(<CareGuide />, { wrapper: TestWrapper });

    expect(mockedRouterReplace).toHaveBeenCalledWith(Routes.HOME);
  });

  test.skip("Renders the CareGuideLanding component when currentScreen is 'landing'", () => {
    render(<CareGuide />, { wrapper: TestWrapper });

    expect(screen.getByText(CARE_GUIDE_STRINGS.landing.welcome)).toBeInTheDocument();
    expect(screen.getByText(CARE_GUIDE_STRINGS.landing.launchJourney)).toBeInTheDocument();
    expect(screen.getByText(CARE_GUIDE_STRINGS.landing.journeyStarted)).toBeInTheDocument();
    // Ensure the cards have the correct heading size
    expect(screen.getAllByRole("heading", { level: 3 })).toHaveLength(2);
    // Support Card
    expect(screen.getByText(CARE_GUIDE_STRINGS.landing.cards.support.header)).toBeInTheDocument();
    expect(screen.getByText(CARE_GUIDE_STRINGS.landing.cards.support.body)).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: CARE_GUIDE_STRINGS.landing.cards.support.buttonText }),
    ).toBeInTheDocument();
    // Own Pace Card
    expect(screen.getByText(CARE_GUIDE_STRINGS.landing.cards.ownPace.header)).toBeInTheDocument();
    expect(screen.getByText(CARE_GUIDE_STRINGS.landing.cards.ownPace.body)).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: CARE_GUIDE_STRINGS.landing.cards.ownPace.buttonText }),
    ).toBeInTheDocument();
  });

  test.skip("Renders the ScheduleLanding component after clicking the lets get started button", () => {
    render(<CareGuide />, { wrapper: TestWrapper });

    fireEvent.click(screen.getByRole("button", { name: CARE_GUIDE_STRINGS.landing.cards.support.buttonText }));
    // Ensure images (video call and overlayed image) and headers are present
    expect(screen.getAllByRole("img")).toHaveLength(2);
    expect(screen.getByText(CARE_GUIDE_STRINGS.scheduleLanding.header)).toBeInTheDocument();
    expect(screen.getByText(CARE_GUIDE_STRINGS.scheduleLanding.body)).toBeInTheDocument();
    // Ensure the card with bullet points and stats is present
    CARE_GUIDE_STRINGS.scheduleLanding.card.bulletPoints.forEach((bulletPoint) => {
      expect(screen.getByText(bulletPoint)).toBeInTheDocument();
    });
    expect(screen.getByText(CARE_GUIDE_STRINGS.scheduleLanding.card.percentage)).toBeInTheDocument();
    expect(screen.getByText(CARE_GUIDE_STRINGS.scheduleLanding.card.percentageText)).toBeInTheDocument();
  });

  test("Renders the Scheduling component with no time slots", () => {
    mockedGetCareGuideTimeSlotsQuery.mockReturnValue({ data: {}, isLoading: false });
    render(<CareGuide />, { wrapper: TestWrapper });
    // Verify we are on the scheduling component
    expect(screen.getByText(CARE_TEAM_STRINGS.schedulingModal.promptTwo("Care Guide"))).toBeInTheDocument();
    const communicationMethodPhoneBtn = screen.getByRole("button", {
      name: CARE_TEAM_STRINGS.schedulingModal.phoneCall,
    });

    expect(communicationMethodPhoneBtn).toBeInTheDocument();
    expect(
      screen.getByRole("button", {
        name: CARE_TEAM_STRINGS.schedulingModal.video,
      }),
    ).toBeInTheDocument();
    expect(screen.getByText(CARE_TEAM_STRINGS.schedulingModal.promptThree)).toBeInTheDocument();
    // This indicates that no communication method is selected
    expect(screen.getByText(CARE_GUIDE_STRINGS.scheduling.selectACommunicationMethod)).toBeInTheDocument();

    fireEvent.click(communicationMethodPhoneBtn);
    // This indicates no time slots available
    expect(screen.getByText(CARE_GUIDE_STRINGS.scheduling.noAvailableAppointmentsCareGuide)).toBeInTheDocument();
  });

  test("Renders the SchedulingConfirmation component after selecting phone, time slot and continuing", () => {
    const mockedTransformedData = transformCareGuideTimeSlots(GET_CARE_GUIDE_TIME_SLOTS_MOCK);

    mockedGetCareGuideTimeSlotsQuery.mockReturnValue({
      data: mockedTransformedData,
      isLoading: false,
    });
    render(<CareGuide />, { wrapper: TestWrapper });

    expect(screen.getByText(CARE_TEAM_STRINGS.schedulingModal.promptTwo("Care Guide"))).toBeInTheDocument();
    const communicationMethodPhoneBtn = screen.getByRole("button", {
      name: CARE_TEAM_STRINGS.schedulingModal.phoneCall,
    });

    fireEvent.click(communicationMethodPhoneBtn);
    const continueBtn = screen.getByRole("button", { name: BUTTON_STRINGS.continue });

    // Time slots loaded but continue button is disabled until time slot is selected
    expect(continueBtn).toBeDisabled();
    fireEvent.click(screen.getAllByText("9:00 - 9:30 AM")[0]);
    expect(continueBtn).toBeEnabled();
    fireEvent.click(continueBtn);
    // Ensure the SchedulingConfirmation component is rendered with the correct information
    expect(screen.getByText("Sunday, October 1, 2023 at 9:00AM")).toBeInTheDocument();
    expect(screen.getByText(CARE_TEAM_STRINGS.phoneCallVisit)).toBeInTheDocument();
    appStrings.features.scheduling.appointmentConfirmation.getReadyBulletPoints.slice(2).forEach((bulletPoint) => {
      expect(screen.getByText(bulletPoint)).toBeInTheDocument();
    });
  });

  test("Renders the SchedulingConfirmation component after selecting video, time slot and continuing and allow to edit", () => {
    const mockedTransformedData = transformCareGuideTimeSlots(GET_CARE_GUIDE_TIME_SLOTS_MOCK);

    mockedGetCareGuideTimeSlotsQuery.mockReturnValue({
      data: mockedTransformedData,
      isLoading: false,
    });
    render(<CareGuide />, { wrapper: TestWrapper });

    fireEvent.click(
      screen.getByRole("button", {
        name: CARE_TEAM_STRINGS.schedulingModal.video,
      }),
    );
    fireEvent.click(screen.getAllByText("9:00 - 9:30 AM")[0]);
    fireEvent.click(screen.getByRole("button", { name: BUTTON_STRINGS.continue }));
    // Ensure the SchedulingConfirmation component is rendered with the correct information
    expect(screen.getByText("Sunday, October 1, 2023 at 9:00AM")).toBeInTheDocument();
    expect(screen.getByText(CARE_TEAM_STRINGS.videoVisit)).toBeInTheDocument();
    appStrings.features.scheduling.appointmentConfirmation.getReadyBulletPoints
      .slice(1, appStrings.features.scheduling.appointmentConfirmation.getReadyBulletPoints.length - 1)
      .forEach((bulletPoint) => {
        expect(screen.getByText(bulletPoint)).toBeInTheDocument();
      });

    fireEvent.click(screen.getByRole("button", { name: BUTTON_STRINGS.edit }));
    fireEvent.click(screen.getAllByText("10:00 - 10:30 AM")[0]);
    fireEvent.click(screen.getByRole("button", { name: BUTTON_STRINGS.continue }));
    expect(screen.getByText("Sunday, October 1, 2023 at 10:00AM")).toBeInTheDocument();
  });

  test.skip("Renders GettingStarted after successful appointment confirmation and allows continuing to Home page", async () => {
    const mockedTransformedData = transformCareGuideTimeSlots(GET_CARE_GUIDE_TIME_SLOTS_MOCK);

    mockedGetCareGuideTimeSlotsQuery.mockReturnValue({
      data: mockedTransformedData,
      isLoading: false,
    });
    mockedCreateCareGuideAppointmentMutation.mockReturnValue([
      vi.fn().mockReturnValue({ unwrap: vi.fn() }),
      { isLoading: false },
    ]);
    render(<CareGuide />, { wrapper: TestWrapper });

    fireEvent.click(screen.getByRole("button", { name: CARE_GUIDE_STRINGS.landing.cards.support.buttonText }));
    fireEvent.click(screen.getByRole("button", { name: BUTTON_STRINGS.continue }));
    fireEvent.click(
      screen.getByRole("button", {
        name: CARE_TEAM_STRINGS.schedulingModal.video,
      }),
    );
    fireEvent.click(screen.getAllByText("9:00 - 9:30 AM")[0]);
    fireEvent.click(screen.getByRole("button", { name: BUTTON_STRINGS.continue }));
    fireEvent.click(screen.getByRole("button", { name: BUTTON_STRINGS.confirm }));
    // await waitFor(() =>
    //   expect(snackbarSpy).toHaveBeenCalledWith({
    //     message: CARE_GUIDE_STRINGS.scheduling.appointmentConfirmed,
    //     isOpen: true,
    //   }),
    // );
    // Verify we navigate to the GettingStarted component
    await screen.findAllByTestId("care-guide-welcome-video");
    // Go back button should only render if the user is coming from the own pace flow
    expect(screen.queryByRole("button", { name: BUTTON_STRINGS.goBack })).not.toBeInTheDocument();
    fireEvent.click(screen.getByRole("button", { name: BUTTON_STRINGS.continue }));
    await waitFor(() => expect(mockedRouterReplace).toHaveBeenCalledWith(Routes.HOME));
    expect(mockedAnalytics).toHaveBeenCalledWith(ClickStreamActivityEventType.CARE_GUIDE_SCHEDULED_WITH_CARE_GUIDE);
    expect(localStorageRemoveItemSpy).toHaveBeenCalledWith(LOCAL_STORAGE_CARE_GUIDE_SCREEN);
    expect(localStorageRemoveItemSpy).toHaveBeenCalledWith(LOCAL_STORAGE_CARE_GUIDE_ORIGINATING_SCREEN);
  });

  test.skip("Renders an error on appointment confirmation attempt", async () => {
    const mockedTransformedData = transformCareGuideTimeSlots(GET_CARE_GUIDE_TIME_SLOTS_MOCK);

    mockedGetCareGuideTimeSlotsQuery.mockReturnValue({
      data: mockedTransformedData,
      isLoading: false,
    });
    const mockedError: AppointmentSchedulingError = {
      status: 400,
      data: {
        code: 400,
        detail: "the appointment time is no longer available",
      },
    };

    mockedCreateCareGuideAppointmentMutation.mockReturnValue([
      vi.fn().mockReturnValue({ unwrap: vi.fn().mockRejectedValue(mockedError) }),
      { isLoading: false },
    ]);
    render(<CareGuide />, { wrapper: TestWrapper });

    fireEvent.click(screen.getByRole("button", { name: CARE_GUIDE_STRINGS.landing.cards.support.buttonText }));
    fireEvent.click(screen.getByRole("button", { name: BUTTON_STRINGS.continue }));
    fireEvent.click(
      screen.getByRole("button", {
        name: CARE_TEAM_STRINGS.schedulingModal.video,
      }),
    );
    fireEvent.click(screen.getAllByText("9:00 - 9:30 AM")[0]);
    fireEvent.click(screen.getByRole("button", { name: BUTTON_STRINGS.continue }));
    fireEvent.click(screen.getByRole("button", { name: BUTTON_STRINGS.confirm }));
    await waitFor(() =>
      expect(snackbarSpy).toHaveBeenCalledWith({
        message: APPOINTMENT_SCHEDULING_STRINGS.schedulingError,
        isOpen: true,
        variant: "error",
      }),
    );
  });

  test.skip("Renders ReadyToMeet component after clicking through GettingStarted and buttons route correctly", async () => {
    render(<CareGuide />, { wrapper: TestWrapper });

    fireEvent.click(screen.getByRole("button", { name: CARE_GUIDE_STRINGS.landing.cards.ownPace.buttonText }));
    // Verify we are on the GettingStarted component
    expect(screen.getByRole("button", { name: BUTTON_STRINGS.goBack })).toBeInTheDocument();
    expect(screen.getByText(CARE_GUIDE_STRINGS.gettingStarted.header)).toBeInTheDocument();
    expect(screen.getByText(CARE_GUIDE_STRINGS.gettingStarted.body)).toBeInTheDocument();
    expect(screen.getByTestId("care-guide-welcome-video")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: BUTTON_STRINGS.continue })).toBeInTheDocument();
    fireEvent.click(screen.getByRole("button", { name: BUTTON_STRINGS.continue }));
    //Verify we are on the ReadyToMeet component
    expect(screen.getByText(CARE_GUIDE_STRINGS.readyToMeet.header)).toBeInTheDocument();
    expect(screen.getByText(CARE_GUIDE_STRINGS.readyToMeet.body)).toBeInTheDocument();
    const doThisLaterBtn = screen.getByRole("button", { name: CARE_GUIDE_STRINGS.readyToMeet.doThisLater });
    const scheduleCallBtn = screen.getByRole("button", { name: CARE_GUIDE_STRINGS.readyToMeet.scheduleCall });

    expect(doThisLaterBtn).toBeInTheDocument();
    expect(scheduleCallBtn).toBeInTheDocument();
    // Ensure clicking do this later button redirects to home and calls the proper analytics event and removes expected properties from local storage
    fireEvent.click(doThisLaterBtn);

    await waitFor(() => {
      expect(mockedRouterPush).toHaveBeenCalledWith(Routes.HOME);
    });
    expect(mockedAnalytics).toHaveBeenCalledWith(ClickStreamActivityEventType.CARE_GUIDE_REDIRECT_TO_H0ME_PAGE);
    expect(localStorageRemoveItemSpy).toHaveBeenCalledWith(LOCAL_STORAGE_CARE_GUIDE_SCREEN);
    expect(localStorageRemoveItemSpy).toHaveBeenCalledWith(LOCAL_STORAGE_CARE_GUIDE_ORIGINATING_SCREEN);

    // Ensure clicking schedule call button redirects to care team scheduling and calls the proper analytics event (we can assume local storage cleanup from the prior assertions)
    fireEvent.click(scheduleCallBtn);

    await waitFor(() => {
      expect(mockedRouterPush).toHaveBeenCalledWith(Routes.CARE_TEAM_SCHEDULING);
    });
    expect(mockedAnalytics).toHaveBeenCalledWith(
      ClickStreamActivityEventType.CARE_GUIDE_REDIRECT_TO_CARE_TEAM_SCHEDULING,
    );
  });
});

const mockExistingAppointment: Appointment = {
  id: "a4f64367-663e-4130-8314-dbc61405fb06",
  slot: {
    start: new Date("2025-04-11T14:30:00.000Z"),
    end: new Date("2025-04-11T14:45:00.000Z"),
  },
  practitioner: {
    id: "17467bbc78424b47908d97a3b4acedb5",
    type: "com.canvasmedical.practitioner",
    firstName: "Jackie",
    lastName: "C",
    avatarLink: "https://s3-us-west-2.amazonaws.com/canvas-avatars/avatar1.png",
    role: "CG",
  },
  communicationMethod: "VIDEO",
  canEditUntil: new Date("2025-04-11T14:29:00.000Z"),
  meetingLink: "https://mock.zoom.us/j/123456",
};

const mockCloseModal = vi.fn();

describe("CareGuideReschedulingModal", () => {
  const snackbarSpy = vi.spyOn(SnackbarStateSlice.actions, "toggleSnackbar");

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test("Renders the Rescheduling care guide appointment modal", async () => {
    mockedGetCareGuideTimeSlotsQuery.mockReturnValue({ data: {}, isLoading: false });
    render(
      <CareGuideReschedulingModal
        isModalOpen
        existingAppointment={mockExistingAppointment}
        closeSchedulingModal={mockCloseModal}
      />,
      { wrapper: TestWrapper },
    );

    // Verify we are on the scheduling component
    expect(screen.getByText(CARE_TEAM_STRINGS.schedulingModal.promptTwo("Care Guide"))).toBeInTheDocument();
    const communicationMethodPhoneBtn = screen.getByRole("button", {
      name: CARE_TEAM_STRINGS.schedulingModal.phoneCall,
    });

    expect(communicationMethodPhoneBtn).toBeInTheDocument();
    expect(
      screen.getByRole("button", {
        name: CARE_TEAM_STRINGS.schedulingModal.video,
      }),
    ).toBeInTheDocument();
    expect(screen.getByText(CARE_TEAM_STRINGS.schedulingModal.promptThree)).toBeInTheDocument();
    // This indicates that no communication method is selected
    expect(screen.getByText(CARE_GUIDE_STRINGS.scheduling.selectACommunicationMethod)).toBeInTheDocument();

    fireEvent.click(communicationMethodPhoneBtn);
    // This indicates no time slots available
    await screen.findByText(CARE_GUIDE_STRINGS.scheduling.noAvailableAppointmentsCareGuide);
    // Ensure use can close the modal via the cancel button
    fireEvent.click(screen.getByRole("button", { name: BUTTON_STRINGS.cancel }));
    expect(mockCloseModal).toHaveBeenCalled();
  });

  test("Renders the time slots and clicks through to the confirmation screen and schedules successfully", async () => {
    const mockedTransformedData = transformCareGuideTimeSlots(GET_CARE_GUIDE_TIME_SLOTS_MOCK);

    mockedGetCareGuideTimeSlotsQuery.mockReturnValue({
      data: mockedTransformedData,
      isLoading: false,
    });
    mockedRescheduleCareGuideAppointmentMutation.mockReturnValue([
      vi.fn().mockReturnValue({ unwrap: vi.fn() }),
      { isLoading: false },
    ]);

    render(
      <CareGuideReschedulingModal
        isModalOpen
        existingAppointment={mockExistingAppointment}
        closeSchedulingModal={mockCloseModal}
      />,
      { wrapper: TestWrapper },
    );

    const nextBtn = screen.getByRole("button", { name: BUTTON_STRINGS.next });

    expect(nextBtn).toBeDisabled();
    fireEvent.click(
      screen.getByRole("button", {
        name: CARE_TEAM_STRINGS.schedulingModal.phoneCall,
      }),
    );
    fireEvent.click(screen.getAllByText("9:00 - 9:30 AM")[0]);

    expect(nextBtn).toBeEnabled();
    fireEvent.click(nextBtn);
    // Ensure the SchedulingConfirmation component is rendered with the correct information
    expect(screen.getByText("Sunday, October 1, 2023 at 9:00AM")).toBeInTheDocument();
    expect(screen.getByText(CARE_TEAM_STRINGS.phoneCallVisit)).toBeInTheDocument();
    appStrings.features.scheduling.appointmentConfirmation.getReadyBulletPoints.slice(2).forEach((bulletPoint) => {
      expect(screen.getByText(bulletPoint)).toBeInTheDocument();
    });

    // Ensure the user can go back to the scheduling screen
    fireEvent.click(screen.getByRole("button", { name: BUTTON_STRINGS.edit }));
    expect(screen.getByText(CARE_TEAM_STRINGS.schedulingModal.promptTwo("Care Guide"))).toBeInTheDocument();

    fireEvent.click(nextBtn);
    fireEvent.click(screen.getByRole("button", { name: BUTTON_STRINGS.confirm }));
    await waitFor(() =>
      expect(snackbarSpy).toHaveBeenCalledWith({
        message: CARE_GUIDE_STRINGS.scheduling.appointmentConfirmed,
        isOpen: true,
      }),
    );
  });

  test("Displays the error snackbar for different error events", async () => {
    const mockedTransformedData = transformCareGuideTimeSlots(GET_CARE_GUIDE_TIME_SLOTS_MOCK);

    mockedGetCareGuideTimeSlotsQuery.mockReturnValue({
      data: mockedTransformedData,
      isLoading: false,
    });

    const mockedError = (detail: AppointmentReschedulingErrorDetail): AppointmentSchedulingError => ({
      status: 400,
      data: {
        code: 400,
        detail,
      },
    });

    mockedRescheduleCareGuideAppointmentMutation.mockReturnValue([
      vi.fn().mockReturnValue({
        unwrap: vi
          .fn()
          .mockRejectedValueOnce(
            mockedError("can only reschedule appointments with a 'proposed', 'pending', or 'booked' status"),
          )
          .mockRejectedValueOnce(
            mockedError("cannot reschedule an appointment less than 1 minute before the start time"),
          )
          .mockRejectedValueOnce(mockedError("the appointment time is no longer available")),
      }),
      { isLoading: false },
    ]);

    render(
      <CareGuideReschedulingModal
        isModalOpen
        existingAppointment={mockExistingAppointment}
        closeSchedulingModal={mockCloseModal}
      />,
      { wrapper: TestWrapper },
    );

    const nextBtn = screen.getByRole("button", { name: BUTTON_STRINGS.next });

    expect(nextBtn).toBeDisabled();
    fireEvent.click(
      screen.getByRole("button", {
        name: CARE_TEAM_STRINGS.schedulingModal.phoneCall,
      }),
    );
    fireEvent.click(screen.getAllByText("9:00 - 9:30 AM")[0]);
    fireEvent.click(nextBtn);
    // Improper appointment status error
    fireEvent.click(screen.getByRole("button", { name: BUTTON_STRINGS.confirm }));
    await waitFor(() =>
      expect(snackbarSpy).toHaveBeenCalledWith({
        message: APPOINTMENT_SCHEDULING_STRINGS.rescheduleError,
        isOpen: true,
        variant: "error",
      }),
    );
    // Rescheduling within 1 minute of appointment error
    fireEvent.click(screen.getByRole("button", { name: BUTTON_STRINGS.confirm }));
    await waitFor(() =>
      expect(snackbarSpy).toHaveBeenCalledWith({
        message: APPOINTMENT_SCHEDULING_STRINGS.rescheduleLessThanTimeAllowed,
        isOpen: true,
        variant: "error",
      }),
    );
    // Time slot no longer available error
    fireEvent.click(screen.getByRole("button", { name: BUTTON_STRINGS.confirm }));
    await waitFor(() =>
      expect(snackbarSpy).toHaveBeenCalledWith({
        message: APPOINTMENT_SCHEDULING_STRINGS.rescheduleError,
        isOpen: true,
        variant: "error",
      }),
    );
  });
});
