import { bffApi } from "@Api/bffApi";
import { AppointmentTimeSlotsResponse } from "@Features/appointmentsNew/types/appointments.types";

import { CareGuideTimeSlots } from "../types/careGuide.types";
import { transformCareGuideTimeSlots } from "../utils/transformCareGuideTimeSlots";

export const careGuideApi = bffApi.injectEndpoints({
  endpoints: (builder) => ({
    /** Retrieves all available time slots for Care Guides */
    getCareGuideTimeSlots: builder.query<CareGuideTimeSlots, void>({
      query: () => ({
        url: "v1/slots?role_code=CG",
        method: "GET",
      }),
      providesTags: ["CareGuide"],
      transformResponse: (response: AppointmentTimeSlotsResponse) => transformCareGuideTimeSlots(response),
    }),
    /** Creates a Care Guide appointment */
    createCareGuideAppointment: builder.mutation<CreateCareGuideAppointmentResponse, CreateCareGuideAppointmentRequest>(
      {
        query: (body) => ({
          url: `v1/appointments?member_id=${body.memberId}`,
          method: "POST",
          body: {
            clinician_id: body.clinicianId,
            start: body.startTime,
            end: body.endTime,
            communication_method: body.communication_method,
            request_tracking_id: body.request_tracking_id,
          },
        }),
        invalidatesTags: ["CareGuide", "Appointment"],
      },
    ),
    rescheduleCareGuideAppointment: builder.mutation<
      CreateCareGuideAppointmentResponse,
      RescheduleCareGuideAppointmentRequest
    >({
      query: (body) => ({
        url: `v1/appointments/${body.appointmentId}?member_id=${body.memberId}`,
        method: "PATCH",
        body: {
          clinician_id: body.clinicianId,
          start: body.startTime,
          end: body.endTime,
          communication_method: body.communication_method,
        },
      }),
      invalidatesTags: ["CareGuide", "Appointment"],
    }),
  }),
});

export const {
  useGetCareGuideTimeSlotsQuery,
  useCreateCareGuideAppointmentMutation,
  useRescheduleCareGuideAppointmentMutation,
} = careGuideApi;

export type CareGuideAppointmentBaseRequest = Readonly<{
  memberId: string;
  clinicianId: string;
  startTime: string;
  endTime: string;
  communication_method: "phone" | "video";
}>;

type CreateCareGuideAppointmentRequest = CareGuideAppointmentBaseRequest &
  Readonly<{
    request_tracking_id: string;
  }>;

type RescheduleCareGuideAppointmentRequest = CareGuideAppointmentBaseRequest &
  Readonly<{
    appointmentId: string;
  }>;

type CreateCareGuideAppointmentResponse = {
  id: string;
  start: string;
  end: string;
  communication_method: "phone" | "video";
  request_tracking_id: string;
  requested_role_code: string;
  requested_specialty: string;
  meeting_link: string;
  status: string;
  clinician: {
    id: string;
    first_name: string;
    last_name: string;
    clinician_role: {
      code: string;
      description: string;
    };
    specialty: {
      code: string;
      description: string;
    };
    avatar_link: string;
    enabled: boolean;
    identifiers: {
      system: string;
      identifier: string;
    }[];
  };
};
