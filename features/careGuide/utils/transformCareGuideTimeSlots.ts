import dayjs from "dayjs";

import { AppointmentTimeSlotsResponse } from "@Features/appointmentsNew/types/appointments.types";

import { CareGuideTimeSlots } from "../types/careGuide.types";

export const transformCareGuideTimeSlots = (
  careGuideTimeSlotsResponse: AppointmentTimeSlotsResponse,
): CareGuideTimeSlots => {
  const timeSlotsMap = careGuideTimeSlotsResponse.available_slots.reduce<CareGuideTimeSlots>(
    (timeSlots, { clinician, slots }) => {
      slots?.forEach(({ start_time, end_time }) => {
        const slotDay = dayjs(start_time).format("dddd, MMMM D, YYYY");

        // If the slotDay is not in the timeSlots object, add it
        if (!(slotDay in timeSlots)) {
          timeSlots[slotDay] = {};
        }

        if (start_time in timeSlots[slotDay]) {
          timeSlots[slotDay][start_time].availableClinicianIds.push(clinician.id);
        } else {
          timeSlots[slotDay][start_time] = {
            startTime: start_time,
            endTime: end_time,
            availableClinicianIds: [clinician.id],
            requestTrackingId: careGuideTimeSlotsResponse.request_tracking_id,
          };
        }
      });

      return timeSlots;
    },
    {},
  );

  return timeSlotsMap;
};
