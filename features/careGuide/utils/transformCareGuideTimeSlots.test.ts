import { describe, test, expect } from "vitest";

import { AppointmentTimeSlotsResponse } from "@Features/appointmentsNew/types/appointments.types";

import { transformCareGuideTimeSlots } from "./transformCareGuideTimeSlots";
import { MOCKED_CLINICIAN_JOHN, MOCKED_CLINICIAN_JANE } from "../mocks/careGuide.mocks";
import { CareGuideTimeSlots } from "../types/careGuide.types";

describe("transformCareGuideTimeSlots", () => {
  test("Should handle empty available slots", () => {
    const careGuideTimeSlotsResponse: AppointmentTimeSlotsResponse = {
      available_slots: [],
      request_tracking_id: "tracking_id_123",
    };

    const result = transformCareGuideTimeSlots(careGuideTimeSlotsResponse);

    expect(result).toEqual({});
  });

  test("Should transform care guide time slots correctly", () => {
    const careGuideTimeSlotsResponse: AppointmentTimeSlotsResponse = {
      available_slots: [
        {
          clinician: MOCKED_CLINICIAN_JOHN,
          slots: [
            {
              start_time: "2023-10-01T09:00:00Z",
              end_time: "2023-10-01T09:30:00Z",
            },
          ],
        },
        {
          clinician: MOCKED_CLINICIAN_JANE,
          slots: [
            {
              start_time: "2023-10-01T09:00:00Z",
              end_time: "2023-10-01T09:30:00Z",
            },
          ],
        },
      ],
      request_tracking_id: "tracking_id_123",
    };

    const expectedOutput: CareGuideTimeSlots = {
      "Sunday, October 1, 2023": {
        "2023-10-01T09:00:00Z": {
          startTime: "2023-10-01T09:00:00Z",
          endTime: "2023-10-01T09:30:00Z",
          availableClinicianIds: ["1", "2"],
          requestTrackingId: "tracking_id_123",
        },
      },
    };

    const result = transformCareGuideTimeSlots(careGuideTimeSlotsResponse);

    expect(result).toEqual(expectedOutput);
  });

  test("Should handle multiple time slots and combine when same time slots are found", () => {
    const careGuideTimeSlotsResponse: AppointmentTimeSlotsResponse = {
      available_slots: [
        {
          clinician: MOCKED_CLINICIAN_JOHN,
          slots: [
            {
              start_time: "2023-10-01T09:00:00Z",
              end_time: "2023-10-01T09:30:00Z",
            },
            {
              start_time: "2023-10-01T10:00:00Z",
              end_time: "2023-10-01T10:30:00Z",
            },
          ],
        },
        {
          clinician: MOCKED_CLINICIAN_JANE,
          slots: [
            {
              start_time: "2023-10-01T10:00:00Z",
              end_time: "2023-10-01T10:30:00Z",
            },
          ],
        },
      ],
      request_tracking_id: "tracking_id_123",
    };

    const expectedOutput: CareGuideTimeSlots = {
      "Sunday, October 1, 2023": {
        "2023-10-01T09:00:00Z": {
          startTime: "2023-10-01T09:00:00Z",
          endTime: "2023-10-01T09:30:00Z",
          availableClinicianIds: ["1"],
          requestTrackingId: "tracking_id_123",
        },
        "2023-10-01T10:00:00Z": {
          startTime: "2023-10-01T10:00:00Z",
          endTime: "2023-10-01T10:30:00Z",
          availableClinicianIds: ["1", "2"],
          requestTrackingId: "tracking_id_123",
        },
      },
    };

    const result = transformCareGuideTimeSlots(careGuideTimeSlotsResponse);

    expect(result).toEqual(expectedOutput);
  });

  test("Should handle multiple time slots across multiple days", () => {
    const careGuideTimeSlotsResponse: AppointmentTimeSlotsResponse = {
      available_slots: [
        {
          clinician: MOCKED_CLINICIAN_JOHN,
          slots: [
            {
              start_time: "2023-10-01T09:00:00Z",
              end_time: "2023-10-01T09:30:00Z",
            },
            {
              start_time: "2023-10-01T10:00:00Z",
              end_time: "2023-10-01T10:30:00Z",
            },
            {
              start_time: "2023-10-02T09:00:00Z",
              end_time: "2023-10-02T09:30:00Z",
            },
            {
              start_time: "2023-10-03T09:00:00Z",
              end_time: "2023-10-03T09:30:00Z",
            },
          ],
        },
        {
          clinician: MOCKED_CLINICIAN_JANE,
          slots: [
            {
              start_time: "2023-10-01T10:00:00Z",
              end_time: "2023-10-01T10:30:00Z",
            },
            {
              start_time: "2023-10-02T10:00:00Z",
              end_time: "2023-10-02T10:30:00Z",
            },
          ],
        },
      ],
      request_tracking_id: "tracking_id_123",
    };

    const expectedOutput: CareGuideTimeSlots = {
      "Sunday, October 1, 2023": {
        "2023-10-01T09:00:00Z": {
          startTime: "2023-10-01T09:00:00Z",
          endTime: "2023-10-01T09:30:00Z",
          availableClinicianIds: ["1"],
          requestTrackingId: "tracking_id_123",
        },
        "2023-10-01T10:00:00Z": {
          startTime: "2023-10-01T10:00:00Z",
          endTime: "2023-10-01T10:30:00Z",
          availableClinicianIds: ["1", "2"],
          requestTrackingId: "tracking_id_123",
        },
      },
      "Monday, October 2, 2023": {
        "2023-10-02T09:00:00Z": {
          startTime: "2023-10-02T09:00:00Z",
          endTime: "2023-10-02T09:30:00Z",
          availableClinicianIds: ["1"],
          requestTrackingId: "tracking_id_123",
        },
        "2023-10-02T10:00:00Z": {
          startTime: "2023-10-02T10:00:00Z",
          endTime: "2023-10-02T10:30:00Z",
          availableClinicianIds: ["2"],
          requestTrackingId: "tracking_id_123",
        },
      },
      "Tuesday, October 3, 2023": {
        "2023-10-03T09:00:00Z": {
          startTime: "2023-10-03T09:00:00Z",
          endTime: "2023-10-03T09:30:00Z",
          availableClinicianIds: ["1"],
          requestTrackingId: "tracking_id_123",
        },
      },
    };

    const result = transformCareGuideTimeSlots(careGuideTimeSlotsResponse);

    expect(result).toEqual(expectedOutput);
  });
});
