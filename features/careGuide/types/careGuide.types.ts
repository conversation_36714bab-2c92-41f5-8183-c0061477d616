export type CareGuideScreens =
  | "landing"
  | "scheduleLanding"
  | "appointmentScheduling"
  | "appointmentConfirmation"
  | "gettingStarted"
  | "readyToMeet";

export type OriginatingCareGuideScreen = Extract<CareGuideScreens, "landing" | "appointmentConfirmation">;

export type CareGuideTimeSlot = Readonly<{
  startTime: string;
  endTime: string;
  availableClinicianIds: string[];
  requestTrackingId: string;
}>;

export type SelectedCareGuideTimeSlot = CareGuideTimeSlot & { timeSlotDayKey: string };

export type CareGuideTimeSlots = Record<string, Record<string, CareGuideTimeSlot>>;
