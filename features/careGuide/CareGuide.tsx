import { useState } from "react";
import { Box } from "@mui/material";

import { CareGuideLanding } from "./components/CareGuideLanding";
import { GettingStarted } from "./components/GettingStarted";
import { ReadyToMeet } from "./components/ReadyToMeet";
import { ScheduleLanding } from "./components/ScheduleLanding";
import { Scheduling } from "./components/scheduling/Scheduling";
import { CareGuideScreens } from "./types/careGuide.types";

const LARGER_MARGIN_TOP_SCREENS: CareGuideScreens[] = ["landing", "appointmentScheduling", "appointmentConfirmation"];

export const CareGuide = () => {
  const [currentScreen, setCurrentScreen] = useState<CareGuideScreens>("appointmentScheduling");
  const marginTop = LARGER_MARGIN_TOP_SCREENS.includes(currentScreen) ? 10 : 7;

  const handleGoToScreen = (screenName: CareGuideScreens) => {
    setCurrentScreen(screenName);
  };

  /**
   * NOTE: Only the scheduling screen is currently utilized for Care Guide scheduling.
   * We may reintroduce the other screens in the future and will leave the logic in place for now.
   * https://github.com/vivantehealth/zi/issues/9223
   */
  return (
    <Box
      sx={
        currentScreen === "appointmentScheduling"
          ? {}
          : {
              backgroundImage: "url(/images/careGuideImages/gradientBackground.webp)",
              backgroundSize: "cover",
              height: "100vh",
            }
      }
    >
      <Box display="flex" justifyContent="center" pt={marginTop}>
        {currentScreen === "landing" && <CareGuideLanding handleGoToScreen={handleGoToScreen} />}

        {currentScreen === "scheduleLanding" && <ScheduleLanding handleGoToScreen={handleGoToScreen} />}

        {currentScreen === "appointmentScheduling" && <Scheduling />}

        {currentScreen === "gettingStarted" && <GettingStarted handleGoToScreen={handleGoToScreen} />}

        {currentScreen === "readyToMeet" && <ReadyToMeet handleGoToScreen={handleGoToScreen} />}
      </Box>
    </Box>
  );
};
