import { <PERSON>, Button, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";

import { CareGuideContainer } from "./CareGuideContainer";
import { CareGuideScreens } from "../types/careGuide.types";

const CARE_GUIDE_STRINGS = appStrings.features.careGuide.scheduleLanding;
/** This is the phone image height from Figma https://www.figma.com/design/3B8qx17m83ItiSVYzFVCuD/Care-Guide?node-id=4385-6479&m=dev */
const GRADIENT_HEIGHT = "317px";
const CONTAINER_RELATIVE_TO_TOP = 225;

type ScheduleLandingProps = Readonly<{
  handleGoToScreen: (screenName: CareGuideScreens) => void;
}>;

const BulletPoint = ({ title }: { title: string }) => {
  return (
    <Box display="flex" alignItems="center" gap={2}>
      <AppIcon name="RightArrow" size="sm" />

      <Typography variant="bodyDense">{title}</Typography>
    </Box>
  );
};

export const ScheduleLanding = ({ handleGoToScreen }: ScheduleLandingProps) => {
  return (
    <CareGuideContainer handleGoBack={() => handleGoToScreen("landing")}>
      <Box display="flex" justifyContent="center">
        <Box
          sx={{
            height: GRADIENT_HEIGHT,
            maskImage: "linear-gradient(180deg, rgba(255,255,255,1) 60%, rgba(255,255,255,0) 90%)",
            position: "absolute",
          }}
        >
          <img src="/images/careGuideImages/videoCall.webp" alt={CARE_GUIDE_STRINGS.videoCallWithCareGuideAltText} />
        </Box>

        <Box
          display="flex"
          flexDirection="column"
          gap={3}
          alignItems="center"
          position="relative"
          top={CONTAINER_RELATIVE_TO_TOP}
        >
          <img src="/images/careGuideImages/phoneOrVideoCall.webp" alt={CARE_GUIDE_STRINGS.phoneOrVideoAltText} />

          <Typography variant="h2" mb={1}>
            {CARE_GUIDE_STRINGS.header}
          </Typography>

          <Typography variant="bodyDense" align="center">
            {CARE_GUIDE_STRINGS.body}
          </Typography>

          <Paper>
            <Box display="flex" flexDirection="column" gap={2}>
              {CARE_GUIDE_STRINGS.card.bulletPoints.map((bulletPoint) => (
                <BulletPoint title={bulletPoint} key={bulletPoint} />
              ))}
            </Box>

            <Paper sx={{ bgcolor: color.background.page, border: "none", p: 4, mt: 4 }}>
              <Box display="flex" gap={5}>
                <Typography variant="h1">{CARE_GUIDE_STRINGS.card.percentage}</Typography>

                <Typography variant="bodyDense">{CARE_GUIDE_STRINGS.card.percentageText}</Typography>
              </Box>
            </Paper>
          </Paper>

          <Button variant="primary" fullWidth onClick={() => handleGoToScreen("appointmentScheduling")} sx={{ mt: 3 }}>
            {appStrings.buttonText.continue}
          </Button>
        </Box>
      </Box>
    </CareGuideContainer>
  );
};
