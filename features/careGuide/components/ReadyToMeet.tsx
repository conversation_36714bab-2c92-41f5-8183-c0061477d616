import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { Box, Button, Typography } from "@mui/material";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { Routes } from "@Types";

import { CareGuideContainer } from "./CareGuideContainer";
import {
  CARE_GUIDE_WIDTH,
  LOCAL_STORAGE_CARE_GUIDE_ORIGINATING_SCREEN,
  LOCAL_STORAGE_CARE_GUIDE_SCREEN,
} from "../assets/constants";
import { useCareGuideStatus } from "../hooks/useCareGuideStatus";
import { CareGuideScreens } from "../types/careGuide.types";

const CARE_GUIDE_STRINGS = appStrings.features.careGuide.readyToMeet;
/** This is the phone image height from Figma https://www.figma.com/design/3B8qx17m83ItiSVYzFVCuD/Care-Guide?node-id=4385-12063&m=dev */
const GRADIENT_HEIGHT = "380px";
/** This is the width of the phone image which the text should be contained within */
const PHONE_IMAGE_WIDTH = "278px";

type ReadyToMeetProps = Readonly<{
  handleGoToScreen: (screenName: CareGuideScreens) => void;
}>;
export const ReadyToMeet = ({ handleGoToScreen }: ReadyToMeetProps) => {
  const router = useRouter();
  const { updateCareGuideStatus } = useCareGuideStatus();
  const { sendEventAnalytics } = useAnalyticsHook();

  const handleOnCareGuideFlowComplete = async (route: Routes.HOME | Routes.CARE_TEAM_SCHEDULING) => {
    await updateCareGuideStatus("completed");
    localStorage.removeItem(LOCAL_STORAGE_CARE_GUIDE_SCREEN);
    localStorage.removeItem(LOCAL_STORAGE_CARE_GUIDE_ORIGINATING_SCREEN);

    sendEventAnalytics(
      route === Routes.HOME
        ? ClickStreamActivityEventType.CARE_GUIDE_REDIRECT_TO_H0ME_PAGE
        : ClickStreamActivityEventType.CARE_GUIDE_REDIRECT_TO_CARE_TEAM_SCHEDULING,
    );

    router.push(route);
  };

  return (
    <CareGuideContainer handleGoBack={() => handleGoToScreen("gettingStarted")}>
      <Box
        display="flex"
        flexDirection="column"
        gap={4}
        justifyContent="center"
        alignItems="center"
        width={PHONE_IMAGE_WIDTH}
        ml={`calc((${CARE_GUIDE_WIDTH} - ${PHONE_IMAGE_WIDTH}) / 2 )`}
      >
        <Typography variant="h2Serif" align="center">
          {CARE_GUIDE_STRINGS.header}
        </Typography>

        <Typography variant="body" align="center">
          {CARE_GUIDE_STRINGS.body}
        </Typography>
      </Box>

      <Box
        sx={{
          height: GRADIENT_HEIGHT,
          maskImage: "linear-gradient(180deg, rgba(255,255,255,1) 60%, rgba(255,255,255,0) 100%)",
          display: "flex",
          justifyContent: "center",
        }}
      >
        <img src="/images/careGuideImages/careTeamPhone.webp" alt={CARE_GUIDE_STRINGS.careTeamImageAltText} />
      </Box>

      <Box display="flex" flexDirection="column" gap={3}>
        <Button variant="secondary" onClick={() => handleOnCareGuideFlowComplete(Routes.HOME)} fullWidth>
          {CARE_GUIDE_STRINGS.doThisLater}
        </Button>

        <Button variant="primary" onClick={() => handleOnCareGuideFlowComplete(Routes.CARE_TEAM_SCHEDULING)} fullWidth>
          {CARE_GUIDE_STRINGS.scheduleCall}
        </Button>
      </Box>
    </CareGuideContainer>
  );
};
