import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { Box, Button, Chip, Paper, Typography } from "@mui/material";
import { color, radius } from "@vivantehealth/design-tokens";
import { useLocalStorage } from "usehooks-ts";

import { appStrings } from "@Assets/app_strings";
import StarsStreamlineSolar from "@Assets/images/stars_streamline_solar.svg";
import { BLUE_TO_PINK_GRADIENT, GradientBorderCard } from "@Components/GradientBorderCard/GradientBorderCard";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { CARE_GUIDE_WIDTH, LOCAL_STORAGE_CARE_GUIDE_ORIGINATING_SCREEN } from "../assets/constants";
import { CareGuideScreens, OriginatingCareGuideScreen } from "../types/careGuide.types";

const CARE_GUIDE_STRINGS = appStrings.features.careGuide.landing;

type CareGuideLanding = Readonly<{
  handleGoToScreen: (screenName: CareGuideScreens) => void;
}>;

export const CareGuideLanding = ({ handleGoToScreen }: CareGuideLanding) => {
  const [, setOriginatingScreen] = useLocalStorage<OriginatingCareGuideScreen>(
    LOCAL_STORAGE_CARE_GUIDE_ORIGINATING_SCREEN,
    "landing",
  );
  const { sendEventAnalytics } = useAnalyticsHook();

  const handleShowMeAround = () => {
    sendEventAnalytics(ClickStreamActivityEventType.CARE_GUIDE_SELF_GUIDED_FLOW);
    setOriginatingScreen("landing");
    handleGoToScreen("gettingStarted");
  };

  const handleScheduleWithCareGuide = () => {
    sendEventAnalytics(ClickStreamActivityEventType.CARE_GUIDE_CG_SUPPORT_FLOW);
    handleGoToScreen("scheduleLanding");
  };

  return (
    <Box width={CARE_GUIDE_WIDTH}>
      <Typography variant="h1Serif" mb={4} textAlign="center">
        {CARE_GUIDE_STRINGS.welcome}
      </Typography>

      <Typography variant="h4" textAlign="center">
        {CARE_GUIDE_STRINGS.launchJourney}
      </Typography>

      <Box display="flex" alignItems="center" gap={1} mt={6} mb={1}>
        <img src="/images/careGuideImages/careGuides.png" alt="" />

        <Typography variant="caption">{CARE_GUIDE_STRINGS.journeyStarted}</Typography>
      </Box>

      <GradientBorderCard
        gradient={BLUE_TO_PINK_GRADIENT}
        borderRadius={radius.radius4}
        borderWidth={1}
        contentSx={{
          background: `#ECEBFA right bottom / auto no-repeat`,
          backgroundSize: "cover",
          p: 4,
          gap: 3,
          justifyContent: "flex-start",
        }}
      >
        <Chip
          variant="filled"
          label={
            <Box display="flex" gap={1}>
              <StarsStreamlineSolar />
              {CARE_GUIDE_STRINGS.cards.support.chipText}
            </Box>
          }
          sx={{
            color: color.text.information,
            bgcolor: "rgba(73, 91, 255, 0.12)",
            p: 0,
            "& .MuiChip-label": {
              py: 1,
              px: 3,
            },
          }}
        />
        <Box>
          <Typography variant="h3" mb={2}>
            {CARE_GUIDE_STRINGS.cards.support.header}
          </Typography>

          <Typography variant="bodyDense">{CARE_GUIDE_STRINGS.cards.support.body}</Typography>
        </Box>

        <Button variant="cardDark" fullWidth sx={{ width: "100%" }} onClick={handleScheduleWithCareGuide}>
          {CARE_GUIDE_STRINGS.cards.support.buttonText}
        </Button>
      </GradientBorderCard>

      <Paper sx={{ p: 4, mt: 4 }}>
        <Box display="flex" flexDirection="column" gap={2}>
          <Typography variant="h3">{CARE_GUIDE_STRINGS.cards.ownPace.header}</Typography>

          <Typography variant="bodyDense" mb={2}>
            {CARE_GUIDE_STRINGS.cards.ownPace.body}
          </Typography>

          <Button variant="secondary" fullWidth onClick={handleShowMeAround}>
            {CARE_GUIDE_STRINGS.cards.ownPace.buttonText}
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};
