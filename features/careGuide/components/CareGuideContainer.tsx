import { Box, Button } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";

import { CARE_GUIDE_WIDTH } from "../assets/constants";

type CareGuideContainerProps = Readonly<{
  showBackButton?: boolean;
  handleGoBack?: () => void;
  children: React.ReactNode;
}>;

export const CareGuideContainer = ({ showBackButton = true, handleGoBack, children }: CareGuideContainerProps) => {
  return (
    <Box display="flex" flexDirection="column" gap={5} width={CARE_GUIDE_WIDTH}>
      {showBackButton ? (
        <Button
          variant="secondary"
          startIcon={<AppIcon name="LeftChevron" />}
          sx={{ alignSelf: "flex-start" }}
          onClick={() => handleGoBack?.()}
        >
          {appStrings.buttonText.goBack}
        </Button>
      ) : null}

      {children}
    </Box>
  );
};
