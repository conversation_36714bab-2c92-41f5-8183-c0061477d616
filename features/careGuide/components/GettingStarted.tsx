import { ActionPlanTargetState } from "@vivantehealth/vivante-core";
import { Box, Button, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { useRouter } from "next/router";
import ReactPlayer from "react-player";
import { useLocalStorage } from "usehooks-ts";

import { appStrings } from "@Assets/app_strings";
import { RADIUS_16_PX } from "@Assets/style_constants";
import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { markActionInProgressIfQualifying, setActionNewState } from "@Features/carePlan/utils/markAsDone.util";
import { useCheckAndUpdateParentActionState } from "@Hooks/useCheckAndUpdateParentActionState";
import { Routes } from "@Types";

import { CareGuideContainer } from "./CareGuideContainer";
import {
  CARE_GUIDE_WIDTH,
  LOCAL_STORAGE_CARE_GUIDE_ORIGINATING_SCREEN,
  LOCAL_STORAGE_CARE_GUIDE_SCREEN,
} from "../assets/constants";
import { useCareGuide } from "../hooks/useCareGuide";
import { useCareGuideStatus } from "../hooks/useCareGuideStatus";
import { CareGuideScreens, OriginatingCareGuideScreen } from "../types/careGuide.types";

const CARE_GUIDE_STRINGS = appStrings.features.careGuide.gettingStarted;
/** This is the combination of padding/margin and button heights */
const FIXED_HEIGHT = "264px";
const VIDEO_RATIO = 0.5625;
const VIDEO_HEIGHT = Number(CARE_GUIDE_WIDTH.slice(0, 3)) * VIDEO_RATIO;

type GettingStartedProps = Readonly<{
  handleGoToScreen: (screenName: CareGuideScreens) => void;
}>;

export const GettingStarted = ({ handleGoToScreen }: GettingStartedProps) => {
  const router = useRouter();
  const { updateCareGuideStatus } = useCareGuideStatus();
  const [originatingScreen] = useLocalStorage<OriginatingCareGuideScreen>(
    LOCAL_STORAGE_CARE_GUIDE_ORIGINATING_SCREEN,
    "landing",
  );
  const { carePlanLoadState, welcomeToCylinderTarget, welcomeToCylinderVideoDriver } = useCareGuide();
  const checkAndUpdateParentActionState = useCheckAndUpdateParentActionState();

  const handleOnContinue = async () => {
    if (originatingScreen === "landing") {
      return handleGoToScreen("readyToMeet");
    }

    await updateCareGuideStatus("completed");
    localStorage.removeItem(LOCAL_STORAGE_CARE_GUIDE_SCREEN);
    localStorage.removeItem(LOCAL_STORAGE_CARE_GUIDE_ORIGINATING_SCREEN);

    router.replace(Routes.HOME);
  };

  const handleOnPlay = () => {
    if (welcomeToCylinderTarget && welcomeToCylinderVideoDriver) {
      markActionInProgressIfQualifying(welcomeToCylinderTarget.id, welcomeToCylinderTarget.state);
      markActionInProgressIfQualifying(welcomeToCylinderVideoDriver.id, welcomeToCylinderVideoDriver.state);
    }
  };

  const handleOnEnded = () => {
    if (welcomeToCylinderTarget && welcomeToCylinderVideoDriver) {
      setActionNewState(
        ActionPlanTargetState.COMPLETED,
        welcomeToCylinderVideoDriver.state,
        welcomeToCylinderVideoDriver.id,
      );
      checkAndUpdateParentActionState(
        welcomeToCylinderTarget.id,
        welcomeToCylinderVideoDriver.id,
        ActionPlanTargetState.COMPLETED,
      );
    }
  };

  return (
    <CareGuideContainer
      showBackButton={originatingScreen === "landing"}
      handleGoBack={() => handleGoToScreen("landing")}
    >
      <Box
        display="flex"
        flexDirection="column"
        gap={5}
        minHeight={`calc(100vh - ${FIXED_HEIGHT})`}
        alignItems="center"
        justifyContent="center"
      >
        {carePlanLoadState === "loading" && !welcomeToCylinderVideoDriver ? (
          <LoadingSpinner open overlayDrawer overlayHeader />
        ) : (
          <>
            <Box display="flex" flexDirection="column" gap={2} alignItems="center">
              <Typography variant="h2">{CARE_GUIDE_STRINGS.header}</Typography>

              <Typography variant="bodyDense">{CARE_GUIDE_STRINGS.body}</Typography>
            </Box>

            <Box border={`1px solid ${color.border.surface.default}`} borderRadius={4}>
              <ReactPlayer
                style={{
                  borderRadius: RADIUS_16_PX,
                  overflow: "hidden",
                }}
                url={welcomeToCylinderVideoDriver?.linkUri}
                controls
                width={CARE_GUIDE_WIDTH}
                height={VIDEO_HEIGHT}
                onPlay={handleOnPlay}
                onEnded={handleOnEnded}
                data-testid="care-guide-welcome-video"
              />
            </Box>

            <Button variant="primary" onClick={handleOnContinue} fullWidth>
              {appStrings.buttonText.continue}
            </Button>
          </>
        )}
      </Box>
    </CareGuideContainer>
  );
};
