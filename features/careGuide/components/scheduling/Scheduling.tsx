import { useRef, useState } from "react";
import { Button, Paper } from "@mui/material";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { Routes } from "@Types";

import { SchedulingBody } from "./SchedulingBody";
import { SchedulingConfirmation } from "./SchedulingConfirmation";
import { useCareGuide } from "../../hooks/useCareGuide";
import { SelectedCareGuideTimeSlot } from "../../types/careGuide.types";
import { CareGuideContainer } from "../CareGuideContainer";

export const Scheduling = () => {
  const router = useRouter();
  const [currentSchedulingScreen, setCurrentSchedulingScreen] = useState<"schedule" | "confirm">("schedule");
  const nextBtnRef = useRef<HTMLButtonElement | null>(null);
  const {
    selectedCommunicationMethod,
    setSelectedCommunicationMethod,
    timeSlotsByDay,
    isLoading,
    selectedTimeSlot,
    setSelectedTimeSlot,
    handleCreateOrUpdateCareGuideAppointment,
    isSubmittingAppointment,
  } = useCareGuide();
  const shouldDisplayContinueButton =
    selectedCommunicationMethod !== undefined && !isLoading && timeSlotsByDay && Object.keys(timeSlotsByDay).length > 0;

  const handleSelectedTimeSlot = (timeSlot: SelectedCareGuideTimeSlot) => {
    setSelectedTimeSlot(timeSlot);
    // On slot selection, focus on the next button
    if (nextBtnRef.current) {
      // Adds a delay to ensure focus after button is changed from disabled to enabled
      setTimeout(() => nextBtnRef.current?.focus(), 1);
    }
  };

  return (
    <CareGuideContainer
      handleGoBack={() =>
        currentSchedulingScreen === "confirm" ? setCurrentSchedulingScreen("schedule") : router.push(Routes.HOME)
      }
    >
      <Paper sx={{ mt: 2 }}>
        {currentSchedulingScreen === "schedule" ? (
          <>
            <SchedulingBody
              selectedCommunicationMethod={selectedCommunicationMethod}
              setSelectedCommunicationMethod={setSelectedCommunicationMethod}
              isLoading={isLoading}
              timeSlotsByDay={timeSlotsByDay}
              selectedTimeSlot={selectedTimeSlot}
              handleSelectedTimeSlot={handleSelectedTimeSlot}
            />

            {shouldDisplayContinueButton ? (
              <Button
                variant="primary"
                onClick={() => {
                  setCurrentSchedulingScreen("confirm");
                }}
                fullWidth
                ref={nextBtnRef}
                sx={{ mt: 6 }}
                disabled={selectedTimeSlot === undefined}
              >
                {appStrings.buttonText.continue}
              </Button>
            ) : null}
          </>
        ) : null}

        {currentSchedulingScreen === "confirm" && selectedTimeSlot && selectedCommunicationMethod ? (
          <SchedulingConfirmation
            selectedTimeSlot={selectedTimeSlot}
            selectedCommunicationMethod={selectedCommunicationMethod}
            handleCurrentSchedulingScreen={setCurrentSchedulingScreen}
            handleCreateCareGuideAppointment={handleCreateOrUpdateCareGuideAppointment}
            isSubmittingAppointment={isSubmittingAppointment}
            displayActionButtons
          />
        ) : null}
      </Paper>
    </CareGuideContainer>
  );
};
