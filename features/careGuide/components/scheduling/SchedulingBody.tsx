import { SessionCommunicationMethod } from "@vivantehealth/vivante-core";

import { appStrings } from "@Assets/app_strings";
import { CareGuideTimeSlots, SelectedCareGuideTimeSlot } from "@Features/careGuide/types/careGuide.types";
import { SchedulingReachByMethod } from "@Features/careTeam/components/schedulingModal/SchedulingReachByMethod";
import { SchedulingSectionHeader } from "@Features/careTeam/components/schedulingModal/SchedulingSectionHeader";

import { SchedulingSection } from "./SchedulingSection";

const CARE_TEAM_STRINGS = appStrings.features.careTeam;

type SchedulingBodyProps = Readonly<{
  selectedCommunicationMethod: SessionCommunicationMethod | undefined;
  setSelectedCommunicationMethod: (communicationMethod: SessionCommunicationMethod) => void;
  isLoading: boolean;
  timeSlotsByDay?: CareGuideTimeSlots;
  selectedTimeSlot?: SelectedCareGuideTimeSlot;
  handleSelectedTimeSlot: (timeSlot: SelectedCareGuideTimeSlot) => void;
}>;
export const SchedulingBody = ({
  selectedCommunicationMethod,
  setSelectedCommunicationMethod,
  isLoading,
  timeSlotsByDay,
  selectedTimeSlot,
  handleSelectedTimeSlot,
}: SchedulingBodyProps) => {
  return (
    <>
      <SchedulingSectionHeader
        number={CARE_TEAM_STRINGS.schedulingModal.one}
        text={CARE_TEAM_STRINGS.schedulingModal.promptTwo("Care Guide")}
        typographyVariant="h4"
      />

      <SchedulingReachByMethod
        selectedCommunicationMethod={selectedCommunicationMethod}
        onCommunicationMethodClick={setSelectedCommunicationMethod}
      />

      <SchedulingSectionHeader
        number={CARE_TEAM_STRINGS.schedulingModal.two}
        text={CARE_TEAM_STRINGS.schedulingModal.promptThree}
        typographyVariant="h4"
      />

      <SchedulingSection
        selectedCommunicationMethod={selectedCommunicationMethod}
        isLoading={isLoading}
        timeSlotsByDay={timeSlotsByDay}
        selectedTimeSlot={selectedTimeSlot}
        handleSelectedTimeSlot={handleSelectedTimeSlot}
      />
    </>
  );
};
