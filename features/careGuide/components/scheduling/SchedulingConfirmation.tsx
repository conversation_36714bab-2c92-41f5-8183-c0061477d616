import { SessionCommunicationMethod } from "@vivantehealth/vivante-core";
import { Box, Typography, Paper, Button, CircularProgress } from "@mui/material";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { AppointmentDuration } from "@Components/AppointmentDuration/AppointmentDuration";
import { AppointmentGetReady } from "@Components/AppointmentGetReady/AppointmentGetReady";
import { AppointmentInformation } from "@Components/AppointmentInformation/AppointmentInformation";
import { SelectedCareGuideTimeSlot } from "@Features/careGuide/types/careGuide.types";
import { logger } from "@Utils/logger";

import { HandleCreateOrUpdateCareGuideAppointment } from "../../hooks/useCareGuide";

const CARE_GUIDE_STRINGS = appStrings.features.careGuide.scheduling;

const getAppointmentDuration = (timeSlot: SelectedCareGuideTimeSlot) => {
  const { startTime, endTime } = timeSlot;

  return dayjs(endTime).diff(dayjs(startTime), "minute");
};

type SchedulingConfirmationProps = Readonly<{
  selectedTimeSlot: SelectedCareGuideTimeSlot;
  selectedCommunicationMethod: SessionCommunicationMethod;
  handleCurrentSchedulingScreen: (screen: "schedule" | "confirm") => void;
  handleCreateCareGuideAppointment: (args: HandleCreateOrUpdateCareGuideAppointment) => void;
  isSubmittingAppointment: boolean;
  displayActionButtons: boolean;
}>;

export const SchedulingConfirmation = ({
  selectedTimeSlot,
  selectedCommunicationMethod,
  handleCurrentSchedulingScreen,
  handleCreateCareGuideAppointment,
  isSubmittingAppointment,
  displayActionButtons,
}: SchedulingConfirmationProps) => {
  const handleCreateAppointment = async () => {
    try {
      await handleCreateCareGuideAppointment({ clinicianIndex: 0 });
    } catch (error) {
      logger.error(error);
    }
  };

  return (
    <Box display="flex" flexDirection="column" gap={5}>
      <Typography variant="h3">{CARE_GUIDE_STRINGS.review}</Typography>

      <Paper>
        <Box display="flex" justifyContent="space-between" alignItems="start">
          <Typography variant="h4">{CARE_GUIDE_STRINGS.careGuideCall}</Typography>

          <AppointmentDuration duration={getAppointmentDuration(selectedTimeSlot)} />
        </Box>

        <Box mt={4}>
          <AppointmentInformation
            startTime={new Date(selectedTimeSlot.startTime)}
            communicationMethod={selectedCommunicationMethod}
          />
        </Box>
      </Paper>

      <Box px={4}>
        <AppointmentGetReady
          headingSize="h4"
          excludeMedicalHistoryNote
          includePhoneNote={selectedCommunicationMethod === SessionCommunicationMethod.PHONE}
        />

        <Typography variant="body" mt={4}>
          {CARE_GUIDE_STRINGS.noCallsWhileDriving}
        </Typography>
      </Box>

      {displayActionButtons ? (
        <Box display="flex" gap={4}>
          <Button
            variant="secondary"
            disabled={isSubmittingAppointment}
            onClick={() => handleCurrentSchedulingScreen("schedule")}
            fullWidth
          >
            {isSubmittingAppointment ? <CircularProgress size={24} color="inherit" /> : appStrings.buttonText.edit}
          </Button>

          <Button variant="primary" disabled={isSubmittingAppointment} onClick={handleCreateAppointment} fullWidth>
            {isSubmittingAppointment ? <CircularProgress size={24} color="inherit" /> : appStrings.buttonText.confirm}
          </Button>
        </Box>
      ) : null}
    </Box>
  );
};
