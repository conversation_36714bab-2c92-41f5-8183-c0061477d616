import { SessionCommunicationMethod } from "@vivantehealth/vivante-core";
import { Box, Chip, CircularProgress, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { useDynamicMaxHeight } from "@Hooks/useDynamicMaxHeight";

import { CareGuideTimeSlot, CareGuideTimeSlots, SelectedCareGuideTimeSlot } from "../../types/careGuide.types";

const CARE_GUIDE_STRINGS = appStrings.features.careGuide;
/** Combination of all elements above and below the scheduling section and accounting for some padding at the bottom */
const HEIGHT_TO_REMOVE = 450;

type SchedulingSectionProps = Readonly<{
  selectedCommunicationMethod?: SessionCommunicationMethod;
  selectedTimeSlot?: SelectedCareGuideTimeSlot;
  handleSelectedTimeSlot: (timeSlot: SelectedCareGuideTimeSlot) => void;
  isLoading: boolean;
  timeSlotsByDay?: CareGuideTimeSlots;
}>;

const getTimeSlotRange = (timeSlot?: CareGuideTimeSlot) => {
  return timeSlot
    ? `${dayjs(timeSlot.startTime).format("h:mm")} - ${dayjs(timeSlot.endTime).format("h:mm A")}`
    : undefined;
};

export const SchedulingSection = ({
  selectedCommunicationMethod,
  selectedTimeSlot,
  handleSelectedTimeSlot,
  isLoading,
  timeSlotsByDay,
}: SchedulingSectionProps) => {
  const maxHeight = useDynamicMaxHeight(HEIGHT_TO_REMOVE);
  const timeSlotsByDayArray = timeSlotsByDay
    ? Object.entries(timeSlotsByDay).sort(([aDayTitle], [bDayTitle]) => dayjs(aDayTitle).diff(dayjs(bDayTitle)))
    : [];
  const selectedSlotId = getTimeSlotRange(selectedTimeSlot);

  if (selectedCommunicationMethod === undefined) {
    return <Typography variant="body">{CARE_GUIDE_STRINGS.scheduling.selectACommunicationMethod}</Typography>;
  }

  if (timeSlotsByDayArray.length === 0 && !isLoading) {
    return <Typography variant="body">{CARE_GUIDE_STRINGS.scheduling.noAvailableAppointmentsCareGuide}</Typography>;
  }

  return isLoading ? (
    <Box width={"100%"} textAlign="center">
      <CircularProgress />
    </Box>
  ) : (
    <Paper sx={{ maxHeight, overflowY: "auto" }}>
      {timeSlotsByDayArray.length
        ? timeSlotsByDayArray.map(([dayTitle, timeSlots], dayIndex) => {
            const isFirstDay = dayIndex === 0;
            const isLastDay = dayIndex === timeSlotsByDayArray.length - 1;
            const selectedSlotKey = `${selectedTimeSlot?.timeSlotDayKey}-${selectedSlotId}`;
            const sortedTimeSlots = Object.values(timeSlots).sort((a, b) =>
              dayjs(a.startTime).diff(dayjs(b.startTime)),
            );

            return (
              <Box
                key={dayTitle}
                pt={isFirstDay ? 0 : 5}
                pb={isLastDay ? 0 : 5}
                borderBottom={isLastDay ? "none" : `1px solid ${color.border.surface.default}`}
              >
                <Typography variant="h4" pb={4}>
                  {dayTitle}
                </Typography>

                <Box display="flex" gap={2} flexWrap="wrap">
                  {sortedTimeSlots.map((timeSlot) => {
                    const text = getTimeSlotRange(timeSlot);
                    const key = `${dayTitle}-${text}`;

                    return (
                      <Chip
                        key={key}
                        label={text}
                        onClick={() => handleSelectedTimeSlot({ ...timeSlot, timeSlotDayKey: dayTitle })}
                        variant={key === selectedSlotKey ? "active" : "inactive"}
                      />
                    );
                  })}
                </Box>
              </Box>
            );
          })
        : null}
    </Paper>
  );
};
