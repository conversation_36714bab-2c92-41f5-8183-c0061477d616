import { useRef, useState } from "react";
import { Appointment } from "@vivantehealth/vivante-core";
import { Button, CircularProgress } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { BaseModal } from "@Components/BaseModal/BaseModal";
import { CareTeamStateSlice } from "@Features/careTeam/store/careTeamStateSlice";
import { useAppDispatch } from "@Store/hooks";
import { logger } from "@Utils/logger";

import { SchedulingBody } from "./scheduling/SchedulingBody";
import { SchedulingConfirmation } from "./scheduling/SchedulingConfirmation";
import { useCareGuide } from "../hooks/useCareGuide";
import { SelectedCareGuideTimeSlot } from "../types/careGuide.types";

const BUTTON_STRINGS = appStrings.buttonText;

type CareGuideReschedulingModalProps = Readonly<{
  isModalOpen: boolean;
  existingAppointment?: Appointment;
  closeSchedulingModal: () => void;
}>;

export const CareGuideReschedulingModal = ({
  isModalOpen,
  existingAppointment,
  closeSchedulingModal,
}: CareGuideReschedulingModalProps) => {
  const dispatch = useAppDispatch();
  const [currentSchedulingScreen, setCurrentSchedulingScreen] = useState<"schedule" | "confirm">("schedule");
  const nextBtnRef = useRef<HTMLButtonElement | null>(null);
  const {
    selectedCommunicationMethod,
    setSelectedCommunicationMethod,
    timeSlotsByDay,
    isLoading,
    selectedTimeSlot,
    setSelectedTimeSlot,
    handleCreateOrUpdateCareGuideAppointment,
    isSubmittingAppointment,
  } = useCareGuide();
  const isSchedulingScreen = currentSchedulingScreen === "schedule";
  const primaryButtonText = isSchedulingScreen ? BUTTON_STRINGS.next : BUTTON_STRINGS.confirm;
  const isNextButtonDisabled =
    isLoading || isSubmittingAppointment || !selectedTimeSlot || !selectedCommunicationMethod;

  const handleSelectedTimeSlot = (timeSlot: SelectedCareGuideTimeSlot) => {
    setSelectedTimeSlot(timeSlot);
    // On slot selection, focus on the next button
    if (nextBtnRef.current) {
      // Adds a delay to ensure focus after button is changed from disabled to enabled
      setTimeout(() => nextBtnRef.current?.focus(), 1);
    }
  };

  const handlePrimaryAction = async () => {
    if (isSchedulingScreen) {
      return setCurrentSchedulingScreen("confirm");
    }

    try {
      await handleCreateOrUpdateCareGuideAppointment({
        clinicianIndex: existingAppointment ? -1 : 0,
        existingAppointment,
      });
      dispatch(CareTeamStateSlice.actions.refreshAppointments());
      closeSchedulingModal();
    } catch (error) {
      logger.error(error);
    }
  };

  return (
    <BaseModal
      isModalOpen={isModalOpen}
      onClose={closeSchedulingModal}
      displayCloseButton={false}
      bodyContent={
        <>
          {currentSchedulingScreen === "schedule" ? (
            <SchedulingBody
              selectedCommunicationMethod={selectedCommunicationMethod}
              setSelectedCommunicationMethod={setSelectedCommunicationMethod}
              isLoading={isLoading}
              timeSlotsByDay={timeSlotsByDay}
              selectedTimeSlot={selectedTimeSlot}
              handleSelectedTimeSlot={handleSelectedTimeSlot}
            />
          ) : null}
          {currentSchedulingScreen === "confirm" && selectedTimeSlot && selectedCommunicationMethod ? (
            <SchedulingConfirmation
              selectedTimeSlot={selectedTimeSlot}
              selectedCommunicationMethod={selectedCommunicationMethod}
              handleCurrentSchedulingScreen={setCurrentSchedulingScreen}
              handleCreateCareGuideAppointment={handleCreateOrUpdateCareGuideAppointment}
              isSubmittingAppointment={isSubmittingAppointment}
              displayActionButtons={false}
            />
          ) : null}
        </>
      }
      actions={
        <>
          <Button
            variant="secondary"
            onClick={isSchedulingScreen ? closeSchedulingModal : () => setCurrentSchedulingScreen("schedule")}
            fullWidth
            disabled={isSubmittingAppointment}
          >
            {isSchedulingScreen ? BUTTON_STRINGS.cancel : BUTTON_STRINGS.edit}
          </Button>

          <Button
            variant="primary"
            onClick={handlePrimaryAction}
            fullWidth
            disabled={isNextButtonDisabled}
            ref={nextBtnRef}
          >
            {isSubmittingAppointment ? <CircularProgress size={24} color="inherit" /> : primaryButtonText}
          </Button>
        </>
      }
    />
  );
};
