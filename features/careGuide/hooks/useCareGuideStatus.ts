import { useEffect, useState, useCallback } from "react";
import { useSelector } from "react-redux";
import { FirestoreDocument } from "@vivantehealth/vivante-core";

import { memberStateSelector } from "@Features/member/store/memberStateSlice";
import { vivanteCoreContainer } from "@Lib/vivanteCore";

type CareGuideStatus = "in_progress" | "completed" | "loading" | "not_applicable";
type CareGuideFirestoreDocumentStatus = Extract<CareGuideStatus, "in_progress" | "completed">;
type MemberPreferencesWithCareGuideStatus = {
  data: { preferences: { careGuideStatus: CareGuideFirestoreDocumentStatus } };
  id: string;
};

const MEMBER_PREFERENCES_COLLECTION = "memberPreferences";

const isMemberPreferenceWithCareGuideStatus = (
  doc: FirestoreDocument | undefined,
): doc is MemberPreferencesWithCareGuideStatus => {
  return (
    doc?.data?.preferences?.careGuideStatus === "in_progress" || doc?.data?.preferences?.careGuideStatus === "completed"
  );
};

export const useCareGuideStatus = () => {
  const [careGuideStatus, setCareGuideStatus] = useState<CareGuideStatus>("loading");
  const member = useSelector(memberStateSelector("member"));

  const getCareGuideStatus = useCallback(
    async (shouldSetState: boolean) => {
      try {
        const firestore = await vivanteCoreContainer.firebaseClient.getFirestore();
        const doc = await firestore
          .collection(MEMBER_PREFERENCES_COLLECTION)
          .document(member?.id ?? "")
          .get();
        const storedCareGuideStatus = isMemberPreferenceWithCareGuideStatus(doc)
          ? doc.data.preferences.careGuideStatus
          : undefined;

        if (shouldSetState) {
          setCareGuideStatus(storedCareGuideStatus ?? "not_applicable");
        }

        return storedCareGuideStatus;
      } catch (error) {
        // If an error occurs getting the status, the safe default is to set the status to not_applicable
        if (shouldSetState) {
          setCareGuideStatus("not_applicable");
        }
      }

      return undefined;
    },
    [member?.id],
  );

  useEffect(() => {
    if (member?.id && careGuideStatus === "loading") {
      getCareGuideStatus(true);
    }
  }, [careGuideStatus, getCareGuideStatus, member?.id]);

  const updateCareGuideStatus = async (status: Extract<CareGuideStatus, "in_progress" | "completed">) => {
    const firestore = await vivanteCoreContainer.firebaseClient.getFirestore();
    const documentToUpsert = firestore.collection(MEMBER_PREFERENCES_COLLECTION).document(member?.id ?? "");
    // Check to see if document exists first to determine if we should create or update
    const careGuideStatusDocument = await getCareGuideStatus(false);

    if (!careGuideStatusDocument) {
      await documentToUpsert.set({ preferences: { careGuideStatus: status } });
    } else {
      await documentToUpsert.update({ preferences: { careGuideStatus: status } });
    }

    setCareGuideStatus(status);
  };

  return { updateCareGuideStatus, careGuideStatus } as const;
};
