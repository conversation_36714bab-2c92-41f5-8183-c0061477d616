import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { Appointment, ClickStreamActivityEventType, SessionCommunicationMethod } from "@vivantehealth/vivante-core";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { ActionPlansStateSlice } from "@Features/carePlan/store/actionPlansStateSlice";
import { memberStateSelector } from "@Features/member/store/memberStateSlice";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { useAppDispatch, useAppSelector } from "@Store/hooks";
import { Routes } from "@Types";

import { processAppointmentSchedulingErrors } from "../../appointmentsNew/utils/processAppointmentSchedulingErrors";
import {
  careGuideApi,
  CareGuideAppointmentBaseRequest,
  useCreateCareGuideAppointmentMutation,
  useGetCareGuideTimeSlotsQuery,
  useRescheduleCareGuideAppointmentMutation,
} from "../api/careGuideApi";
import { SelectedCareGuideTimeSlot } from "../types/careGuide.types";

const CARE_GUIDE_STRINGS = appStrings.features.careGuide.scheduling;

const determineClinicianId = (
  isRescheduling: boolean,
  clinicianIndex: number,
  availableClinicianIds: string[],
  existingAppointment?: Appointment,
) => {
  if (isRescheduling && clinicianIndex === -1) {
    return (
      availableClinicianIds.find((clinicianId) => clinicianId === existingAppointment?.practitioner.id) ??
      availableClinicianIds[0]
    );
  }

  return availableClinicianIds[clinicianIndex];
};

export type HandleCreateOrUpdateCareGuideAppointment = {
  /** We use -1 as the initial index if rescheduling. This ensures the catch block handling of trying a different clinician
   * does not skip a clinician in the list if we are unable to reschedule with the existing appointment clinician
   */
  clinicianIndex: number;
  existingAppointment?: Appointment;
  handleCloseSchedulingModal?: () => void;
};

export const useCareGuide = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [selectedCommunicationMethod, setSelectedCommunicationMethod] = useState<
    SessionCommunicationMethod | undefined
  >();
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<SelectedCareGuideTimeSlot | undefined>();
  const { data: timeSlotsByDay, isLoading } = useGetCareGuideTimeSlotsQuery();
  const [invokeCreateCareGuideAppointment, { isLoading: isSubmittingAppointment }] =
    useCreateCareGuideAppointmentMutation();
  const [invokeRescheduleCareGuideAppointment, { isLoading: isSubmittingRescheduleAppointment }] =
    useRescheduleCareGuideAppointmentMutation();
  const { sendEventAnalytics } = useAnalyticsHook();
  const member = useSelector(memberStateSelector("member"));
  const {
    targetEntities: carePlanEntities,
    driverEntities: carePlanDrivers,
    loadState: carePlanLoadState,
  } = useAppSelector((state) => state.actionPlansState);
  const welcomeToCylinderTarget = carePlanEntities
    ? Object.values(carePlanEntities).find(({ title }) => title.toLowerCase() === "getting started with cylinder")
    : undefined;
  const welcomeToCylinderVideoDriver = welcomeToCylinderTarget?.drivers
    ?.map((driverId) => carePlanDrivers[driverId])
    .find(({ title }) => title.toLowerCase() === "watch video");

  useEffect(() => {
    if (!carePlanEntities && carePlanLoadState !== "loading") {
      dispatch(ActionPlansStateSlice.actions.loadActionPlans());
    }
  }, [carePlanLoadState, carePlanEntities, dispatch]);

  const actionsOnSuccessfulAppointmentCreation = (isNewAppointment: boolean) => {
    if (isNewAppointment) {
      sendEventAnalytics(ClickStreamActivityEventType.CARE_GUIDE_SCHEDULED_WITH_CARE_GUIDE);
    }

    dispatch(
      SnackbarStateSlice.actions.toggleSnackbar({
        message: CARE_GUIDE_STRINGS.appointmentConfirmed,
        isOpen: true,
      }),
    );

    router.push(Routes.CARE_TEAM);
  };

  const handleCreateOrUpdateCareGuideAppointment = async ({
    clinicianIndex = 0,
    existingAppointment,
    handleCloseSchedulingModal,
  }: HandleCreateOrUpdateCareGuideAppointment) => {
    /**
     * This is an edge case where somehow the user is able to get to the confirmation screen without having
     * selected a time slot. The likelihood of occurring is near impossible but just in case
     *  */
    if (!selectedTimeSlot) {
      dispatch(
        SnackbarStateSlice.actions.toggleSnackbar({
          message: CARE_GUIDE_STRINGS.missingTimeSlot,
          isOpen: true,
          variant: "error",
        }),
      );
      return;
    }

    const { startTime, endTime, availableClinicianIds, requestTrackingId } = selectedTimeSlot;
    const isRescheduling = existingAppointment?.id !== undefined && existingAppointment.id.length > 0;
    const careGuideAppointmentBaseRequest: CareGuideAppointmentBaseRequest = {
      memberId: member?.id ?? "",
      clinicianId: determineClinicianId(isRescheduling, clinicianIndex, availableClinicianIds, existingAppointment),
      startTime,
      endTime,
      communication_method: selectedCommunicationMethod === SessionCommunicationMethod.PHONE ? "phone" : "video",
    };

    try {
      isRescheduling
        ? await invokeRescheduleCareGuideAppointment({
            ...careGuideAppointmentBaseRequest,
            appointmentId: existingAppointment.id,
          }).unwrap()
        : await invokeCreateCareGuideAppointment({
            ...careGuideAppointmentBaseRequest,
            request_tracking_id: requestTrackingId,
          }).unwrap();

      actionsOnSuccessfulAppointmentCreation(!isRescheduling);
    } catch (error) {
      const processedError = processAppointmentSchedulingErrors(error);

      // If the error is related to the appointment time being unavailable, try the next clinician if available
      if (
        processedError.type === "APPOINTMENT_TIME_UNAVAILABLE" &&
        availableClinicianIds[clinicianIndex + 1] !== undefined
      ) {
        handleCreateOrUpdateCareGuideAppointment({ clinicianIndex: clinicianIndex + 1, existingAppointment });
        return;
      }
      // If the error is related to the request tracking id, invalidate the care guide api cache to get a new request_tracking_id
      if (processedError.type === "REQUEST_TRACKING_ID_ERROR") {
        dispatch(careGuideApi.util.invalidateTags(["CareGuide"]));
      }

      dispatch(
        SnackbarStateSlice.actions.toggleSnackbar({
          message: processedError.message,
          isOpen: true,
          variant: "error",
        }),
      );
      // As the user can not reschedule within 1 minute of the appointment, we force close the modal
      if (processedError.type === "RESCHEDULING_WITHIN_1_MINUTE" && handleCloseSchedulingModal) {
        handleCloseSchedulingModal();
      }

      throw processedError;
    }
  };

  return {
    selectedCommunicationMethod,
    setSelectedCommunicationMethod,
    isLoading,
    timeSlotsByDay,
    selectedTimeSlot,
    setSelectedTimeSlot,
    handleCreateOrUpdateCareGuideAppointment,
    isSubmittingAppointment: isSubmittingAppointment || isSubmittingRescheduleAppointment,
    welcomeToCylinderTarget,
    welcomeToCylinderVideoDriver,
    carePlanLoadState,
  } as const;
};
