import { AppointmentTimeSlotsResponse, Clinician } from "@Features/appointmentsNew/types/appointments.types";

export const MOCKED_CLINICIAN_JOHN: Clinician = {
  first_name: "<PERSON>",
  last_name: "<PERSON><PERSON>",
  avatar_link: "link_to_avatar",
  id: "1",
  clinician_role: {
    code: "CG",
    description: "Care Guide",
  },
  specialty: {
    code: "CG",
    description: "Care Guide",
  },
  enabled: true,
  identifiers: [
    {
      system: "system",
      identifier: "identifier",
    },
  ],
};

export const MOCKED_CLINICIAN_JANE: Clinician = {
  first_name: "<PERSON>",
  last_name: "<PERSON>",
  avatar_link: "link_to_avatar2",
  id: "2",
  clinician_role: {
    code: "CG",
    description: "Care Guide",
  },
  specialty: {
    code: "CG",
    description: "Care Guide",
  },
  enabled: true,
  identifiers: [
    {
      system: "system",
      identifier: "identifier",
    },
  ],
};

export const GET_CARE_GUIDE_TIME_SLOTS_MOCK: AppointmentTimeSlotsResponse = {
  available_slots: [
    {
      clinician: MOCKED_CLINICIAN_JOHN,
      slots: [
        {
          start_time: "2023-10-01T09:00:00Z",
          end_time: "2023-10-01T09:30:00Z",
        },
        {
          start_time: "2023-10-01T10:00:00Z",
          end_time: "2023-10-01T10:30:00Z",
        },
        {
          start_time: "2023-10-02T09:00:00Z",
          end_time: "2023-10-02T09:30:00Z",
        },
        {
          start_time: "2023-10-03T09:00:00Z",
          end_time: "2023-10-03T09:30:00Z",
        },
      ],
    },
    {
      clinician: MOCKED_CLINICIAN_JANE,
      slots: [
        {
          start_time: "2023-10-01T10:00:00Z",
          end_time: "2023-10-01T10:30:00Z",
        },
        {
          start_time: "2023-10-02T10:00:00Z",
          end_time: "2023-10-02T10:30:00Z",
        },
      ],
    },
  ],
  request_tracking_id: "tracking_id_123",
};
