import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Activity } from "@vivantehealth/vivante-core";

import { CoursesStateSlice, coursesStateSelector } from "@Features/courses/store/coursesStateSlice";
import { vivanteCoreContainer } from "@Lib/vivanteCore";

import { ActivityScreen } from "./components/ActivityScreen";

type ActivityScreenContainerProps = Readonly<{
  onClose: () => void;
  backButtonText?: string;
  disableCourseReroute?: boolean;
}>;

const { skipCourseItem, completeCourseItem } = CoursesStateSlice.actions;

export const ActivityScreenContainer = ({
  onClose,
  backButtonText,
  disableCourseReroute,
}: ActivityScreenContainerProps) => {
  const [activity, setActivity] = useState<Activity | null>(null);
  const dispatch = useDispatch();
  const currentCourseItem = useSelector(coursesStateSelector("currentCourseItem"));

  const loadActivity = async (activityId: string) => {
    const selectedActivity = await vivanteCoreContainer
      .getActivityUseCaseFactory()
      .createGetActivityByIdUseCase()
      .execute(activityId);

    setActivity(selectedActivity);
  };

  useEffect(() => {
    if (currentCourseItem?.resource) {
      loadActivity(currentCourseItem.resource.id);
    }
  }, [currentCourseItem]);

  const skipActivityCallback = () => {
    dispatch(skipCourseItem());
    onClose();
  };
  const completeActivityCallback = () => {
    dispatch(completeCourseItem({ disableCourseReroute }));
    onClose();
  };

  const closeOnClickCallback = () => {
    onClose();
  };

  return (
    <ActivityScreen
      activity={activity}
      backButtonText={backButtonText}
      closeOnClickCallback={closeOnClickCallback}
      skipActivityCallback={skipActivityCallback}
      completeActivityCallback={completeActivityCallback}
    />
  );
};
