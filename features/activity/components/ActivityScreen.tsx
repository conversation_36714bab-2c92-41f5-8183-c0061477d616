import { Activity } from "@vivantehealth/vivante-core";
import { Box, Button, Card, CardMedia, Divider, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { RADIUS_16_PX } from "@Assets/style_constants";
import { BackButton } from "@Components/BackButton/BackButton";
import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { linkifyText } from "@Utils/linkify";

const BUTTON_WIDTH = "212px";

type ActivityScreenProps = Readonly<{
  activity: Activity | null;
  backButtonText?: string;
  skipActivityCallback: () => void;
  completeActivityCallback: () => void;
  closeOnClickCallback: () => void;
}>;

export const ActivityScreen = ({
  activity,
  backButtonText,
  closeOnClickCallback,
  skipActivityCallback,
  completeActivityCallback,
}: ActivityScreenProps) =>
  !activity ? (
    <LoadingSpinner open overlayHeader={false} />
  ) : (
    <Box display="flex" flexDirection="column" gap={5}>
      <Box>
        <BackButton onClick={closeOnClickCallback}>
          {backButtonText || appStrings.features.courses.backToCourseRoadmap}
        </BackButton>
      </Box>
      <Card sx={{ p: 5 }}>
        <Box display="flex" flexDirection="column" gap={3}>
          <CardMedia
            image={activity.imageLink}
            title={activity.title}
            sx={{ height: "312px", borderRadius: RADIUS_16_PX }}
          />
          <Typography variant="h1Serif" mt={1}>
            {activity.title}
          </Typography>
          <Typography variant="body">{activity.description}</Typography>
          <Divider />
          <Typography variant="body">{activity.theory}</Typography>
          <Typography variant="h4" mt={1}>
            {appStrings.features.courses.tryThis}
          </Typography>
          <Typography variant="body">{linkifyText(activity.tipBody)}</Typography>
        </Box>
      </Card>
      <Box display="flex" justifyContent="space-between">
        <Button onClick={skipActivityCallback} variant="secondary" sx={{ width: BUTTON_WIDTH }}>
          {appStrings.buttonText.skip}
        </Button>
        <Button onClick={completeActivityCallback} variant="primary" sx={{ width: BUTTON_WIDTH }}>
          {appStrings.buttonText.complete}
        </Button>
      </Box>
    </Box>
  );
