import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import * as Sentry from "@sentry/nextjs";
import { FirebaseError } from "firebase/app";
import { updatePassword as firebaseUpdatePassword } from "firebase/auth";

import { appStrings } from "@Assets/app_strings";
import { useLazyFirstFactorSignInQuery } from "@Features/authentication/api/authenticationApi";
import { useAuthentication } from "@Features/authentication/hooks/useAuthentication";
import { authenticationStateSlice } from "@Features/authentication/store/authenticationStateSlice";
import { processFirstFactorSignInError } from "@Features/login/utils/processFirstFactorSignInError";
import { memberStateSelector } from "@Features/member/store/memberStateSlice";
import { MFA_TYPE } from "@Features/multiFactorAuthentication/assets/constants";
import { MfaType } from "@Features/multiFactorAuthentication/types/mfa.types";
import { getFirebaseAuth } from "@Utils/getFirebaseAuth";
import { isFetchFromBffError } from "@Utils/isFetchFromBffError";

import { UpdatePassword } from "../types/changePassword.types";

type ChangePasswordStatusValues = "IDLE" | "SUBMITTING" | "SUCCESS" | "PASSWORD_RESET_SENT";

export type ChangePasswordStatus =
  | { status: ChangePasswordStatusValues }
  | {
      status: "MFA_REQUIRED";
      mfaType: MfaType;
      sentTo: string;
    }
  | {
      status: "SUBMITTING_MFA";
      mfaType: MfaType;
      sentTo: string;
    }
  | {
      status: "ERROR";
      errorMessage: string;
    };

export const useChangePassword = () => {
  const dispatch = useDispatch();
  const member = useSelector(memberStateSelector("member"));
  const [changePasswordStatus, setChangePasswordStatus] = useState<ChangePasswordStatus>({ status: "IDLE" });
  const { authenticateUserWithCustomToken, sendPasswordReset } = useAuthentication();
  const [invokeFirstFactorSignIn] = useLazyFirstFactorSignInQuery();

  const handleChangePasswordError = (errorMessage: string) => {
    setChangePasswordStatus({ status: "ERROR", errorMessage });

    Sentry.captureMessage(`Change Password Error: ${errorMessage}`, "error");
  };

  const forceRecentLogin = async (currentPassword: string) => {
    const { signin_token } = await invokeFirstFactorSignIn({
      email: member?.email ?? "",
      password: currentPassword,
    }).unwrap();

    await authenticateUserWithCustomToken(signin_token, false);
  };

  const updateFirebasePassword = async (newPassword: string) => {
    const firebaseAuth = await getFirebaseAuth();

    if (firebaseAuth?.currentUser === null) {
      throw new Error("User is not authenticated");
    }

    await firebaseUpdatePassword(firebaseAuth?.currentUser, newPassword);
  };

  const handleStatusChange = (currentPassword: string, isMfaVerified: boolean) => {
    if (changePasswordStatus.status === "MFA_REQUIRED" && isMfaVerified) {
      setChangePasswordStatus({
        status: "SUBMITTING_MFA",
        mfaType: changePasswordStatus.mfaType,
        sentTo: changePasswordStatus.sentTo,
      });
    } else {
      dispatch(
        authenticationStateSlice.actions.storeMFACredentials({ email: member?.email ?? "", password: currentPassword }),
      );
      setChangePasswordStatus({ status: "SUBMITTING" });
    }
  };

  const updatePassword = async ({ currentPassword, newPassword, isMfaVerified }: UpdatePassword) => {
    handleStatusChange(currentPassword, isMfaVerified);

    try {
      if (!isMfaVerified) {
        await forceRecentLogin(currentPassword);
      }

      await updateFirebasePassword(newPassword);

      setChangePasswordStatus({ status: "SUCCESS" });
    } catch (error) {
      if (isFetchFromBffError(error)) {
        const processedFirstFactorSignIn = processFirstFactorSignInError(error);

        if (processedFirstFactorSignIn.type === "MFA_REQUIRED") {
          return setChangePasswordStatus({
            status: "MFA_REQUIRED",
            mfaType: processedFirstFactorSignIn.mfaSupportedMethod === MFA_TYPE.EMAIL ? MFA_TYPE.EMAIL : MFA_TYPE.SMS,
            sentTo: processedFirstFactorSignIn.verificationCodeSentTo,
          });
        }

        if (
          processedFirstFactorSignIn.type === "SIGN_IN_ERROR" &&
          processedFirstFactorSignIn.error === appStrings.features.authentication.invalidCredentials
        ) {
          return setChangePasswordStatus({
            status: "ERROR",
            errorMessage: appStrings.features.changePassword.currentPasswordIncorrect,
          });
        }

        return handleChangePasswordError(processedFirstFactorSignIn.error);
      }

      if (error instanceof Error || error instanceof FirebaseError) {
        return handleChangePasswordError(error.message);
      }

      // Neither instance of Error or FirebaseError, stringify error as we don't know the shape otherwise. This is a fallback
      handleChangePasswordError(JSON.stringify(error));
    }
  };

  const requestResetPassword = async () => {
    setChangePasswordStatus({ status: "SUBMITTING" });

    await sendPasswordReset();

    setChangePasswordStatus({ status: "PASSWORD_RESET_SENT" });
  };

  return { updatePassword, changePasswordStatus, requestResetPassword } as const;
};
