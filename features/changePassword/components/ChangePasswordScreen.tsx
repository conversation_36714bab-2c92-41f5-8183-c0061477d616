import { CreateMemberUseCase } from "@vivantehealth/vivante-core";
import { Box, Button, CircularProgress, Paper, Typography } from "@mui/material";
import { FormProvider, useForm } from "react-hook-form";

import { appStrings } from "@Assets/app_strings";
import { BackButton } from "@Components/BackButton/BackButton";
import { ErrorText } from "@Components/ErrorText/ErrorText";
import { FormInput } from "@Components/form/Fields";
import { MFA_TYPE } from "@Features/multiFactorAuthentication/assets/constants";
import { CodeVerificationScreen } from "@Features/multiFactorAuthentication/components/CodeVerificationScreen";
import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";

import { ChangePasswordStatus } from "../hooks/useChangePassword";
import { UpdatePassword } from "../types/changePassword.types";

const CHANGE_PASSWORD_STRINGS = appStrings.features.changePassword;

const DEFAULT_CHANGE_PASSWORD_VALUES = {
  currentPassword: "",
  newPassword: "",
  confirmNewPassword: "",
};

type ChangePasswordScreenProps = Readonly<{
  onBackClick: () => void;
  onSavePreferencesClick: (updatePasswordProps: UpdatePassword) => void;
  onForgotPasswordClick: () => void;
  changePasswordStatus: ChangePasswordStatus;
}>;

export const ChangePasswordScreen = ({
  onBackClick,
  onSavePreferencesClick,
  onForgotPasswordClick,
  changePasswordStatus,
}: ChangePasswordScreenProps) => {
  const { formWidth } = useResponsiveStylesHook();
  const methods = useForm({ defaultValues: DEFAULT_CHANGE_PASSWORD_VALUES });
  const { handleSubmit, watch, getValues } = methods;
  const isSaveButtonDisabled = watch(["currentPassword", "newPassword", "confirmNewPassword"]).some(
    (value) => value.toString().length === 0,
  );
  const errorMessage = changePasswordStatus.status === "ERROR" ? changePasswordStatus.errorMessage : undefined;
  const isPasswordSaving = changePasswordStatus.status === "SUBMITTING";

  return (
    <>
      <BackButton onClick={onBackClick}>{CHANGE_PASSWORD_STRINGS.backToSettings}</BackButton>

      {changePasswordStatus.status === "MFA_REQUIRED" || changePasswordStatus.status === "SUBMITTING_MFA" ? (
        <Box mt={6}>
          <CodeVerificationScreen
            type="ChangePassword"
            formHeader={CHANGE_PASSWORD_STRINGS.mfaFormHeader}
            formSubheader={CHANGE_PASSWORD_STRINGS.mfaFormSubHeader(changePasswordStatus.mfaType)}
            isEmailMFA={changePasswordStatus.mfaType === MFA_TYPE.EMAIL}
            isLoginScreen={false}
            headerIcon={changePasswordStatus.mfaType === MFA_TYPE.EMAIL ? "Mail" : "ChatBubbleNotification"}
            isNewRegistration={false}
            isChangePasswordSubmitting={changePasswordStatus.status === "SUBMITTING_MFA"}
            handleOnSubmitAction={() => {
              onSavePreferencesClick({ ...getValues(), isMfaVerified: true });
            }}
          />
        </Box>
      ) : (
        <Paper sx={{ width: formWidth, mt: 6 }}>
          <Typography variant="h3" mb={5}>
            {CHANGE_PASSWORD_STRINGS.changePassword}
          </Typography>

          <FormProvider {...methods}>
            <form
              onSubmit={handleSubmit((data: typeof DEFAULT_CHANGE_PASSWORD_VALUES) => {
                onSavePreferencesClick({ ...data, isMfaVerified: false });
              })}
              style={{ height: "100%" }}
            >
              <Box display="flex" flexDirection="column" gap={4} my={5}>
                <FormInput
                  name="currentPassword"
                  label={CHANGE_PASSWORD_STRINGS.currentPassword}
                  type="password"
                  required
                  rules={{
                    required: {
                      value: true,
                      message: appStrings.sharedFormText.requiredMessage,
                    },
                  }}
                />

                <FormInput
                  name="newPassword"
                  label={CHANGE_PASSWORD_STRINGS.newPassword}
                  type="password"
                  required
                  rules={{
                    required: {
                      value: true,
                      message: appStrings.sharedFormText.requiredMessage,
                    },
                    pattern: {
                      value: CreateMemberUseCase.PasswordValidationRegex,
                      message: appStrings.features.authentication.invalidPasswordError,
                    },
                  }}
                />

                <FormInput
                  label={appStrings.features.signUp.confirmPasswordLabel}
                  name="confirmNewPassword"
                  type="password"
                  required
                  rules={{
                    watchData: {
                      inputName: "newPassword",
                      message: CHANGE_PASSWORD_STRINGS.passwordsNeedToMatch,
                    },
                    required: {
                      value: true,
                      message: appStrings.sharedFormText.requiredMessage,
                    },
                  }}
                />

                {errorMessage && <ErrorText errorMessage={errorMessage} />}
              </Box>

              <Button type="submit" variant="primary" disabled={isSaveButtonDisabled || isPasswordSaving} fullWidth>
                {isPasswordSaving ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  CHANGE_PASSWORD_STRINGS.saveButtonText
                )}
              </Button>
            </form>
          </FormProvider>

          <Button
            variant="tertiary"
            onClick={onForgotPasswordClick}
            sx={{ mt: 5 }}
            disabled={changePasswordStatus.status === "SUBMITTING"}
          >
            {CHANGE_PASSWORD_STRINGS.forgotPassword}
          </Button>
        </Paper>
      )}
    </>
  );
};
