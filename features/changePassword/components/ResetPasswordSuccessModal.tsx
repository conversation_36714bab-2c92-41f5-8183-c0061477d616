import { Button, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { BaseModal } from "@Components/BaseModal/BaseModal";

const RESET_PASSWORD_STRINGS = appStrings.features.resetPassword;

type ResetPasswordSuccessModalProps = Readonly<{
  isOpen: boolean;
  onClose: () => void;
}>;

export const ResetPasswordSuccessModal = ({ isOpen, onClose }: ResetPasswordSuccessModalProps) => {
  return (
    <BaseModal
      isModalOpen={isOpen}
      onClose={onClose}
      title={RESET_PASSWORD_STRINGS.sentEmailHeader}
      bodyContent={<Typography variant="body">{RESET_PASSWORD_STRINGS.sentEmailText}</Typography>}
      actions={
        <Button variant="primary" onClick={onClose} fullWidth>
          {appStrings.buttonText.ok}
        </Button>
      }
    />
  );
};
