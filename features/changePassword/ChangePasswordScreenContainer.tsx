import { useDispatch } from "react-redux";

import { appStrings } from "@Assets/app_strings";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";
import { Routes } from "@Types";

import { ChangePasswordScreen } from "./components/ChangePasswordScreen";
import { ResetPasswordSuccessModal } from "./components/ResetPasswordSuccessModal";
import { useChangePassword } from "./hooks/useChangePassword";

export const ChangePasswordScreenContainer = () => {
  const dispatch = useDispatch();
  const { updatePassword, changePasswordStatus, requestResetPassword } = useChangePassword();

  const navigateToSettings = () => {
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: Routes.SETTINGS,
        screenName: "Settings",
      }),
    );
  };

  if (changePasswordStatus.status === "SUCCESS") {
    navigateToSettings();
    dispatch(
      SnackbarStateSlice.actions.toggleSnackbar({
        isOpen: true,
        message: appStrings.features.changePassword.savedPasswordSuccessfully,
      }),
    );
    return;
  }

  return (
    <>
      <ResetPasswordSuccessModal
        isOpen={changePasswordStatus.status === "PASSWORD_RESET_SENT"}
        onClose={navigateToSettings}
      />

      <ChangePasswordScreen
        onBackClick={navigateToSettings}
        onSavePreferencesClick={updatePassword}
        onForgotPasswordClick={requestResetPassword}
        changePasswordStatus={changePasswordStatus}
      />
    </>
  );
};
