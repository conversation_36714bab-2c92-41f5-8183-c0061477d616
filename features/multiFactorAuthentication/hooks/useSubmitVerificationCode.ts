import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import * as Sentry from "@sentry/nextjs";
import { FirebaseError } from "firebase/app";

import { appStrings } from "@Assets/app_strings";
import {
  useLazyFirstFactorSignInQuery,
  useLazySecondFactorSignInQuery,
} from "@Features/authentication/api/authenticationApi";
import { LOCAL_STORAGE_DEVICE_TOKEN } from "@Features/authentication/assets/constants";
import { useAuthentication } from "@Features/authentication/hooks/useAuthentication";
import { initializeFirebaseAuth, signInSuccess } from "@Features/authentication/store/authenticationEpics";
import { authenticationStateSelector } from "@Features/authentication/store/authenticationStateSlice";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";
import { isFetchFromBffError } from "@Utils/isFetchFromBffError";

import { processTwoFactorsSignInError } from "../utils/processTwoFactorsSignInError";

type SubmissionStatus =
  | {
      state: "error";
      message: string;
    }
  | {
      state: "loading";
    }
  | {
      state: "default";
    };

const CODE_VERIFICATION_STRINGS = appStrings.features.codeVerification;

/**
 * Set of flags and functions to execute request to code verification with sign in/up and resend email for code
 */
export function useSubmitVerificationCode() {
  const dispatch = useDispatch();
  const userCredentials = useSelector(authenticationStateSelector("mfaCredentials"));
  const [verifyMfaCode] = useLazySecondFactorSignInQuery();
  const [resendVerificationCode] = useLazyFirstFactorSignInQuery();
  const { authenticateNewlyCreatedUser, authenticateUserWithCustomToken } = useAuthentication();
  /** Represent current status of the form's submission */
  const [submissionStatus, setSubmissionStatus] = useState<SubmissionStatus>({
    state: "default",
  });

  const verifyCodeAndSignInUser = async (
    verificationCode: string,
    isTrustedDevice: boolean,
    isNewRegistration: boolean,
    isChangePassword: boolean,
  ) => {
    setSubmissionStatus({ state: "loading" });

    try {
      const { signin_token: signInToken, trusted_device_token: trustedDeviceToken } = await verifyMfaCode({
        email: userCredentials.email,
        password: userCredentials.password,
        phone: userCredentials?.mobilePhone,
        verificationCode,
        trustThisDevice: isTrustedDevice,
      }).unwrap();

      if (trustedDeviceToken) {
        // store registration device id to "remember" device to skip MFA from next time
        localStorage.setItem(LOCAL_STORAGE_DEVICE_TOKEN, trustedDeviceToken);
      }
      // If we're in the change password flow, we want to authenticate the user without triggering any other actions
      if (isChangePassword) {
        return await authenticateUserWithCustomToken(signInToken, false);
      }

      if (isNewRegistration) {
        await authenticateNewlyCreatedUser(signInToken);

        dispatch(initializeFirebaseAuth());
        dispatch(signInSuccess());
      } else {
        await authenticateUserWithCustomToken(signInToken);
      }

      dispatch(
        SnackbarStateSlice.actions.toggleSnackbar({
          isOpen: true,
          message: CODE_VERIFICATION_STRINGS.successfulVerification,
        }),
      );
    } catch (error) {
      Sentry.captureException(error);

      if (error instanceof FirebaseError) {
        setSubmissionStatus({
          state: "error",
          message: appStrings.features.authentication.generalError,
        });
      }

      if (isFetchFromBffError(error)) {
        const processedMfaVerificationError = processTwoFactorsSignInError(error);

        return setSubmissionStatus({ state: "error", message: processedMfaVerificationError.error });
      }

      setSubmissionStatus({
        state: "error",
        message: CODE_VERIFICATION_STRINGS.wrongCodeError,
      });
    }
  };

  const requestResendOfVerificationCode = async () => {
    await resendVerificationCode({
      email: userCredentials.email,
      password: userCredentials.password,
      phone: userCredentials?.mobilePhone,
    }).unwrap();
  };

  return {
    /** Current status of the submission */
    submissionStatus,
    /** Login or Sign up with code verification */
    verifyCodeAndSignInUser,
    /** Resend verification code to email/sms */
    requestResendOfVerificationCode,
    /** User credentials */
    userCredentials,
  };
}
