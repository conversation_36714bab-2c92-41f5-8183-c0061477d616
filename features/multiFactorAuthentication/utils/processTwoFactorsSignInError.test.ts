import { describe, test, expect } from "vitest";

import { appStrings } from "@Assets/app_strings";
import { BffFetchError } from "@Utils/isFetchFromBffError";

import { processTwoFactorsSignInError } from "./processTwoFactorsSignInError";

const AUTHENTICATION_STRINGS = appStrings.features.authentication;
const MFA_CODE_STRINGS = appStrings.features.codeVerification;

describe("processTwoFactorsSignInError", () => {
  test("Should return SIGN_IN_ERROR for status 400", () => {
    const error: BffFetchError = {
      status: 400,
      data: { errors: [{ code: "", title: "", detail: "" }], status_code: 400 },
    };
    const result = processTwoFactorsSignInError(error);

    expect(result).toEqual({ type: "SIGN_IN_ERROR", error: AUTHENTICATION_STRINGS.failedServerValidation });
  });

  test("Should return SIGN_IN_ERROR for INVALID_CREDENTIALS error code", () => {
    const error: BffFetchError = {
      status: 401,
      data: { errors: [{ code: "INVALID_CREDENTIALS", title: "Invalid credentials", detail: "" }], status_code: 401 },
    };
    const result = processTwoFactorsSignInError(error);

    expect(result).toEqual({ type: "SIGN_IN_ERROR", error: AUTHENTICATION_STRINGS.invalidCredentials });
  });

  test("Should return MFA_VERIFICATION_ERROR for MFA_ERROR_INVALID_CODE error code", () => {
    const error: BffFetchError = {
      status: 401,
      data: { errors: [{ code: "MFA_ERROR_INVALID_CODE", title: "Invalid MFA code", detail: "" }], status_code: 401 },
    };
    const result = processTwoFactorsSignInError(error);

    expect(result).toEqual({ type: "MFA_VERIFICATION_ERROR", error: MFA_CODE_STRINGS.codeValidationError });
  });

  test("Should return MFA_VERIFICATION_ERROR for MFA_ERROR_EXPIRED_CODE error code", () => {
    const error: BffFetchError = {
      status: 401,
      data: { errors: [{ code: "MFA_ERROR_EXPIRED_CODE", title: "Expired code", detail: "" }], status_code: 401 },
    };
    const result = processTwoFactorsSignInError(error);

    expect(result).toEqual({ type: "MFA_VERIFICATION_ERROR", error: MFA_CODE_STRINGS.codeExpired });
  });

  test("Should return MFA_VERIFICATION_ERROR for MFA_ERROR_MAX_VERIFICATION_ATTEMPTS_REACHED error code", () => {
    const error: BffFetchError = {
      status: 401,
      data: {
        errors: [{ code: "MFA_ERROR_MAX_VERIFICATION_ATTEMPTS_REACHED", title: "Max attempts reached", detail: "" }],
        status_code: 401,
      },
    };
    const result = processTwoFactorsSignInError(error);

    expect(result).toEqual({ type: "MFA_VERIFICATION_ERROR", error: MFA_CODE_STRINGS.maxAttemptsReached });
  });

  test("Should return generic MFA_VERIFICATION_ERROR for unknown error code", () => {
    const error: BffFetchError = {
      status: 500,
      data: { errors: [{ code: "UNKNOWN_ERROR", title: "Unknown error", detail: "" }], status_code: 500 },
    };
    const result = processTwoFactorsSignInError(error);

    expect(result).toEqual({
      type: "MFA_VERIFICATION_ERROR",
      error: AUTHENTICATION_STRINGS.genericAuthenticationError,
    });
  });

  test("Should return generic MFA_VERIFICATION_ERROR for random error code", () => {
    const error: BffFetchError = {
      status: 418,
      data: { errors: [{ code: "IM_A_TEAPOT", title: "I'm a teapot", detail: "I'm a teapot" }], status_code: 418 },
    };

    const result = processTwoFactorsSignInError(error);

    expect(result).toEqual({
      type: "MFA_VERIFICATION_ERROR",
      error: AUTHENTICATION_STRINGS.genericAuthenticationError,
    });
  });
});
