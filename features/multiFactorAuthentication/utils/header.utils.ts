import { appStrings } from "@Assets/app_strings";

import { SCREEN_VARIANT, MFA_TYPE } from "../assets/constants";
import { MfaType, MfaVerificationHeaderIcon } from "../types/mfa.types";

const MFA_VERIFICATION_STRINGS = appStrings.features.codeVerification;

export function headerIsScreenVariant(
  searchParams: string | null,
): searchParams is (typeof SCREEN_VARIANT)[keyof typeof SCREEN_VARIANT] {
  return searchParams === SCREEN_VARIANT.REGISTRATION || searchParams === SCREEN_VARIANT.LOGIN;
}

const VERIFICATION_HEADERS_BY_MFA_TYPE = {
  [MFA_TYPE.EMAIL]: {
    [SCREEN_VARIANT.REGISTRATION]: {
      header: MFA_VERIFICATION_STRINGS.verifyYourIdentityHeader,
      subheader: MFA_VERIFICATION_STRINGS.formSubheader,
    },
    [SCREEN_VARIANT.LOGIN]: {
      header: MFA_VERIFICATION_STRINGS.formHeaderReturningUser,
      subheader: MFA_VERIFICATION_STRINGS.formSubheaderReturningUser,
    },
  },
  [MFA_TYPE.SMS]: {
    [SCREEN_VARIANT.REGISTRATION]: {
      header: MFA_VERIFICATION_STRINGS.verifyYourIdentityHeader,
      subheader: MFA_VERIFICATION_STRINGS.smsSubHeader,
    },
    [SCREEN_VARIANT.LOGIN]: {
      header: MFA_VERIFICATION_STRINGS.formHeaderReturningUser,
      subheader: MFA_VERIFICATION_STRINGS.smsSubHeader,
    },
  },
} as const satisfies Record<
  MfaType,
  Record<(typeof SCREEN_VARIANT)[keyof typeof SCREEN_VARIANT], { header: string; subheader: string }>
>;

/** Returns title and subheader of a screen based on given query parameter */
export function getFormHeaderAndSubHeader(
  searchParams: URLSearchParams,
  text: "HEADER" | "SUBHEADER",
  mfaType: MfaType,
) {
  const variantQuery = searchParams.get("variant");
  const headersByMfaType = VERIFICATION_HEADERS_BY_MFA_TYPE[mfaType];

  // Default case - when query parameter is not something expected.
  // it returns the most general message combination
  if (!headerIsScreenVariant(variantQuery)) {
    return text === "HEADER"
      ? headersByMfaType[SCREEN_VARIANT.REGISTRATION].header
      : headersByMfaType[SCREEN_VARIANT.LOGIN].subheader;
  }

  return text === "HEADER" ? headersByMfaType[variantQuery].header : headersByMfaType[variantQuery].subheader;
}

export const getHeaderIcon = (mfaType: MfaType, searchParams: URLSearchParams): MfaVerificationHeaderIcon => {
  const variantQuery = searchParams.get("variant");

  if (mfaType === "SMS") {
    return "ChatBubbleNotification";
  }

  if (!headerIsScreenVariant(variantQuery) || variantQuery === SCREEN_VARIANT.LOGIN) {
    return "Devices";
  }

  return "Mail";
};
