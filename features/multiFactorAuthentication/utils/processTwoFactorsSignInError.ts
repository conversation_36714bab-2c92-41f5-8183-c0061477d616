import * as Sentry from "@sentry/nextjs";

import { appStrings } from "@Assets/app_strings";
import { BffFetchError } from "@Utils/isFetchFromBffError";

type ProcessTwoFactorsSignInError =
  | { type: "SIGN_IN_ERROR"; error: string }
  | { type: "MFA_VERIFICATION_ERROR"; error: string };

const AUTHENTICATION_STRINGS = appStrings.features.authentication;
const MFA_CODE_STRINGS = appStrings.features.codeVerification;
const TWO_FACTOR_SIGN_IN_ERROR_MESSAGES = {
  MFA_ERROR_INVALID_CODE: MFA_CODE_STRINGS.codeValidationError,
  MFA_ERROR_EXPIRED_CODE: MFA_CODE_STRINGS.codeExpired,
  MFA_ERROR_MAX_VERIFICATION_ATTEMPTS_REACHED: MFA_CODE_STRINGS.maxAttemptsReached,
  MFA_ERROR: AUTHENTICATION_STRINGS.genericAuthenticationError,
} as const;

const isSignInError = (code: string): code is keyof typeof TWO_FACTOR_SIGN_IN_ERROR_MESSAGES => {
  return code in TWO_FACTOR_SIGN_IN_ERROR_MESSAGES;
};

export const processTwoFactorsSignInError = (error: BffFetchError): ProcessTwoFactorsSignInError => {
  const { data } = error;
  const { code: errorCode, title, detail: errorDetail } = data.errors[0];

  Sentry.captureException(
    new Error(
      `Second factor sign in error. Error code: ${errorCode}, Error message: ${title}, Error detail: ${errorDetail}`,
    ),
  );

  if (error.status === 400) {
    return { type: "SIGN_IN_ERROR", error: AUTHENTICATION_STRINGS.failedServerValidation };
  }

  if (errorCode === "INVALID_CREDENTIALS") {
    return { type: "SIGN_IN_ERROR", error: AUTHENTICATION_STRINGS.invalidCredentials };
  }

  return {
    type: "MFA_VERIFICATION_ERROR",
    error: isSignInError(errorCode)
      ? TWO_FACTOR_SIGN_IN_ERROR_MESSAGES[errorCode]
      : AUTHENTICATION_STRINGS.genericAuthenticationError,
  };
};
