import { describe, test, expect } from "vitest";

import { appStrings } from "@Assets/app_strings";

import { getFormHeaderAndSubHeader, getHeaderIcon } from "./header.utils";
import { MFA_TYPE, SCREEN_VARIANT } from "../assets/constants";

describe("getFormHeaderAndSubHeader", () => {
  test("should return default header when variant is not specified and mfa type is email", () => {
    const searchParams = new URLSearchParams();
    const result = getFormHeaderAndSubHeader(searchParams, "HEADER", MFA_TYPE.EMAIL);

    expect(result).toBe(appStrings.features.codeVerification.verifyYourIdentityHeader);
  });

  test("should return default subheader when variant is not specified and mfa type is email", () => {
    const searchParams = new URLSearchParams();
    const result = getFormHeaderAndSubHeader(searchParams, "SUBHEADER", MFA_TYPE.EMAIL);

    expect(result).toBe(appStrings.features.codeVerification.formSubheaderReturningUser);
  });

  test("should return registration header when variant is registration and mfa type is email", () => {
    const searchParams = new URLSearchParams({ variant: SCREEN_VARIANT.REGISTRATION });
    const result = getFormHeaderAndSubHeader(searchParams, "HEADER", MFA_TYPE.EMAIL);

    expect(result).toBe(appStrings.features.codeVerification.verifyYourIdentityHeader);
  });

  test("should return registration subheader when variant is registration and mfa type is email", () => {
    const searchParams = new URLSearchParams({ variant: SCREEN_VARIANT.REGISTRATION });
    const result = getFormHeaderAndSubHeader(searchParams, "SUBHEADER", MFA_TYPE.EMAIL);

    expect(result).toBe(appStrings.features.codeVerification.formSubheader);
  });

  test("should return login header when variant is login and mfa type is email", () => {
    const searchParams = new URLSearchParams({ variant: SCREEN_VARIANT.LOGIN });
    const result = getFormHeaderAndSubHeader(searchParams, "HEADER", MFA_TYPE.EMAIL);

    expect(result).toBe(appStrings.features.codeVerification.formHeaderReturningUser);
  });

  test("should return login subheader when variant is login and mfa type is email", () => {
    const searchParams = new URLSearchParams({ variant: SCREEN_VARIANT.LOGIN });
    const result = getFormHeaderAndSubHeader(searchParams, "SUBHEADER", MFA_TYPE.EMAIL);

    expect(result).toBe(appStrings.features.codeVerification.formSubheaderReturningUser);
  });

  test("should return registeration header when variant is registeration and mfa type is sms", () => {
    const searchParams = new URLSearchParams({ variant: SCREEN_VARIANT.REGISTRATION });
    const result = getFormHeaderAndSubHeader(searchParams, "HEADER", MFA_TYPE.SMS);

    expect(result).toBe(appStrings.features.codeVerification.verifyYourIdentityHeader);
  });

  test("should return registeration subheader when variant is registeration and mfa type is sms", () => {
    const searchParams = new URLSearchParams({ variant: SCREEN_VARIANT.REGISTRATION });
    const result = getFormHeaderAndSubHeader(searchParams, "SUBHEADER", MFA_TYPE.SMS);

    expect(result).toBe(appStrings.features.codeVerification.smsSubHeader);
  });

  test("should return login subheader when variant is login and mfa type is sms", () => {
    const searchParams = new URLSearchParams({ variant: SCREEN_VARIANT.LOGIN });
    const result = getFormHeaderAndSubHeader(searchParams, "SUBHEADER", MFA_TYPE.SMS);

    expect(result).toBe(appStrings.features.codeVerification.smsSubHeader);
  });
});

describe("getHeaderIcon", () => {
  test("should return Devices icon when screen variant is not specified and mfa type is email", () => {
    const searchParams = new URLSearchParams();
    const result = getHeaderIcon(MFA_TYPE.EMAIL, searchParams);

    expect(result).toBe("Devices");
  });

  test("should return ChatBubbleNotification icon when mfa type is SMS and screen variant is not specified", () => {
    const searchParams = new URLSearchParams();
    const result = getHeaderIcon(MFA_TYPE.SMS, searchParams);

    expect(result).toBe("ChatBubbleNotification");
  });

  test("should return Mail icon when variant is registration and mfa type is EMAIL", () => {
    const searchParams = new URLSearchParams({ variant: SCREEN_VARIANT.REGISTRATION });
    const result = getHeaderIcon(MFA_TYPE.EMAIL, searchParams);

    expect(result).toBe("Mail");
  });

  test("should return Devices icon when variant is login and mfa type is EMAIL", () => {
    const searchParams = new URLSearchParams({ variant: SCREEN_VARIANT.LOGIN });
    const result = getHeaderIcon(MFA_TYPE.EMAIL, searchParams);

    expect(result).toBe("Devices");
  });

  test("should return ChatBubbleNotification icon when variant is registration and mfa type is SMS", () => {
    const searchParams = new URLSearchParams({ variant: SCREEN_VARIANT.REGISTRATION });
    const result = getHeaderIcon(MFA_TYPE.SMS, searchParams);

    expect(result).toBe("ChatBubbleNotification");
  });

  test("should return ChatBubbleNotification icon when variant is login and mfa type is SMS", () => {
    const searchParams = new URLSearchParams({ variant: SCREEN_VARIANT.LOGIN });
    const result = getHeaderIcon(MFA_TYPE.SMS, searchParams);

    expect(result).toBe("ChatBubbleNotification");
  });
});
