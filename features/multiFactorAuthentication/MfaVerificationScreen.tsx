import { useDispatch } from "react-redux";
import { useSearchParams } from "next/navigation";

import { AuthFormContainer } from "@Components/AuthFormContainer/AuthFormContainer";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { Routes } from "@Types";

import { MFA_TYPE, SCREEN_VARIANT } from "./assets/constants";
import { CodeVerificationScreen } from "./components/CodeVerificationScreen";
import { MfaStateError } from "./components/MfaStateError";
import { useSubmitVerificationCode } from "./hooks/useSubmitVerificationCode";
import { getFormHeaderAndSubHeader, getHeaderIcon, headerIsScreenVariant } from "./utils/header.utils";

const MFA_TYPES = Object.values(MFA_TYPE);

const isNewRegistration = (variant: string | null) => {
  // Default case - when query parameter is not something expected.
  // We'd expect the user to login in this case
  if (!headerIsScreenVariant(variant)) {
    return false;
  }

  return variant === SCREEN_VARIANT.REGISTRATION;
};

export const MfaVerificationScreen = () => {
  const dispatch = useDispatch();
  /** This screen varies based on its "variant" query parameter */
  const searchParams = useSearchParams();
  const { userCredentials } = useSubmitVerificationCode();

  const variantSearchParam = searchParams.get("variant");
  const mfaTypeSearchParam = searchParams.get("mfaType");
  /**  Default to email verification if query param is missing as majority of current clients are using email mfa */
  const mfaType = MFA_TYPES.find((type) => type === mfaTypeSearchParam) ?? MFA_TYPE.EMAIL;
  const isEmailMFA = mfaType === MFA_TYPE.EMAIL;
  const isLoginScreen = variantSearchParam === SCREEN_VARIANT.LOGIN;
  const headerIcon = getHeaderIcon(mfaType, searchParams);
  const formHeader = getFormHeaderAndSubHeader(searchParams, "HEADER", mfaType);
  const formSubheader = getFormHeaderAndSubHeader(searchParams, "SUBHEADER", mfaType);

  const handleBackToUserInfo = () => {
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: isLoginScreen ? Routes.LOGIN : Routes.SIGN_UP,
        screenName: isLoginScreen ? "SignIn" : "SignUp",
      }),
    );
  };

  const handleGoToUpdateVerificationMethod = () => {
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: Routes.UPDATE_VERIFICATION_PHONE,
        screenName: "UpdateVerificationMethod",
      }),
    );
  };

  return (
    <AuthFormContainer>
      {userCredentials.email === "" || userCredentials.password === "" ? (
        <MfaStateError headerIcon={headerIcon} handleBackToUserInfo={handleBackToUserInfo} />
      ) : (
        <CodeVerificationScreen
          type="Login"
          formHeader={formHeader}
          formSubheader={formSubheader}
          isEmailMFA={isEmailMFA}
          isNewRegistration={isNewRegistration(variantSearchParam)}
          handleBackToUserInfo={handleBackToUserInfo}
          handleGoToUpdateVerificationMethod={handleGoToUpdateVerificationMethod}
          isLoginScreen={isLoginScreen}
          headerIcon={headerIcon}
        />
      )}
    </AuthFormContainer>
  );
};
