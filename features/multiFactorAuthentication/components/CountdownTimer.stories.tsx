import type { Meta, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import { CountDownTimer } from "./CountdownTimer";

const meta: Meta<typeof CountDownTimer> = {
  title: "@Features/emailVerificationScreen/CountdownTimer",
  component: CountDownTimer,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: cylinderThemeDecorator,
};

export default meta;
type Story = StoryObj<typeof CountDownTimer>;

export const Primary: Story = {
  args: {
    requestResendOfVerificationCode: () => {},
  },
};
