import { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { Box, Button, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";

type ResendTimer = {
  /** When user can resend an email in milliseconds (the format in Date.getTime()) */
  waitUntil: number | null;
  /** How many seconds a user needs to wait for retry - it should be 1 second = 1 digit */
  remainingTime: number | null;
};

const MFA_VERIFICATION_STRINGS = appStrings.features.codeVerification;
/** Length of the blocking timer in seconds */
const BLOCK_RESEND_TIME = 300;
const INTERVAL_IN_MILLISECONDS = 1000;

/**
 * Function to activate remaining time. It returns the time when the user can resend the email.
 */
export function getLatestRemainingTime() {
  const currentTimestamp = Math.floor(new Date().getTime() / 1000);

  return {
    waitUntil: currentTimestamp + BLOCK_RESEND_TIME,
    remainingTime: BLOCK_RESEND_TIME,
  };
}

/**
 * Logic to set countdown timer. It starts to countdown timer when `resendTimer` state set.
 *  */
function useCountdownTimer(
  /** callback function to invoke when `remainingTime` become 0 */
  onTimeUpCallback?: () => void,
) {
  const [resendTimer, setResendTimer] = useState<ResendTimer>(getLatestRemainingTime());
  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // When resendTimer is updated, it starts countdown
  useEffect(() => {
    if (resendTimer.waitUntil === null) return;

    countdownIntervalRef.current = setInterval(() => {
      if (resendTimer.waitUntil) {
        const currentTimestamp = Math.floor(new Date().getTime() / 1000);

        // Reset timer when remaining time is 0
        if (currentTimestamp > resendTimer.waitUntil) {
          setResendTimer({ waitUntil: null, remainingTime: null });
          onTimeUpCallback && onTimeUpCallback();
          countdownIntervalRef.current && clearInterval(countdownIntervalRef.current);
          return;
        }

        // Decrease remaining time by 1 second
        const remainingTime = resendTimer.waitUntil - currentTimestamp;

        setResendTimer((prevState) => ({
          ...prevState,
          remainingTime: remainingTime,
        }));
      }
    }, INTERVAL_IN_MILLISECONDS);

    return () => {
      countdownIntervalRef.current && clearInterval(countdownIntervalRef.current);
    };
  }, [countdownIntervalRef, resendTimer, onTimeUpCallback, setResendTimer]);

  /** Function that resets the time to 1 min */
  function resetResendTimer() {
    setResendTimer(getLatestRemainingTime());
  }

  return [resendTimer, resetResendTimer] as const;
}

/**
 * Returns string to display remaining seconds in mm:ss format
 */
function displayRemainingSecond(remainingSeconds: number | null) {
  if (remainingSeconds === null) return "0:00";

  const curatedSeconds = Math.trunc(remainingSeconds % 60);

  if (remainingSeconds < 60) {
    return `00:${curatedSeconds > 9 ? curatedSeconds : `0${curatedSeconds}`}`;
  }

  return `0${Math.floor(remainingSeconds / 60)}:${curatedSeconds > 9 ? curatedSeconds : `0${curatedSeconds}`}`;
}

type CountdownTimerProps = {
  requestResendOfVerificationCode: () => void;
  isEmailMFA: boolean;
};

export function CountDownTimer({ requestResendOfVerificationCode, isEmailMFA }: CountdownTimerProps) {
  const dispatch = useDispatch();
  const [resendTimer, resetResendTimer] = useCountdownTimer();

  const handleResendVerificationCode = () => {
    requestResendOfVerificationCode();
    resetResendTimer();
    dispatch(
      SnackbarStateSlice.actions.toggleSnackbar({
        isOpen: true,
        message: isEmailMFA ? MFA_VERIFICATION_STRINGS.resentEmail : MFA_VERIFICATION_STRINGS.resentSmsCodeVerification,
      }),
    );
  };

  return resendTimer.remainingTime ? (
    <Box display="flex" gap={1} mt={4} mb={5}>
      <Typography variant="action" color={color.text.disabled}>
        {`${isEmailMFA ? MFA_VERIFICATION_STRINGS.remainingTime : MFA_VERIFICATION_STRINGS.resendSmsIn} ${displayRemainingSecond(resendTimer.remainingTime)}`}
      </Typography>
    </Box>
  ) : (
    <Button variant="tertiary" onClick={handleResendVerificationCode} sx={{ mt: 4, mb: 5 }}>
      {isEmailMFA ? MFA_VERIFICATION_STRINGS.resendEmail : MFA_VERIFICATION_STRINGS.resendSmsCode}
    </Button>
  );
}
