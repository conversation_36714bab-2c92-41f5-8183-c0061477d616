import { Paper, Grid, Typography, Button } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";

import { MfaVerificationHeaderIcon } from "../types/mfa.types";

const STATE_ERROR_STRINGS = appStrings.features.codeVerification.stateError;

type MfaStateErrorProps = {
  headerIcon: MfaVerificationHeaderIcon;
  handleBackToUserInfo: () => void;
};

export const MfaStateError = ({ headerIcon, handleBackToUserInfo }: MfaStateErrorProps) => {
  const { formWidth } = useResponsiveStylesHook();

  return (
    <Paper sx={{ width: formWidth }}>
      <AppIcon name={headerIcon} size="lg" containerStyles={{ mb: 5 }} />

      <Grid item mb={2}>
        <Typography variant="h3" color={color.text.strong}>
          {STATE_ERROR_STRINGS.title}
        </Typography>
      </Grid>

      <Grid item>
        <Typography variant="body" whiteSpace="pre-wrap">
          {STATE_ERROR_STRINGS.helpText}
        </Typography>
      </Grid>

      <Button variant="tertiary" onClick={handleBackToUserInfo} sx={{ mt: 4, mb: 5 }}>
        {STATE_ERROR_STRINGS.link}
      </Button>
    </Paper>
  );
};
