import { useEffect, useRef, useState } from "react";
import { Box, Button, FormControlLabel, Grid, IconButton, Typography, Paper, Link } from "@mui/material";
import { CircularProgress } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { ErrorText } from "@Components/ErrorText/ErrorText";
import { FormCheckbox, FormInput } from "@Components/form/Fields";
import { Form } from "@Components/form/Form";
import { HavingTrouble } from "@Components/HavingTrouble/HavingTrouble";
import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";
import { formatPhoneNumber } from "@Utils/formatPhoneNumber";

import { CountDownTimer } from "./CountdownTimer";
import { LearnMoreModal } from "./LearnMoreModal";
import { useSubmitVerificationCode } from "../hooks/useSubmitVerificationCode";
import { MfaVerificationHeaderIcon } from "../types/mfa.types";

const MFA_VERIFICATION_STRINGS = appStrings.features.codeVerification;
const SHARED_FORM_TEXT = appStrings.sharedFormText;

type MfaVerificationSubmitData = { code: string; rememberDevice: boolean };

type CodeVerificationScreenFromLoginProps = Readonly<{
  type: "Login";
  isNewRegistration: boolean;
  isLoginScreen: boolean;
  isChangePasswordSubmitting?: never;
  handleBackToUserInfo: () => void;
  handleGoToUpdateVerificationMethod: () => void;
  handleOnSubmitAction?: never;
}>;

type CodeVerificationScreenFromChangePasswordProps = Readonly<{
  type: "ChangePassword";
  isNewRegistration: false;
  isLoginScreen: false;
  isChangePasswordSubmitting: boolean;
  handleBackToUserInfo?: never;
  handleGoToUpdateVerificationMethod?: never;
  handleOnSubmitAction: () => void;
}>;
type CodeVerificationScreenBaseProps = Readonly<{
  formHeader: string;
  formSubheader: string;
  isEmailMFA: boolean;
  headerIcon: MfaVerificationHeaderIcon;
}>;

type CodeVerificationScreenProps = CodeVerificationScreenBaseProps &
  (CodeVerificationScreenFromLoginProps | CodeVerificationScreenFromChangePasswordProps);

const isMfaVerificationSubmitData = (data: unknown): data is MfaVerificationSubmitData => {
  return data != null && typeof data === "object" && "code" in data && "rememberDevice" in data;
};

export const CodeVerificationScreen = ({
  type,
  formHeader,
  formSubheader,
  isEmailMFA,
  isLoginScreen,
  headerIcon,
  isNewRegistration,
  isChangePasswordSubmitting,
  handleBackToUserInfo,
  handleGoToUpdateVerificationMethod,
  handleOnSubmitAction,
}: CodeVerificationScreenProps) => {
  const errorMessageRef = useRef<HTMLDivElement>(null);
  const { formWidth } = useResponsiveStylesHook();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { submissionStatus, verifyCodeAndSignInUser, requestResendOfVerificationCode, userCredentials } =
    useSubmitVerificationCode();
  const isSubmissionLoading =
    submissionStatus.state === "loading" || (isChangePasswordSubmitting && submissionStatus.state !== "error");

  // Read error message on screen reader when it appears
  useEffect(() => {
    if (submissionStatus.state === "error" && errorMessageRef?.current) {
      errorMessageRef?.current.focus();
    }
  }, [submissionStatus]);

  const onSubmit = async (data: MfaVerificationSubmitData) => {
    if (isSubmissionLoading) return;

    await verifyCodeAndSignInUser(data.code, data.rememberDevice, isNewRegistration, type === "ChangePassword");
    handleOnSubmitAction?.();
  };

  return (
    <>
      <LearnMoreModal isVisible={isModalVisible} handleCloseModal={() => setIsModalVisible(false)} />

      <Paper sx={{ width: formWidth }}>
        <AppIcon name={headerIcon} size="lg" containerStyles={{ mb: 5 }} />

        <Form
          defaultValues={{
            code: "",
            rememberDevice: false,
          }}
          formHeader={formHeader}
          formSubheader={formSubheader}
          onSubmit={(data) => {
            if (isMfaVerificationSubmitData(data)) {
              onSubmit(data);
            }
          }}
        >
          {/* Code input */}
          <Grid item my={5}>
            <FormInput
              type="text"
              inputMode="numeric"
              name="code"
              label={MFA_VERIFICATION_STRINGS.codeLabel}
              placeholder={MFA_VERIFICATION_STRINGS.codePlaceholder}
              rules={{
                required: { value: true, message: SHARED_FORM_TEXT.requiredMessage },
                pattern: {
                  value: /^\d{6}$/,
                  message: MFA_VERIFICATION_STRINGS.codeValidationError,
                },
              }}
              required
              pattern="\d*"
              onChange={(value) => value.replace(/\D/g, "").slice(0, 6)}
            />
          </Grid>

          {/* API error */}
          {submissionStatus.state === "error" ? (
            <ErrorText errorMessage={submissionStatus.message} errorTextRef={errorMessageRef} sx={{ mb: 5 }} />
          ) : null}

          {/* Remember device checkbox */}
          <Box mb={5}>
            <FormControlLabel
              control={<FormCheckbox name="rememberDevice" ariaLabel={MFA_VERIFICATION_STRINGS.rememberThisDevice} />}
              label={
                <Typography variant="body" tabIndex={-1}>
                  {MFA_VERIFICATION_STRINGS.rememberThisDevice}
                </Typography>
              }
              sx={{ mr: 0 }}
            />

            <IconButton onClick={() => setIsModalVisible(true)} disableRipple>
              <AppIcon name="Help" size="sm" containerStyles={{ display: "flex" }} color={color.icon.default} />
            </IconButton>
          </Box>

          {isEmailMFA ? (
            <Typography variant="bodyDense" mb={4}>
              {MFA_VERIFICATION_STRINGS.emailVerifyText1}

              <span style={{ fontWeight: "bold", display: "inline" }}>{userCredentials.email}</span>

              {type === "Login" ? (
                <>
                  {MFA_VERIFICATION_STRINGS.emailVerifyText2}

                  <Link onClick={handleBackToUserInfo} href="#">
                    {MFA_VERIFICATION_STRINGS.emailVerifyReturnCTA}
                  </Link>

                  {MFA_VERIFICATION_STRINGS.emailVerifyText3}
                </>
              ) : null}
            </Typography>
          ) : (
            <Typography variant="bodyDense" mb={4}>
              {`${MFA_VERIFICATION_STRINGS.smsSentCodeTo(formatPhoneNumber(userCredentials.mobilePhone ?? ""))} ${
                isLoginScreen ? "" : MFA_VERIFICATION_STRINGS.smsRegistrationSentCodeTo
              }`}

              {!isLoginScreen && type === "Login" ? (
                <Link variant="bodyDense" onClick={handleGoToUpdateVerificationMethod} href="#">
                  {MFA_VERIFICATION_STRINGS.smsRegistrationSentCodeToLink}
                </Link>
              ) : null}
            </Typography>
          )}

          <Button type="submit" fullWidth variant="primary" disabled={isSubmissionLoading}>
            {isSubmissionLoading ? (
              <CircularProgress thickness={4} size={24} color="inherit" />
            ) : (
              MFA_VERIFICATION_STRINGS.submission
            )}
          </Button>

          <CountDownTimer requestResendOfVerificationCode={requestResendOfVerificationCode} isEmailMFA={isEmailMFA} />

          <HavingTrouble />
        </Form>
      </Paper>
    </>
  );
};
