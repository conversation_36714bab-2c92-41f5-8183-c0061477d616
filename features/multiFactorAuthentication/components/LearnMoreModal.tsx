import { Box, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { BaseModal } from "@Components/BaseModal/BaseModal";

const EMAIL_VERIFICATION_STRING = appStrings.features.codeVerification.modal;

type LearnMoreModalProps = Readonly<{
  isVisible: boolean;
  handleCloseModal: () => void;
}>;

export function LearnMoreModal({ isVisible, handleCloseModal }: LearnMoreModalProps) {
  return (
    <BaseModal
      isModalOpen={isVisible}
      title={EMAIL_VERIFICATION_STRING.learnMore}
      onClose={handleCloseModal}
      bodyContent={
        <Box display="grid" gap={5}>
          <Box display="grid" gap={1}>
            <Typography variant="h4" color={color.text.strong}>
              {EMAIL_VERIFICATION_STRING.rememberDevice}
            </Typography>
            <Typography variant="body">{EMAIL_VERIFICATION_STRING.rememberDeviceBody}</Typography>
          </Box>
          <Box display="grid" gap={1}>
            <Typography variant="h4" color={color.text.strong}>
              {EMAIL_VERIFICATION_STRING.benefits}
            </Typography>
            <Typography variant="body">{EMAIL_VERIFICATION_STRING.benefitsBody}</Typography>
            <Typography variant="body">{EMAIL_VERIFICATION_STRING.benefitsBody2}</Typography>
          </Box>
          <Box display="grid" gap={1}>
            <Typography variant="h4" color={color.text.strong}>
              {EMAIL_VERIFICATION_STRING.importantToNote}
            </Typography>
            <Typography variant="body">{EMAIL_VERIFICATION_STRING.importantToNoteBody}</Typography>
          </Box>
        </Box>
      }
    />
  );
}
