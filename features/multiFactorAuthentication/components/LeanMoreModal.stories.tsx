import type { Meta, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import { LearnMoreModal } from "./LearnMoreModal";

const meta: Meta<typeof LearnMoreModal> = {
  title: "@Features/emailVerificationScreen/LearnMoreModal",
  component: LearnMoreModal,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: cylinderThemeDecorator,
};

export default meta;
type Story = StoryObj<typeof LearnMoreModal>;

export const Primary: Story = {
  args: {
    isVisible: true,
    handleCloseModal: () => alert("Close Modal"),
  },
};
