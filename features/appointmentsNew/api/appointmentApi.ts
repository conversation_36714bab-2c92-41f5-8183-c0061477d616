import { Appointment } from "@vivantehealth/vivante-core";

import { bffApi } from "@Api/bffApi";

import {
  AppointmentTimeSlots,
  AppointmentTimeSlotsResponse,
  CreateAppointmentRequest,
  CreateAppointmentResponse,
  RescheduleAppointmentRequest,
} from "../types/appointments.types";
import {
  transformAppointmentList,
  transformAppointmentTimeSlots,
  transformGiAppointmentTimeSlots,
} from "../utils/appointments.utils";

export const appointmentsApi = bffApi.injectEndpoints({
  endpoints: (builder) => ({
    /** Retrieves all available time slots for MD/PA based on the state */
    getGiAppointmentTimeSlots: builder.query<AppointmentTimeSlots, GiAppointmentTimeSlotsRequest>({
      query: (body) => ({
        url: `v1/slots/primary?us_state=${body.usState}`,
        method: "GET",
      }),
      transformResponse: transformGiAppointmentTimeSlots,
      providesTags: ["Appointment"],
    }),
    getAppointmentTimeSlots: builder.query<AppointmentTimeSlots, AppointmentTimeSlotsRequest>({
      query: (options) => ({
        url: `v1/slots`,
        method: "GET",
        params: {
          member_id: options?.memberId,
          role_code: options?.roleCode,
          us_state: options?.usState,
          clinician_id: options?.clinicianId,
          specialty: options?.specialty,
        },
      }),
      transformResponse: (appointments: AppointmentTimeSlotsResponse, _, { specialty }) => {
        /** If specialty is specified as INT, than we want to ensure our time slots include empty days */
        return transformAppointmentTimeSlots(appointments, specialty === "INT");
      },
      providesTags: ["Appointment"],
    }),
    /** Creates an appointment */
    createAppointment: builder.mutation<CreateAppointmentResponse, CreateAppointmentRequest>({
      query: (body) => ({
        url: `v1/appointments?member_id=${body.memberId}`,
        method: "POST",
        body: {
          clinician_id: body.clinicianId,
          start: body.startTime,
          end: body.endTime,
          communication_method: body.communicationMethod,
          request_tracking_id: body.requestTrackingId,
        },
      }),
      invalidatesTags: ["Appointment"],
    }),
    /** Reschedules an appointment */
    rescheduleAppointment: builder.mutation<CreateAppointmentResponse, RescheduleAppointmentRequest>({
      query: (body) => ({
        url: `v1/appointments/${body.appointmentId}?member_id=${body.memberId}`,
        method: "PATCH",
        body: {
          clinician_id: body.clinicianId,
          start: body.startTime,
          end: body.endTime,
          communication_method: body.communicationMethod,
        },
      }),
      invalidatesTags: ["Appointment"],
    }),
    /** Cancels an appointment */
    cancelAppointment: builder.mutation<void, DeleteAppointmentRequest>({
      query: (body) => ({
        url: `v1/appointments/${body.appointmentId}?member_id=${body.memberId}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Appointment"],
    }),
    /** Get all appointments for the user */
    getAppointmentList: builder.query<Appointment[], void>({
      query: () => ({ url: `v1/appointments`, method: "GET" }),
      transformResponse: transformAppointmentList,
      providesTags: ["Appointment"],
    }),
  }),
});

export const {
  useGetGiAppointmentTimeSlotsQuery,
  useGetAppointmentTimeSlotsQuery,
  useCreateAppointmentMutation,
  useRescheduleAppointmentMutation,
  useCancelAppointmentMutation,
  useGetAppointmentListQuery,
} = appointmentsApi;

type GiAppointmentTimeSlotsRequest = {
  usState: string;
};

type DeleteAppointmentRequest = {
  appointmentId: string;
  memberId: string;
};

type AppointmentTimeSlotsRequest = {
  usState?: string;
  clinicianId?: string;
  memberId?: string;
  roleCode?: "MD" | "PA" | "CG" | "HC" | "RD";
  specialty?: "GI" | "INT";
};
