import { useState } from "react";
import { ActionPlanTargetState } from "@vivantehealth/vivante-core";
import dayjs from "dayjs";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/router";

import { setActionNewState } from "@Features/carePlan/utils/markAsDone.util";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";
import { useAppDispatch, useAppSelector } from "@Store/hooks";
import { Routes } from "@Types";

import { useMdTimeSlots } from "./useMdTimeSlots";
import { appointmentsApi, useCreateAppointmentMutation, useRescheduleAppointmentMutation } from "../api/appointmentApi";
import { AppointmentBaseRequest, AppointmentScreen, AppointmentTimeSlotSelection } from "../types/appointments.types";
import { processAppointmentSchedulingErrors } from "../utils/processAppointmentSchedulingErrors";

export const useAppointments = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const appointmentToRescheduleId = searchParams.get("appointmentToRescheduleId");
  const interventionId = searchParams.get("interventionId");
  const member = useAppSelector((state) => state.memberState.member);
  const memberUsState = member?.usState ?? "AL";
  const [appointmentScreen, setAppointmentScreen] = useState<AppointmentScreen>("STATE_SELECTION");
  const [selectedState, setSelectedState] = useState(memberUsState);
  const [appointment, setAppointment] = useState<AppointmentTimeSlotSelection | undefined>();
  const { isFetching, isFetchingTimeSlotsError, timeSlots } = useMdTimeSlots(selectedState);
  const [invokeCreateAppointment, { isLoading: isSubmittingAppointment }] = useCreateAppointmentMutation();
  const [invokeRescheduleAppointment, { isLoading: isSubmittingReschedule }] = useRescheduleAppointmentMutation();
  const defaultPosition =
    Object.keys(timeSlots ?? {}).findIndex((key) => dayjs(appointment?.slot.start).format("YYYY-MM-DD") === key) ?? 0;

  const handleOnStateSelection = (selectedState: string) => {
    setSelectedState(selectedState);
    setAppointmentScreen("SCHEDULE_APPOINTMENT");
  };

  const handleOnAppointmentSelected = (appointment: AppointmentTimeSlotSelection) => {
    setAppointment(appointment);
    setAppointmentScreen("CONFIRM_APPOINTMENT");
  };

  const handleScheduleOrRescheduleAppointment = async () => {
    if (!appointment || !member?.id) return;

    try {
      const appointmentInformation: AppointmentBaseRequest = {
        memberId: member.id,
        clinicianId: appointment.clinician.id,
        startTime: appointment.slot.start,
        endTime: appointment.slot.end,
        communicationMethod: appointment.communicationMethod === "PHONE" ? "phone" : "video",
      };

      appointmentToRescheduleId
        ? await invokeRescheduleAppointment({
            ...appointmentInformation,
            appointmentId: appointmentToRescheduleId,
          }).unwrap()
        : await invokeCreateAppointment({
            ...appointmentInformation,
            requestTrackingId: appointment.clinician.requestTrackingId,
          }).unwrap();

      if (interventionId) {
        setActionNewState(ActionPlanTargetState.COMPLETED, undefined, interventionId);
      }

      router.replace(Routes.CARE_TEAM);
    } catch (error) {
      const processedError = processAppointmentSchedulingErrors(error);

      // If the error is related to the request tracking id, invalidate the care guide api cache to get a new request_tracking_id
      if (processedError.type === "REQUEST_TRACKING_ID_ERROR") {
        dispatch(appointmentsApi.util.invalidateTags(["Appointment"]));
      }

      dispatch(
        SnackbarStateSlice.actions.toggleSnackbar({
          message: processedError.message,
          isOpen: true,
          variant: "error",
        }),
      );
      // As the user can not reschedule within 1 minute of the appointment, we force redirect to the care team screen
      if (processedError.type === "RESCHEDULING_WITHIN_1_MINUTE") {
        router.replace(Routes.CARE_TEAM);
      }
    }
  };

  const handleOnBack = () => {
    if (appointmentScreen === "SCHEDULE_APPOINTMENT") {
      return setAppointmentScreen("STATE_SELECTION");
    }
    if (appointmentScreen === "CONFIRM_APPOINTMENT") {
      return setAppointmentScreen("SCHEDULE_APPOINTMENT");
    }

    return router.back();
  };

  const handleRefreshPage = () => {
    router.reload();
  };

  return {
    appointment,
    appointmentScreen,
    selectedState,
    isFetching,
    isSubmitting: isSubmittingAppointment || isSubmittingReschedule,
    timeSlots,
    isFetchingTimeSlotsError,
    defaultPosition: defaultPosition === -1 ? 0 : defaultPosition,
    setSelectedState,
    handleOnStateSelection,
    handleOnAppointmentSelected,
    handleScheduleOrRescheduleAppointment,
    handleOnBack,
    handleRefreshPage,
  } as const;
};
