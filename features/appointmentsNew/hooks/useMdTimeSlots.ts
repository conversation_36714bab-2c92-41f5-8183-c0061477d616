import { useEffect } from "react";
import { skipToken } from "@reduxjs/toolkit/query";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/router";

import { Routes } from "@Types";

import { useGetGiAppointmentTimeSlotsQuery, useGetAppointmentTimeSlotsQuery } from "../api/appointmentApi";
/**
 * Conditionally fetches MD time slots based on the role in the URL
 * We use a different endpoint for GI and Internist time slots and thus different RTK Query hooks.
 */
export const useMdTimeSlots = (selectedState: string) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const role = searchParams.get("role")?.toLowerCase();

  useEffect(() => {
    /**
     * If the role is not GI or Internist, redirect to the home page.
     */
    if (role !== "gi" && role !== "internist") {
      router.push(Routes.HOME);
    }
  }, [role, router]);

  const {
    isFetching: isFetchingGiTimeSlots,
    isError: isFetchingGiTimeSlotsError,
    data: giTimeSlots,
  } = useGetGiAppointmentTimeSlotsQuery(role === "gi" ? { usState: selectedState } : skipToken);
  const {
    isFetching: isFetchingInternistTimeSlots,
    isError: isFetchingInternistTimeSlotsError,
    data: internistTimeSlots,
  } = useGetAppointmentTimeSlotsQuery(
    role === "internist" ? { usState: selectedState, specialty: "INT", roleCode: "MD" } : skipToken,
  );

  return {
    isFetching: isFetchingGiTimeSlots || isFetchingInternistTimeSlots,
    isFetchingTimeSlotsError: isFetchingGiTimeSlotsError || isFetchingInternistTimeSlotsError,
    timeSlots: giTimeSlots ?? internistTimeSlots,
  } as const;
};
