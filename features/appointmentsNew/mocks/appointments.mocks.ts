import dayjs from "dayjs";

import { AppointmentTimeSlotsResponse, GiAppointmentTimeSlotsResponse } from "../types/appointments.types";

export const createMockTimeSlot = (numberOfDaysToAdd: number, startHour: number, startMinute: number) => {
  const startTime = dayjs()
    .startOf("day")
    .add(numberOfDaysToAdd, "day")
    .add(startHour, "hour")
    .add(startMinute, "minute");
  const endTime = startTime.add(60, "minute");

  return {
    start_time: startTime.toISOString(),
    end_time: endTime.toISOString(),
  };
};

export const MOCK_GI_APPOINTMENT_TIME_SLOT_RESPONSE: GiAppointmentTimeSlotsResponse = {
  requests: [
    {
      available_slots: [
        {
          clinician: {
            avatar_link: "",
            clinician_role: {
              code: "MD",
              description: "Medical Doctor",
            },
            enabled: true,
            first_name: "<PERSON>",
            id: "9024890",
            identifiers: [
              {
                identifier: "233452534",
                system: "Canvas",
              },
            ],
            last_name: "Do<PERSON>",
            specialty: {
              code: "GI",
              description: "Gastroenterology",
            },
          },
          slots: [
            createMockTimeSlot(0, 19, 30),
            createMockTimeSlot(0, 20, 0),
            createMockTimeSlot(0, 20, 30),
            createMockTimeSlot(0, 21, 0),
            createMockTimeSlot(0, 21, 30),
            createMockTimeSlot(0, 22, 0),
            createMockTimeSlot(0, 22, 30),
            createMockTimeSlot(0, 23, 0),
            createMockTimeSlot(0, 23, 30),
            createMockTimeSlot(1, 0, 0),
            createMockTimeSlot(1, 0, 30),
            createMockTimeSlot(1, 1, 0),
            createMockTimeSlot(1, 1, 30),
            createMockTimeSlot(1, 2, 0),
            createMockTimeSlot(1, 2, 30),
            createMockTimeSlot(1, 3, 0),
            createMockTimeSlot(3, 16, 0),
            createMockTimeSlot(3, 16, 30),
          ],
        },
      ],
      request_tracking_id: "12345",
      role_code: "MD",
      specialty: "GI",
    },
    {
      available_slots: [
        {
          clinician: {
            avatar_link: "",
            clinician_role: {
              code: "PA",
              description: "Physician Assistant",
            },
            enabled: true,
            first_name: "Jane",
            id: "348590834",
            identifiers: [
              {
                identifier: "1231234",
                system: "Canvas",
              },
            ],
            last_name: "Doe",
            specialty: {
              code: "GI",
              description: "Gastroenterology",
            },
          },
          slots: [createMockTimeSlot(0, 19, 30), createMockTimeSlot(0, 20, 0)],
        },
      ],
      request_tracking_id: "67890",
      role_code: "PA",
      specialty: "GI",
    },
  ],
};

export const MOCK_APPOINTMENT_TIME_SLOT_RESPONSE: AppointmentTimeSlotsResponse = {
  available_slots: [
    {
      clinician: {
        avatar_link: "",
        clinician_role: {
          code: "MD",
          description: "Medical Doctor",
        },
        enabled: true,
        first_name: "John",
        id: "9024890",
        identifiers: [
          {
            identifier: "233452534",
            system: "Canvas",
          },
        ],
        last_name: "Doe",
        specialty: {
          code: "INT",
          description: "Internal Medicine",
        },
        focus: {
          code: "IM",
          description: "Internal Medicine",
        },
      },
      slots: [
        createMockTimeSlot(0, 19, 30),
        createMockTimeSlot(0, 20, 0),
        createMockTimeSlot(0, 20, 30),
        createMockTimeSlot(2, 21, 0),
        createMockTimeSlot(2, 21, 30),
      ],
    },
    {
      clinician: {
        avatar_link: "",
        clinician_role: {
          code: "MD",
          description: "Medical Doctor",
        },
        enabled: true,
        first_name: "Jane",
        id: "348590834",
        identifiers: [
          {
            identifier: "1231234",
            system: "Canvas",
          },
        ],
        last_name: "Doe",
        specialty: {
          code: "INT",
          description: "Internal Medicine",
        },
        focus: {
          code: "FM",
          description: "Family Medicine",
        },
      },
      slots: [createMockTimeSlot(0, 19, 30), createMockTimeSlot(0, 20, 0), createMockTimeSlot(3, 16, 0)],
    },
  ],
  request_tracking_id: "12345",
};
