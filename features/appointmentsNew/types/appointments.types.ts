import { AppointmentCommunicationMethod } from "@vivantehealth/vivante-core";

type CommunicationMethod = "phone" | "video";

export type Clinician = Readonly<{
  id: string;
  first_name: string;
  last_name: string;
  clinician_role: {
    code: string;
    description: string;
  };
  specialty?: {
    code: string;
    description: string;
  };
  focus?: {
    code: string;
    description: string;
  };
  avatar_link: string;
  enabled: boolean;
  identifiers: {
    system: string;
    identifier: string;
  }[];
  licensure_states?: string[];
}>;

export type AppointmentBaseRequest = Readonly<{
  memberId: string;
  clinicianId: string;
  startTime: string;
  endTime: string;
  communicationMethod: CommunicationMethod;
}>;

export type CreateAppointmentRequest = AppointmentBaseRequest &
  Readonly<{
    requestTrackingId: string;
  }>;

export type RescheduleAppointmentRequest = AppointmentBaseRequest &
  Readonly<{
    appointmentId: string;
  }>;

export type CreateAppointmentResponse = {
  id: string;
  start: string;
  end: string;
  communication_method: CommunicationMethod;
  request_tracking_id: string;
  meeting_link: string;
  status: string;
  clinician: Clinician;
};

export type TimeSlot = {
  start_time: string;
  end_time: string;
};

export type GiAppointmentTimeSlotsResponse = {
  requests: {
    role_code: "MD" | "PA";
    specialty?: "GI" | "INT";
    request_tracking_id: string;
    available_slots: {
      clinician: Clinician;
      slots: TimeSlot[];
    }[];
  }[];
};

export type ClinicianWithTimeSlots = {
  id: string;
  type: string;
  firstName: string;
  lastName: string;
  avatarLink: string;
  role: string;
  displayableRole?: string;
  requestTrackingId: string;
  availableSlots: AppointmentTimeSlot[];
};

export type AppointmentTimeSlotSelection = {
  slot: AppointmentTimeSlot;
  clinician: ClinicianWithTimeSlots;
  communicationMethod: AppointmentCommunicationMethod;
};

export type AppointmentTimeSlot = {
  start: string;
  end: string;
};

export type AppointmentTimeSlots = Record<string, ClinicianWithTimeSlots[]>;

export type AppointmentScreen = "STATE_SELECTION" | "SCHEDULE_APPOINTMENT" | "CONFIRM_APPOINTMENT";

export type AppointmentTimeSlotsResponse = Readonly<{
  request_tracking_id: string;
  available_slots: {
    clinician: Clinician;
    slots: TimeSlot[] | null;
  }[];
}>;

export type AppointmentListResponse = {
  appointments: {
    id: string;
    start: string;
    end: string;
    communication_method: CommunicationMethod;
    request_tracking_id: string;
    meeting_link?: string;
    status: string;
    clinician: Clinician;
  }[];
};
