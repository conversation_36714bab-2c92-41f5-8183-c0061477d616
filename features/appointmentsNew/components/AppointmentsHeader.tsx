import { Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { BackButton } from "@Components/BackButton/BackButton";

export const AppointmentsHeader = ({ header, onBackClick }: { header: string; onBackClick: () => void }) => {
  return (
    <>
      <BackButton onClick={onBackClick}>{appStrings.buttonText.back}</BackButton>

      <Typography variant="h2Serif" mt={9} mb={6}>
        {header}
      </Typography>
    </>
  );
};
