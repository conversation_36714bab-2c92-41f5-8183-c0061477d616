import { Box, Typography, Button, CircularProgress } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";

const SCHEDULING_STRINGS = appStrings.features.scheduling;

type AppointmentsFooterProps = Readonly<{
  shouldDisplayDisclaimer: boolean;
  isSubmitting?: boolean;
  buttonText?: string;
  minWidth?: number;
  onSubmitCallback?: () => void;
}>;

export const AppointmentsFooter = ({
  shouldDisplayDisclaimer,
  isSubmitting,
  buttonText,
  minWidth,
  onSubmitCallback,
}: AppointmentsFooterProps) => {
  return (
    <Box
      display="flex"
      position="fixed"
      alignItems="center"
      justifyContent="center"
      bottom={0}
      left={0}
      py={5}
      bgcolor={color.background.surface.primary}
      width="100%"
    >
      {shouldDisplayDisclaimer ? (
        <Typography variant="bodyDense" mr={7}>
          {SCHEDULING_STRINGS.disclaimer}
        </Typography>
      ) : null}

      {onSubmitCallback ? (
        <Button
          variant="primary"
          onClick={onSubmitCallback}
          sx={{ minWidth: minWidth || "200px" }}
          disabled={isSubmitting}
        >
          {isSubmitting ? <CircularProgress size={24} color="inherit" /> : buttonText}
        </Button>
      ) : null}
    </Box>
  );
};
