import { AppointmentUSState } from "@vivantehealth/vivante-core";
import { KeyboardArrowDown } from "@mui/icons-material";
import { Select, MenuItem, Typography, Box } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

import { AppointmentsFooter } from "./AppointmentsFooter";
import { AppointmentsHeader } from "./AppointmentsHeader";
import { FOOTER_HEIGHT } from "../assets/constants";

const SCHEDULING_STRINGS = appStrings.features.scheduling;

type AppointmentsStateSelectionProps = Readonly<{
  selectedState: string;
  setSelectedState: (state: string) => void;
  submitStateCallback: (selectedState: string) => void;
  onBackCallback: () => void;
}>;

export const AppointmentsStateSelection = ({
  selectedState,
  setSelectedState,
  submitStateCallback,
  onBackCallback,
}: AppointmentsStateSelectionProps) => {
  const stateKeys = Object.keys(AppointmentUSState);

  return (
    <>
      <Box mb={FOOTER_HEIGHT}>
        <AppointmentsHeader header={SCHEDULING_STRINGS.stateConfirmationHeader} onBackClick={onBackCallback} />

        <Select
          onChange={(e) => setSelectedState(e.target.value)}
          IconComponent={KeyboardArrowDown}
          fullWidth
          value={selectedState}
        >
          {Object.values(AppointmentUSState).map((stateName, stateIndex) => {
            return (
              <MenuItem value={stateKeys[stateIndex]} key={stateName}>
                {stateName}
              </MenuItem>
            );
          })}
        </Select>

        <Typography variant="body" mt={6} mb={5}>
          {SCHEDULING_STRINGS.locationNote}
        </Typography>

        <Typography variant="body">{SCHEDULING_STRINGS.medicalTeamNote}</Typography>
      </Box>

      <AppointmentsFooter
        shouldDisplayDisclaimer={false}
        buttonText={appStrings.buttonText.next}
        onSubmitCallback={() => submitStateCallback(selectedState)}
      />
    </>
  );
};
