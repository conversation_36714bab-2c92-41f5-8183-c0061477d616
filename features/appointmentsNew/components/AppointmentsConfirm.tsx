import { Box, Paper } from "@mui/material";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { AppointmentDuration } from "@Components/AppointmentDuration/AppointmentDuration";
import { AppointmentGetReady } from "@Components/AppointmentGetReady/AppointmentGetReady";
import { AppointmentInformation } from "@Components/AppointmentInformation/AppointmentInformation";
import { GutCheckResultsNote } from "@Components/GutCheckResultsNote/GutCheckResultsNote";
import { PractitionerInformation } from "@Components/PractitionerInformation/PractitionerInformation";

import { AppointmentsFooter } from "./AppointmentsFooter";
import { AppointmentsHeader } from "./AppointmentsHeader";
import { FOOTER_HEIGHT } from "../assets/constants";
import { AppointmentTimeSlotSelection } from "../types/appointments.types";

const SCHEDULING_STRINGS = appStrings.features.scheduling;

type AppointmentsConfirmProps = Readonly<{
  isSubmitting: boolean;
  appointmentSelection: AppointmentTimeSlotSelection;
  onConfirmationCallback: () => void;
  onBackCallback: () => void;
}>;
export const AppointmentsConfirm = ({
  isSubmitting,
  appointmentSelection,
  onConfirmationCallback,
  onBackCallback,
}: AppointmentsConfirmProps) => {
  const slotDuration = dayjs(appointmentSelection.slot.end).diff(dayjs(appointmentSelection.slot.start), "minute");

  return (
    <>
      <Box mb={FOOTER_HEIGHT}>
        <AppointmentsHeader header={SCHEDULING_STRINGS.confirmation.header} onBackClick={onBackCallback} />

        <Paper sx={{ mb: 6 }}>
          <Box display="flex" justifyContent="space-between" alignItems="start">
            <PractitionerInformation practitioner={appointmentSelection.clinician} />

            <AppointmentDuration duration={slotDuration} />
          </Box>

          <Box mt={4}>
            <AppointmentInformation
              startTime={new Date(appointmentSelection.slot.start)}
              communicationMethod={appointmentSelection.communicationMethod}
            />
          </Box>
        </Paper>

        <AppointmentGetReady headingSize="h3" />

        <GutCheckResultsNote />
      </Box>

      <AppointmentsFooter
        isSubmitting={isSubmitting}
        shouldDisplayDisclaimer
        buttonText={appStrings.buttonText.confirmAppointment}
        onSubmitCallback={onConfirmationCallback}
        minWidth={224}
      />
    </>
  );
};
