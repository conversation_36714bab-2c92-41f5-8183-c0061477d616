import { Button, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { BaseModal } from "@Components/BaseModal/BaseModal";

import { AppointmentsConfirm } from "./AppointmentsConfirm";
import { AppointmentsSchedule } from "./AppointmentsSchedule";
import { AppointmentsStateSelection } from "./AppointmentsStateSelection";
import { AppointmentScreen, AppointmentTimeSlots, AppointmentTimeSlotSelection } from "../types/appointments.types";

const SCHEDULING_STRINGS = appStrings.features.scheduling;

type AppointmentsScreenProps = Readonly<{
  appointmentScreen: AppointmentScreen;
  selectedState: string;
  isLoading: boolean;
  isSubmitting: boolean;
  availability: AppointmentTimeSlots | undefined;
  isFetchingTimeSlotsError: boolean;
  appointmentSelection: AppointmentTimeSlotSelection | undefined;
  defaultPosition: number;
  setSelectedState: (state: string) => void;
  submitStateCallback: (selectedState: string) => void;
  onSlotSelectionCallback: (appointment: AppointmentTimeSlotSelection) => void;
  onConfirmationCallback: () => void;
  onBackCallback: () => void;
  handleRefreshPage: () => void;
}>;

export const AppointmentsScreen = ({
  appointmentScreen,
  selectedState,
  isLoading,
  isSubmitting,
  availability,
  isFetchingTimeSlotsError,
  appointmentSelection,
  defaultPosition,
  setSelectedState,
  submitStateCallback,
  onSlotSelectionCallback,
  onConfirmationCallback,
  onBackCallback,
  handleRefreshPage,
}: AppointmentsScreenProps) => {
  const isScheduleAppointment =
    appointmentScreen === "SCHEDULE_APPOINTMENT" && availability !== undefined && defaultPosition >= 0;

  return (
    <>
      <BaseModal
        isModalOpen={isFetchingTimeSlotsError && appointmentScreen === "SCHEDULE_APPOINTMENT"}
        title={SCHEDULING_STRINGS.timeSlotRetrievalError.title}
        bodyContent={<Typography variant="body">{SCHEDULING_STRINGS.timeSlotRetrievalError.body}</Typography>}
        actions={
          <Button variant="primary" onClick={handleRefreshPage} fullWidth>
            {SCHEDULING_STRINGS.timeSlotRetrievalError.buttonText}
          </Button>
        }
        onClose={handleRefreshPage}
        displayCloseButton={false}
      />

      {appointmentScreen === "STATE_SELECTION" && selectedState && (
        <AppointmentsStateSelection
          selectedState={selectedState}
          setSelectedState={setSelectedState}
          onBackCallback={onBackCallback}
          submitStateCallback={submitStateCallback}
        />
      )}

      {isScheduleAppointment && (
        <AppointmentsSchedule
          onBackCallback={onBackCallback}
          availability={availability}
          isLoading={isLoading}
          onSlotSelectionCallback={onSlotSelectionCallback}
          defaultPosition={defaultPosition}
        />
      )}

      {appointmentScreen === "CONFIRM_APPOINTMENT" && appointmentSelection && (
        <AppointmentsConfirm
          isSubmitting={isSubmitting}
          onBackCallback={onBackCallback}
          appointmentSelection={appointmentSelection}
          onConfirmationCallback={onConfirmationCallback}
        />
      )}
    </>
  );
};
