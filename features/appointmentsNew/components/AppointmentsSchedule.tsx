import { useState } from "react";
import { Box, Typography, Paper, Chip } from "@mui/material";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { AppointmentDuration } from "@Components/AppointmentDuration/AppointmentDuration";
import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { OutlinedIconButton } from "@Components/OutlinedIconButton/OutlinedIconButton";
import { PractitionerInformation } from "@Components/PractitionerInformation/PractitionerInformation";

import { AppointmentsHeader } from "./AppointmentsHeader";
import { AppointmentTimeSlots, AppointmentTimeSlotSelection } from "../types/appointments.types";

const SCHEDULING_STRINGS = appStrings.features.scheduling;

type AppointmentsScheduleProps = Readonly<{
  availability: AppointmentTimeSlots;
  defaultPosition: number;
  isLoading: boolean;
  onSlotSelectionCallback: (appointment: AppointmentTimeSlotSelection) => void;
  onBackCallback: () => void;
}>;

export const AppointmentsSchedule = ({
  availability,
  defaultPosition,
  isLoading,
  onSlotSelectionCallback,
  onBackCallback,
}: AppointmentsScheduleProps) => {
  const [position, setPosition] = useState(defaultPosition);
  const dateKeys = Object.keys(availability);
  const availabilityValues = Object.values(availability);
  const currentDate = dayjs(dateKeys[position]).format("dddd, MMMM D");
  /** Sort to ensure PA's are always listed after MDs */
  const currentDateBlock = [...(availabilityValues?.[position] ?? [])]?.sort((a, b) => {
    return a.role.localeCompare(b.role);
  });

  const next = () => {
    setPosition((prevPosition) => prevPosition + 1);
  };
  const previous = () => {
    setPosition((prevPosition) => prevPosition - 1);
  };

  return isLoading ? (
    <LoadingSpinner open />
  ) : (
    <>
      <AppointmentsHeader header={SCHEDULING_STRINGS.chooseAppointmentTime} onBackClick={onBackCallback} />

      <Box display="flex" flexDirection="column" gap={6}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <OutlinedIconButton
            icon="LeftChevron"
            onClick={previous}
            ariaLabel={SCHEDULING_STRINGS.ariaLabels.previousDay}
            disabled={position === 0}
          />

          <Typography variant="h4">{currentDate}</Typography>

          <OutlinedIconButton
            icon="RightChevron"
            onClick={next}
            ariaLabel={SCHEDULING_STRINGS.ariaLabels.nextDay}
            disabled={position + 1 >= dateKeys.length}
          />
        </Box>

        {!currentDateBlock || currentDateBlock?.length < 1 ? (
          <Typography textAlign="center" variant="body">
            {SCHEDULING_STRINGS.noAvailableAppointments}
          </Typography>
        ) : (
          currentDateBlock?.map((practitioner) => {
            const appointmentDuration = dayjs(practitioner.availableSlots[0].end).diff(
              dayjs(practitioner.availableSlots[0].start),
              "minute",
            );

            return (
              <Paper key={practitioner.id}>
                <Box display="flex" justifyContent="space-between" alignItems="start" mb={4}>
                  <PractitionerInformation practitioner={practitioner} />

                  <AppointmentDuration duration={appointmentDuration} />
                </Box>

                <Box display="flex" rowGap={2} columnGap={3} flexWrap="wrap">
                  {practitioner.availableSlots.map((slot) => (
                    <Chip
                      variant="inactive"
                      label={dayjs(slot.start).format("h:mm a")}
                      onClick={() =>
                        onSlotSelectionCallback({
                          slot,
                          clinician: practitioner,
                          communicationMethod: "VIDEO",
                        })
                      }
                      key={`${slot.start}-${practitioner.id}`}
                    />
                  ))}
                </Box>
              </Paper>
            );
          })
        )}
      </Box>
    </>
  );
};
