import { AppointmentsScreen } from "./components/AppointmentsScreen";
import { useAppointments } from "./hooks/useAppointments";

export const AppointmentsScreenContainer = () => {
  const {
    appointment,
    appointmentScreen,
    selectedState,
    isFetching,
    isSubmitting,
    timeSlots,
    isFetchingTimeSlotsError,
    defaultPosition,
    setSelectedState,
    handleOnStateSelection,
    handleOnAppointmentSelected,
    handleScheduleOrRescheduleAppointment,
    handleOnBack,
    handleRefreshPage,
  } = useAppointments();

  return (
    <AppointmentsScreen
      isLoading={isFetching}
      isSubmitting={isSubmitting}
      appointmentScreen={appointmentScreen}
      selectedState={selectedState}
      setSelectedState={setSelectedState}
      submitStateCallback={handleOnStateSelection}
      availability={timeSlots}
      isFetchingTimeSlotsError={isFetchingTimeSlotsError}
      onSlotSelectionCallback={handleOnAppointmentSelected}
      appointmentSelection={appointment}
      onConfirmationCallback={handleScheduleOrRescheduleAppointment}
      onBackCallback={handleOnBack}
      defaultPosition={defaultPosition}
      handleRefreshPage={handleRefreshPage}
    />
  );
};
