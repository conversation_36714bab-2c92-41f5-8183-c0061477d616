import { SessionCommunicationMethod } from "@vivantehealth/vivante-core";
import { fireEvent, render, screen } from "@testing-library/react";
import dayjs from "dayjs";
import { vi, describe, test, expect, Mock } from "vitest";

import { appStrings } from "@Assets/app_strings";

import { AppointmentsScreenContainer } from "./AppointmentsScreenContainer";
import { useAppointments } from "./hooks/useAppointments";
import {
  MOCK_APPOINTMENT_TIME_SLOT_RESPONSE,
  MOCK_GI_APPOINTMENT_TIME_SLOT_RESPONSE,
} from "./mocks/appointments.mocks";
import { transformAppointmentTimeSlots, transformGiAppointmentTimeSlots } from "./utils/appointments.utils";

const SCHEDULING_STRINGS = appStrings.features.scheduling;
const CARE_TEAM_STRINGS = appStrings.features.careTeam;
const DATE = dayjs();
const DATE_FORMAT = "dddd, MMMM D";

vi.mock("./hooks/useAppointments", () => ({
  useAppointments: vi.fn(),
}));
/** We're mocking this as it has a dependency on redux store which we are not setup to test with yet */
vi.mock("@Hooks/responsiveStylesHook", () => ({
  useResponsiveStylesHook: () => ({
    formWidth: "450px",
    drawerWidth: "0px",
    contextWidth: "1075px",
  }),
}));

const selectedSlotDate = DATE.startOf("day").add(3, "days");

const selectedSlot = {
  end: selectedSlotDate.add(17, "hours").toISOString(),
  start: selectedSlotDate.add(16, "hours").toISOString(),
};

const appointment = {
  slot: selectedSlot,
  clinician: {
    availableSlots: [
      selectedSlot,
      {
        end: selectedSlotDate.add(17.5, "hours").toISOString(),
        start: selectedSlotDate.add(16.5, "hours").toISOString(),
      },
    ],
    avatarLink: "",
    displayableRole: "Gastroenterology",
    firstName: "John",
    id: "9024890",
    lastName: "Doe",
    requestTrackingId: "12345",
    role: "MD",
    type: "GI",
  },
  communicationMethod: SessionCommunicationMethod.VIDEO,
};

describe("AppointmentsScreenContainer", () => {
  test("Renders state selection screen", () => {
    const mockUseAppointments: ReturnType<typeof useAppointments> = {
      appointment: undefined,
      appointmentScreen: "STATE_SELECTION",
      selectedState: "TN",
      isFetching: true,
      isSubmitting: false,
      isFetchingTimeSlotsError: false,
      timeSlots: {},
      defaultPosition: 0,
      setSelectedState: vi.fn(),
      handleOnStateSelection: vi.fn(),
      handleOnAppointmentSelected: vi.fn(),
      handleScheduleOrRescheduleAppointment: vi.fn(),
      handleOnBack: vi.fn(),
      handleRefreshPage: vi.fn(),
    };

    (useAppointments as Mock).mockReturnValue(mockUseAppointments);

    render(<AppointmentsScreenContainer />);

    expect(screen.getByText(SCHEDULING_STRINGS.stateConfirmationHeader)).toBeInTheDocument();
    expect(screen.getByRole("combobox")).toBeInTheDocument();
    expect(screen.getByText(SCHEDULING_STRINGS.locationNote)).toBeInTheDocument();
    expect(screen.getByText(SCHEDULING_STRINGS.medicalTeamNote)).toBeInTheDocument();

    const backButton = screen.getByRole("button", { name: /back/i });

    expect(backButton).toBeInTheDocument();

    backButton.click();
    expect(mockUseAppointments.handleOnBack).toHaveBeenCalled();

    const nextBtn = screen.getByRole("button", { name: /next/i });

    expect(nextBtn).toBeInTheDocument();
    nextBtn.click();
    expect(mockUseAppointments.handleOnStateSelection).toHaveBeenCalled();
  });

  test("Renders schedule appointment screen with several days of appointments for GI (MD/PA)", () => {
    const mockUseAppointments: ReturnType<typeof useAppointments> = {
      appointment: undefined,
      appointmentScreen: "SCHEDULE_APPOINTMENT",
      selectedState: "TN",
      isFetching: false,
      isSubmitting: false,
      isFetchingTimeSlotsError: false,
      timeSlots: transformGiAppointmentTimeSlots(MOCK_GI_APPOINTMENT_TIME_SLOT_RESPONSE),
      defaultPosition: 0,
      setSelectedState: vi.fn(),
      handleOnStateSelection: vi.fn(),
      handleOnAppointmentSelected: vi.fn(),
      handleScheduleOrRescheduleAppointment: vi.fn(),
      handleOnBack: vi.fn(),
      handleRefreshPage: vi.fn(),
    };

    (useAppointments as Mock).mockReturnValue(mockUseAppointments);

    render(<AppointmentsScreenContainer />);

    expect(screen.getByText(SCHEDULING_STRINGS.chooseAppointmentTime)).toBeInTheDocument();
    expect(screen.getByText(DATE.format(DATE_FORMAT))).toBeInTheDocument();
    expect(screen.getByText("John Doe, MD")).toBeInTheDocument();
    expect(screen.getByText("Jane Doe, PA")).toBeInTheDocument();
    expect(screen.getAllByText(CARE_TEAM_STRINGS.physicianAssistant)).toHaveLength(2);
    // Expect multiple time slots to be present
    expect(screen.getAllByRole("button", { name: "7:30 pm" })).toHaveLength(2);
    expect(screen.getAllByRole("button", { name: "8:00 pm" })).toHaveLength(2);
    expect(screen.getAllByRole("button", { name: "8:30 pm" })).toHaveLength(1);

    //Find the prev/next buttons
    const prevButton = screen.getByRole("button", { name: /previous/i });
    const nextButton = screen.getByRole("button", { name: /next/i });

    expect(prevButton).toBeInTheDocument();
    expect(prevButton).toBeDisabled();
    expect(nextButton).toBeInTheDocument();
    expect(nextButton).toBeEnabled();

    fireEvent.click(nextButton);
    expect(prevButton).toBeEnabled();
    fireEvent.click(nextButton);

    expect(screen.getByText(DATE.add(2, "days").format(DATE_FORMAT))).toBeInTheDocument();
    expect(screen.getByText(SCHEDULING_STRINGS.noAvailableAppointments)).toBeInTheDocument();

    fireEvent.click(nextButton);
    expect(nextButton).toBeDisabled();
    expect(screen.getByText(DATE.add(3, "days").format(DATE_FORMAT))).toBeInTheDocument();
    const timeSlot = screen.getByRole("button", { name: "4:00 pm" });

    expect(timeSlot).toBeInTheDocument();

    fireEvent.click(timeSlot);
    expect(mockUseAppointments.handleOnAppointmentSelected).toHaveBeenCalledWith(appointment);
  });

  test("Renders schedule appointment screen for INT (MD)", () => {
    const mockUseAppointments: ReturnType<typeof useAppointments> = {
      appointment: undefined,
      appointmentScreen: "SCHEDULE_APPOINTMENT",
      selectedState: "TN",
      isFetching: false,
      isSubmitting: false,
      isFetchingTimeSlotsError: false,
      timeSlots: transformAppointmentTimeSlots(MOCK_APPOINTMENT_TIME_SLOT_RESPONSE, true),
      defaultPosition: 0,
      setSelectedState: vi.fn(),
      handleOnStateSelection: vi.fn(),
      handleOnAppointmentSelected: vi.fn(),
      handleScheduleOrRescheduleAppointment: vi.fn(),
      handleOnBack: vi.fn(),
      handleRefreshPage: vi.fn(),
    };

    (useAppointments as Mock).mockReturnValue(mockUseAppointments);

    render(<AppointmentsScreenContainer />);

    expect(screen.getByText(SCHEDULING_STRINGS.chooseAppointmentTime)).toBeInTheDocument();
    expect(screen.getByText(DATE.format(DATE_FORMAT))).toBeInTheDocument();
    expect(screen.getByText("John Doe, MD")).toBeInTheDocument();
    expect(screen.getByText("Jane Doe, MD")).toBeInTheDocument();
    expect(screen.getAllByText(CARE_TEAM_STRINGS.internalMedicine)).toHaveLength(1);
    expect(screen.getAllByText(CARE_TEAM_STRINGS.familyMedicine)).toHaveLength(1);
  });

  test("Renders the confirmation screen", () => {
    const mockUseAppointments: ReturnType<typeof useAppointments> = {
      appointment: appointment,
      appointmentScreen: "CONFIRM_APPOINTMENT",
      selectedState: "TN",
      isFetching: false,
      isSubmitting: false,
      isFetchingTimeSlotsError: false,
      timeSlots: transformGiAppointmentTimeSlots(MOCK_GI_APPOINTMENT_TIME_SLOT_RESPONSE),
      defaultPosition: 0,
      setSelectedState: vi.fn(),
      handleOnStateSelection: vi.fn(),
      handleOnAppointmentSelected: vi.fn(),
      handleScheduleOrRescheduleAppointment: vi.fn(),
      handleOnBack: vi.fn(),
      handleRefreshPage: vi.fn(),
    };

    (useAppointments as Mock).mockReturnValue(mockUseAppointments);

    render(<AppointmentsScreenContainer />);

    expect(screen.getByText(SCHEDULING_STRINGS.confirmation.header)).toBeInTheDocument();
    expect(screen.getByText("John Doe, MD")).toBeInTheDocument();
    expect(screen.getByText(CARE_TEAM_STRINGS.gastroenterology)).toBeInTheDocument();
    expect(screen.getByText("60min")).toBeInTheDocument();
    expect(screen.getByText(`${DATE.add(3, "days").format(`${DATE_FORMAT}, YYYY`)} at 4:00PM`)).toBeInTheDocument();
    expect(screen.getByText("Video visit")).toBeInTheDocument();
    const confirmBtn = screen.getByRole("button", { name: /confirm appointment/i });

    expect(confirmBtn).toBeInTheDocument();
    fireEvent.click(confirmBtn);

    expect(mockUseAppointments.handleScheduleOrRescheduleAppointment).toHaveBeenCalled();
  });
});
