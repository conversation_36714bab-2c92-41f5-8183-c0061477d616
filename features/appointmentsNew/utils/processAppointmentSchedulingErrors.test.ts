import * as Sentry from "@sentry/nextjs";
import { describe, test, expect, vi } from "vitest";

import { appStrings } from "@Assets/app_strings";

import {
  processAppointmentSchedulingErrors,
  AppointmentSchedulingError,
  AppointmentReschedulingErrorDetail,
  AppointmentSchedulingErrorDetail,
} from "./processAppointmentSchedulingErrors";

const APPOINTMENT_SCHEDULING_STRINGS = appStrings.features.scheduling;

vi.mock("@sentry/nextjs", async (importActual) => {
  const actual = await importActual<typeof import("@sentry/nextjs")>();

  return {
    ...actual,
    captureException: vi.fn(),
  };
});

const generateError = (
  status: number,
  detail: AppointmentReschedulingErrorDetail | AppointmentSchedulingErrorDetail,
): AppointmentSchedulingError => {
  return {
    status,
    data: {
      code: status,
      detail,
    },
  };
};

describe("processAppointmentSchedulingErrors", () => {
  test("Should return APPOINTMENT_TIME_UNAVAILABLE for unavailable appointment time", () => {
    const error = generateError(400, "the appointment time is no longer available");

    const result = processAppointmentSchedulingErrors(error);

    expect(result).toEqual({
      type: "APPOINTMENT_TIME_UNAVAILABLE",
      message: APPOINTMENT_SCHEDULING_STRINGS.schedulingError,
    });
  });

  test("Should return RESCHEDULING_WITHIN_3_HOURS for rescheduling within 1 minute", () => {
    const error = generateError(400, "cannot reschedule an appointment less than 1 minute before the start time");

    const result = processAppointmentSchedulingErrors(error);

    expect(result).toEqual({
      type: "RESCHEDULING_WITHIN_1_MINUTE",
      message: APPOINTMENT_SCHEDULING_STRINGS.rescheduleLessThanTimeAllowed,
    });
  });

  test("Should return RESCHEDULING_ERROR for invalid rescheduling status", () => {
    const error = generateError(
      400,
      "can only reschedule appointments with a 'proposed', 'pending', or 'booked' status",
    );

    const result = processAppointmentSchedulingErrors(error);

    expect(result).toEqual({
      type: "RESCHEDULING_ERROR",
      message: APPOINTMENT_SCHEDULING_STRINGS.rescheduleError,
    });
    expect(Sentry.captureException).toHaveBeenCalledWith(error);
  });

  test("Should return RESCHEDULING_ERROR for appointment not found", () => {
    const error = generateError(404, "appointment 123 not found");

    const result = processAppointmentSchedulingErrors(error);

    expect(result).toEqual({
      type: "RESCHEDULING_ERROR",
      message: APPOINTMENT_SCHEDULING_STRINGS.rescheduleError,
    });
    expect(Sentry.captureException).toHaveBeenCalledWith(error);
  });

  test("Should return REQUEST_TRACKING_ID_ERROR for request tracking issues", () => {
    const resultTrackingNotFound = generateError(400, "request tracking 123 not found");
    const resultTrackingAlreadyUsed = generateError(400, "request tracking 123 has already been used");
    const resultTrackingExpired = generateError(400, "request tracking 123 is expired");

    expect(processAppointmentSchedulingErrors(resultTrackingNotFound)).toEqual({
      type: "REQUEST_TRACKING_ID_ERROR",
      message: APPOINTMENT_SCHEDULING_STRINGS.schedulingError,
    });
    expect(Sentry.captureException).toHaveBeenCalledWith(resultTrackingNotFound);

    expect(processAppointmentSchedulingErrors(resultTrackingAlreadyUsed)).toEqual({
      type: "REQUEST_TRACKING_ID_ERROR",
      message: APPOINTMENT_SCHEDULING_STRINGS.schedulingError,
    });
    expect(Sentry.captureException).toHaveBeenCalledWith(resultTrackingAlreadyUsed);

    expect(processAppointmentSchedulingErrors(resultTrackingExpired)).toEqual({
      type: "REQUEST_TRACKING_ID_ERROR",
      message: APPOINTMENT_SCHEDULING_STRINGS.schedulingError,
    });
    expect(Sentry.captureException).toHaveBeenCalledWith(resultTrackingExpired);
  });

  test("Should return UNKNOWN_ERROR for unknown errors", () => {
    const error = {
      status: 500,
      data: {
        code: 500,
        detail: "unexpected error occurred",
      },
    };

    const result = processAppointmentSchedulingErrors(error);

    expect(result).toEqual({
      type: "UNKNOWN_ERROR",
      message: APPOINTMENT_SCHEDULING_STRINGS.schedulingError,
    });
    expect(Sentry.captureException).toHaveBeenCalledWith(error);
  });

  test("Should return UNKNOWN_ERROR for non-AppointmentSchedulingError errors", () => {
    const error = new Error("Some random error");

    const result = processAppointmentSchedulingErrors(error);

    expect(result).toEqual({
      type: "UNKNOWN_ERROR",
      message: APPOINTMENT_SCHEDULING_STRINGS.schedulingError,
    });
    expect(Sentry.captureException).toHaveBeenCalledWith(error);
  });
});
