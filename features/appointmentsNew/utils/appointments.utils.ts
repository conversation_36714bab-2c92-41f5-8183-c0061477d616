import { Appointment } from "@vivantehealth/vivante-core";
import { DateTemplate } from "@vivantehealth/vivante-core/dist/domain/value-objects/date/date.util";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";

import {
  AppointmentListResponse,
  AppointmentTimeSlots,
  AppointmentTimeSlotsResponse,
  Clinician,
  GiAppointmentTimeSlotsResponse,
  TimeSlot,
} from "../types/appointments.types";

const CARE_TEAM_STRINGS = appStrings.features.careTeam;

const PRACTITIONER_ROLE_DISPLAYABLE = {
  CG: CARE_TEAM_STRINGS.careGuide,
  PA: CARE_TEAM_STRINGS.physicianAssistant,
  MD: CARE_TEAM_STRINGS.medicalDoctor,
  HC: CARE_TEAM_STRINGS.healthCoach,
  RD: CARE_TEAM_STRINGS.dietitian,
};

const isDisplayableRole = (role: string): role is keyof typeof PRACTITIONER_ROLE_DISPLAYABLE => {
  return Object.keys(PRACTITIONER_ROLE_DISPLAYABLE).includes(role);
};

const getDisplayableRole = (clinician: Clinician) => {
  const clinicianRole = clinician.clinician_role?.code;
  const displayableRole = isDisplayableRole(clinicianRole) ? PRACTITIONER_ROLE_DISPLAYABLE[clinicianRole] : "";

  if (clinician.specialty?.code === "GI") return CARE_TEAM_STRINGS.gastroenterology;

  if (clinician.specialty?.code === "INT" && clinician.focus) {
    return clinician.focus.code === "IM" ? CARE_TEAM_STRINGS.internalMedicine : CARE_TEAM_STRINGS.familyMedicine;
  }

  return displayableRole;
};

const addClinicianTimeSlots = (
  timeSlots: AppointmentTimeSlots,
  slots: TimeSlot[] | null,
  clinician: Clinician,
  requestTrackingId: string,
) => {
  slots?.forEach(({ start_time, end_time }) => {
    const slotDay = dayjs(start_time).format("YYYY-MM-DD");
    const newClinicianDate = {
      id: clinician.id,
      type: clinician?.specialty?.code ?? "",
      firstName: clinician.first_name,
      lastName: clinician.last_name,
      avatarLink: clinician.avatar_link,
      role: clinician.clinician_role?.code ?? "",
      displayableRole: getDisplayableRole(clinician),
      requestTrackingId,
      availableSlots: [{ start: start_time, end: end_time }],
    };

    if (!(slotDay in timeSlots)) {
      timeSlots[slotDay] = [newClinicianDate];
      return;
    }

    const clinicianExistsForDay = timeSlots[slotDay].find(({ id }) => id === clinician.id);

    if (clinicianExistsForDay) {
      clinicianExistsForDay.availableSlots.push({
        start: start_time,
        end: end_time,
      });
      return;
    }

    timeSlots[slotDay].push(newClinicianDate);
  });
};

const getGiTimeSlotsByDay = (appointmentTimeSlots: GiAppointmentTimeSlotsResponse) => {
  return appointmentTimeSlots.requests.reduce<AppointmentTimeSlots>(
    (timeSlots, { available_slots, request_tracking_id, role_code, specialty }) => {
      available_slots.forEach(({ clinician, slots }) => {
        addClinicianTimeSlots(
          timeSlots,
          slots,
          {
            ...clinician,
            specialty: {
              code: specialty ?? clinician?.specialty?.code ?? "",
              description: clinician?.specialty?.description ?? "",
            },
            clinician_role: {
              code: role_code ?? clinician?.clinician_role?.code ?? "",
              description: clinician?.clinician_role?.description ?? "",
            },
            focus: {
              code: clinician?.focus?.code ?? "",
              description: clinician?.focus?.description ?? "",
            },
          },
          request_tracking_id,
        );
      });

      return timeSlots;
    },
    {},
  );
};

const getTimeSlotsByDay = (appointmentTimeSlots: AppointmentTimeSlotsResponse) => {
  const requestTrackingId = appointmentTimeSlots.request_tracking_id;

  return appointmentTimeSlots.available_slots.reduce<AppointmentTimeSlots>((timeSlots, { clinician, slots }) => {
    addClinicianTimeSlots(timeSlots, slots, clinician, requestTrackingId);

    return timeSlots;
  }, {});
};

const insertMissingDays = (timeSlots: AppointmentTimeSlots) => {
  const timeSlotsWithMissingDays = { ...timeSlots };
  const timeSlotKeys = Object.keys(timeSlotsWithMissingDays).sort((a, b) => dayjs(a).diff(dayjs(b)));
  let currentDateKey = dayjs().format("YYYY-MM-DD");

  while (currentDateKey !== timeSlotKeys[timeSlotKeys.length - 1]) {
    const slotDate = dayjs(currentDateKey);
    const slotDay: DateTemplate = `${slotDate.year()}-${slotDate.format("MM")}-${slotDate.format("DD")}`;

    if (!(slotDay in timeSlotsWithMissingDays)) {
      timeSlotsWithMissingDays[slotDay] = [];
    }

    currentDateKey = slotDate.add(1, "day").format("YYYY-MM-DD");
  }

  return timeSlotsWithMissingDays;
};

const timeSlotsSortedByDate = (timeSlots: AppointmentTimeSlots) => {
  const sortedKeys = Object.keys(timeSlots).sort((a, b) => dayjs(a).diff(dayjs(b)));
  const sortedTimeSlots = sortedKeys.reduce<AppointmentTimeSlots>((timeSlotsAcc, key) => {
    const slotDate = dayjs(key);
    const slotDay: DateTemplate = `${slotDate.year()}-${slotDate.format("MM")}-${slotDate.format("DD")}`;

    timeSlotsAcc[slotDay] = timeSlots[slotDay];

    return timeSlotsAcc;
  }, {});

  return sortedTimeSlots;
};

export const transformGiAppointmentTimeSlots = (appointmentTimeSlots: GiAppointmentTimeSlotsResponse) => {
  if (appointmentTimeSlots.requests.length === 0) {
    return {};
  }

  const timeSlots = getGiTimeSlotsByDay(appointmentTimeSlots);
  const timeSlotsWithMissingDays = insertMissingDays(timeSlots);
  const timeSlotsSorted = timeSlotsSortedByDate(timeSlotsWithMissingDays);

  return timeSlotsSorted;
};

export const transformAppointmentTimeSlots = (
  appointmentTimeSlots: AppointmentTimeSlotsResponse,
  fillWithMissingDays: boolean,
): AppointmentTimeSlots => {
  if (appointmentTimeSlots.available_slots.length === 0) {
    return {};
  }

  const timeSlots = getTimeSlotsByDay(appointmentTimeSlots);
  const timeSlotsSorted = fillWithMissingDays
    ? timeSlotsSortedByDate(insertMissingDays(timeSlots))
    : timeSlotsSortedByDate(timeSlots);

  return timeSlotsSorted;
};

export const transformAppointmentList = (appointmentList: AppointmentListResponse): Appointment[] => {
  return appointmentList.appointments.map((appointment) => ({
    id: appointment.id,
    slot: {
      start: new Date(appointment.start),
      end: new Date(appointment.end),
    },
    practitioner: {
      id:
        appointment.clinician.identifiers?.find(({ system }) => system === "GIThrive")?.identifier ??
        appointment.clinician.id,
      type: appointment.clinician.specialty?.code ?? "",
      firstName: appointment.clinician.first_name,
      lastName: appointment.clinician.last_name,
      avatarLink: appointment.clinician.avatar_link,
      role: appointment.clinician.clinician_role?.code ?? "",
      displayableRole: getDisplayableRole(appointment.clinician),
    },
    communicationMethod: appointment.communication_method === "phone" ? "PHONE" : "VIDEO",
    canEditUntil: dayjs(appointment.start).subtract(1, "minute").toDate(),
    meetingLink: appointment.meeting_link,
  }));
};
