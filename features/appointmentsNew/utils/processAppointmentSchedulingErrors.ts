import * as Sentry from "@sentry/nextjs";

import { appStrings } from "@Assets/app_strings";

const APPOINTMENT_SCHEDULING_STRINGS = appStrings.features.scheduling;

export type AppointmentReschedulingErrorDetail =
  | "cannot reschedule an appointment less than 1 minute before the start time"
  | `appointment ${string} not found`
  | `can only reschedule appointments with a 'proposed', 'pending', or 'booked' status`
  | "cannot reschedule an appointment with a clinician of a different role or specialty"
  | "the appointment time is no longer available";

export type AppointmentSchedulingErrorDetail =
  | `request tracking ${string} not found`
  | `request tracking ${string} has already been used`
  | `request tracking ${string} is expired`
  | `no clinicians found`
  | "the appointment time is no longer available";

type AppointmentCancellationErrorDetail = `appointment ${string} not found`;

export type AppointmentSchedulingError = {
  status: number;
  data: {
    code: number;
    detail: AppointmentSchedulingErrorDetail | AppointmentReschedulingErrorDetail | AppointmentCancellationErrorDetail;
  };
};

export const isAppointmentSchedulingError = (error: unknown): error is AppointmentSchedulingError => {
  return (
    error != null &&
    typeof error === "object" &&
    "status" in error &&
    "data" in error &&
    error.data != null &&
    typeof error.data === "object" &&
    "detail" in error.data
  );
};

type ProcessAppointmentSchedulingErrorResponse =
  | {
      type: "APPOINTMENT_TIME_UNAVAILABLE" | "REQUEST_TRACKING_ID_ERROR" | "UNKNOWN_ERROR";
      message: typeof APPOINTMENT_SCHEDULING_STRINGS.schedulingError;
    }
  | { type: "RESCHEDULING_ERROR"; message: typeof APPOINTMENT_SCHEDULING_STRINGS.rescheduleError }
  | {
      type: "RESCHEDULING_WITHIN_1_MINUTE";
      message: typeof APPOINTMENT_SCHEDULING_STRINGS.rescheduleLessThanTimeAllowed;
    };

export const processAppointmentSchedulingErrors = (error: unknown): ProcessAppointmentSchedulingErrorResponse => {
  if (isAppointmentSchedulingError(error)) {
    const { detail } = error.data;
    const detailSplitBySpace = detail.split(" ");

    if (detail === "the appointment time is no longer available") {
      return { type: "APPOINTMENT_TIME_UNAVAILABLE", message: APPOINTMENT_SCHEDULING_STRINGS.schedulingError };
    }

    if (detail === "cannot reschedule an appointment less than 1 minute before the start time") {
      return {
        type: "RESCHEDULING_WITHIN_1_MINUTE",
        message: APPOINTMENT_SCHEDULING_STRINGS.rescheduleLessThanTimeAllowed,
      };
    }

    if (
      detail === `can only reschedule appointments with a 'proposed', 'pending', or 'booked' status` ||
      (detailSplitBySpace[0] === "appointment" && `${detailSplitBySpace[2]} ${detailSplitBySpace[3]}` === "not found")
    ) {
      Sentry.captureException(error);
      return { type: "RESCHEDULING_ERROR", message: APPOINTMENT_SCHEDULING_STRINGS.rescheduleError };
    }

    if (`${detailSplitBySpace[0]} ${detailSplitBySpace[1]}` === "request tracking") {
      Sentry.captureException(error);
      return { type: "REQUEST_TRACKING_ID_ERROR", message: APPOINTMENT_SCHEDULING_STRINGS.schedulingError };
    }
  }

  Sentry.captureException(error);
  return { type: "UNKNOWN_ERROR", message: APPOINTMENT_SCHEDULING_STRINGS.schedulingError };
};
