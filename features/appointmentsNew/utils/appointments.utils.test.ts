import dayjs from "dayjs";
import { describe, test, expect } from "vitest";

import { transformAppointmentList, transformGiAppointmentTimeSlots } from "./appointments.utils";
import { transformAppointmentTimeSlots } from "./appointments.utils";
import {
  createMockTimeSlot,
  MOCK_APPOINTMENT_TIME_SLOT_RESPONSE,
  MOCK_GI_APPOINTMENT_TIME_SLOT_RESPONSE,
} from "../mocks/appointments.mocks";
import { AppointmentListResponse, GiAppointmentTimeSlotsResponse } from "../types/appointments.types";
import { AppointmentTimeSlotsResponse } from "../types/appointments.types";

const convertReturnType = (timeSlot: { start_time: string; end_time: string }) => {
  return {
    start: timeSlot.start_time,
    end: timeSlot.end_time,
  };
};

describe("appointments.utils", () => {
  describe("transformGiAppointmentTimeSlots", () => {
    test("Should transform appointment time slots into the expected structure", () => {
      const initialDate = dayjs();
      const result = transformGiAppointmentTimeSlots(MOCK_GI_APPOINTMENT_TIME_SLOT_RESPONSE);

      expect(result).toEqual({
        [initialDate.format("YYYY-MM-DD")]: [
          {
            availableSlots: [
              convertReturnType(createMockTimeSlot(0, 19, 30)),
              convertReturnType(createMockTimeSlot(0, 20, 0)),
              convertReturnType(createMockTimeSlot(0, 20, 30)),
              convertReturnType(createMockTimeSlot(0, 21, 0)),
              convertReturnType(createMockTimeSlot(0, 21, 30)),
              convertReturnType(createMockTimeSlot(0, 22, 0)),
              convertReturnType(createMockTimeSlot(0, 22, 30)),
              convertReturnType(createMockTimeSlot(0, 23, 0)),
              convertReturnType(createMockTimeSlot(0, 23, 30)),
            ],
            avatarLink: "",
            displayableRole: "Gastroenterology",
            firstName: "John",
            id: "9024890",
            lastName: "Doe",
            requestTrackingId: "12345",
            role: "MD",
            type: "GI",
          },
          {
            availableSlots: [
              convertReturnType(createMockTimeSlot(0, 19, 30)),
              convertReturnType(createMockTimeSlot(0, 20, 0)),
            ],
            avatarLink: "",
            displayableRole: "Gastroenterology",
            firstName: "Jane",
            id: "348590834",
            lastName: "Doe",
            requestTrackingId: "67890",
            role: "PA",
            type: "GI",
          },
        ],
        [initialDate.add(1, "day").format("YYYY-MM-DD")]: [
          {
            availableSlots: [
              convertReturnType(createMockTimeSlot(1, 0, 0)),
              convertReturnType(createMockTimeSlot(1, 0, 30)),
              convertReturnType(createMockTimeSlot(1, 1, 0)),
              convertReturnType(createMockTimeSlot(1, 1, 30)),
              convertReturnType(createMockTimeSlot(1, 2, 0)),
              convertReturnType(createMockTimeSlot(1, 2, 30)),
              convertReturnType(createMockTimeSlot(1, 3, 0)),
            ],
            avatarLink: "",
            displayableRole: "Gastroenterology",
            firstName: "John",
            id: "9024890",
            lastName: "Doe",
            requestTrackingId: "12345",
            role: "MD",
            type: "GI",
          },
        ],
        [initialDate.add(2, "day").format("YYYY-MM-DD")]: [],
        [initialDate.add(3, "day").format("YYYY-MM-DD")]: [
          {
            availableSlots: [
              convertReturnType(createMockTimeSlot(3, 16, 0)),
              convertReturnType(createMockTimeSlot(3, 16, 30)),
            ],
            avatarLink: "",
            displayableRole: "Gastroenterology",
            firstName: "John",
            id: "9024890",
            lastName: "Doe",
            requestTrackingId: "12345",
            role: "MD",
            type: "GI",
          },
        ],
      });
    });

    test("Should handle empty available slots", () => {
      const mockAppointmentTimeSlots: GiAppointmentTimeSlotsResponse = {
        requests: [],
      };

      const result = transformGiAppointmentTimeSlots(mockAppointmentTimeSlots);

      expect(result).toEqual({});
    });
  });

  describe("transformAppointmentTimeSlots", () => {
    test("Should return an empty object when given an empty response", () => {
      const mockResponse: AppointmentTimeSlotsResponse = {
        request_tracking_id: "12345",
        available_slots: [],
      };
      const result = transformAppointmentTimeSlots(mockResponse, true);

      expect(result).toEqual({});
    });

    test("Should transform appointment time slots into the expected structure WITH added empty days", () => {
      const initialDate = dayjs();
      const result = transformAppointmentTimeSlots(MOCK_APPOINTMENT_TIME_SLOT_RESPONSE, true);

      expect(result).toEqual({
        [initialDate.format("YYYY-MM-DD")]: [
          {
            availableSlots: [
              convertReturnType(createMockTimeSlot(0, 19, 30)),
              convertReturnType(createMockTimeSlot(0, 20, 0)),
              convertReturnType(createMockTimeSlot(0, 20, 30)),
            ],
            avatarLink: "",
            displayableRole: "Internal Medicine",
            firstName: "John",
            id: "9024890",
            lastName: "Doe",
            requestTrackingId: "12345",
            role: "MD",
            type: "INT",
          },
          {
            availableSlots: [
              convertReturnType(createMockTimeSlot(0, 19, 30)),
              convertReturnType(createMockTimeSlot(0, 20, 0)),
            ],
            avatarLink: "",
            displayableRole: "Family Medicine",
            firstName: "Jane",
            id: "348590834",
            lastName: "Doe",
            requestTrackingId: "12345",
            role: "MD",
            type: "INT",
          },
        ],
        [initialDate.add(1, "day").format("YYYY-MM-DD")]: [],
        [initialDate.add(2, "day").format("YYYY-MM-DD")]: [
          {
            availableSlots: [
              convertReturnType(createMockTimeSlot(2, 21, 0)),
              convertReturnType(createMockTimeSlot(2, 21, 30)),
            ],
            avatarLink: "",
            displayableRole: "Internal Medicine",
            firstName: "John",
            id: "9024890",
            lastName: "Doe",
            requestTrackingId: "12345",
            role: "MD",
            type: "INT",
          },
        ],
        [initialDate.add(3, "day").format("YYYY-MM-DD")]: [
          {
            availableSlots: [convertReturnType(createMockTimeSlot(3, 16, 0))],
            avatarLink: "",
            displayableRole: "Family Medicine",
            firstName: "Jane",
            id: "348590834",
            lastName: "Doe",
            requestTrackingId: "12345",
            role: "MD",
            type: "INT",
          },
        ],
      });
    });

    test("Should transform appointment time slots into the expected structure WITHOUT added empty days", () => {
      const initialDate = dayjs();
      const result = transformAppointmentTimeSlots(MOCK_APPOINTMENT_TIME_SLOT_RESPONSE, false);

      expect(result).toEqual({
        [initialDate.format("YYYY-MM-DD")]: [
          {
            availableSlots: [
              convertReturnType(createMockTimeSlot(0, 19, 30)),
              convertReturnType(createMockTimeSlot(0, 20, 0)),
              convertReturnType(createMockTimeSlot(0, 20, 30)),
            ],
            avatarLink: "",
            displayableRole: "Internal Medicine",
            firstName: "John",
            id: "9024890",
            lastName: "Doe",
            requestTrackingId: "12345",
            role: "MD",
            type: "INT",
          },
          {
            availableSlots: [
              convertReturnType(createMockTimeSlot(0, 19, 30)),
              convertReturnType(createMockTimeSlot(0, 20, 0)),
            ],
            avatarLink: "",
            displayableRole: "Family Medicine",
            firstName: "Jane",
            id: "348590834",
            lastName: "Doe",
            requestTrackingId: "12345",
            role: "MD",
            type: "INT",
          },
        ],
        [initialDate.add(2, "day").format("YYYY-MM-DD")]: [
          {
            availableSlots: [
              convertReturnType(createMockTimeSlot(2, 21, 0)),
              convertReturnType(createMockTimeSlot(2, 21, 30)),
            ],
            avatarLink: "",
            displayableRole: "Internal Medicine",
            firstName: "John",
            id: "9024890",
            lastName: "Doe",
            requestTrackingId: "12345",
            role: "MD",
            type: "INT",
          },
        ],
        [initialDate.add(3, "day").format("YYYY-MM-DD")]: [
          {
            availableSlots: [convertReturnType(createMockTimeSlot(3, 16, 0))],
            avatarLink: "",
            displayableRole: "Family Medicine",
            firstName: "Jane",
            id: "348590834",
            lastName: "Doe",
            requestTrackingId: "12345",
            role: "MD",
            type: "INT",
          },
        ],
      });
    });

    describe("transformAppointmentList", () => {
      test("Should transform appointment list into the expected structure", () => {
        const mockAppointmentList: AppointmentListResponse = {
          appointments: [
            {
              clinician: {
                avatar_link: "https://main-api.dev.vivantehealth.com/user/68910f17-9c1d-4b6c-ae5d-753e201a8d92/avatar/",
                clinician_role: {
                  code: "RD",
                  description: "Registered Dietitian",
                },
                enabled: true,
                first_name: "Training",
                id: "167d9655-e770-4d36-aefc-ffe806db88b0",
                identifiers: [
                  {
                    identifier: "2820f534-cbf5-4d6a-a143-5e5149da415d",
                    system: "Iterable",
                  },
                  {
                    identifier: "7611c1d373a54123b83c0380b6ef36c1",
                    system: "Canvas",
                  },
                  {
                    identifier: "68910f17-9c1d-4b6c-ae5d-753e201a8d92",
                    system: "GIThrive",
                  },
                ],
                last_name: "AnotherDietitianDev",
              },
              communication_method: "phone",
              end: "2025-06-02T18:00:00Z",
              id: "08a764cc-f13d-4544-a8c6-7488dda91cbc",
              request_tracking_id: "e0655aa6-d0d0-47e2-a2f4-e58f68ec0986",
              start: "2025-06-02T17:30:00Z",
              status: "proposed",
            },
          ],
        };

        const result = transformAppointmentList(mockAppointmentList);

        expect(result).toEqual([
          {
            id: "08a764cc-f13d-4544-a8c6-7488dda91cbc",
            slot: {
              start: new Date("2025-06-02T17:30:00Z"),
              end: new Date("2025-06-02T18:00:00Z"),
            },
            practitioner: {
              id: "68910f17-9c1d-4b6c-ae5d-753e201a8d92",
              type: "",
              firstName: "Training",
              lastName: "AnotherDietitianDev",
              avatarLink: "https://main-api.dev.vivantehealth.com/user/68910f17-9c1d-4b6c-ae5d-753e201a8d92/avatar/",
              role: "RD",
              displayableRole: "Dietitian",
            },
            communicationMethod: "PHONE",
            canEditUntil: dayjs("2025-06-02T17:30:00Z").subtract(1, "minute").toDate(),
            meetingLink: undefined,
          },
        ]);
      });

      test("Should handle empty appointment list", () => {
        const mockAppointmentList = { appointments: [] };
        const result = transformAppointmentList(mockAppointmentList);

        expect(result).toEqual([]);
      });
    });
  });
});
