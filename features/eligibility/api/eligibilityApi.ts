import { EligibilityProcessState, FormData, JsonSchemaForm } from "@vivantehealth/vivante-core";

import { bffApi } from "@Api/bffApi";
import { UserAttributes } from "@Features/authentication/api/registrationApi";

export const eligibilityApi = bffApi.injectEndpoints({
  endpoints: (builder) => ({
    /** Send filled data in eligibility form to activate the SSO user */
    activateUser: builder.mutation<UserActivationResponse, UserActivationRequest>({
      query: (attributes) => {
        const body = JSON.stringify({
          data: {
            type: "subscription_activation",
            attributes,
          },
        });

        return {
          url: `v1/subscription/activation`,
          method: "POST",
          body,
        };
      },
    }),
    activatePassiveV2User: builder.mutation<PassiveV2UserActivationResponse, PassiveV2UserActivationRequest>({
      query: (data) => {
        return {
          url: `v1/legacy/eligibility_process`,
          method: "POST",
          body: { data },
        };
      },
    }),
  }),
});

export const { useActivateUserMutation, useActivatePassiveV2UserMutation } = eligibilityApi;

type PassiveV2UserActivationRequest = {
  passive_v2_eligibility_check: boolean;
  fields: FormData;
};

type PassiveV2UserActivationResponse = {
  data: {
    attributes: EligibilityProcessState & { submissionFormSchema: JsonSchemaForm; fields: FormData };
    id: string;
    type: string;
  };
};
/** Required fields to activate the registration of a user */
type UserActivationRequest = Partial<
  UserAttributes & {
    date_of_birth: string;
    recovery_email: string;
  }
>;
/** Response from User Action endpoint after successful registration of a user */
type UserActivationResponse = {
  data: {
    type: string;
    id: string;
    attributes: {
      created_at: string;
      license_type: string;
      payer_id: string;
      registration_code: string;
      status: string;
      updated_at: string;
    };
    relationships: Record<string, { data: { id: string; type: string } }>;
  };
};

export type EligibilityApiErrorCode =
  | GenericEligibilityApiErrorCodes
  | EligibilityValidationErrorCodes
  | UserFeedbackErrorCodes;

export type EligibilityValidationErrorCodes = "INVALID_ATTRIBUTE" | "MISSING_ATTRIBUTE";

export type GenericEligibilityApiErrorCodes =
  | "INVALID_REQUEST"
  | "MISSING_ATTRIBUTES"
  | "MISSING_MEMBER_ID"
  | "MISSING_CONFIGURATION";

export type UserFeedbackErrorCodes =
  | "MEMBER_NOT_FOUND"
  | "ELIGIBILITY_NOT_FOUND"
  | "EXPIRED_ELIGIBILITY"
  | "PRE_EXISTING_SUBSCRIPTION";

export type EligibilityApiError<T = EligibilityApiErrorCode> = {
  code: T;
  detail: string;
  status: string;
  title: string;
  source?: {
    pointer: string;
  };
};
