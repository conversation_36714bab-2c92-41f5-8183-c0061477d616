import type { Meta, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import defaultFormMockData from "./data/eligibilityDefaultFormMockData.json";
import walmartFormMockData from "./data/eligibilityWalmartFormMockData.json";
import { EligibilityScreen } from "./EligibilityScreen";

const meta: Meta<typeof EligibilityScreen> = {
  decorators: cylinderThemeDecorator,
  title: "@Features/eligibility/Eligibility",
  component: EligibilityScreen,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof EligibilityScreen>;

const defaultArgs = {
  onSubmit: (data: unknown) => alert(JSON.stringify(data)),
  logoutCallback: () => alert("logout button was clicked"),
};

export const DefaultForm: Story = {
  args: {
    ...defaultArgs,
    formData: JSON.parse(JSON.stringify(defaultFormMockData)).jsonSchemaForm,
  },
};

export const WalmartForm: Story = {
  args: {
    ...defaultArgs,
    formData: JSON.parse(JSON.stringify(walmartFormMockData)).jsonSchemaForm,
    isWalmartForm: true,
  },
};
