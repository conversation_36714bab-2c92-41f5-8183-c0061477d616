import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";

import { appStrings } from "@Assets/app_strings";
import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import { EligibilityResultsScreen } from "./EligibilityResultsScreen";

const meta: Meta<typeof EligibilityResultsScreen> = {
  decorators: cylinderThemeDecorator,
  title: "@Features/eligibility/EligibilityResults",
  component: EligibilityResultsScreen,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof EligibilityResultsScreen>;

export const HelpScreen: Story = {
  args: {
    header: appStrings.features.eligibility.helpScreenResultHeader,
    subheader: appStrings.features.eligibility.helpScreenResultSubheader,
    showPrimaryBtn: true,
    primaryBtnText: appStrings.buttonText.logOut,
    primaryBtnCallback: () => alert("log out button clicked"),
  },
};

export const TwoButtonsScreen: Story = {
  args: {
    header: appStrings.features.eligibility.helpScreenResultHeader,
    subheader: appStrings.features.eligibility.helpScreenResultSubheader,
    showPrimaryBtn: true,
    primaryBtnText: appStrings.buttonText.logOut,
    primaryBtnCallback: () => alert("log out button clicked"),
    showSecondaryBtn: true,
    secondaryBtnCallback: () => alert("try again button was clicked"),
  },
};
