/* eslint-disable no-alert */
import type { Meta, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import helpFormData from "./data/eligibilityHelpFormData.json";
import { EligibilityHelpScreen } from "./EligibilityHelpScreen";

const meta: Meta<typeof EligibilityHelpScreen> = {
  decorators: cylinderThemeDecorator,
  title: "@Features/eligibility/Eligibilityhelp",
  component: EligibilityHelpScreen,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof EligibilityHelpScreen>;

export const Primary: Story = {
  args: {
    onSubmit: (data) => alert(JSON.stringify(data)),
    backClickCallback: () => alert("back button was clicked"),
    formData: JSON.parse(JSON.stringify(helpFormData)).jsonSchemaForm,
  },
};
