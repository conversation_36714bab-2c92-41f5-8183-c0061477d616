import { JsonSchemaForm } from "@vivantehealth/vivante-core";
import { Box, Button, Grid, Paper } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { AuthFormContainer } from "@Components/AuthFormContainer/AuthFormContainer";
import { Form } from "@Components/form/Form";
import { FormFields } from "@Components/form/FormFields";
import { formatInputData } from "@Components/form/utils/formatInputData";
import { SmsUsageDisclaimer } from "@Components/SmsUsageDisclaimer/SmsUsageDisclaimer";
import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";
import { Routes } from "@Types";
import { isOfType } from "@Utils/isOfType";

const BUTTON_STRINGS = appStrings.buttonText;

type EligibilityScreenProps = Readonly<{
  onSubmit: (data: Record<string, string | number>, event?: React.BaseSyntheticEvent) => void;
  logoutCallback: () => void;
  previouslyInputData?: Record<string, string>;
  formData: JsonSchemaForm | undefined;
  isWalmartForm: boolean;
  serverValidationErrors?: Record<string, string>;
}>;

export const EligibilityScreen = ({
  onSubmit,
  logoutCallback,
  formData,
  previouslyInputData,
  isWalmartForm,
  serverValidationErrors,
}: EligibilityScreenProps) => {
  const { formWidth } = useResponsiveStylesHook();
  const { inputProperties, defaultValues, requiredFields } = formatInputData(formData, previouslyInputData);

  return (
    <AuthFormContainer>
      <Paper sx={{ width: formWidth }}>
        <Form
          defaultValues={defaultValues}
          onSubmit={(data) => {
            /**
             * Eligibility form data is dynamically generated on the backend and differs between clients thus we
             * just ensure its an object within our type guard
             */
            if (isOfType<Record<string, string | number>>(data, [])) {
              onSubmit(data);
            }
          }}
          formHeader={formData?.title ?? ""}
          formSubheader={formData?.description ?? ""}
          serverValidationErrors={serverValidationErrors}
        >
          <Grid item my={5}>
            <FormFields inputProperties={inputProperties} requiredFields={requiredFields} />
          </Grid>

          <Box my={5} display="flex" flexDirection="row" gap={3}>
            <Button variant="secondary" aria-label={BUTTON_STRINGS.logOut} onClick={logoutCallback} fullWidth>
              {BUTTON_STRINGS.logOut}
            </Button>

            <Button type="submit" variant="primary" aria-label={BUTTON_STRINGS.next} fullWidth>
              {BUTTON_STRINGS.next}
            </Button>
          </Box>

          <SmsUsageDisclaimer />

          {isWalmartForm && (
            <Button
              variant="tertiary"
              href={Routes.ELIGIBILITY_HELP}
              aria-label={BUTTON_STRINGS.needHelp}
              sx={{ mt: 5 }}
            >
              {BUTTON_STRINGS.needHelp}
            </Button>
          )}
        </Form>
      </Paper>
    </AuthFormContainer>
  );
};
