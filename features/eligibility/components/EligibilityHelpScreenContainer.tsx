import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { getReportTicketLoadState, submitReportTicket } from "@Features/reportTicket/store/reportTicketSlice";
import { useSignOutHook } from "@Hooks/signOutHook";
import { Routes } from "@Types";

import helpFormData from "./data/eligibilityHelpFormData.json";
import { EligibilityHelpScreen } from "./EligibilityHelpScreen";
import { EligibilityResultsScreen } from "./EligibilityResultsScreen";

const ELIGIBILITY_STRINGS = appStrings.features.eligibility;

export const EligibilityHelpScreenContainer = () => {
  const [showResults, setShowResults] = useState(false);
  const { signOutOfApp } = useSignOutHook();
  const router = useRouter();
  const dispatch = useDispatch();
  const submissionLoadState = useSelector(getReportTicketLoadState);
  const formData = JSON.parse(JSON.stringify(helpFormData)).jsonSchemaForm;

  const onBackClick = () => router.push(Routes.ELIGIBILITY);

  const onLogout = () => {
    signOutOfApp();
  };

  const onSubmit = (data: Record<string, string | number>) => {
    const reportDetailMessage = Object.keys(data)
      .map((key) => `${key}: ${data[key] || ""}`)
      .join("\n");

    dispatch(
      submitReportTicket({
        ticket: {
          subject: ELIGIBILITY_STRINGS.submitTicketSubject,
          message: ELIGIBILITY_STRINGS.submitTicketMessage(reportDetailMessage),
        },
      }),
    );
  };

  useEffect(() => {
    // to do: handle submissionLoadState failure
    if (submissionLoadState === "loaded" || submissionLoadState === "failure") {
      setShowResults(true);
    }
  }, [submissionLoadState]);

  if (submissionLoadState === "loading") {
    return <LoadingSpinner open overlayDrawer overlayHeader />;
  }

  return showResults ? (
    <EligibilityResultsScreen
      header={ELIGIBILITY_STRINGS.helpScreenResultHeader}
      subheader={ELIGIBILITY_STRINGS.helpScreenResultSubheader}
      showPrimaryBtn
      primaryBtnText={appStrings.buttonText.logOut}
      primaryBtnCallback={onLogout}
    />
  ) : (
    <EligibilityHelpScreen onSubmit={onSubmit} formData={formData} backClickCallback={onBackClick} />
  );
};
