{"jsonSchemaForm": {"$schema": "http://json-schema.org/draft-07/schema#", "description": "Walmart has provided this benefit at no cost to you. Simply provide the information below to verify your eligibility.", "properties": {"birth_date": {"format": "date", "formatMaximum": "2022-01-01", "formatMinimum": "1870-01-01", "meta": {"errors": {"format": "Enter a valid date format", "formatMaximum": "Enter a valid date that is not in the future", "formatMinimum": "Enter a valid date after Jan 01 1870"}}, "order": 5, "title": "Birthday", "type": "string"}, "country_code": {"default": "+1", "meta": {"hidden": true}, "readOnly": true, "type": "string"}, "first_name": {"maxLength": 35, "meta": {"errors": {"maxLength": "Enter a name shorter than 35 characters", "minLength": "Enter a name larger than 1 character", "pattern": "Enter a name containing letters, numbers, spaces, and any of the following special characters: ,;'-.?!&/\\#+=()"}}, "minLength": 1, "order": 2, "pattern": "^[a-zA-Z0-9\\s!&,()+'\\-./;?=#\\\\]{1,35}$", "title": "First name", "type": "string"}, "last_name": {"maxLength": 35, "meta": {"errors": {"maxLength": "Enter a name shorter than 35 characters", "minLength": "Enter a name larger than 1 character", "pattern": "Enter a name containing letters, numbers, spaces, and any of the following special characters: ,;'-.?!&/\\#+=()"}}, "minLength": 1, "order": 3, "pattern": "^[a-zA-Z0-9\\s!&,()+'\\-./;?=#\\\\]{1,35}$", "title": "Last name", "type": "string"}, "member_id": {"meta": {"errors": {"minLength": "Please enter a valid Member Id"}, "placeholder": "Member Id (as appears on card)"}, "minLength": 1, "order": 7, "title": "Member Id"}, "payer": {"enum": ["AETNA", "00520", "UMR", "71084"], "meta": {"enum-titles": {"00520": "Blue Advantage/BCBS Arkansas", "AETNA": "<PERSON><PERSON><PERSON>", "UMR": "UMR/UnitedHealthcare"}, "placeholder": "Insurance Company"}, "order": 6, "title": "Insurance Company", "type": "string"}, "phone_mobile": {"format": "telephone", "meta": {"errors": {"pattern": "Needs to be exactly 10 digits"}}, "order": 4, "pattern": "^\\d{10}$", "title": "Mobile phone", "type": "string"}}, "required": ["payer", "first_name", "last_name", "phone_mobile", "country_code", "birth_date", "member_id"], "title": "Complete to get started", "type": "object"}, "submittedData": {}}