{"jsonSchemaForm": {"$schema": "http://json-schema.org/draft-07/schema#", "description": "Please enter your information to finish creating your account.", "properties": {"birthDate": {"format": "date", "formatMaximum": "2022-01-01", "formatMinimum": "1870-01-01", "meta": {"errors": {"format": "Enter a valid date format", "formatMaximum": "Enter a valid date that is not in the future", "formatMinimum": "Enter a valid date after Jan 01 1870"}}, "order": 4, "title": "Birthday", "type": "string"}, "countryCode": {"default": "+1", "meta": {"hidden": true}, "readOnly": true, "type": "string"}, "firstName": {"maxLength": 35, "meta": {"errors": {"maxLength": "Enter a name shorter than 35 characters", "minLength": "Enter a name larger than 1 character", "pattern": "Enter a name containing letters, numbers, spaces, and any of the following special characters: ,;'-.?!&/\\#+=()"}}, "minLength": 1, "order": 1, "pattern": "^[a-zA-Z0-9\\s!&,()+'\\-./;?=#\\\\]{1,35}$", "title": "First name", "type": "string"}, "lastName": {"maxLength": 35, "meta": {"errors": {"maxLength": "Enter a name shorter than 35 characters", "minLength": "Enter a name larger than 1 character", "pattern": "Enter a name containing letters, numbers, spaces, and any of the following special characters: ,;'-.?!&/\\#+=()"}}, "minLength": 1, "order": 2, "pattern": "^[a-zA-Z0-9\\s!&,()+'\\-./;?=#\\\\]{1,35}$", "title": "Last name", "type": "string"}, "phoneMobile": {"format": "telephone", "meta": {"errors": {"pattern": "Needs to be exactly 10 digits"}}, "order": 3, "pattern": "^\\d{10}$", "title": "Mobile phone", "type": "string"}}, "required": ["firstName", "lastName", "phoneMobile", "countryCode", "birthDate"], "title": "Personal information", "type": "object"}, "submittedData": {}}