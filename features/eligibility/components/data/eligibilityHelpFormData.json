{"jsonSchemaForm": {"$schema": "http://json-schema.org/draft-07/schema#", "description": "Please provide as much information as you have so we can assist you in signing up.", "properties": {"birthDate": {"format": "date", "formatMaximum": "2022-01-01", "formatMinimum": "1870-01-01", "meta": {"errors": {"format": "Enter a valid date format", "formatMaximum": "Enter a valid date that is not in the future", "formatMinimum": "Enter a valid date after Jan 01 1870"}}, "order": 4, "title": "Birthday", "type": "string"}, "countryCode": {"default": "+1", "meta": {"hidden": true}, "readOnly": true, "type": "string"}, "firstName": {"maxLength": 35, "meta": {"errors": {"maxLength": "Enter a name shorter than 35 characters", "minLength": "Enter a name larger than 1 character", "pattern": "Enter a name containing letters, numbers, spaces, and any of the following special characters: ,;'-.?!&/\\#+=()"}}, "minLength": 1, "order": 2, "pattern": "^[a-zA-Z0-9\\s!&,()+'\\-./;?=#\\\\]{1,35}$", "title": "First name", "type": "string"}, "lastName": {"maxLength": 35, "meta": {"errors": {"maxLength": "Enter a name shorter than 35 characters", "minLength": "Enter a name larger than 1 character", "pattern": "Enter a name containing letters, numbers, spaces, and any of the following special characters: ,;'-.?!&/\\#+=()"}}, "minLength": 1, "order": 3, "pattern": "^[a-zA-Z0-9\\s!&,()+'\\-./;?=#\\\\]{1,35}$", "title": "Last name", "type": "string"}, "payer": {"enum": ["AETNA", "00520", "UMR", "71084", "NOT_SURE"], "meta": {"enum-titles": {"00520": "Blue Advantage/BCBS Arkansas", "AETNA": "<PERSON><PERSON><PERSON>", "UMR": "UMR/UnitedHealthcare", "NOT_SURE": "Not sure"}, "placeholder": "Insurance Company"}, "order": 1, "title": "Insurance Company", "type": "string"}, "phoneMobile": {"format": "telephone", "meta": {"errors": {"pattern": "Needs to be exactly 10 digits"}}, "order": 6, "pattern": "^\\d{10}$", "title": "Mobile phone", "type": "string"}, "zipCode": {"maxLength": 5, "meta": {"errors": {"maxLength": "5 digits are required", "minLength": "5 digits are required", "pattern": "Use only numbers"}}, "minLength": 5, "order": 5, "pattern": "^[0-9 ]{1,5}$", "title": "Zip code", "type": "string"}, "haveInsuranceCard": {"order": 7, "title": "Do you have an insurance card?", "type": "string"}, "needHelpWith": {"order": 8, "title": "What do you need help with?", "type": "string"}}, "required": ["payer", "firstName", "lastName", "phoneMobile", "countryCode", "birthDate", "zipCode"], "title": "Insurance eligibility", "type": "object"}, "submittedData": {}}