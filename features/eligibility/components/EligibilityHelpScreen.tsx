import { JsonSchemaForm } from "@vivantehealth/vivante-core";
import { Box, Button, Grid, Paper } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { AuthFormContainer } from "@Components/AuthFormContainer/AuthFormContainer";
import { Form } from "@Components/form/Form";
import { FormFields } from "@Components/form/FormFields";
import { formatInputData } from "@Components/form/utils/formatInputData";
import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";
import { isOfType } from "@Utils/isOfType";

type EligibilityHelpScreenProps = Readonly<{
  onSubmit: (data: Record<string, string | number>) => void;
  formData: JsonSchemaForm;
  backClickCallback: () => void;
}>;

export const EligibilityHelpScreen = ({ onSubmit, formData, backClickCallback }: EligibilityHelpScreenProps) => {
  const { formWidth } = useResponsiveStylesHook();
  const { inputProperties, defaultValues, requiredFields } = formatInputData(formData);

  return (
    <AuthFormContainer>
      <Paper sx={{ width: formWidth }}>
        <Form
          defaultValues={defaultValues}
          onSubmit={(data) => {
            /**
             * Eligibility form data is dynamically generated on the backend and differs between clients thus we
             * just ensure its an object within our type guard
             */
            if (isOfType<Record<string, string | number>>(data, [])) {
              onSubmit(data);
            }
          }}
          formHeader={formData.title}
          formSubheader={formData.description}
        >
          <Grid item my={5}>
            <FormFields inputProperties={inputProperties} requiredFields={requiredFields} />
          </Grid>
          <Box mt={3} mb={2} display="flex" flexDirection="row">
            <Button variant="secondary" aria-label={appStrings.buttonText.back} onClick={backClickCallback} fullWidth>
              {appStrings.buttonText.back}
            </Button>
            <Box ml="12px" mr="12px" />
            <Button type="submit" variant="primary" aria-label={appStrings.buttonText.next} fullWidth>
              {appStrings.buttonText.next}
            </Button>
          </Box>
        </Form>
      </Paper>
    </AuthFormContainer>
  );
};
