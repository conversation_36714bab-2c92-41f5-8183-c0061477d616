import { Box, Button, Grid, Paper, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { AuthFormContainer } from "@Components/AuthFormContainer/AuthFormContainer";
import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";

type EligibilityResultsScreenProps = Readonly<{
  header: string | undefined;
  subheader: string;
  showPrimaryBtn: boolean;
  primaryBtnText: string;
  primaryBtnCallback: () => void;
  showSecondaryBtn?: boolean;
  secondaryBtnCallback?: () => void;
}>;

export const EligibilityResultsScreen = ({
  header,
  subheader,
  showPrimaryBtn,
  primaryBtnText,
  primaryBtnCallback,
  showSecondaryBtn,
  secondaryBtnCallback,
}: EligibilityResultsScreenProps) => {
  const { formWidth } = useResponsiveStylesHook();

  return (
    <AuthFormContainer>
      <Paper sx={{ width: formWidth }}>
        {header && header?.length > 0 ? (
          <Grid item sx={{ mb: 4 }}>
            <Typography variant="h3">{header}</Typography>
          </Grid>
        ) : null}

        <Grid item sx={{ mb: 5 }}>
          <Typography variant="body">{subheader}</Typography>
        </Grid>

        <Box display="flex" gap={3}>
          {showSecondaryBtn && (
            <>
              <Button type="button" variant="secondary" onClick={secondaryBtnCallback} fullWidth>
                {appStrings.buttonText.tryAgain}
              </Button>
            </>
          )}

          {showPrimaryBtn && (
            <Button type="button" variant="primary" onClick={primaryBtnCallback} fullWidth>
              {primaryBtnText}
            </Button>
          )}
        </Box>
      </Paper>
    </AuthFormContainer>
  );
};
