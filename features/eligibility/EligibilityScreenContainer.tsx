import { useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { EligibilityProcessState, Form } from "@vivantehealth/vivante-core";
import * as Sentry from "@sentry/nextjs";

import { appStrings } from "@Assets/app_strings";
import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { authenticationStateSelector } from "@Features/authentication/store/authenticationStateSlice";
import {
  eligibilityRegistrationSubmitted,
  selectEligibilityProcessCurrentFormData,
  selectEligibilityProcessSubmitFormLoadState,
  selectPassiveV2EligibilityCheck,
  selectRegistrationVersion,
  submitEligibilityProcessFormDataFail,
  submitEligibilityProcessFormDataSuccess,
} from "@Features/eligibility/store/eligibilityProcessStateSlice";
import { OnboardingError } from "@Features/onboarding/components/OnboardingError";
import { useOnboardingHook } from "@Features/onboarding/hooks/onboardingHook";
import { useSignOutHook } from "@Hooks/signOutHook";
import { Routes } from "@Types";
import { formatPhoneNumber } from "@Utils/formatPhoneNumber";
import { isFetchFromBffError } from "@Utils/isFetchFromBffError";

import { EligibilityApiError, useActivatePassiveV2UserMutation, useActivateUserMutation } from "./api/eligibilityApi";
import { EligibilityResultsScreen } from "./components/EligibilityResultsScreen";
import { EligibilityScreen } from "./components/EligibilityScreen";
import { processEligibilityError, UserFeedbackError } from "./utils/processEligibilityError";

const BUTTON_STRINGS = appStrings.buttonText;

type PrimaryBtnAction = Record<
  UserFeedbackError["actionType"],
  { primaryBtnText: string; primaryBtnCallback: () => void }
>;

const getEligibilityResults = (
  userFeedbackError: UserFeedbackError | undefined,
  eligibilityState: EligibilityProcessState,
  handleTryAgain: () => void,
  handleLogout: () => void,
  handleLogin: () => void,
) => {
  const primaryBtnAction = {
    LOGIN: {
      primaryBtnText: BUTTON_STRINGS.logIn,
      primaryBtnCallback: handleLogin,
    },
    TRY_AGAIN: {
      primaryBtnText: BUTTON_STRINGS.tryAgain,
      primaryBtnCallback: handleTryAgain,
    },
    LOGOUT: {
      primaryBtnText: BUTTON_STRINGS.logOut,
      primaryBtnCallback: handleLogout,
    },
  } as const satisfies PrimaryBtnAction;

  // If an error occurs during the v2 flow eligibility check, we will utilize the userFeedbackError object
  if (userFeedbackError) {
    return {
      header: userFeedbackError.header,
      subheader: userFeedbackError.subheader,
      showPrimaryBtn: true,
      ...primaryBtnAction[userFeedbackError.actionType],
    };
  }

  // Otherwise, we will utilize the eligibilityState object which is returned via the v1 flow eligibility check
  return {
    header: eligibilityState?.eligibilityResultTitle,
    subheader: eligibilityState?.eligibilityResultMessage,
    showPrimaryBtn: eligibilityState?.actions?.suggestLogout,
    primaryBtnText: eligibilityState?.actions?.logoutTitle || BUTTON_STRINGS.logOut,
    primaryBtnCallback: handleLogout,
    showSecondaryBtn: eligibilityState?.canSubmit,
    secondaryBtnCallback: handleTryAgain,
  };
};

const normalizeFormData = (formData: Record<string, string | number>) => {
  /** object that contains all fields that have values to send to the activation API */
  return Object.entries(formData).reduce<Record<string, string>>((result, [key, value]) => {
    if (value) {
      result[key] = String(value);
    }

    return result;
  }, {});
};

export const EligibilityScreenContainer = () => {
  const dispatch = useDispatch();
  const isRegistrationV2 = useSelector(selectRegistrationVersion);
  const [showForm, setShowForm] = useState(true);
  const [isFormSubmitting, setIsFormSubmitting] = useState(false);
  const [userFeedbackError, setUserFeedbackError] = useState<UserFeedbackError>();
  const [redirectRoute, setRedirectRoute] = useState<Routes>();
  const validationErrors = useRef<Record<string, string>>(undefined);
  const v2FormData = useRef<Record<string, string>>(undefined);
  const [activateUser] = useActivateUserMutation();
  const [activatePassiveV2User] = useActivatePassiveV2UserMutation();
  const { signOutOfApp } = useSignOutHook();
  const { eligibilityState, eligibilityCheckLoading, isEligible, displayUserStatusError } =
    useOnboardingHook(redirectRoute);

  const previouslyInputDataState =
    useSelector(selectEligibilityProcessCurrentFormData) || v2FormData?.current || eligibilityState?.submittedData;
  const submissionLoadState = useSelector(selectEligibilityProcessSubmitFormLoadState);
  const passiveV2EligibilityCheck = useSelector(selectPassiveV2EligibilityCheck);
  const mfaCredentials = useSelector(authenticationStateSelector("mfaCredentials"));
  /**
   * To avoid having to cast the type, we will convert the object to a Record<string, string> type and memoize the result
   */
  const previouslyInputData = useMemo(() => {
    const previouslyInputDataMap = previouslyInputDataState
      ? Object.entries(previouslyInputDataState).reduce<Record<string, string>>((previousData, [key, value]) => {
          previousData[key] = value?.toString() ?? "";

          return previousData;
        }, {})
      : {};

    if (mfaCredentials.mobilePhone) {
      previouslyInputDataMap.phone_mobile = formatPhoneNumber(mfaCredentials.mobilePhone);
    }

    return Object.keys(previouslyInputDataMap).length > 0 ? previouslyInputDataMap : undefined;
  }, [mfaCredentials.mobilePhone, previouslyInputDataState]);

  const isSubmissionLoading = submissionLoadState === "loading";

  useEffect(() => {
    if (eligibilityState?.eligibilityResultMessage) {
      setShowForm(false);
    }
  }, [eligibilityState]);

  const onLogout = () => signOutOfApp();
  const onSubmit = async (data: Record<string, string | number>) => {
    // Remove all hyphens that are added while filling out the form from the phone number prior to submitting
    const sanitizedPhoneNumber =
      typeof data.phone_mobile === "string" ? data.phone_mobile?.replace(/-/g, "") : data.phone_mobile;

    /**
     * Only utilize the v2 registration flow if isRegistrationV2 is true and passiveV2EligibilityCheck is false
     * If both isRegistrationV2 === true and passiveV2EligibilityCheck === true, we will utilize the v1 registration flow
     * This is a result of having to use a single client access code for both v1 and v2 registration flows
     */
    // =============== v2 registration ===============
    if (isRegistrationV2 && !passiveV2EligibilityCheck) {
      // Use this when the new API is enabled - SSO and regular registration API will be the same
      await submitV2OrSSOUserEligibilityData({ ...data, phone_mobile: sanitizedPhoneNumber });
      return;
    }

    await submitPassiveV2EligibilityData({ ...data, phone_mobile: sanitizedPhoneNumber });
  };

  const onTryAgain = () => setShowForm(true);
  const onLogin = async () => {
    setRedirectRoute(Routes.LOGIN);
    onLogout();
  };
  /**
   * TODO: This is a temporary solution for confirming that switching over clients from the v1 eligibility flow to v2 is successful.
   * TODO: In turn, this should be removed once the v1 eligibility flow is deprecated. We should change to sending all eligibility submissions
   * TODO: through the new API via `submitV2OrSSOUserEligibilityData` and remove the `submitPassiveV2EligibilityData` function.
   * TODO: At that time, we should change the name of `submitV2OrSSOUserEligibilityData` to `submitEligibilityData` or something similar.
   * TODO: We should also cleanup the EligibilityProcessEpics and State files to remove the v1 eligibility flow logic.
   */
  const submitPassiveV2EligibilityData = async (formData: Record<string, string | number>) => {
    setIsFormSubmitting(true);

    const activationFormData = normalizeFormData(formData);

    try {
      const eligibilityProcessState = await activatePassiveV2User({
        passive_v2_eligibility_check: !!passiveV2EligibilityCheck,
        fields: activationFormData,
      }).unwrap();

      const { submissionFormSchema, submittedData, fields, ...eligibilityState } =
        eligibilityProcessState.data.attributes;

      dispatch(
        submitEligibilityProcessFormDataSuccess({
          ...eligibilityState,
          submissionForm: new Form(submissionFormSchema, submittedData),
          submittedData: submittedData ?? fields,
        }),
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : JSON.stringify(error);

      Sentry.captureMessage(errorMessage, "error");
      dispatch(submitEligibilityProcessFormDataFail(errorMessage));
    }

    setIsFormSubmitting(false);
  };

  const submitV2OrSSOUserEligibilityData = async (formData: Record<string, string | number>) => {
    setIsFormSubmitting(true);

    const activationFormData = normalizeFormData(formData);

    try {
      await activateUser(activationFormData).unwrap();

      dispatch(eligibilityRegistrationSubmitted());
    } catch (error) {
      if (isFetchFromBffError<EligibilityApiError>(error)) {
        const processedErrors = processEligibilityError(error);

        if (processedErrors.type === "SERVER_VALIDATION_ERRORS") {
          validationErrors.current = processedErrors.serverValidationErrors;
        }

        if (processedErrors.type === "USER_FEEDBACK_ERRORS") {
          setUserFeedbackError(processedErrors.userFeedbackErrors);
          // Store form data to be used when the user clicks the try again button
          v2FormData.current = activationFormData;
          setShowForm(false);
        }
      }

      setIsFormSubmitting(false);
    }
  };

  if (displayUserStatusError) {
    return <OnboardingError />;
  }

  if (isSubmissionLoading || eligibilityCheckLoading || isEligible) {
    return <LoadingSpinner open overlayDrawer overlayHeader />;
  }

  return (
    <>
      {eligibilityState ? (
        showForm ? (
          <EligibilityScreen
            onSubmit={onSubmit}
            formData={eligibilityState.submissionForm?.jsonSchemaForm}
            previouslyInputData={previouslyInputData}
            logoutCallback={onLogout}
            isWalmartForm={eligibilityState.submissionFormType === "walmart"}
            serverValidationErrors={validationErrors.current}
          />
        ) : (
          <EligibilityResultsScreen
            {...getEligibilityResults(userFeedbackError, eligibilityState, onTryAgain, onLogout, onLogin)}
          />
        )
      ) : null}
      <LoadingSpinner open={isSubmissionLoading || isFormSubmitting} overlayHeader overlayDrawer />
    </>
  );
};
