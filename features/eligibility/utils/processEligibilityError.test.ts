import { describe, expect, test } from "vitest";

import { appStrings } from "@Assets/app_strings";

import { processEligibilityError, USER_FEEDBACK_ERROR_MESSAGES } from "../utils/processEligibilityError";

describe("processEligibilityError", () => {
  test("Should return an invalidRequest", () => {
    const result = processEligibilityError({
      data: {
        errors: [
          {
            code: "MISSING_MEMBER_ID",
            title: "Missing member id",
            detail: "Missing member id",
            status: "300",
          },
        ],
        status_code: 300,
      },
      status: 300,
    });

    expect(result.type).toBe("INVALID_REQUEST");
  });

  test("Should return serverValidationErrors when the error code is a validation error", () => {
    const invalidAttributeResult = processEligibilityError({
      data: {
        errors: [
          {
            code: "MISSING_ATTRIBUTE",
            title: "Missing attribute",
            detail: "Attribute 'last_name' must be provided",
            status: "400",
            source: {
              pointer: "/attributes/last_name",
            },
          },
          {
            code: "INVALID_ATTRIBUTE",
            title: "Missing attribute",
            detail: "String length must be less than or equal to 35",
            status: "400",
            source: {
              pointer: "/attributes/first_name",
            },
          },
        ],
        status_code: 400,
      },
      status: 400,
    });

    expect(invalidAttributeResult.type).toBe("SERVER_VALIDATION_ERRORS");
    if (invalidAttributeResult.type === "SERVER_VALIDATION_ERRORS") {
      expect(invalidAttributeResult.serverValidationErrors).toMatchObject({
        last_name: appStrings.sharedFormText.requiredMessage,
        first_name: "String length must be less than or equal to 35",
      });
    }
  });

  test("Should return member not found result object", () => {
    const memberNotFoundResult = processEligibilityError({
      data: {
        errors: [
          {
            code: "MEMBER_NOT_FOUND",
            title: "Member not found",
            detail: "Member with ID '1b0e9161-a211-414e-b686-b2da9daca5c4' not found",
            status: "400",
          },
        ],
        status_code: 400,
      },
      status: 400,
    });

    expect(memberNotFoundResult.type).toBe("USER_FEEDBACK_ERRORS");
    if (memberNotFoundResult.type === "USER_FEEDBACK_ERRORS") {
      expect(memberNotFoundResult.userFeedbackErrors).toMatchObject(USER_FEEDBACK_ERROR_MESSAGES.MEMBER_NOT_FOUND);
    }
  });

  test("Should return eligibility not found result object", () => {
    const eligibilityNotFoundResult = processEligibilityError({
      data: {
        errors: [
          {
            code: "ELIGIBILITY_NOT_FOUND",
            title: "Member is ineligible",
            detail: "No eligibility record found",
            status: "400",
          },
        ],
        status_code: 400,
      },
      status: 400,
    });

    expect(eligibilityNotFoundResult.type).toBe("USER_FEEDBACK_ERRORS");
    if (eligibilityNotFoundResult.type === "USER_FEEDBACK_ERRORS") {
      expect(eligibilityNotFoundResult.userFeedbackErrors).toMatchObject(
        USER_FEEDBACK_ERROR_MESSAGES.ELIGIBILITY_NOT_FOUND,
      );
    }
  });

  test("Should return expired eligibility result object", () => {
    const expiredEligibilityResult = processEligibilityError({
      data: {
        errors: [
          {
            code: "EXPIRED_ELIGIBILITY",
            title: "Member is ineligible",
            detail: "Member is not eligible (status: not_eligible_expired)",
            status: "400",
          },
        ],
        status_code: 400,
      },
      status: 400,
    });

    expect(expiredEligibilityResult.type).toBe("USER_FEEDBACK_ERRORS");
    if (expiredEligibilityResult.type === "USER_FEEDBACK_ERRORS") {
      expect(expiredEligibilityResult.userFeedbackErrors).toMatchObject(
        USER_FEEDBACK_ERROR_MESSAGES.EXPIRED_ELIGIBILITY,
      );
    }
  });

  test("Should return pre-existing subscription result object", () => {
    const preExistingSubscriptionResult = processEligibilityError({
      data: {
        errors: [
          {
            code: "PRE_EXISTING_SUBSCRIPTION",
            title: "Member is ineligible",
            detail: "Member already has an active subscription",
            status: "400",
          },
        ],
        status_code: 400,
      },
      status: 400,
    });

    expect(preExistingSubscriptionResult.type).toBe("USER_FEEDBACK_ERRORS");
    if (preExistingSubscriptionResult.type === "USER_FEEDBACK_ERRORS") {
      expect(preExistingSubscriptionResult.userFeedbackErrors).toMatchObject(
        USER_FEEDBACK_ERROR_MESSAGES.PRE_EXISTING_SUBSCRIPTION,
      );
    }
  });
});
