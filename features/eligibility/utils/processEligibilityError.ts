import * as Sentry from "@sentry/nextjs";

import { appStrings } from "@Assets/app_strings";
import { ErrorStateSlice } from "@Features/error/store/errorStateSlice";
import { store } from "@Store/store";
import { BffFetchError } from "@Utils/isFetchFromBffError";

import {
  EligibilityApiError,
  EligibilityApiErrorCode,
  EligibilityValidationErrorCodes,
  GenericEligibilityApiErrorCodes,
  UserFeedbackErrorCodes,
} from "../api/eligibilityApi";

const ELIGIBILITY_STRINGS = appStrings.features.eligibility;
/** These are error codes related to errors that the user CAN NOT do anything to correct */
const GENERIC_ERROR_CODES = [
  "INVALID_REQUEST",
  "MISSING_ATTRIBUTES",
  "MISSING_MEMBER_ID",
  "MISSING_CONFIGURATION",
] as const satisfies GenericEligibilityApiErrorCodes[];
/**
 * These are error codes related to validation errors from the backend. These shouldn't be triggered as frontend
 * validation is the same, but in case we do, we need to display the errors to the user to correct
 * */
const VALIDATION_ERROR_CODES = [
  "INVALID_ATTRIBUTE",
  "MISSING_ATTRIBUTE",
] as const satisfies EligibilityValidationErrorCodes[];
/**
 * These are error codes related to errors that the user can address in some way or at minimum we can provide
 * more information to the user about
 * */
const USER_FEEDBACK_ERROR_CODES = [
  "MEMBER_NOT_FOUND",
  "ELIGIBILITY_NOT_FOUND",
  "EXPIRED_ELIGIBILITY",
  "PRE_EXISTING_SUBSCRIPTION",
] as const satisfies UserFeedbackErrorCodes[];

export type UserFeedbackError = {
  header: string;
  subheader: string;
  actionType: "LOGIN" | "TRY_AGAIN" | "LOGOUT";
};

export const USER_FEEDBACK_ERROR_MESSAGES = {
  MEMBER_NOT_FOUND: { ...ELIGIBILITY_STRINGS.eligibilityErrors.eligibilityNotFound, actionType: "TRY_AGAIN" },
  ELIGIBILITY_NOT_FOUND: { ...ELIGIBILITY_STRINGS.eligibilityErrors.eligibilityNotFound, actionType: "TRY_AGAIN" },
  EXPIRED_ELIGIBILITY: { ...ELIGIBILITY_STRINGS.eligibilityErrors.expiredEligibility, actionType: "LOGOUT" },
  PRE_EXISTING_SUBSCRIPTION: { ...ELIGIBILITY_STRINGS.eligibilityErrors.preExistingSubscription, actionType: "LOGIN" },
} as const satisfies Record<UserFeedbackErrorCodes, UserFeedbackError>;

type ProcessEligibilityErrorResponse =
  | { type: "INVALID_REQUEST" }
  | {
      type: "SERVER_VALIDATION_ERRORS";
      serverValidationErrors: Record<string, string>;
    }
  | {
      type: "USER_FEEDBACK_ERRORS";
      userFeedbackErrors: UserFeedbackError;
    };

const isExpectedEligibilityErrorCodeType = <T extends EligibilityApiErrorCode>(
  apiError: EligibilityApiError,
  errorCode: T[],
): apiError is EligibilityApiError<T> => {
  return !!errorCode.find((code) => code === apiError.code);
};

const normalizeErrorTypes = (errors: EligibilityApiError[]) => {
  return errors.reduce<{
    invalidRequest: EligibilityApiError<GenericEligibilityApiErrorCodes>[];
    validationErrors: EligibilityApiError<EligibilityValidationErrorCodes>[];
    userFeedbackErrors: EligibilityApiError<UserFeedbackErrorCodes>[];
  }>(
    (aggregatedErrors, apiError) => {
      if (isExpectedEligibilityErrorCodeType(apiError, GENERIC_ERROR_CODES)) {
        aggregatedErrors.invalidRequest.push(apiError);
      }

      if (isExpectedEligibilityErrorCodeType(apiError, VALIDATION_ERROR_CODES)) {
        aggregatedErrors.validationErrors.push(apiError);
      }

      if (isExpectedEligibilityErrorCodeType(apiError, USER_FEEDBACK_ERROR_CODES)) {
        aggregatedErrors.userFeedbackErrors.push(apiError);
      }

      return aggregatedErrors;
    },
    { invalidRequest: [], validationErrors: [], userFeedbackErrors: [] },
  );
};

/** Processes the error to determine the type of errors within and how to display them to the end user */
export const processEligibilityError = (error: BffFetchError<EligibilityApiError>): ProcessEligibilityErrorResponse => {
  const { errors } = error.data;
  const { invalidRequest, validationErrors, userFeedbackErrors } = normalizeErrorTypes(errors);

  if (validationErrors.length) {
    const serverValidationErrors = validationErrors.reduce<Record<string, string>>(
      (aggregatedValidationErrors, { code, detail, source }) => {
        const fieldName = source?.pointer.split("/");

        /** As we can only show one error per field, we will only show the first error we encounter */
        if (fieldName && !aggregatedValidationErrors[fieldName[fieldName.length - 1]]) {
          aggregatedValidationErrors[fieldName[fieldName.length - 1]] =
            code === "MISSING_ATTRIBUTE" ? appStrings.sharedFormText.requiredMessage : detail;
        }

        return aggregatedValidationErrors;
      },
      {},
    );

    return { type: "SERVER_VALIDATION_ERRORS", serverValidationErrors };
  }

  if (userFeedbackErrors.length) {
    return {
      type: "USER_FEEDBACK_ERRORS",
      userFeedbackErrors: USER_FEEDBACK_ERROR_MESSAGES[userFeedbackErrors[0].code],
    };
  }

  /**
   * If we get an invalid request error OR an unknown error, we want to display the ErrorModal with a generic message
   * while logging the actual error details to sentry
   * */
  const { code, detail, title } = invalidRequest.length > 0 ? invalidRequest[0] : errors[0];
  const errorToLog = `Eligibility API Error --- Status: ${error.status} --- Code: ${code} --- Detail: ${detail} --- Title: ${title}`;

  Sentry.withScope((scope) => {
    scope.setLevel("error");
    Sentry.captureException(new Error(errorToLog));
  });

  store.dispatch(
    ErrorStateSlice.actions.setError({
      message: ELIGIBILITY_STRINGS.serverOrInvalidRequestError,
    }),
  );
  return { type: "INVALID_REQUEST" };
};
