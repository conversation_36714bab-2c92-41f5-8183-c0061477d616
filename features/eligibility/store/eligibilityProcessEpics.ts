import { EligibilityProcessState, FormData } from "@vivantehealth/vivante-core";
import { Action, PayloadAction } from "@reduxjs/toolkit";
import { Epic, ofType } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { catchError, map, switchMap } from "rxjs/operators";

import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { logger } from "@Utils/logger";

import {
  loadEligibilityProcessState,
  loadEligibilityProcessStateFail,
  loadEligibilityProcessStateSuccess,
  submitEligibilityProcessFormData,
  submitEligibilityProcessFormDataFail,
  submitEligibilityProcessFormDataSuccess,
} from "./eligibilityProcessStateSlice";

let retryAttempts = 5;
const submitAccessCodeForEligibilityDataEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(loadEligibilityProcessState.type),
    switchMap(() =>
      from(
        vivanteCoreContainer.getEligibilityUseCaseFactory().createGetEligibilityProcessStateUseCase().execute(),
      ).pipe(
        map((eligibilityProcessState: EligibilityProcessState) =>
          loadEligibilityProcessStateSuccess(eligibilityProcessState),
        ),
        catchError((error) => {
          retryAttempts--;
          if (retryAttempts > 0) {
            return of(loadEligibilityProcessState());
          }

          return of(loadEligibilityProcessStateFail(error));
        }),
      ),
    ),
  );
};

const submitEligibilityFormDataEpic: Epic = (actions$: Observable<PayloadAction<FormData>>) => {
  return actions$.pipe(
    ofType(submitEligibilityProcessFormData.type),
    switchMap((action: PayloadAction<FormData>) =>
      from(
        vivanteCoreContainer
          .getEligibilityUseCaseFactory()
          .createPostEligibilitySubmissionFormDataUseCase()
          .execute(action.payload),
      ).pipe(
        map((eligibilityProcessState: EligibilityProcessState) =>
          submitEligibilityProcessFormDataSuccess(eligibilityProcessState),
        ),
        catchError((error) => {
          logger.error(error);
          return [loadEligibilityProcessState(), submitEligibilityProcessFormDataFail(error)];
        }),
      ),
    ),
  );
};

export const eligibilityProcessEpics = [submitAccessCodeForEligibilityDataEpic, submitEligibilityFormDataEpic];
