import {
  EligibilityProcessState,
  EligibilityProcessStatus,
  Form,
  FormData,
  JsonSchemaForm,
} from "@vivantehealth/vivante-core";
import { PayloadAction, createSlice } from "@reduxjs/toolkit";

import { MfaType } from "@Features/multiFactorAuthentication/types/mfa.types";
import { RootState } from "@Store/store";
import { LoadState } from "@Types";
import { formatPhoneNumber } from "@Utils/formatPhoneNumber";
import { processError } from "@Utils/slice.util";

export type EligibilityProcessStateType = Readonly<{
  eligibilityProcessState: EligibilityProcessState | null;
  currentData: FormData | null;
  loadState: LoadState;
  submitFormLoadState: LoadState;
  eligibilityError: string | null;
  submissionForm?: Form | null;
  accessCode: string | null;
  isRegistered: boolean | null;
  /** This flag is passed into /v1/legacy/eligibility_process endpoint to determine which eligibility path to take on the backend */
  passiveV2EligibilityCheck: boolean | null;
  /** Flag to tell if the signup / sign in should use BFF endpoints or legacy ones */
  registration_v2: boolean;
  /** Flag to determine if we need to utilize the MFA flow */
  mfaRequired: boolean | null;
  /** If MFA is required, this is the type of verification method utilized */
  mfaSupportedMethod: MfaType | undefined;
  /**
   * Data pre-filled during registrations. Some registration method like SSO carries these information from SSO portal
   * TODO: Investigate if this is actually needed, does not currently appear to be used and how does this differ from currentData?
   * */
  prePopulatedUserData: Partial<{
    first_name: string;
    last_name: string;
    phone_mobile: string;
    date_of_birth: string;
    sso_email: string;
    us_state: string;
    member_id: string;
  }> | null;
}>;

type EligibilityDataPayload = Pick<EligibilityProcessStateType, "isRegistered" | "accessCode" | "registration_v2"> & {
  submissionForm: JsonSchemaForm;
};

const initialEligibilityProcessState: EligibilityProcessState = {
  eligibilityResultMessage: "",
  canSubmit: false,
  actions: { suggestLogout: false, logoutTitle: "" },
  status: EligibilityProcessStatus.UNKNOWN,
};

export const initialState: EligibilityProcessStateType = {
  eligibilityProcessState: null,
  currentData: null,
  loadState: null,
  submitFormLoadState: null,
  eligibilityError: null,
  submissionForm: null,
  accessCode: null,
  isRegistered: null,
  passiveV2EligibilityCheck: null,
  registration_v2: false,
  mfaRequired: null,
  mfaSupportedMethod: undefined,
  prePopulatedUserData: null,
};

const eligibilityProcessSlice = createSlice({
  name: "eligibilityProcessState",
  initialState,
  reducers: {
    loadEligibilityProcessState: (state) => ({ ...state, loadState: "loading" }),
    loadEligibilityProcessStateFail: (state, action: PayloadAction<Error>) => {
      processError({ error: action.payload.message });

      return {
        ...state,
        loadState: "failure",
        eligibilityError: action.payload.message,
      };
    },
    loadEligibilityProcessStateSuccess: (state, action: PayloadAction<EligibilityProcessState>) => ({
      ...state,
      eligibilityProcessState: action.payload,
      loadState: "loaded",
    }),
    submitEligibilityProcessFormData: (state, action: PayloadAction<FormData>) => ({
      ...state,
      submitFormLoadState: "loading",
      currentData: action.payload,
      eligibilityProcessState: null,
    }),
    submitEligibilityProcessFormDataFail: (state, action: PayloadAction<string>) => {
      processError({ error: action.payload });

      return {
        ...state,
        submitFormLoadState: "failure",
        eligibilityError: action.payload,
      };
    },
    submitEligibilityProcessFormDataSuccess: (state, action: PayloadAction<EligibilityProcessState>) => ({
      ...state,
      submitFormLoadState: "loaded",
      eligibilityProcessState: action.payload,
    }),
    updateCurrentData: (state, action: PayloadAction<FormData>) => ({
      ...state,
      currentData: action.payload,
    }),
    updateEligibilityDataV2: (
      state,
      action: PayloadAction<
        EligibilityDataPayload & {
          prePopulatedUserData?: EligibilityProcessStateType["prePopulatedUserData"];
        }
      >,
    ) => {
      return {
        ...state,
        isRegistered: action.payload.isRegistered,
        prePopulatedUserData: action.payload?.prePopulatedUserData ?? {},
        accessCode: action.payload.accessCode,
        registration_v2: action.payload.registration_v2,
        eligibilityProcessState: {
          ...(state.eligibilityProcessState ?? initialEligibilityProcessState),
          submissionForm: {
            ...state.submissionForm,
            getTitle: state.submissionForm?.getTitle ?? (() => ""),
            getDescription: state.submissionForm?.getDescription ?? (() => ""),
            updateAndGetForm: state.submissionForm?.updateAndGetForm ?? (() => ({})),
            jsonSchemaForm: action.payload.submissionForm,
          },
        },
      };
    },
    /** This is to process the eligibility registration submission for usage with BFF endpoints to support v2 registration flow and SSO */
    eligibilityRegistrationSubmitted: (state) => {
      return {
        ...state,
        eligibilityProcessState: {
          ...(state?.eligibilityProcessState ?? initialEligibilityProcessState),
          status: EligibilityProcessStatus.ELIGIBLE,
        },
      };
    },
    /** Update registration v2 flag to switch BFF vs legacy endpoints for registration / login */
    updateRegistrationConfig: (
      state,
      action: PayloadAction<
        Pick<EligibilityProcessStateType, "accessCode" | "registration_v2" | "passiveV2EligibilityCheck"> &
          Partial<Pick<EligibilityProcessStateType, "mfaRequired" | "mfaSupportedMethod">>
      >,
    ) => {
      return {
        ...state,
        accessCode: action.payload.accessCode,
        registration_v2: action.payload.registration_v2,
        passiveV2EligibilityCheck: action.payload.passiveV2EligibilityCheck,
        mfaRequired: action.payload?.mfaRequired ?? state.mfaRequired,
        mfaSupportedMethod: action.payload?.mfaSupportedMethod,
      };
    },
    mergePartnerPreVerifyData: (
      state,
      action: PayloadAction<{ eligibilityData: Record<string, string | number>; registrationCode: string }>,
    ) => {
      const mergedCurrentData = Object.entries(action.payload.eligibilityData ?? {}).reduce(
        (mergedContent, [key, value]) => {
          return {
            ...mergedContent,
            [key]: key === "phone_mobile" && typeof value === "string" ? formatPhoneNumber(value) : value,
          };
        },
        state.currentData ?? {},
      );

      return {
        ...state,
        currentData: mergedCurrentData,
        accessCode: action.payload.registrationCode,
      };
    },
  },
});

export const {
  loadEligibilityProcessState,
  loadEligibilityProcessStateFail,
  loadEligibilityProcessStateSuccess,
  submitEligibilityProcessFormData,
  submitEligibilityProcessFormDataFail,
  submitEligibilityProcessFormDataSuccess,
  updateCurrentData,
  updateEligibilityDataV2,
  eligibilityRegistrationSubmitted,
  updateRegistrationConfig,
  mergePartnerPreVerifyData,
} = eligibilityProcessSlice.actions;

export const selectEligibilityProcessState = (state: RootState) => state.eligibilityState.eligibilityProcessState;
export const selectEligibilityProcessCurrentFormData = (state: RootState) => state.eligibilityState.currentData;
export const isEligibilityProcessLoading = (state: RootState) => state.eligibilityState.loadState === "loading";
export const selectEligibilityProcessSubmitFormLoadState = (state: RootState) =>
  state.eligibilityState.submitFormLoadState;
export const selectIsEligible = (state: RootState) =>
  state.eligibilityState.eligibilityProcessState?.status === EligibilityProcessStatus.ELIGIBLE;
export const selectIsRegistered = (state: RootState) => state.eligibilityState.isRegistered;
export const selectEligibilityError = (state: RootState) => state.eligibilityState.eligibilityError;

export const eligibilityProcessStateReducer = eligibilityProcessSlice.reducer;
export const selectRegistrationVersion = (state: RootState) => state.eligibilityState.registration_v2;
export const selectAccessCode = (state: RootState) => state.eligibilityState.accessCode;
export const selectPassiveV2EligibilityCheck = (state: RootState) => state.eligibilityState.passiveV2EligibilityCheck;
export const selectMfaRequired = (state: RootState) => state.eligibilityState.mfaRequired;
export const selectMfaSupportedMethod = (state: RootState) => state.eligibilityState.mfaSupportedMethod;
