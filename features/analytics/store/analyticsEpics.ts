import { ClickStreamActivityContextExtra, ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { PayloadAction, createAction } from "@reduxjs/toolkit";
import Router from "next/router";
import { Epic, StateObservable, ofType } from "redux-observable";
import { from, Observable } from "rxjs";
import { concatMap, map, withLatestFrom } from "rxjs/operators";

import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { RootState } from "@Store/store";
import { getFirebaseAuth } from "@Utils/getFirebaseAuth";
import { logger } from "@Utils/logger";

export type AnalyticsMeta = Readonly<{
  eventType: ClickStreamActivityEventType;
  currentScreen?: string;
  activityContextExtra?: ClickStreamActivityContextExtra;
}>;

export const sendAnalytics = createAction("sendAnalytics", (analyticsMeta: AnalyticsMeta) => ({
  payload: analyticsMeta,
}));

export const analyticsSent = createAction("analyticsSent");
export const analyticsNoop = createAction("analyticsNoop");

const getCurrentUserToken = async () => {
  const firebaseAuth = await getFirebaseAuth();
  const currentUserToken = await firebaseAuth.currentUser?.getIdTokenResult();

  return currentUserToken;
};

const analyticsEpic: Epic = (
  actions$: Observable<PayloadAction<AnalyticsMeta>>,
  state$: StateObservable<RootState>,
) => {
  return actions$.pipe(
    ofType(sendAnalytics.type),
    withLatestFrom(state$),
    concatMap(([action, state]) =>
      from(getCurrentUserToken()).pipe(
        map((currentUserToken) => {
          const analyticsMeta = (action as PayloadAction<AnalyticsMeta>).payload;
          const currentScreen: string = analyticsMeta?.currentScreen ?? Router.asPath;
          const { activityContextExtra, eventType } = analyticsMeta;

          const memberId = state.memberState?.member?.id;
          const cylinderPersonId = currentUserToken?.claims?.cylinder_person_id;
          const toSend = {
            timestamp: Date.now(),
            eventType,
            context: {
              memberId,
              cylinderPersonId,
              currentScreen,
              extra: {
                ...activityContextExtra,
              },
            },
          };

          logger.info("*ANALYTICS", toSend);
          vivanteCoreContainer.getClickStreamUseCaseFactory().createTrackActivityUseCase().execute(toSend);
          return analyticsSent();
        }),
      ),
    ),
  );
};

/**
 * Syntactic sugar for creating an epic which listens to dispatches of the provided
 * `actionType`, and invokes the provided `metaProvider` if its of a function type to produce
 * the `AnalyticsMeta` required by the `sendAnalytics` dispatch; or if `metaProvider` is a
 * simple string scalar, uses that to create a `metaProvider` which is then used to dispatch to
 * `sendAnalytics`.
 * @param actionType
 * @param metaProvider Either a `ClickStreamActivityEventType`, or a function that takes
 * a payload and current root state, which then produces `AnalyticsMeta` for downstread consumption.
 * @returns
 */
export const createSimpleAnalyticsEpic = <PAYLOAD = void>(
  actionType: string,
  metaProvider: ClickStreamActivityEventType | ((payload?: PAYLOAD, state?: RootState) => AnalyticsMeta),
) => {
  return (actions$: Observable<PayloadAction<PAYLOAD>>, state$: StateObservable<RootState>) =>
    actions$.pipe(
      ofType(actionType),
      withLatestFrom(state$),
      map(([action, state]) => {
        if (typeof metaProvider === "function") {
          const meta = metaProvider(action.payload, state);

          return meta ? sendAnalytics(meta) : analyticsNoop();
        }

        return metaProvider
          ? sendAnalytics({
              eventType: metaProvider,
            })
          : analyticsNoop();
      }),
    );
};

export const analyticsEpics = [analyticsEpic];
