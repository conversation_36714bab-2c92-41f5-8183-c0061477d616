import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useRouter, useSearchParams } from "next/navigation";
import { useDebounceValue } from "usehooks-ts";

import { NavOptions, NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { Routes } from "@Types";

import { useGetArticlesQuery, useSearchArticlesQuery } from "./api/articlesApi";
import { ArticlesScreen } from "./components/ArticlesScreen";
import { useNavigateArticles } from "./hooks/useNavigateArticles";
import { Article, ArticleMediaType } from "./types/articles.types";

const SEARCH_DEBOUNCE_TIME = 500;

export const ArticlesScreenContainer = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { navigateToArticle, navigateToArticleCategory } = useNavigateArticles();
  const searchParams = useSearchParams();
  const searchParamsSearchTerm = searchParams.get("searchTerm");
  const [searchTerm, setSearchTerm] = useState(searchParamsSearchTerm ?? "");
  const [debouncedSearchTerm] = useDebounceValue(searchTerm, SEARCH_DEBOUNCE_TIME);
  const { isLoading: isArticlesLoading, data } = useGetArticlesQuery();
  const {
    isLoading: isSearchLoading,
    isFetching: isSearchFetching,
    data: searchArticles,
  } = useSearchArticlesQuery(debouncedSearchTerm, {
    skip: debouncedSearchTerm === "",
  });
  const isLoading = isArticlesLoading || isSearchLoading || isSearchFetching;

  useEffect(() => {
    dispatch(NavigationStateSlice.actions.setActiveNavOption(NavOptions.ARTICLES));
  }, [dispatch]);

  const onArticleClick = (article: Article, currentScreen: string, articleMediaType: ArticleMediaType) => {
    const originatingScreen = `${currentScreen}${searchTerm !== "" ? `?searchTerm=${searchTerm}` : ""}`;

    navigateToArticle(article, originatingScreen, articleMediaType);
  };

  const onViewAllClick = (categoryId: string) => {
    navigateToArticleCategory(categoryId);
  };

  const onSearch = (term: string) => {
    /** Clear the searchTerm from the query params so we don't accidently end up back on a cleared search */
    if (term === "" && searchParamsSearchTerm !== "") {
      router.replace(Routes.ARTICLES);
    }

    setSearchTerm(term);
  };

  return (
    <ArticlesScreen
      categorizedArticles={data?.categorizedArticles ?? new Map()}
      idToCategoryMap={data?.categoryMap ?? {}}
      searchArticles={debouncedSearchTerm !== "" && searchArticles ? searchArticles : null}
      onArticleClick={onArticleClick}
      onViewAllClick={onViewAllClick}
      onSearch={onSearch}
      searchTerm={searchTerm}
      debouncedSearchTerm={debouncedSearchTerm}
      isLoading={isLoading}
    />
  );
};
