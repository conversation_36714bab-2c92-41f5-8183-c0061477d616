import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { ActionPlanTargetState, ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { skipToken } from "@reduxjs/toolkit/query";
import { useParams, useSearchParams } from "next/navigation";
import { useRouter } from "next/router";

import { NavOptions } from "@Components/Navigation/NavOptions";
import { setActionPlanTargetState } from "@Features/carePlan/store/actionPlansStateSlice";
import { shouldMarkAsDone } from "@Features/carePlan/utils/markAsDone.util";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { useCheckAndUpdateParentActionState } from "@Hooks/useCheckAndUpdateParentActionState";
import { useAppSelector } from "@Store/hooks";
import { Routes } from "@Types";

import { useGetArticleQuery, useGetArticlesQuery } from "../api/articlesApi";
import { ArticleMediaType } from "../types/articles.types";

const getArticleMediaType = (mediaType: string | null): ArticleMediaType => {
  if (mediaType === "pdf") {
    return "pdf";
  }
  if (mediaType === "video") {
    return "video";
  }

  return "image";
};

export const useArticle = () => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const router = useRouter();
  const { article: articleId } = useParams<{ article: string }>();
  const searchParams = useSearchParams();
  const originatingScreen = searchParams.get("originatingScreen");
  const mediaType = getArticleMediaType(searchParams.get("mediaType"));
  const categoryId = searchParams.get("categoryId");
  const [isPdfLoading, setIsPdfLoading] = useState(mediaType === "pdf");

  const { data: articles, isLoading: isArticlesLoading } = useGetArticlesQuery();
  const articleFromArticles =
    !isArticlesLoading && articles
      ? articles?.categorizedArticles.get(categoryId ?? "")?.get(articleId ?? "")
      : undefined;
  const { data: queriedArticle, isLoading: isArticleLoading } = useGetArticleQuery(
    (!isArticlesLoading && articleFromArticles) || !articleId ? skipToken : articleId,
  );
  const article = articleFromArticles || queriedArticle;
  const category = categoryId !== "undefined" && categoryId != null ? categoryId : article?.articleCategoryId;
  const recommendedArticles = [...(articles?.categorizedArticles.get(category ?? "")?.values() ?? [])]
    .filter((article) => article.id !== articleId)
    .slice(0, 6);
  const isLoading = isArticlesLoading || isArticleLoading;

  const driverEntities = useAppSelector((state) => state.actionPlansState.driverEntities);
  const driverEntity = driverEntities[article?.actionId ?? ""];
  const checkAndUpdateParentActionState = useCheckAndUpdateParentActionState();
  const shouldMarkDriverAsCompleted =
    articleId &&
    article &&
    article?.actionId &&
    driverEntity &&
    driverEntity?.state !== ActionPlanTargetState.COMPLETED;

  useEffect(() => {
    dispatch(NavigationStateSlice.actions.setActiveNavOption(NavOptions.ARTICLES));
  }, [dispatch]);

  useEffect(() => {
    if (article?.articleCategory?.toLowerCase() === "recipes") {
      sendEventAnalytics(ClickStreamActivityEventType.CONTENT_VIEWED_RECIPE, {
        articleId,
        title: article.title,
      });
    }

    if (articleId && article) {
      sendEventAnalytics(ClickStreamActivityEventType.ARTICLE_OPENED, {
        articleId,
        title: article.title,
      });
    }
  }, [article, articleId, sendEventAnalytics]);

  const handleOnBack = () => {
    router.push(originatingScreen ?? Routes.ARTICLES);
  };

  const handleOnPlay = () => {
    sendEventAnalytics(ClickStreamActivityEventType.ARTICLE_VIDEO_PLAYED, {
      articleId,
      articleTitle: article?.title ?? "",
    });
  };

  if (shouldMarkDriverAsCompleted) {
    const targetEntityId = driverEntity?.targetId;

    if (shouldMarkAsDone(article.actionId, driverEntities[article.actionId].state)) {
      if (targetEntityId) {
        checkAndUpdateParentActionState(targetEntityId, article.actionId, ActionPlanTargetState.COMPLETED);
      }

      dispatch(
        setActionPlanTargetState({
          targetId: article.actionId,
          newTargetState: ActionPlanTargetState.COMPLETED,
        }),
      );
    }
  }

  const handleIsPdfLoading = (loading: boolean) => {
    setIsPdfLoading(loading);
  };

  return {
    mediaType,
    article,
    articleId,
    isLoading,
    handleOnBack,
    handleOnPlay,
    recommendedArticles,
    isPdfLoading,
    handleIsPdfLoading,
  } as const;
};
