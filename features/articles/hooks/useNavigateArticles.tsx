import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { useRouter } from "next/router";

import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { Routes } from "@Types";

import { Article, ArticleMediaType } from "../types/articles.types";

export const useNavigateArticles = () => {
  const router = useRouter();
  const { sendEventAnalytics } = useAnalyticsHook();

  const navigateToArticle = (article: Article, originatingScreen: string, articleMediaType: ArticleMediaType) => {
    sendEventAnalytics(ClickStreamActivityEventType.ENTER_SCREEN, { articleId: article.id ?? "" });
    router.push(
      `${Routes.ARTICLES}/${article.id}?categoryId=${article.articleCategoryId}&mediaType=${articleMediaType}&originatingScreen=${originatingScreen}`,
    );
  };

  const navigateToArticleCategory = (articleCategory: string) => {
    sendEventAnalytics(ClickStreamActivityEventType.ENTER_SCREEN, { articleCategory });
    router.push(`${Routes.ARTICLES}/category/${articleCategory}`);
  };

  return { navigateToArticle, navigateToArticleCategory } as const;
};
