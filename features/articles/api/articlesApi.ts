import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { getBaseApiUrl } from "@Utils/getBaseApiUrl";

import { Article, GetArticlesResponse } from "../types/articles.types";
import {
  transformArticle,
  transformArticleCatalog,
  transformNewsFeed,
  transformArticles,
} from "../utils/articles.util";
/** Maintain cache for 1 hour as Articles are rarely added/changed */
const ARTICLES_CACHE_TIME = 60 * 60;
/** Maintain cache for 5 minutes for individual articles to not overload the cache with data that will be rarely reutilized */
const ARTICLE_CACHE_TIME = 60 * 5;

export const articlesApi = createApi({
  reducerPath: "articlesApi",
  baseQuery: fetchBaseQuery({
    baseUrl: getBaseApiUrl("ARTICLE_API_URL"),
    prepareHeaders: async (headers) => {
      const firebaseToken = await vivanteCoreContainer.authClient.getValidAccessToken();

      headers.set("content-type", "application/vnd.api+json");
      headers.set("accept", "application/vnd.api+json");
      headers.set("Authorization", `Bearer ${firebaseToken}`);

      return headers;
    },
  }),
  keepUnusedDataFor: ARTICLES_CACHE_TIME,
  endpoints: (builder) => ({
    getArticles: builder.query<GetArticlesResponse, void>({
      query: () =>
        "/Article/?filter[status]=ENABLED&filter[ArticleCategory.status]=ENABLED&fields[Article]=title,body,time,myOrder,by,imageLink,totalLikes,totalDislikes,MyRating,ArticleCategory&fields[ArticleCategory]=title,myOrder&include=MyRating,ArticleCategory&sort=myOrder&page[size]=500",
      transformResponse: transformArticleCatalog,
    }),
    getArticle: builder.query<Article, string>({
      query: (articleId) =>
        `/Article/${articleId}/?filter[status]=ENABLED&include=MyRating,ArticleCategory&fields[Article]=title,body,time,tag,myOrder,imageLink,totalLikes,totalDislikes,ArticleCategory,MyRating`,
      transformResponse: transformArticle,
      keepUnusedDataFor: ARTICLE_CACHE_TIME,
    }),
    getArticleNewsFeed: builder.query<Article[], void>({
      query: () =>
        "/newsfeed/?filter[status]=ENABLED&filter[ArticleCategory.status]=ENABLED&fields[Article]=title,body,time,myOrder,by,imageLink,ArticleCategory,MyRating&fields[ArticleCategory]=title,myOrder&include=ArticleCategory,MyRating&sort=myOrder&page[size]=3",
      transformResponse: transformNewsFeed,
    }),
    searchArticles: builder.query<Article[], string>({
      query: (searchTerm: string) => `/Article/?filter[title]=${searchTerm}`,
      transformResponse: transformArticles,
    }),
  }),
});

export const { useGetArticlesQuery, useGetArticleQuery, useGetArticleNewsFeedQuery, useSearchArticlesQuery } =
  articlesApi;
