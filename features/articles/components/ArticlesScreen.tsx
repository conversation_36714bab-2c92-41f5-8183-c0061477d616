import { Box, IconButton, OutlinedInput, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { Routes } from "@Types";

import { ArticleSearch } from "./ArticleSearch";
import { CategorizedArticles } from "./CategorizedArticles";
import { Article, ArticleMediaType, CategorizedArticlesMap } from "../types/articles.types";
import { determineArticleMediaType } from "../utils/articles.util";

const ARTICLES_STRINGS = appStrings.features.articles;

type ArticlesScreenProps = Readonly<{
  categorizedArticles: CategorizedArticlesMap;
  idToCategoryMap: Record<string, string>;
  searchArticles: Article[] | null;
  onArticleClick: (article: Article, currentScreen: string, articleMediaType: ArticleMediaType) => void;
  onViewAllClick: (category: string) => void;
  onSearch: (searchTerm: string) => void;
  searchTerm: string;
  debouncedSearchTerm: string;
  isLoading: boolean;
}>;

export const ArticlesScreen = ({
  categorizedArticles,
  idToCategoryMap,
  onArticleClick,
  onViewAllClick,
  searchArticles,
  onSearch,
  searchTerm,
  debouncedSearchTerm,
  isLoading,
}: ArticlesScreenProps) => {
  return (
    <Box display="grid" gap={6}>
      <Box display="grid" gap={5}>
        <Typography variant="h1Serif">{ARTICLES_STRINGS.articles}</Typography>

        <OutlinedInput
          value={searchTerm}
          onChange={(e) => onSearch(e.target.value)}
          placeholder={ARTICLES_STRINGS.searchArticles}
          startAdornment={<AppIcon name="Search" color={color.icon.strong} />}
          disabled={isLoading}
          endAdornment={
            searchTerm ? (
              <IconButton sx={{ p: 0 }} onClick={() => onSearch("")} disableRipple>
                <AppIcon name="Close" color={color.icon.strong} />
              </IconButton>
            ) : null
          }
        />
      </Box>

      {searchTerm.length && debouncedSearchTerm.length && searchArticles ? (
        <ArticleSearch
          searchArticles={searchArticles}
          onArticleClick={(article) => onArticleClick(article, Routes.ARTICLES, determineArticleMediaType(article))}
          isLoading={isLoading}
        />
      ) : (
        <CategorizedArticles
          categorizedArticles={categorizedArticles}
          idToCategoryMap={idToCategoryMap}
          onArticleClick={onArticleClick}
          onViewAllClick={onViewAllClick}
          isLoading={isLoading}
        />
      )}
    </Box>
  );
};
