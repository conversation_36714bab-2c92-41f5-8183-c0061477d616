import { useDispatch } from "react-redux";
import { Skeleton, Typography } from "@mui/material";
import { typography } from "@vivantehealth/design-tokens";
import { useParams } from "next/navigation";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { RADIUS_FULL_PX } from "@Assets/style_constants";
import { BackButton } from "@Components/BackButton/BackButton";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { Routes } from "@Types";

import { ArticleGrid } from "./ArticlesGrid";
import { useGetArticlesQuery } from "../api/articlesApi";
import { LOADING_ARTICLES } from "../assets/constants";
import { useNavigateArticles } from "../hooks/useNavigateArticles";
import { Article } from "../types/articles.types";
import { determineArticleMediaType } from "../utils/articles.util";

export const ArticlesCategoryScreen = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { navigateToArticle } = useNavigateArticles();
  const { category: categoryId } = useParams<{ category: string }>();
  const { isLoading, data } = useGetArticlesQuery();
  const category = data?.categoryMap[categoryId];
  const articles = [...(data?.categorizedArticles.get(categoryId)?.values() ?? [])];

  const onArticleClick = (article: Article) => {
    navigateToArticle(article, router.asPath, determineArticleMediaType(article));
  };

  const onBackClick = () =>
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: Routes.ARTICLES,
        screenName: "Articles",
      }),
    );

  return (
    <>
      {isLoading ? (
        <>
          <Skeleton variant="rounded" height="50px" width="185px" sx={{ borderRadius: RADIUS_FULL_PX }} />

          <Skeleton variant="text" height={typography.heading1Serif.lineHeight} width="135px" sx={{ my: 5 }} />
        </>
      ) : (
        <>
          <BackButton onClick={onBackClick}>{appStrings.features.articles.backToArticles}</BackButton>

          <Typography variant="h1Serif" tabIndex={0} my={5}>
            {category}
          </Typography>
        </>
      )}

      <ArticleGrid
        articles={isLoading || articles === undefined ? LOADING_ARTICLES : articles}
        onArticleClick={onArticleClick}
        isLoading={isLoading}
      />
    </>
  );
};
