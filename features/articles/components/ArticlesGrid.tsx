import { Grid } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { EducationCard } from "@Components/EducationCard/EducationCard";

import { Article } from "../types/articles.types";

type ArticleGridProps = Readonly<{
  articles: Article[];
  onArticleClick: (article: Article) => void;
  isLoading?: boolean;
}>;

export const ArticleGrid = ({ articles, onArticleClick, isLoading }: ArticleGridProps) => (
  <Grid container spacing={4} direction="row">
    {articles?.map((article) => {
      const ariaLabel = `${appStrings.a11y.article}. ${
        article?.articleCategory ? `${appStrings.a11y.category(article?.articleCategory)}.` : ""
      } ${appStrings.a11y.title(article.title)}. ${appStrings.a11y.articleTime(
        article.time,
      )}. ${appStrings.a11y.callToAction(appStrings.buttonText.readMore)}.`;

      return (
        <EducationCard
          key={article.id}
          ariaLabel={ariaLabel}
          title={article.title}
          body={article?.body}
          imageSrc={article.imageLink}
          category={article.articleCategory}
          buttonText={appStrings.buttonText.readMore}
          onClick={() => onArticleClick(article)}
          icon="Clock"
          iconText={article.time}
          isSkeletonLoading={isLoading}
        />
      );
    })}
  </Grid>
);
