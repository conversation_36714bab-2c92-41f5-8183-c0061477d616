import React from "react";
import { render, screen } from "@testing-library/react";
import { vi, describe, afterEach, test, expect, beforeAll, afterAll } from "vitest";

import { appStrings } from "@Assets/app_strings";
import { TestWrapper } from "@TestUtils/TestWrapper";

import { ArticleScreen } from "./ArticleScreen";
import { useArticle } from "../hooks/useArticle";
import { MOCK_ARTICLE_RESPONSE } from "../mocks/articles.mocks";
import { transformArticle } from "../utils/articles.util";

const MOCKED_ARTICLE = transformArticle(MOCK_ARTICLE_RESPONSE);

const MOCKED_USE_ARTICLE_RESPONSE: ReturnType<typeof useArticle> = {
  isLoading: false,
  mediaType: "image",
  article: MOCKED_ARTICLE,
  articleId: "123",
  handleOnBack: vi.fn(),
  handleOnPlay: vi.fn(),
  recommendedArticles: [
    { ...MOCKED_ARTICLE, id: "987", title: "Recommended article", body: "Recommended article body" },
  ],
  isPdfLoading: false,
  handleIsPdfLoading: vi.fn(),
};

const mockedUseArticle = vi.fn();

vi.mock("../hooks/useArticle", () => ({
  useArticle: () => mockedUseArticle(),
}));

vi.mock("../hooks/useNavigateArticles", () => ({
  useNavigateArticles: () => ({
    navigateToArticle: vi.fn(),
  }),
}));

vi.mock("next/navigation", async () => {
  const actual = await vi.importActual("next/navigation");

  return {
    ...actual,
    useSearchParams: () => ({
      get: vi.fn(() => "originatingScreen"),
    }),
  };
});

vi.mock("react-player", () => {
  return {
    default: vi.fn(() => <div data-testid="react-player" />),
  };
});

vi.mock("react-pdf", async () => {
  const actual = await vi.importActual("react-pdf");

  return {
    ...actual,
    Document: vi.fn(() => <div data-testid="react-pdf" />),
    Page: vi.fn(() => <div />),
  };
});

describe("ArticleScreen", () => {
  const originalScrollIntoView = window.HTMLElement.prototype.scrollIntoView;

  beforeAll(() => {
    window.HTMLElement.prototype.scrollIntoView = function () {};
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  afterAll(() => {
    window.HTMLElement.prototype.scrollIntoView = originalScrollIntoView;
  });

  test("renders article (video or image) loading skeleton when loading and mediaType is video", () => {
    mockedUseArticle.mockReturnValue({ ...MOCKED_USE_ARTICLE_RESPONSE, isLoading: true, mediaType: "video" });

    const { container } = render(<ArticleScreen />);
    // eslint-disable-next-line testing-library/no-container, testing-library/no-node-access
    const skeletons = container.querySelectorAll(".MuiSkeleton-root");

    // Check for the proper number of skeleton elements
    expect(skeletons.length).toBe(9);
  });

  test("renders pdf article loading skeleton when loading and mediaType is pdf", () => {
    mockedUseArticle.mockReturnValue({ ...MOCKED_USE_ARTICLE_RESPONSE, isLoading: true, mediaType: "pdf" });

    const { container } = render(<ArticleScreen />);
    // eslint-disable-next-line testing-library/no-container, testing-library/no-node-access
    const skeletons = container.querySelectorAll(".MuiSkeleton-root");

    // Check for the proper number of skeleton elements
    expect(skeletons.length).toBe(6);
  });

  test("Renders an article with an image", () => {
    mockedUseArticle.mockReturnValue(MOCKED_USE_ARTICLE_RESPONSE);

    render(<ArticleScreen />);
    // We use the aria-label to find the button
    expect(screen.getByRole("button", { name: "Back." })).toBeInTheDocument();
    expect(screen.getByRole("img", { name: MOCKED_ARTICLE.title })).toBeInTheDocument();
    expect(screen.getByRole("heading", { name: MOCKED_ARTICLE.articleCategory })).toBeInTheDocument();
    expect(screen.getByRole("heading", { name: MOCKED_ARTICLE.time })).toBeInTheDocument();
    expect(screen.getByText(MOCKED_ARTICLE.title)).toBeInTheDocument();
    if (MOCKED_ARTICLE.body) {
      expect(screen.getByText(MOCKED_ARTICLE.body)).toBeInTheDocument();
    }

    expect(screen.getByText(appStrings.features.articles.recommendedArticles)).toBeInTheDocument();
  });

  test("Renders an article with a video", async () => {
    mockedUseArticle.mockReturnValue({
      ...MOCKED_USE_ARTICLE_RESPONSE,
      mediaType: "video",
      article: { ...MOCKED_ARTICLE, title: "Video article", videoLink: "http://example.com/video.mp4" },
    });

    render(<ArticleScreen />);
    // Ensure mocked video player is in DOM while image is not
    expect(screen.getByTestId("react-player")).toBeInTheDocument();
    expect(screen.queryByRole("img", { name: MOCKED_ARTICLE.title })).not.toBeInTheDocument();
    expect(screen.getByRole("heading", { name: MOCKED_ARTICLE.articleCategory })).toBeInTheDocument();
    expect(screen.getByRole("heading", { name: MOCKED_ARTICLE.time })).toBeInTheDocument();
    expect(screen.getByText("Video article")).toBeInTheDocument();
    if (MOCKED_ARTICLE.body) {
      expect(screen.getByText(MOCKED_ARTICLE.body)).toBeInTheDocument();
    }
  });

  test("Renders an article with a pdf", () => {
    mockedUseArticle.mockReturnValue({
      ...MOCKED_USE_ARTICLE_RESPONSE,
      mediaType: "pdf",
      article: { ...MOCKED_ARTICLE, title: "PDF article", pdfLink: "https://example.com/article.pdf" },
    });

    render(<ArticleScreen />, { wrapper: TestWrapper });

    // Ensure mocked pdf player is in DOM while image is not
    expect(screen.getByTestId("react-pdf")).toBeInTheDocument();
    expect(screen.queryByRole("img", { name: MOCKED_ARTICLE.title })).not.toBeInTheDocument();
    expect(screen.getByText(appStrings.features.articles.recommendedArticles)).toBeInTheDocument();
  });

  test("Hide recommended articles if PDF is loading", () => {
    mockedUseArticle.mockReturnValue({
      ...MOCKED_USE_ARTICLE_RESPONSE,
      mediaType: "pdf",
      article: { ...MOCKED_ARTICLE, title: "PDF article", pdfLink: "https://example.com/article.pdf" },
      isPdfLoading: true,
    });

    render(<ArticleScreen />, { wrapper: TestWrapper });
    expect(screen.queryByText(appStrings.features.articles.recommendedArticles)).not.toBeInTheDocument();
  });
});
