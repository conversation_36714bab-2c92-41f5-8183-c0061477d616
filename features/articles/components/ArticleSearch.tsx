import { Box, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

import { ArticleGrid } from "./ArticlesGrid";
import { LOADING_ARTICLES } from "../assets/constants";
import { Article } from "../types/articles.types";

type ArticleSearchProps = Readonly<{
  searchArticles: Article[];
  onArticleClick: (article: Article) => void;
  isLoading: boolean;
}>;

export const ArticleSearch = ({ searchArticles, onArticleClick, isLoading }: ArticleSearchProps) => {
  return searchArticles?.length || isLoading ? (
    <ArticleGrid
      articles={isLoading ? LOADING_ARTICLES : searchArticles}
      onArticleClick={onArticleClick}
      isLoading={isLoading}
    />
  ) : (
    <Box display="flex" minWidth="100%" justifyContent="center">
      <Typography variant="caption">{appStrings.features.articles.searchNoMatches}</Typography>
    </Box>
  );
};
