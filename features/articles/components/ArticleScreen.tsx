import React, { useEffect, useRef } from "react";
import { Box, Typography, Card, CardMedia, useMediaQuery } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import ReactPlayer from "react-player";

import { appStrings } from "@Assets/app_strings";
import { RADIUS_16_PX, SPACING_40_PX } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { BackButton } from "@Components/BackButton/BackButton";
import { linkifyText } from "@Utils/linkify";

import { ArticlePdf } from "./ArticlePdf";
import { ArticleScreenLoadingSkeleton } from "./ArticleScreenLoadingSkeleton";
import { RecommendedArticles } from "./RecommendedArticles";
import { ARTICLE_IMAGE_HEIGHT } from "../assets/constants";
import { useArticle } from "../hooks/useArticle";

const BUTTON_STRINGS = appStrings.buttonText;

// Matches the MUI Card Media Width
const VIDEO_WIDTH = "634px";
const NARROW_SCREEN_VIDEO_WIDTH = "590px";
const VIDEO_RATIO = 0.5625;

export const ArticleScreen = () => {
  const {
    mediaType,
    article,
    articleId,
    isLoading,
    handleOnBack,
    handleOnPlay,
    recommendedArticles,
    isPdfLoading,
    handleIsPdfLoading,
  } = useArticle();
  const isNarrowScreen = useMediaQuery("(max-width:1199px)");
  const videoWidth = isNarrowScreen ? NARROW_SCREEN_VIDEO_WIDTH : VIDEO_WIDTH;
  const videoHeight = Number(videoWidth.slice(0, 3)) * VIDEO_RATIO;
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollIntoView({
        behavior: "instant",
        block: "start",
        inline: "nearest",
      });
    }
  }, [articleId]);

  if (isLoading) {
    return <ArticleScreenLoadingSkeleton articleMediaType={mediaType} />;
  }

  return (
    <>
      <Box ref={containerRef} sx={{ position: "relative", top: `-${SPACING_40_PX}` }} />

      {article?.pdfLink ? (
        <ArticlePdf
          pdfLink={article.pdfLink}
          articleId={articleId}
          articleTitle={article.title}
          handleOnBack={handleOnBack}
          handleIsPdfLoading={handleIsPdfLoading}
        />
      ) : (
        <>
          <BackButton onClick={handleOnBack}>{BUTTON_STRINGS.back}</BackButton>

          <Card sx={{ p: 5, mt: 5 }}>
            <Box display="flex" flexDirection="column" gap={3}>
              {article?.videoLink ? (
                <Box borderRadius={4}>
                  <ReactPlayer
                    style={{
                      borderRadius: RADIUS_16_PX,
                      overflow: "hidden",
                    }}
                    url={article.videoLink}
                    controls
                    width={videoWidth}
                    height={videoHeight}
                    onPlay={handleOnPlay}
                  />
                </Box>
              ) : (
                <CardMedia
                  image={article?.imageLink}
                  title={article?.title}
                  tabIndex={0}
                  sx={{ height: ARTICLE_IMAGE_HEIGHT, borderRadius: 4 }}
                />
              )}

              <Box display="flex" gap={4} pt={1}>
                <Typography variant="h4" color={color.text.subtle}>
                  {article?.articleCategory}
                </Typography>

                <Box display="flex" gap={1}>
                  <AppIcon name="Clock" size="sm" color={color.icon.subtle} />
                  <Typography variant="h4" color={color.text.subtle}>
                    {article?.time}
                  </Typography>
                </Box>
              </Box>

              <Typography variant="h1Serif" tabIndex={0}>
                {article?.title}
              </Typography>

              {article?.body && (
                <Typography variant="body" tabIndex={0} sx={{ pt: 2, wordWrap: "break-word", whiteSpace: "pre-wrap" }}>
                  {linkifyText(article.body)}
                </Typography>
              )}
            </Box>
          </Card>
        </>
      )}

      {recommendedArticles.length > 0 && !isPdfLoading ? (
        <RecommendedArticles key={articleId} recommendedArticles={recommendedArticles} />
      ) : null}
    </>
  );
};
