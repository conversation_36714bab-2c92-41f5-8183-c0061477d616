import { Box, Typography, Button, Skeleton } from "@mui/material";
import { typography } from "@vivantehealth/design-tokens";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { RADIUS_FULL_PX, SPACING_16_PX } from "@Assets/style_constants";
import { Carousel } from "@Components/Carousel/Carousel";
import { EducationCard } from "@Components/EducationCard/EducationCard";

import { ITEMS_TO_SCROLL, LOADING_ARTICLES } from "../assets/constants";
import { Article, ArticleMediaType, CategorizedArticlesMap } from "../types/articles.types";
import { articleEducationCardAriaLabel, determineArticleMediaType } from "../utils/articles.util";

const BUTTON_TEXT = appStrings.buttonText;
/** The Carousel container with padding is 500px and the Buttons are 34px tall, we take half of that to move the buttons into position */
const LEFT_BTN_TOP_OFFSET = 230;
const RIGHT_BTN_TOP_OFFSET = -235;

type ArticleCategoryProps = Readonly<{
  categorizedArticles: CategorizedArticlesMap;
  idToCategoryMap: Record<string, string>;
  onArticleClick: (article: Article, currentScreen: string, articleMediaType: ArticleMediaType) => void;
  onViewAllClick: (category: string) => void;
  isLoading: boolean;
}>;

export const CategorizedArticles = ({
  categorizedArticles,
  idToCategoryMap,
  onViewAllClick,
  onArticleClick,
  isLoading,
}: ArticleCategoryProps) => {
  const router = useRouter();

  return isLoading
    ? LOADING_ARTICLES.map(({ id }) => (
        <Box key={`categorizedArticleSkeleton-${id}`} display="grid" gap={4}>
          <Box display="flex" justifyContent="space-between" alignItems="center" p={0} mb={`-${SPACING_16_PX}`}>
            <Skeleton variant="text" height={typography.heading2Serif.lineHeight} width={"115px"} />

            <Skeleton variant="rounded" height="35px" width="80px" sx={{ borderRadius: RADIUS_FULL_PX }} />
          </Box>

          <Carousel
            numberOfItemsToScroll={ITEMS_TO_SCROLL}
            leftBtnOffset={LEFT_BTN_TOP_OFFSET}
            rightBtnOffset={RIGHT_BTN_TOP_OFFSET}
            isLoading
          >
            <Box display="flex" gap={4}>
              {[...LOADING_ARTICLES, { ...LOADING_ARTICLES[0], id: "loading-3" }].map((article) => (
                <EducationCard
                  key={article.id}
                  {...article}
                  imageSrc=""
                  ariaLabel=""
                  buttonText=""
                  isSkeletonLoading
                  isContainedInCarousel
                />
              ))}
            </Box>
          </Carousel>
        </Box>
      ))
    : [...categorizedArticles.entries()]?.map(([category, articles]) => (
        <Box key={category} display="grid" gap={4}>
          <Box display="flex" justifyContent="space-between" alignItems="center" p={0} mb={`-${SPACING_16_PX}`}>
            <Typography variant="h2Serif">{idToCategoryMap[category]}</Typography>

            {articles?.size > 2 && (
              <Button
                variant="secondary"
                size="small"
                aria-label={`${appStrings.a11y.callToAction(BUTTON_TEXT.viewAll)}.`}
                onClick={() => onViewAllClick(category)}
              >
                {BUTTON_TEXT.viewAll}
              </Button>
            )}
          </Box>

          <Carousel
            numberOfItemsToScroll={ITEMS_TO_SCROLL}
            leftBtnOffset={LEFT_BTN_TOP_OFFSET}
            rightBtnOffset={RIGHT_BTN_TOP_OFFSET}
            isLoading={isLoading}
          >
            <Box display="flex" gap={4}>
              {[...articles.values()].slice(0, 6).map((article) => (
                <EducationCard
                  key={article.id}
                  ariaLabel={articleEducationCardAriaLabel(article)}
                  title={article.title}
                  body={article?.body}
                  imageSrc={article.imageLink}
                  category={article.articleCategory}
                  buttonText={appStrings.buttonText.readMore}
                  onClick={() => onArticleClick(article, router.asPath, determineArticleMediaType(article))}
                  icon="Clock"
                  iconText={article.time}
                  isSkeletonLoading={isLoading}
                  isContainedInCarousel
                />
              ))}
            </Box>
          </Carousel>
        </Box>
      ));
};
