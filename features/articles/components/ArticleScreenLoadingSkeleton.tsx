import React from "react";
import { Box, Card, Skeleton } from "@mui/material";
import { typography } from "@vivantehealth/design-tokens";

import { RADIUS_FULL_PX } from "@Assets/style_constants";

import { ARTICLE_IMAGE_HEIGHT, ARTICLE_PDF_WIDTH } from "../assets/constants";
import { ArticleMediaType } from "../types/articles.types";

export const ArticleScreenLoadingSkeleton = ({ articleMediaType }: { articleMediaType: ArticleMediaType }) => {
  return articleMediaType === "pdf" ? (
    <>
      <Box display="flex" justifyContent="space-between" alignItems="center" width={ARTICLE_PDF_WIDTH}>
        <Skeleton variant="rounded" height="50px" width="115px" sx={{ borderRadius: RADIUS_FULL_PX }} />

        <Skeleton variant="rounded" height="50px" width="260px" sx={{ borderRadius: RADIUS_FULL_PX }} />
      </Box>

      <Box mt={5}>
        <Skeleton variant="rectangular" width={ARTICLE_PDF_WIDTH} height={950} sx={{ borderRadius: 4 }} />

        <Box display="flex" justifyContent="space-between" alignItems="center" mt={5} width={ARTICLE_PDF_WIDTH}>
          <Skeleton variant="rounded" height="50px" width="215px" sx={{ borderRadius: RADIUS_FULL_PX }} />

          <Skeleton variant="text" width={75} sx={{ lineHeight: typography.body.lineHeight }} />

          <Skeleton variant="rounded" height="50px" width="215px" sx={{ borderRadius: RADIUS_FULL_PX }} />
        </Box>
      </Box>
    </>
  ) : (
    <>
      <Skeleton variant="rounded" height="50px" width="115px" sx={{ borderRadius: RADIUS_FULL_PX }} />

      <Card sx={{ p: 5, mt: 5 }}>
        <Box display="flex" flexDirection="column" gap={3}>
          <Skeleton variant="rounded" sx={{ height: ARTICLE_IMAGE_HEIGHT, borderRadius: 4 }} />

          <Box display="flex" gap={4} pt={1}>
            <Skeleton variant="text" height={typography.heading4.lineHeight} width={"55px"} />

            <Box display="flex" gap={1}>
              <Skeleton variant="circular" height="20px" width="20px" />
              <Skeleton variant="text" height={typography.heading4.lineHeight} width="30px" />
            </Box>
          </Box>

          <Skeleton variant="text" height={typography.heading1Serif.lineHeight} width="250px" />

          <Box>
            <Skeleton variant="text" height={typography.body.lineHeight} width="100%" />
            <Skeleton variant="text" height={typography.body.lineHeight} width="100%" />
            <Skeleton variant="text" height={typography.body.lineHeight} width="100%" />
          </Box>
        </Box>
      </Card>
    </>
  );
};
