import { useRef, useState } from "react";
import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { Box, Typography, ButtonGroup, Button, Paper, Skeleton } from "@mui/material";
import * as Sentry from "@sentry/nextjs";
import FileSaver from "file-saver";
import { Document, Page } from "react-pdf";
import { OnDocumentLoadSuccess, OnError } from "react-pdf/dist/cjs/shared/types";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { BackButton } from "@Components/BackButton/BackButton";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { ARTICLE_PDF_WIDTH } from "../assets/constants";
import "pdfjs-dist/build/pdf.worker.min.mjs";

const BUTTON_STRINGS = appStrings.buttonText;
const ARTICLE_STRINGS = appStrings.features.articles.pdf;
const PAGINATION_BTN_WIDTH = "212px";
// Width defined in Figma https://www.figma.com/design/8tkrRyyha0TKlyV8ZUfICc/Rebrand-Phase-1---Product-UI?node-id=8195-79902&m=dev
const ERROR_WIDTH = "448px";

type ArticlePdfProps = Readonly<{
  handleOnBack: () => void;
  pdfLink: string;
  articleId: string;
  articleTitle: string;
  handleIsPdfLoading: (loading: boolean) => void;
}>;

export const ArticlePdf = ({ handleOnBack, pdfLink, articleId, articleTitle, handleIsPdfLoading }: ArticlePdfProps) => {
  const [numberOfPages, setNumberOfPages] = useState<number>();
  const [pageNumber, setPageNumber] = useState(1);
  const [pdf, setPdf] = useState<string>();
  const [displayError, setDisplayError] = useState(false);
  // Ref to the iframe for printing which is used to allow the user to print the PDF
  const iframeRef = useRef<HTMLIFrameElement | null>(null);
  const { sendEventAnalytics } = useAnalyticsHook();

  const handleDocumentLoadedSuccessfully: OnDocumentLoadSuccess = async (document) => {
    const pdfData = await document.getData();
    const pdfBlob = new Blob([pdfData], { type: "application/pdf" });

    setPdf(URL.createObjectURL(pdfBlob));
    setNumberOfPages(document.numPages);
    handleIsPdfLoading(false);
  };

  const handleDocumentLoadFailed: OnError = (error) => {
    setDisplayError(true);
    handleIsPdfLoading(false);

    Sentry.captureException(error);
  };

  const handlePagination = (paginate: "NEXT" | "PREVIOUS") => {
    if (paginate === "NEXT") {
      return setPageNumber((prevPageNumber) => prevPageNumber + 1);
    }

    return setPageNumber((prevPageNumber) => prevPageNumber - 1);
  };

  const handleFileSave = () => {
    sendEventAnalytics(ClickStreamActivityEventType.ARTICLE_PDF_DOWNLOADED, {
      articleId,
      articleTitle,
    });

    FileSaver.saveAs(pdfLink, articleTitle);
  };

  const handleFilePrint = () => {
    sendEventAnalytics(ClickStreamActivityEventType.ARTICLE_PDF_PRINTED, {
      articleId,
      articleTitle,
    });

    iframeRef.current?.contentWindow?.print();
  };

  return (
    <>
      {displayError ? (
        <>
          <BackButton onClick={handleOnBack}>{BUTTON_STRINGS.back}</BackButton>

          <Paper sx={{ mt: 5, width: ERROR_WIDTH }}>
            <Typography variant="h3">{ARTICLE_STRINGS.loadingError.header}</Typography>

            <Typography variant="body" mt={2} mb={5}>
              {ARTICLE_STRINGS.loadingError.subHeader}
            </Typography>

            <Button variant="primary" onClick={() => window.location.reload()} fullWidth>
              {ARTICLE_STRINGS.loadingError.buttonText}
            </Button>
          </Paper>
        </>
      ) : (
        <>
          <Box display="flex" justifyContent="space-between" alignItems="center" width={ARTICLE_PDF_WIDTH}>
            <BackButton onClick={handleOnBack}>{BUTTON_STRINGS.back}</BackButton>

            {pdf ? (
              <ButtonGroup>
                <Button variant="secondary" onClick={handleFileSave} startIcon={<AppIcon name="Download" />}>
                  {ARTICLE_STRINGS.download}
                </Button>

                <Button variant="secondary" onClick={handleFilePrint} startIcon={<AppIcon name="Printer" />}>
                  {ARTICLE_STRINGS.print}
                </Button>
              </ButtonGroup>
            ) : null}
          </Box>

          <Box mt={5}>
            <Document
              file={pdfLink}
              onLoadSuccess={handleDocumentLoadedSuccessfully}
              onLoadError={handleDocumentLoadFailed}
              loading={() => (
                <Skeleton variant="rectangular" width={ARTICLE_PDF_WIDTH} height={950} sx={{ borderRadius: 4 }} />
              )}
            >
              <Page pageNumber={pageNumber} width={ARTICLE_PDF_WIDTH} />
            </Document>

            {pdf ? (
              <Box display="flex" justifyContent="space-between" alignItems="center" mt={5} width={ARTICLE_PDF_WIDTH}>
                <Button
                  variant="secondary"
                  sx={{ width: PAGINATION_BTN_WIDTH }}
                  onClick={() => handlePagination("PREVIOUS")}
                  disabled={pageNumber === 1}
                >
                  {BUTTON_STRINGS.previous}
                </Button>

                <Typography variant="body">{`Page ${pageNumber} of ${numberOfPages}`}</Typography>

                <Button
                  variant="primary"
                  sx={{ width: PAGINATION_BTN_WIDTH }}
                  onClick={() => handlePagination("NEXT")}
                  disabled={pageNumber === numberOfPages}
                >
                  {BUTTON_STRINGS.next}
                </Button>
              </Box>
            ) : null}

            {pdf ? <iframe ref={iframeRef} src={pdf} style={{ display: "none" }} /> : null}
          </Box>
        </>
      )}
    </>
  );
};
