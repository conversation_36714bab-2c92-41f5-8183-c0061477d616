import { fireEvent, render, screen } from "@testing-library/react";
import { vi, describe, test, expect } from "vitest";

import { appStrings } from "@Assets/app_strings";
import { mockedNextRouter } from "@TestUtils/mockedNextRouter.util";

import { CategorizedArticles } from "./CategorizedArticles";
import { MOCK_ARTICLES_RESPONSE } from "../mocks/articles.mocks";
import { transformArticleCatalog } from "../utils/articles.util";

const mockedOnArticleClick = vi.fn();
const mockedOnViewAllClick = vi.fn();
const articleCatalog = transformArticleCatalog(MOCK_ARTICLES_RESPONSE);
// Assigning the mocked router to a variable to import it in the test
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockedRouter = mockedNextRouter;

describe("CategorizedArticles", () => {
  test("Should show loading skeleton when loading including the Carousel", () => {
    const { container } = render(
      <CategorizedArticles
        categorizedArticles={articleCatalog.categorizedArticles}
        idToCategoryMap={articleCatalog.categoryMap}
        onArticleClick={mockedOnArticleClick}
        onViewAllClick={mockedOnViewAllClick}
        isLoading
      />,
    );
    // eslint-disable-next-line testing-library/no-container, testing-library/no-node-access
    const skeletons = container.querySelectorAll(".MuiSkeleton-root");

    // Check for the proper number of skeleton elements
    expect(skeletons.length).toBe(50);
  });

  test("Should render article categories and articles and articles are clickable", () => {
    const testArticle = articleCatalog.categorizedArticles
      .get("0643569a-dfef-4180-91e4-0f58566ecb83")
      ?.get("45c0c196-8dbc-4d18-a7b9-70ad3d9c214e");

    render(
      <CategorizedArticles
        categorizedArticles={articleCatalog.categorizedArticles}
        idToCategoryMap={articleCatalog.categoryMap}
        onArticleClick={mockedOnArticleClick}
        onViewAllClick={mockedOnViewAllClick}
        isLoading={false}
      />,
    );
    // Heading and 2 articles
    expect(screen.getAllByText("Lifestyle")).toHaveLength(3);
    // Heading and 2 articles
    expect(screen.getAllByText("Nutrition")).toHaveLength(4);
    // Heading and 1 articles
    expect(screen.getAllByText("Medicine")).toHaveLength(2);
    // Check if the articles are clickable
    fireEvent.click(screen.getByText(testArticle?.title ?? ""));
    expect(mockedOnArticleClick).toHaveBeenCalledWith(testArticle, "/path?query=string", "image");
    // Verify the view All button is present as Nutrition has more than 2 articles
    const viewAllButton = screen.getByRole("button", { name: "Call To Action: View all." });

    expect(viewAllButton).toBeInTheDocument();

    fireEvent.click(viewAllButton);
    expect(mockedOnViewAllClick).toHaveBeenCalledWith("0643569a-dfef-4180-91e4-0f58566ecb83");

    // Verify the carousel is present by checking for the prev/next buttons (one set per category) and that by default prev is disabled while next is enabled
    const previousBtns = screen.getAllByRole("button", { name: appStrings.buttonText.previous });
    const nextBtns = screen.getAllByRole("button", { name: appStrings.buttonText.next });

    expect(previousBtns).toHaveLength(3);
    expect(nextBtns).toHaveLength(3);

    for (const btn of previousBtns) {
      expect(btn).toBeDisabled();
    }
    for (const btn of nextBtns) {
      expect(btn).toBeEnabled();
    }
  });
});
