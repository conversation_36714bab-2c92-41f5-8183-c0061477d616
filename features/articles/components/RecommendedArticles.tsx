import { Box, Typography } from "@mui/material";
import { useSearchParams } from "next/navigation";

import { appStrings } from "@Assets/app_strings";
import { Carousel } from "@Components/Carousel/Carousel";
import { EducationCard } from "@Components/EducationCard/EducationCard";
import { Routes } from "@Types";

import { ITEMS_TO_SCROLL } from "../assets/constants";
import { useNavigateArticles } from "../hooks/useNavigateArticles";
import { Article } from "../types/articles.types";
import { articleEducationCardAriaLabel, determineArticleMediaType } from "../utils/articles.util";

/** The Carousel container is 400px and the Buttons are 34px tall, we take half of that to move the buttons into position */
const BUTTONS_TOP_OFFSET = 217;

type RecommendedArticlesProps = Readonly<{
  recommendedArticles: Article[];
}>;

export const RecommendedArticles = ({ recommendedArticles }: RecommendedArticlesProps) => {
  const { navigateToArticle } = useNavigateArticles();
  const searchParams = useSearchParams();
  const originatingScreen = searchParams.get("originatingScreen");

  return (
    <Box mt={11}>
      <Typography variant="h2Serif" mb={4}>
        {appStrings.features.articles.recommendedArticles}
      </Typography>

      <Carousel
        numberOfItemsToScroll={ITEMS_TO_SCROLL}
        leftBtnOffset={BUTTONS_TOP_OFFSET}
        rightBtnOffset={-BUTTONS_TOP_OFFSET}
        isLoading={false}
      >
        <Box display="flex" gap={4}>
          {recommendedArticles.map((article) => (
            <EducationCard
              key={article.id}
              ariaLabel={articleEducationCardAriaLabel(article)}
              title={article.title}
              body={article?.body}
              imageSrc={article.imageLink}
              category={article.articleCategory}
              buttonText={appStrings.buttonText.readMore}
              onClick={() =>
                navigateToArticle(article, originatingScreen ?? Routes.ARTICLES, determineArticleMediaType(article))
              }
              icon="Clock"
              iconText={article.time}
              isContainedInCarousel
            />
          ))}
        </Box>
      </Carousel>
    </Box>
  );
};
