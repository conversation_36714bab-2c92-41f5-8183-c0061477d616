import { appStrings } from "@Assets/app_strings";

import {
  ArticlesResponse,
  GetArticlesResponse,
  ArticleResponse,
  Article,
  ArticleMediaType,
  CategorizedArticlesMap,
} from "../types/articles.types";

const sortCallback = (orderA: number, orderB: number, order: "asc" | "desc") => {
  if (orderA < orderB) {
    return order === "asc" ? -1 : 1;
  }

  if (orderA > orderB) {
    return order === "asc" ? 1 : -1;
  }

  return 0;
};

/**
 * Sorts categories by provided order and maps articles to their respective categories.
 */
export const transformArticleCatalog = (response: ArticlesResponse): GetArticlesResponse => {
  const { data, included } = response;
  const articleToCategoryMap: CategorizedArticlesMap = new Map();
  const idToCategoryMap: Record<string, string> = {};

  [...included]
    .sort(({ attributes: { myOrder: myOrderA } }, { attributes: { myOrder: myOrderB } }) =>
      sortCallback(myOrderA, myOrderB, "asc"),
    )
    .forEach(({ attributes, id }) => {
      articleToCategoryMap.set(id, new Map());
      idToCategoryMap[id] = attributes.title;
    });

  data.forEach(({ attributes: article, id, relationships }) => {
    const categoryId = relationships.ArticleCategory.data.id;

    articleToCategoryMap
      .get(categoryId)
      ?.set(id, { ...article, id, articleCategory: idToCategoryMap[categoryId], articleCategoryId: categoryId });
  });

  return { categorizedArticles: articleToCategoryMap, categoryMap: idToCategoryMap };
};

export const transformArticle = (response: ArticleResponse) => {
  const { data, included } = response;

  return {
    ...data.attributes,
    articleCategory: included[0].attributes.title,
    articleCategoryId: data.relationships.ArticleCategory.data.id,
  };
};

export const transformArticles = (response: ArticlesResponse) => {
  const { data, included } = response;

  return data.map(({ attributes, id, relationships }) => {
    const articleCategoryId = relationships.ArticleCategory.data.id;
    const categoryTitle = included.find((category) => category.id === articleCategoryId)?.attributes?.title;

    return { ...attributes, id, articleCategory: categoryTitle, articleCategoryId };
  });
};

export const transformNewsFeed = (response: ArticlesResponse) => {
  const articles = transformArticles(response);
  const sortedArticles = [...articles].sort(({ myOrder: myOrderA }, { myOrder: myOrderB }) =>
    sortCallback(myOrderA ?? 0, myOrderB ?? 0, "asc"),
  );

  return sortedArticles;
};

export const determineArticleMediaType = (article: Article): ArticleMediaType => {
  if (article.pdfLink) {
    return "pdf";
  }

  if (article.videoLink) {
    return "video";
  }

  return "image";
};

export const articleEducationCardAriaLabel = (article: Article) =>
  `${appStrings.a11y.article}. ${
    article?.articleCategory ? `${appStrings.a11y.category(article?.articleCategory)}.` : ""
  } ${appStrings.a11y.title(article.title)}. ${appStrings.a11y.articleTime(
    article.time,
  )}. ${appStrings.a11y.callToAction(appStrings.buttonText.readMore)}.`;
