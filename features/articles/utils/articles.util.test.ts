import { describe, expect, test } from "vitest";

import {
  articleEducationCardAriaLabel,
  determineArticleMediaType,
  transformArticle,
  transformArticleCatalog,
  transformArticles,
  transformNewsFeed,
} from "./articles.util";
import {
  MOCK_ARTICLES_RESPONSE,
  MOCK_ARTICLE_RESPONSE,
  MOCKED_CATEGORIES,
  MOCKED_SEARCH_ARTICLES,
  MOCKED_NEWSFEED_ARTICLES,
} from "../mocks/articles.mocks";

describe("Articles transform helper functions", () => {
  describe("transformArticleCatalog", () => {
    const result = transformArticleCatalog(MOCK_ARTICLES_RESPONSE);

    test("Should categorize articles by their respective category id", () => {
      expect(result.categorizedArticles.size).toBe(3);
      expect(result.categorizedArticles.get("2f9d169a-8e72-4470-9b9a-e241584c1be2")?.size).toBe(2);
      expect(result.categorizedArticles.get("0ee4db8a-a924-4062-a71b-1f38110f2fae")?.size).toBe(1);
      expect(result.categorizedArticles.get("0643569a-dfef-4180-91e4-0f58566ecb83")?.size).toBe(3);
    });

    test("Should return a map of category ids to category titles", () => {
      expect(result.categoryMap).toMatchObject({
        "2f9d169a-8e72-4470-9b9a-e241584c1be2": "Lifestyle",
        "0ee4db8a-a924-4062-a71b-1f38110f2fae": "Medicine",
        "0643569a-dfef-4180-91e4-0f58566ecb83": "Nutrition",
      });
    });

    test("Should return categories in ascending order", () => {
      const expectedOrder = [...MOCKED_CATEGORIES].sort((a, b) => a.attributes.myOrder - b.attributes.myOrder);
      const categoryArray = Array.from(result.categorizedArticles.keys());

      for (let categoryIdx = 0; categoryIdx < categoryArray.length; categoryIdx += 1) {
        expect(categoryArray[categoryIdx]).toBe(expectedOrder[categoryIdx].id);
      }
    });

    test("Should return categorized articles in Maps", () => {
      expect(
        result.categorizedArticles
          .get("2f9d169a-8e72-4470-9b9a-e241584c1be2")
          ?.get("e61684e7-1d6d-4644-b7c8-39667d1f5fbe"),
      ).toMatchObject({
        body: "Testing article creation",
        by: "tester",
        imageLink:
          "https://imagedelivery.dev.vivantehealth.com/absolute?image=https%3A%2F%2Fstorage.googleapis.com/cylinderhealth-media-assets-ea9d%2Farticles-images%2FAre-Food-Allergies-On-The-Rise.jpg&client_info=",
        myOrder: 0,
        time: "1min",
        title: "A test article on 2023-11-30",
        id: "e61684e7-1d6d-4644-b7c8-39667d1f5fbe",
        articleCategoryId: "2f9d169a-8e72-4470-9b9a-e241584c1be2",
        articleCategory: "Lifestyle",
      });
    });

    test("Should return an empty map if no articles are found", () => {
      const result = transformArticleCatalog({ data: [], included: [] });

      expect(result.categorizedArticles.size).toBe(0);
      expect(result.categoryMap).toMatchObject({});
    });
  });

  describe("transformArticle", () => {
    test("Should return an article with its respective category", () => {
      const result = transformArticle(MOCK_ARTICLE_RESPONSE);

      expect(result).toMatchObject({
        body: "Testing article creation",
        by: "tester",
        imageLink:
          "https://imagedelivery.dev.vivantehealth.com/absolute?image=https%3A%2F%2Fstorage.googleapis.com/cylinderhealth-media-assets-ea9d%2Farticles-images%2FAre-Food-Allergies-On-The-Rise.jpg&client_info=",
        myOrder: 0,
        time: "1min",
        title: "A test article on 2023-11-30",
        articleCategory: "Lifestyle",
      });
    });
  });

  describe("transformArticles", () => {
    test("Should return an array of articles with their respective category", () => {
      const result = transformArticles(MOCKED_SEARCH_ARTICLES);

      expect(result).toHaveLength(3);
      expect(result[0].articleCategory).toBe("Lifestyle");
      expect(result[1].articleCategory).toBe("Medicine");
      expect(result[2].articleCategory).toBe("Medicine");
    });

    test("Should return an empty array if no articles are found", () => {
      const result = transformArticles({ data: [], included: [] });

      expect(result).toHaveLength(0);
    });
  });

  describe("transformNewsFeed", () => {
    test("Should return an array of articles sorted by myOrder in ascending order", () => {
      const result = transformNewsFeed(MOCKED_NEWSFEED_ARTICLES);

      expect(result).toHaveLength(3);
      expect(result[0].myOrder).toBe(0);
      expect(result[2].myOrder).toBe(70);
    });

    test("Should determine the correct media type for an article", () => {
      const baseArticle = transformArticle(MOCK_ARTICLE_RESPONSE);

      expect(determineArticleMediaType({ ...baseArticle, pdfLink: "http://example.com/sample.pdf" })).toBe("pdf");
      expect(determineArticleMediaType({ ...baseArticle, videoLink: "http://example.com/sample.mp4" })).toBe("video");
      expect(determineArticleMediaType(baseArticle)).toBe("image");
    });

    describe("articleEducationCardAriaLabel", () => {
      test("Should generate the correct aria-label for an article with a category", () => {
        const article = {
          title: "A test article",
          articleCategory: "Lifestyle",
          time: "1min",
          imageLink: "",
          body: "",
          by: "",
        };

        const result = articleEducationCardAriaLabel(article);

        expect(result).toBe(
          "Article. Category: Lifestyle. Title: A test article. Length of time to read article: 1min. Call To Action: Read more.",
        );
      });
    });
  });
});
