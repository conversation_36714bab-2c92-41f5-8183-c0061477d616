export type Article = Readonly<{
  id?: string;
  title: string;
  articleCategory?: string;
  articleCategoryId?: string;
  imageLink: string;
  /** If videoLink is provided, than we use the imageLink as the thumbnail image but videoLink as the media within the article */
  videoLink?: string;
  /** If pdfLink is provided, than we use the imageLink as the thumbnail image but pdfLink as the media within the article */
  pdfLink?: string;
  actionId?: string;
  body?: string;
  time: string;
  by: string;
  myOrder?: number;
}>;

type ArticleRelationship = { ArticleCategory: { data: { id: string; type: string } }; MyRating: { data: null } };

type Category = Readonly<{
  attributes: { myOrder: number; title: string };
  id: string;
  type: string;
}>;

/** Map<ArticleCategory, Map<ArticleId, Article>> */
export type CategorizedArticlesMap = Map<string, Map<string, Article>>;

export type ArticlesResponse = {
  data: {
    attributes: Article;
    id: string;
    type: string;
    relationships: ArticleRelationship;
  }[];
  included: Category[];
};

export type ArticleResponse = {
  data: {
    attributes: Article;
    id: string;
    type: string;
    relationships: ArticleRelationship;
  };
  included: Category[];
};

export type GetArticlesResponse = { categorizedArticles: CategorizedArticlesMap; categoryMap: Record<string, string> };

export type ArticleMediaType = "pdf" | "video" | "image";
