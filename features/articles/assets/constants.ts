import { Article } from "../types/articles.types";

export const LOADING_ARTICLES: Article[] = [
  {
    id: "loading-1",
    title: "Loading...",
    imageLink: "",
    articleCategory: "",
    time: "",
    by: "",
  },
  {
    id: "loading-2",
    title: "Loading...",
    imageLink: "",
    articleCategory: "",
    time: "",
    by: "",
  },
];

// Width defined in Figma https://www.figma.com/design/8tkrRyyha0TKlyV8ZUfICc/Rebrand-Phase-1---Product-UI?node-id=8089-18493&t=ZTc5vY461XydoHdV-4
export const ARTICLE_PDF_WIDTH = 732;
export const ARTICLE_IMAGE_HEIGHT = "312px";
/** Number of carousel items to scroll at a time */
export const ITEMS_TO_SCROLL = 2;
