import { PayloadAction, createSlice } from "@reduxjs/toolkit";

import { RootState } from "@Store/store";

/// //////////////////////////////////////////////////////
/// state

export type ErrorMeta = Readonly<{
  message: string;
  title?: string;
  buttonText?: string;
  // Allow for custom button click handling. The error modal will still call its onClose action as well
  onButtonClick?: () => void;
}>;

export interface ErrorState {
  error: ErrorMeta | null;
}

export const initialState: ErrorState = {
  error: null,
};

/// //////////////////////////////////////////////////////
/// slice

export const ErrorStateSlice = createSlice({
  name: "errorState",
  initialState,
  reducers: {
    setError: (state: ErrorState, action: PayloadAction<ErrorMeta>) => ({
      ...state,
      error: action.payload,
    }),
    clearError: () => initialState,
  },
});

/// //////////////////////////////////////////////////////
/// selectors

export const selectError = (state: RootState) => state.errorState.error;

export const errorStateReducer = ErrorStateSlice.reducer;
