import { SnackbarToggleState } from "@Features/snackbar/store/snackbarStateSlice";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";
import { store } from "@Store/store";

import { ErrorMeta, ErrorStateSlice } from "../store/errorStateSlice";

export const errorDispatcher = (error: ErrorMeta) => {
  setTimeout(() => {
    store.dispatch(ErrorStateSlice.actions.setError(error));
  }, 1);
};

export const errorSnackbarDispatcher = (options: SnackbarToggleState) => {
  setTimeout(() => {
    store.dispatch(SnackbarStateSlice.actions.toggleSnackbar(options));
  }, 1);
};
