import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ClickStreamActivityEventType, SymptomGroupHistoryItem } from "@vivantehealth/vivante-core";
import { startOfMonth, endOfMonth } from "date-fns";

import { NavOptions, NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { progressStateSelector, ProgressStateSlice } from "@Features/progress/store/progressStateSlice";
import {
  symptomLoggingStateSelector,
  SymptomLoggingStateSlice,
} from "@Features/symptomLogging/store/symptomLoggingStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { ProgressScreen } from "./components/ProgressScreen";
import { useMonthDebounce } from "./hooks/useMonthDebounce";

const staticDate = new Date();

export const ProgressScreenContainer = () => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const symptomScores = useSelector(progressStateSelector("symptomScores"));
  const history = useSelector(progressStateSelector("history"));
  const isLoading = useSelector(progressStateSelector("loadState")) === "loading";
  const currentDayStateValue = useSelector(progressStateSelector("currentDate"));
  const [currentDate, setCurrentDate] = useState(currentDayStateValue ?? staticDate);
  const { handleMonthChange, isMonthLoading } = useMonthDebounce(symptomScores);
  const { prepareCreateSymptomLogQuestion, prepareEditSymptomLogQuestion } = SymptomLoggingStateSlice.actions;
  const { loadDayData, loadProgress, deleteSymptomLog, updateCurrentDay } = ProgressStateSlice.actions;

  useEffect(() => {
    if (currentDayStateValue !== null && currentDayStateValue !== currentDate) {
      setCurrentDate(currentDayStateValue);
    }
  }, [currentDate, currentDayStateValue]);

  useEffect(() => {
    sendEventAnalytics(ClickStreamActivityEventType.CONTENT_VIEWED_PROGRESS);
    // Disable eslint warning for this line because we only want this to run on initial render
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    dispatch(
      loadProgress({
        startingDate: startOfMonth(currentDate),
        endingDate: endOfMonth(currentDate),
        currentDate,
      }),
    );
    dispatch(NavigationStateSlice.actions.setActiveNavOption(NavOptions.PROGRESS));
    return () => {
      dispatch(updateCurrentDay(staticDate)); // reset the day data if user navigates away
    };
  }, [dispatch, loadProgress, currentDate, updateCurrentDay]);

  const handleDayClick = (newDay: Date) => {
    setCurrentDate(newDay);
    dispatch(loadDayData(newDay));
  };

  const deleteSymptomLogCallback = (symptomGroupHistoryItem: SymptomGroupHistoryItem) => {
    dispatch(deleteSymptomLog(symptomGroupHistoryItem));
  };

  const createSymptomLogCallback = useSelector(symptomLoggingStateSelector("symptomLogQuestion"))
    ? () => {
        dispatch(prepareCreateSymptomLogQuestion());
      }
    : undefined;

  const editSymptomLogCallback = (symptomGroupHistoryItem: SymptomGroupHistoryItem) => {
    dispatch(prepareEditSymptomLogQuestion(symptomGroupHistoryItem));
  };

  return (
    <ProgressScreen
      currentDate={currentDate}
      symptomScores={symptomScores}
      handleMonthClick={handleMonthChange}
      handleDayClick={handleDayClick}
      history={history}
      deleteSymptomLogCallback={deleteSymptomLogCallback}
      createSymptomLogCallback={createSymptomLogCallback}
      editSymptomLogCallback={editSymptomLogCallback}
      isLoading={isLoading || isMonthLoading}
      isSymptomScoresLoading={isMonthLoading}
    />
  );
};
