import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { SymptomScore } from "@vivantehealth/vivante-core";
import { startOfMonth, endOfMonth } from "date-fns";
import { useDebounceValue } from "usehooks-ts";

import { ProgressStateSlice } from "@Features/progress/store/progressStateSlice";

export const useMonthDebounce = (symptomScores: Record<string, SymptomScore>) => {
  const dispatch = useDispatch();
  const [isMonthLoading, setIsMonthLoading] = useState(false);
  const [debouncedDate, setNewDate] = useDebounceValue<Date | null>(null, 500);
  const { loadSymptomScoresOfSelectedDay } = ProgressStateSlice.actions;

  const handleMonthChange = (date: Date) => {
    setNewDate(date);
    setIsMonthLoading(true);
  };

  useEffect(() => {
    if (debouncedDate) {
      dispatch(
        loadSymptomScoresOfSelectedDay({
          startingDate: startOfMonth(debouncedDate),
          endingDate: endOfMonth(debouncedDate),
        }),
      );
    }
  }, [debouncedDate, dispatch, loadSymptomScoresOfSelectedDay]);

  // we need to set the loading state to false only after the symptomScores are updated
  useEffect(() => {
    setIsMonthLoading(false);
  }, [symptomScores]);

  return { handleMonthChange, isMonthLoading };
};
