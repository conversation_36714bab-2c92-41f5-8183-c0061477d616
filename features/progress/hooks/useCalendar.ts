import { useEffect, useState } from "react";
import dayjs, { Dayjs } from "dayjs";

type UseCalendarArguments = Readonly<{
  currentDate: Date | null;
  handleDayClick: (date: Date) => void;
  handleMonthClick: (date: Date) => void;
}>;

type UseCalendarReturnType = Readonly<{
  locallyStoredDate: Dayjs;
  todayDateString: string;
  handleCalendarDayClick: (newDay: Dayjs) => void;
}>;

export const useCalendar = ({ currentDate, handleDayClick }: UseCalendarArguments): UseCalendarReturnType => {
  const [todayDateString] = useState<string>(dayjs(new Date()).format("YYYY-MM-DD"));
  const [locallyStoredDate, setLocallyStoredDate] = useState<Dayjs>(dayjs(currentDate));

  useEffect(() => {
    if (currentDate) {
      setLocallyStoredDate(dayjs(currentDate));
    }
  }, [currentDate]);

  const handleCalendarDayClick = (newDay: Dayjs) => {
    setLocallyStoredDate(newDay);
    handleDayClick(newDay.toDate());
  };

  return {
    locallyStoredDate,
    todayDateString,
    handleCalendarDayClick,
  };
};

export const calculateWeeksInMonth = (month: number, year: number): number => {
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);
  const daysInMonth = lastDay.getDate();

  const extraDaysAtBeginningOfMonth = firstDay.getDay();
  const extraDaysAtEndOfMonth = 6 - lastDay.getDay();

  const extraDays = extraDaysAtBeginningOfMonth + extraDaysAtEndOfMonth;
  const numberOfWeeksInMonth = Math.ceil((daysInMonth + extraDays) / 7);

  return numberOfWeeksInMonth;
};
