import {
  ClickStreamActivityEventType,
  GetSymptomScoringUseCaseProps,
  Note,
  NoteCategory,
  SymptomGroupHistoryItem,
} from "@vivantehealth/vivante-core";
import { Action, PayloadAction } from "@reduxjs/toolkit";
import { Epic, StateObservable, ofType } from "redux-observable";
import { Observable, forkJoin, from, of } from "rxjs";
import { switchMap, map, catchError, withLatestFrom, mergeMap } from "rxjs/operators";

import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { RootState } from "@Store/store";

import { LoadProgressProps, ProgressStateSlice } from "./progressStateSlice";
import { createSimpleAnalyticsEpic } from "../../analytics/store/analyticsEpics";

const {
  loadDayData,
  loadProgress,
  loadProgressNotes,
  loadProgressNotesSuccess,
  loadNoteCategories,
  loadNoteCategoriesSuccess,
  loadProgressSuccess,
  loadSymptomScoresOfSelectedDay,
  loadSymptomScoresOfSelectedDaySuccess,
  loadHistory,
  loadHistorySuccess,
  loadProgressFailure,
  upsertNote,
  upsertNoteSuccess,
  deleteSymptomLog,
  deleteSymptomSuccess,
  deleteSymptomLogFailed,
  reloadProgress,
} = ProgressStateSlice.actions;

const loadProgressEpic: Epic = (actions$: Observable<PayloadAction<LoadProgressProps>>) =>
  actions$.pipe(
    ofType(loadProgress.type),
    switchMap((action: PayloadAction<LoadProgressProps>) => [
      loadSymptomScoresOfSelectedDay({
        startingDate: action.payload.startingDate,
        endingDate: action.payload.endingDate,
      }),
      loadNoteCategories(),
      loadDayData(action.payload.currentDate),
      loadProgressSuccess(),
    ]),
  );

const reloadProgressEpic: Epic = (actions$: Observable<Action>, state$: StateObservable<RootState>) =>
  actions$.pipe(
    ofType(reloadProgress.type),
    withLatestFrom(state$),
    switchMap((args) => {
      const state = args[1];

      if (state.progressState.currentDate && state.progressState.startingDate && state.progressState.endingDate) {
        return [
          loadProgress({
            currentDate: state.progressState.currentDate,
            startingDate: state.progressState.startingDate,
            endingDate: state.progressState.endingDate,
          }),
        ];
      }

      return [];
    }),
  );

const loadDayDataEpic: Epic = (actions$: Observable<PayloadAction<Date>>) =>
  actions$.pipe(
    ofType(loadDayData.type),
    switchMap((action: PayloadAction<Date>) => [loadProgressNotes(action.payload), loadHistory(action.payload)]),
  );

const loadSymptomScoresOfSelectedDayEpic: Epic = (
  actions$: Observable<PayloadAction<GetSymptomScoringUseCaseProps>>,
) => {
  return actions$.pipe(
    ofType(loadSymptomScoresOfSelectedDay.type),
    switchMap((action: PayloadAction<GetSymptomScoringUseCaseProps>) => {
      return from(
        vivanteCoreContainer.getHistoryUseCaseFactory().createGetSymptomScoringUseCase().execute(action.payload),
      ).pipe(
        map((symptomScores) => loadSymptomScoresOfSelectedDaySuccess(symptomScores)),
        catchError((error) => {
          return of(loadProgressFailure(error));
        }),
      );
    }),
  );
};

const loadProgressNoteCategoriesEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(loadNoteCategories.type),

    switchMap(() => {
      return from(vivanteCoreContainer.getNotesUseCaseFactory().createGetNoteCategoriesUseCase().execute()).pipe(
        map((categories: NoteCategory[]) => {
          return loadNoteCategoriesSuccess(
            [...categories].sort((a: NoteCategory, b: NoteCategory) => a.order - b.order),
          );
        }),
        catchError((e) => {
          return of(loadProgressFailure(e));
        }),
      );
    }),
  );
};

const loadProgressNotesEpic: Epic = (actions$: Observable<PayloadAction<Date>>) => {
  return actions$.pipe(
    ofType(loadProgressNotes.type),
    switchMap((action: PayloadAction<Date>) => {
      return from(
        vivanteCoreContainer.getNotesUseCaseFactory().createGetNotesByDateUseCase().execute(action.payload),
      ).pipe(
        map((notes) => loadProgressNotesSuccess(notes)),
        catchError((error) => {
          return of(loadProgressFailure(error));
        }),
      );
    }),
  );
};

const loadHistoryEpic: Epic = (actions$: Observable<PayloadAction<Date>>) => {
  return actions$.pipe(
    ofType(loadHistory.type),
    switchMap((action: PayloadAction<Date>) => {
      return from(
        vivanteCoreContainer
          .getHistoryUseCaseFactory()
          .createGetPartOfDayBucketedHistoryItemsUseCase()
          .execute(action.payload),
      ).pipe(
        map((notes) => loadHistorySuccess(notes)),
        catchError((error) => {
          return of(loadProgressFailure(error));
        }),
      );
    }),
  );
};

const upsertNoteEpic: Epic = (actions$: Observable<PayloadAction<Note & { shouldSetNoteSaving: boolean }>>) => {
  return actions$.pipe(
    ofType(upsertNote.type),
    mergeMap((action: PayloadAction<Note & { shouldSetNoteSaving: boolean }>) => {
      const note: Note = {
        id: action.payload.id,
        noteCategoryId: action.payload.noteCategoryId,
        day: action.payload.day,
        body: action.payload.body,
      };

      return from(vivanteCoreContainer.getNotesUseCaseFactory().createSetNoteUseCase().execute(note)).pipe(
        /**
         * We check if the existing note has an id, if it does, we pass it to the success action
         * This is to ensure we can properly determine if the node was created or updated for analytics
         */
        map((noteId) => upsertNoteSuccess({ ...note, id: note.id ? noteId : undefined })),
        catchError((error) => {
          return of(loadProgressFailure(error));
        }),
      );
    }),
  );
};

const deleteSymptomLogEpic: Epic = (actions$: Observable<PayloadAction<SymptomGroupHistoryItem>>) => {
  return actions$.pipe(
    ofType(deleteSymptomLog.type),

    switchMap((action: PayloadAction<SymptomGroupHistoryItem>) => {
      const requests = action.payload.symptomLogs.map((item) =>
        from(vivanteCoreContainer.getMonitoringUseCaseFactory().createDeleteSymptomLogUseCase().execute(item.id)),
      );

      return forkJoin(requests).pipe(
        switchMap(() => [deleteSymptomSuccess(), reloadProgress()]),
        catchError((error) => {
          return of(deleteSymptomLogFailed(error));
        }),
      );
    }),
  );
};

const analyticsEpics: Epic[] = [
  createSimpleAnalyticsEpic(
    deleteSymptomSuccess.type,
    ClickStreamActivityEventType.SYMPTOM_LOGGING_LOG_DELETE_PERFORMED,
  ),
  createSimpleAnalyticsEpic<Note>(upsertNoteSuccess.type, (payload) => ({
    eventType: payload?.id ? ClickStreamActivityEventType.NOTE_EDITED : ClickStreamActivityEventType.NOTE_SAVED,
    activityContextExtra: {
      noteId: payload?.id,
      noteCategoryId: payload?.noteCategoryId,
    },
  })),
];

export const progressEpics = [
  loadDayDataEpic,
  loadProgressEpic,
  loadProgressNoteCategoriesEpic,
  loadProgressNotesEpic,
  loadSymptomScoresOfSelectedDayEpic,
  loadHistoryEpic,
  upsertNoteEpic,
  deleteSymptomLogEpic,
  reloadProgressEpic,
  ...analyticsEpics,
];
