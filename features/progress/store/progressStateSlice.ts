// Expect this error as the action is actually used in epic and not in the reducer
/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  GetSymptomScoringUseCaseProps,
  Note,
  NoteCategory,
  PartOfDayBucketedHistoryItems,
  SymptomGroupHistoryItem,
  SymptomScore,
  VivanteApiError,
} from "@vivantehealth/vivante-core";
import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { endOfMonth, startOfMonth } from "date-fns";

import { LoadState } from "@Types";
import { buildSliceStateSelector, processError } from "@Utils/slice.util";

/// //////////////////////////////////////////////////////
/// state

export interface ProgressState {
  loadState: LoadState;
  symptomScores: Record<string, SymptomScore>;
  notes: Note[];
  noteCategories: NoteCategory[];
  noteSaving: boolean;
  startingDate: Date | null;
  endingDate: Date | null;
  currentDate: Date | null;
  history: PartOfDayBucketedHistoryItems[];
  showSymptomLoggingDrawer: boolean;
  returnToCarePlan: boolean;
}

export const initialState: ProgressState = {
  loadState: "loading",
  symptomScores: {},
  notes: [],
  noteCategories: [],
  noteSaving: false,
  startingDate: null,
  endingDate: null,
  currentDate: null,
  history: [],
  showSymptomLoggingDrawer: false,
  returnToCarePlan: false,
};

export type LoadProgressProps = GetSymptomScoringUseCaseProps &
  Readonly<{
    currentDate: Date;
  }>;

/// //////////////////////////////////////////////////////
/// slice

export const ProgressStateSlice = createSlice({
  name: "progressState",
  initialState,
  reducers: {
    loadProgress: (state: ProgressState, action: PayloadAction<LoadProgressProps>) => ({
      ...state,
      startingDate: action.payload.startingDate,
      endingDate: action.payload.endingDate,
      currentDate: action.payload.currentDate,
    }),
    reloadProgress: (state) => state,
    loadSymptomScoresOfSelectedDay: (state: ProgressState, action: PayloadAction<GetSymptomScoringUseCaseProps>) => ({
      ...state,
      startingDate: action.payload.startingDate,
      endingDate: action.payload.endingDate,
    }),
    loadSymptomScoresOfSelectedDaySuccess: (state, action: PayloadAction<Record<string, SymptomScore>>) => ({
      ...state,
      symptomScores: action.payload,
    }),
    updateCurrentDay: (state, action: PayloadAction<Date>) => ({ ...state, currentDate: action.payload }),
    loadDayData: (state, action: PayloadAction<Date>) => ({ ...state, currentDate: action.payload }),
    loadProgressNotes: (state, _: PayloadAction<Date>) => ({ ...state, loadState: "loading" }),
    loadProgressNotesSuccess: (state, action: PayloadAction<Note[]>) => ({ ...state, notes: action.payload }),
    loadNoteCategories: (state) => state,
    loadNoteCategoriesSuccess: (state, action: PayloadAction<NoteCategory[]>) => ({
      ...state,
      noteCategories: action.payload,
    }),
    loadHistory: (state, _: PayloadAction<Date>) => state,
    loadHistorySuccess: (state, action: PayloadAction<PartOfDayBucketedHistoryItems[]>) => ({
      ...state,
      loadState: "loaded",
      history: action.payload,
    }),
    loadProgressSuccess: (state) => state,
    loadProgressFailure: (state, action: PayloadAction<Error | VivanteApiError>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
    upsertNote: (state, action: PayloadAction<Note & { shouldSetNoteSaving: boolean }>) => {
      return { ...state, noteSaving: action.payload.shouldSetNoteSaving };
    },
    upsertNoteSuccess: (state, _: PayloadAction<Pick<Note, "body" | "day" | "noteCategoryId"> & { id?: string }>) => {
      return { ...state, noteSaving: false };
    },
    deleteSymptomLog: (state, _: PayloadAction<SymptomGroupHistoryItem>) => ({ ...state }),
    deleteSymptomSuccess: (state) => state,
    deleteSymptomLogSuccess: (state) => ({ ...state, loadState: "loaded" }),
    deleteSymptomLogFailed: (state, action: PayloadAction<Error | VivanteApiError>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
    toggleSymptomLoggingDrawer: (state, action: PayloadAction<boolean>) => ({
      ...state,
      showSymptomLoggingDrawer: action.payload,
    }),
    toggleReturnToCarePlan: (state, action: PayloadAction<boolean>) => ({ ...state, returnToCarePlan: action.payload }),
    resetToCurrentDayAndMonth: (state: ProgressState) => {
      const todaysDate = new Date();

      return {
        ...state,
        currentDate: todaysDate,
        startingDate: startOfMonth(todaysDate),
        endingDate: endOfMonth(todaysDate),
      };
    },
  },
});

/// //////////////////////////////////////////////////////
/// selectors

export const progressStateSelector = buildSliceStateSelector("progressState");

export const progressStateReducer = ProgressStateSlice.reducer;
