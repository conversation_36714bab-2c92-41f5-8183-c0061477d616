import { SymptomScore } from "@vivantehealth/vivante-core";
import { PickersDayProps } from "@mui/x-date-pickers";
import { color } from "@vivantehealth/design-tokens";
import dayjs, { Dayjs } from "dayjs";

import { appStrings } from "@Assets/app_strings";

const PROGRESS_STRINGS = appStrings.features.progress;

type GetDayInfoArguments = Readonly<{
  props: PickersDayProps<Dayjs> & { "data-timestamp"?: number };
  symptomScores: Record<string, SymptomScore>;
  todayDateString: string;
  currentlyInViewMonthNumber: number;
  locallyStoredDate: Dayjs;
  isLoading: boolean;
}>;

type GetDayInfoReturnType = Readonly<{
  date: Dayjs;
  numberToRender: number;
  computedStyles: React.CSSProperties;
  isToday: boolean;
  isDaySelected: boolean;
  isDisabled: boolean;
  ariaLabel: string;
  severityForDay: SymptomScore;
}>;

const ACTIVE_STYLE = {
  backgroundColor: color.background.surface.primary,
  color: color.text.strong,
};

const INACTIVE_STYLE = {
  backgroundColor: color.background.surface.primary,
  color: color.text.input.disabled,
};

const SEVERITY_STRING_LOOKUP = {
  0: PROGRESS_STRINGS.severityStrings.noSymptoms,
  1: PROGRESS_STRINGS.severityStrings.mildSymptoms,
  2: PROGRESS_STRINGS.severityStrings.moderateSymptoms,
  3: PROGRESS_STRINGS.severityStrings.severeSymptoms,
};

export const getDayInfo = ({
  props,
  symptomScores,
  todayDateString,
  currentlyInViewMonthNumber,
  locallyStoredDate,
  isLoading,
}: GetDayInfoArguments): GetDayInfoReturnType => {
  const dateOfDayComponent = dayjs(props?.["data-timestamp"] ? new Date(props["data-timestamp"]) : new Date());

  const dayOfComponentAriaText = dateOfDayComponent.format("MMMM, D, YYYY");
  const dayOfComponentDayNumber = dateOfDayComponent.date();
  const dayOfComponentMonthNumber = dateOfDayComponent.month();
  const dayOfComponentYear = dateOfDayComponent.year();
  const locallyStoredDateDayNumber = locallyStoredDate.date();
  const locallyStoredDateMonthNumber = locallyStoredDate.month();
  const locallyStoredDateYear = locallyStoredDate.year();

  const isDayInCurrentlySelectedMonth = dayOfComponentMonthNumber === locallyStoredDateMonthNumber;

  const isDayInCurrentlySelectedYear = dayOfComponentYear === locallyStoredDateYear;

  const isDayInCurrentlyViewedMonth = dayOfComponentMonthNumber === currentlyInViewMonthNumber;

  const isDaySelected =
    dayOfComponentDayNumber === locallyStoredDateDayNumber &&
    isDayInCurrentlySelectedMonth &&
    isDayInCurrentlySelectedYear;

  const formattedDateString = dateOfDayComponent.format("YYYY-MM-DD");
  const isToday = formattedDateString === todayDateString;

  const computedSeverityOfDay = symptomScores[formattedDateString];
  const symptomsSeverityString = SEVERITY_STRING_LOOKUP[computedSeverityOfDay];

  const isDisabled = !isDayInCurrentlyViewedMonth || isLoading;
  const computedStyles = isLoading || !isDayInCurrentlyViewedMonth ? INACTIVE_STYLE : ACTIVE_STYLE;

  const ariaLabel = `${dayOfComponentAriaText}. ${
    isToday ? `${PROGRESS_STRINGS.todaysDate}.` : ""
  } ${symptomsSeverityString}. ${
    isDaySelected ? PROGRESS_STRINGS.isCurrentlySelectedDate : PROGRESS_STRINGS.hitEnterToSelectDate
  }.`;

  return {
    date: dateOfDayComponent,
    numberToRender: dayOfComponentDayNumber,
    computedStyles,
    isToday,
    isDaySelected,
    isDisabled,
    ariaLabel,
    severityForDay: computedSeverityOfDay,
  };
};
