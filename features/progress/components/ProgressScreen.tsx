import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import {
  ClickStreamActivityEventType,
  Food,
  FoodLogHistoryItem,
  PartOfDayBucketedHistoryItems,
  SymptomGroupHistoryItem,
  SymptomScore,
} from "@vivantehealth/vivante-core";
import { Box, Grid, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { SPACING_16_PX, SPACING_24_PX, SPACING_32_PX, SPACING_8_PX } from "@Assets/style_constants";
import { BasicMenu } from "@Components/BasicMenu/BasicMenu";
import { TrackAFoodDrawer } from "@Features/foodTracking/components/TrackAFoodDrawer/TrackAFoodDrawer";
import { FoodLogStateSlice } from "@Features/foodTracking/store/foodLogStateSlice";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { ProgressStateSlice } from "@Features/progress/store/progressStateSlice";
import { SymptomLoggingDrawerContainer } from "@Features/symptomLogging/SymptomLoggingDrawerContainer";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { Routes } from "@Types";

import { ProgressCalendar } from "./ProgressCalendar";
import { ProgressNotes } from "./ProgressNotes";
import { ProgressTracking } from "./ProgressTracking";

const PROGRESS_STRINGS = appStrings.features.progress;

type ProgressScreenProps = Readonly<{
  currentDate: Date;
  symptomScores: Record<string, SymptomScore>;
  history: PartOfDayBucketedHistoryItems[];
  isLoading: boolean;
  isSymptomScoresLoading: boolean;
  deleteSymptomLogCallback: (symptomGroupHistoryItem: SymptomGroupHistoryItem) => void;
  editSymptomLogCallback: (symptomGroupHistoryItem: SymptomGroupHistoryItem) => void;
  createSymptomLogCallback?: () => void;
  handleMonthClick: (date: Date) => void;
  handleDayClick: (date: Date) => void;
}>;

const { toggleSymptomLoggingDrawer } = ProgressStateSlice.actions;

export type SelectedFoodItem = Readonly<{
  foodItem: Food;
  numberOfServings: FoodLogHistoryItem["numberOfServings"];
  servingSize: FoodLogHistoryItem["servingSize"];
  foodLogTime: FoodLogHistoryItem["dateTime"];
  foodLogId: FoodLogHistoryItem["foodLogId"];
}>;

export const ProgressScreen = ({
  currentDate,
  symptomScores,
  history,
  isLoading,
  isSymptomScoresLoading,
  handleMonthClick,
  handleDayClick,
  deleteSymptomLogCallback,
  editSymptomLogCallback,
  createSymptomLogCallback,
}: ProgressScreenProps) => {
  const dispatch = useDispatch();
  const [notesLoading, setNotesLoading] = useState(false);
  const [isTrackAFoodDrawerOpen, setIsTrackAFoodDrawerOpen] = useState(false);
  const [selectedFoodItem, setSelectedFoodItem] = useState<SelectedFoodItem>();
  const { sendEventAnalytics } = useAnalyticsHook();

  useEffect(() => {
    setNotesLoading(false);
  }, [history]);

  const handleUpdateProgress = () => {
    if (createSymptomLogCallback) {
      createSymptomLogCallback();
    }

    dispatch(toggleSymptomLoggingDrawer(true));
  };

  const handleEditSymptomLog = (historyItem: SymptomGroupHistoryItem) => {
    editSymptomLogCallback(historyItem);
    dispatch(toggleSymptomLoggingDrawer(true));
  };

  const handleOnFoodClick = (props: SelectedFoodItem) => {
    setSelectedFoodItem({ ...props });
    setIsTrackAFoodDrawerOpen(true);
  };

  const handleOnFoodDrawerClose = () => {
    setIsTrackAFoodDrawerOpen(false);
    setSelectedFoodItem(undefined);
  };

  const handleDeleteFoodLog = (snackbarMessage: string) => {
    if (selectedFoodItem?.foodLogId) {
      dispatch(FoodLogStateSlice.actions.deleteFoodLog({ foodLogId: selectedFoodItem.foodLogId, snackbarMessage }));
      sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_LOG_DELETE_PERFORMED);
    }
  };

  return (
    <>
      <TrackAFoodDrawer
        isDrawerOpen={isTrackAFoodDrawerOpen && !!selectedFoodItem}
        onDrawerClose={handleOnFoodDrawerClose}
        onDeleteFoodLog={handleDeleteFoodLog}
        {...selectedFoodItem}
      />
      <Box display="flex" justifyContent="space-between" mb={SPACING_32_PX}>
        <Typography variant="h1Serif">{PROGRESS_STRINGS.pageHeader}</Typography>
        <ProgressUpdateDropdownButton createSymptomLogCallback={handleUpdateProgress} />
      </Box>
      <Grid container spacing={SPACING_24_PX} justifyContent={{ md: "center", large: "start" }}>
        <Grid item lg={6} md={8} sm={12}>
          <ProgressCalendar
            currentDate={currentDate}
            symptomScores={symptomScores}
            handleMonthClick={handleMonthClick}
            handleDayClick={(date) => {
              handleDayClick(date);
              setNotesLoading(true);
            }}
            isLoading={!!isLoading}
            isSymptomScoresLoading={isSymptomScoresLoading}
          />
          <Box mt={3} display={{ xs: "none", lg: "block" }}>
            <ProgressNotes
              currentDate={currentDate}
              isLoading={Object.keys(symptomScores).length === 0 || notesLoading}
            />
          </Box>
        </Grid>
        <Grid item lg={6} md={8} sm={12}>
          <ProgressTracking
            currentDate={currentDate}
            history={history}
            onSymptomDelete={deleteSymptomLogCallback}
            onSymptomEdit={handleEditSymptomLog}
            handleOnFoodClick={handleOnFoodClick}
            isLoading={isLoading}
          />
        </Grid>
        <Grid item lg={6} md={8} sm={12} display={{ lg: "none", md: "block" }}>
          <ProgressNotes
            currentDate={currentDate}
            isLoading={Object.keys(symptomScores).length === 0 || notesLoading}
          />
        </Grid>
      </Grid>
      <SymptomLoggingDrawerContainer />
    </>
  );
};

const ICON_STYLES = { marginRight: SPACING_16_PX, marginTop: `-${SPACING_8_PX}` };

function ProgressUpdateDropdownButton({ createSymptomLogCallback }: { createSymptomLogCallback: () => void }) {
  const dispatch = useDispatch();
  const [foodIcon, setFoodIcon] = useState<"Food" | "FoodHover">("Food");
  const [symptomIcon, setSymptomIcon] = useState<"Symptoms" | "SymptomsHover">("Symptoms");

  const handleUpdateProgress = () => {
    createSymptomLogCallback();
    dispatch(toggleSymptomLoggingDrawer(true));
  };

  return (
    <BasicMenu
      ariaLabel={PROGRESS_STRINGS.updateProgressDropdown}
      id="progress-update-dropdown"
      disableRipple
      menuItems={[
        {
          text: PROGRESS_STRINGS.trackFood,
          onClick: () => {
            dispatch(
              NavigationStateSlice.actions.navigateTo({
                path: Routes.Food_Tracking,
                screenName: "FoodTracking",
              }),
            );
          },
          leftIcon: foodIcon,
          onMouseOver: () => setFoodIcon("FoodHover"),
          onMouseOut: () => setFoodIcon("Food"),
          iconStyles: ICON_STYLES,
        },
        {
          text: PROGRESS_STRINGS.trackSymptoms,
          onClick: handleUpdateProgress,
          leftIcon: symptomIcon,
          onMouseOver: () => setSymptomIcon("SymptomsHover"),
          onMouseOut: () => setSymptomIcon("Symptoms"),
          iconStyles: ICON_STYLES,
        },
      ]}
      menuIconLeft="Plus"
      buttonVariant="primary"
      buttonSize="small"
    >
      {PROGRESS_STRINGS.updateProgressButtonText}
    </BasicMenu>
  );
}
