import React from "react";
import {
  HistoryItem,
  HistoryItemType,
  PartOfDayBucketedHistoryItems,
  SymptomGroupHistoryItem,
  isSymptomGroupHistoryItem,
} from "@vivantehealth/vivante-core";
import { Box, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { AppIcon, IconVariant } from "@Components/AppIcon/AppIcon";

import { ProgressItemChips } from "./ProgressItemChips";
import { SelectedFoodItem } from "./ProgressScreen";
import { SymptomEditMenu } from "./SymptomEditMenu";

const PROGRESS_STRINGS = appStrings.features.progress;

type ProgressTrackingPartOfDayProps = Readonly<{
  bucketedHistory: PartOfDayBucketedHistoryItems;
  onSymptomDelete: (symptomGroupHistoryItem: SymptomGroupHistoryItem) => void;
  onSymptomEdit: (symptomGroupHistoryItem: SymptomGroupHistoryItem) => void;
  handleOnFoodClick: (props: SelectedFoodItem) => void;
}>;

const isHistoryItemType = (itemType: string): itemType is HistoryItemType => {
  return ["food", "h2", "stool", "symptomGroup"].includes(itemType);
};
/**
 * Groups the bucketed history items by time and item type to make separating them easier in the UI
 */
const groupBucketedHistoryItemsByTime = (bucketedHistory: PartOfDayBucketedHistoryItems) => {
  return bucketedHistory.items.reduce<Record<string, Record<HistoryItemType, HistoryItem[]>>>((groupedByTime, item) => {
    const time = dayjs(item.dateTime).format("h:mm a");

    if (!(time in groupedByTime)) {
      groupedByTime[time] = { food: [], h2: [], stool: [], symptomGroup: [] };
    }

    groupedByTime[time][item.itemType].push(item);

    return groupedByTime;
  }, {});
};

export const ProgressTrackingPartOfDay = ({
  bucketedHistory,
  onSymptomDelete,
  onSymptomEdit,
  handleOnFoodClick,
}: ProgressTrackingPartOfDayProps) => {
  const groupByTime = groupBucketedHistoryItemsByTime(bucketedHistory);

  return bucketedHistory.items.length > 0 ? (
    <Box>
      <Typography variant="h4" mb={5} color={color.text.strong}>
        {bucketedHistory.partOfDay}
      </Typography>

      <Box display="flex" flexDirection="column" gap={3}>
        {Object.keys(groupByTime).map((time, bodyIndex) => {
          const items = groupByTime[time];

          return (
            <React.Fragment key={`${time}-${bodyIndex}`}>
              <Box display="flex">
                <AppIcon name="Clock" size="sm" color={color.icon.default} />
                <Typography variant="actionDense" ml={1}>
                  {time}
                </Typography>
              </Box>

              <Box display="flex" flexDirection="column" gap={3}>
                {Object.entries(items).map(([itemType, historyItems]) => {
                  if (!isHistoryItemType(itemType) || historyItems.length === 0) return null;

                  const { iconName, itemTitle } = historyInfoLookup[itemType];

                  return (
                    <Paper sx={{ p: 2 }} key={`${itemTitle}-${bodyIndex}`}>
                      <Box display="flex" justifyContent="space-between" pb={3}>
                        <Box display="flex" alignItems="center">
                          <AppIcon name={iconName} containerStyles={{ width: "32px", height: "32px" }} />
                          <Typography variant="actionDense" ml={2} color={color.text.strong}>
                            {itemTitle}
                          </Typography>
                        </Box>

                        {isSymptomGroupHistoryItem(historyItems[0]) && (
                          <SymptomEditMenu
                            symptomItem={historyItems[0]}
                            onDelete={onSymptomDelete}
                            onEdit={onSymptomEdit}
                          />
                        )}
                      </Box>

                      <Box display="flex" flexWrap="wrap" gap={2}>
                        {historyItems.map((historyItem, historyItemIndex) => (
                          <ProgressItemChips
                            historyItem={historyItem}
                            handleOnFoodClick={handleOnFoodClick}
                            key={`${historyItem.itemType}${historyItemIndex}`}
                          />
                        ))}
                      </Box>
                    </Paper>
                  );
                })}
              </Box>
            </React.Fragment>
          );
        })}
      </Box>
    </Box>
  ) : null;
};

type HistoryInfo = Readonly<{
  iconName: IconVariant;
  itemTitle: string;
}>;
type HistoryInfoLookup = Record<HistoryItemType, HistoryInfo>;

const historyInfoLookup = {
  food: {
    iconName: "Food",
    itemTitle: PROGRESS_STRINGS.food,
  },
  h2: {
    iconName: "GIMate",
    itemTitle: PROGRESS_STRINGS.h2,
  },
  stool: {
    iconName: "StoolWithBackground",
    itemTitle: PROGRESS_STRINGS.stool,
  },
  symptomGroup: {
    iconName: "Symptoms",
    itemTitle: PROGRESS_STRINGS.symptoms,
  },
} as const satisfies HistoryInfoLookup;
