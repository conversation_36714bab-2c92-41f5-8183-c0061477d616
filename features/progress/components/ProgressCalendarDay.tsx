import { SymptomScore } from "@vivantehealth/vivante-core";
import { Box } from "@mui/material";
import { PickersDayProps } from "@mui/x-date-pickers";
import { color, typography } from "@vivantehealth/design-tokens";
import dayjs, { Dayjs } from "dayjs";

import { getDayInfo } from "../utils/getDayInfo";

type ProgressCalendarDayProps = Readonly<{
  props: PickersDayProps<Dayjs> & { "data-timestamp"?: number };
  symptomScores: Record<string, SymptomScore>;
  todayDateString: string;
  currentlyInViewMonthNumber: number;
  locallyStoredDate: Dayjs;
  isLoading: boolean;
  handleClick: (newDay: Dayjs) => void;
}>;

const SEVERITY_COLOR_MAP = {
  1: color.background.severity.noSymptoms,
  2: color.background.severity.mild,
  3: color.background.severity.severe,
};

const isWithinSeverityColorMap = (severity: number): severity is keyof typeof SEVERITY_COLOR_MAP => {
  return severity === 1 || severity === 2 || severity === 3;
};

export const ProgressCalendarDay = ({
  props,
  symptomScores,
  todayDateString,
  currentlyInViewMonthNumber,
  locallyStoredDate,
  isLoading,
  handleClick,
}: ProgressCalendarDayProps) => {
  const { date, numberToRender, computedStyles, isToday, isDaySelected, isDisabled, ariaLabel, severityForDay } =
    getDayInfo({
      props,
      symptomScores,
      todayDateString,
      currentlyInViewMonthNumber,
      locallyStoredDate,
      isLoading,
    });

  return (
    <Box sx={styles.dayContainer}>
      <Box
        tabIndex={!isDisabled ? 0 : -1}
        aria-label={ariaLabel}
        onClick={() => {
          if (!isDisabled) {
            handleClick(dayjs(date));
          }
        }}
        onKeyDown={(event) => {
          if (event.key === "Enter" && !isDisabled) {
            handleClick(dayjs(date));
          }
        }}
        sx={{
          ...typography.heading4,
          ...computedStyles,
          ...styles.day,
          cursor: isDisabled ? "" : "pointer",
          ...(isDaySelected ? { border: `1px solid ${color.border.action.hover}` } : {}),
          ...(isToday ? { backgroundColor: color.background.surface.secondary } : {}),
        }}
      >
        {numberToRender}
      </Box>
      {isWithinSeverityColorMap(severityForDay) ? (
        <Box sx={{ ...styles.todayDot, backgroundColor: SEVERITY_COLOR_MAP[severityForDay] }} />
      ) : null}
    </Box>
  );
};

const styles = {
  dayContainer: {
    position: "relative",
    margin: "0 auto 12px",
  },
  day: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "32px",
    width: "32px",
    borderRadius: "50%",
  },
  todayDot: {
    position: "absolute",
    left: "50%",
    bottom: "-8px",
    transform: "translateX(-50%)",
    height: "6px",
    width: "6px",
    borderRadius: "50%",
    zIndex: 2,
  },
};
