import { useState } from "react";
import { SymptomScore } from "@vivantehealth/vivante-core";
import { CircularProgress, Paper, useMediaQuery, useTheme } from "@mui/material";
import { DateCalendar } from "@mui/x-date-pickers";
import { color, typography } from "@vivantehealth/design-tokens";
import dayjs, { Dayjs } from "dayjs";

import { RADIUS_FULL_PX, SPACING_4_PX, SPACING_8_PX, SPACING_16_PX } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";

import { ProgressCalendarDay } from "./ProgressCalendarDay";
import { ProgressCalendarInfoBar } from "./ProgressCalendarInfoBar";
import { calculateWeeksInMonth, useCalendar } from "../hooks/useCalendar";
/** This is the additional width needed to make the label sit between the switcher arrows of the calendar */
const getArrowSwitcherWidth = (isSmallBreakpoint: boolean, isMediumBreakpoint: boolean) => {
  if (isSmallBreakpoint) {
    return "590px";
  }

  if (isMediumBreakpoint) {
    return "359px";
  }

  return "250px";
};

type ProgressCalendarProps = Readonly<{
  currentDate: Date;
  symptomScores: Record<string, SymptomScore>;
  isLoading: boolean;
  isSymptomScoresLoading: boolean;
  handleDayClick: (date: Date) => void;
  handleMonthClick: (date: Date) => void;
}>;

export const ProgressCalendar = ({
  currentDate,
  symptomScores,
  isLoading,
  isSymptomScoresLoading,
  handleDayClick,
  handleMonthClick,
}: ProgressCalendarProps) => {
  const [currentMonthInView, setCurrentMonthInView] = useState(dayjs(currentDate).month());
  const [currentYear, setCurrentYear] = useState(dayjs(currentDate).year());
  const { locallyStoredDate, todayDateString, handleCalendarDayClick } = useCalendar({
    currentDate,
    handleDayClick,
    handleMonthClick,
  });

  const theme = useTheme();
  const isExtraSmallBreakpoint = useMediaQuery(theme.breakpoints.only("xs"));
  const isSmallBreakpoint = useMediaQuery(theme.breakpoints.only("sm"));
  const isMediumBreakpoint = useMediaQuery(theme.breakpoints.only("md"));

  const weeksVisible = calculateWeeksInMonth(currentMonthInView, currentYear);

  const calendarHeight = `${weeksVisible === 6 ? "400px" : "350px"} !important`;
  const arrowSwitcherWidth = getArrowSwitcherWidth(isExtraSmallBreakpoint || isSmallBreakpoint, isMediumBreakpoint);

  const handleMonthChange = (val: Dayjs) => {
    setCurrentMonthInView(val.month());
    setCurrentYear(val.year());
    handleMonthClick(val.toDate());
  };

  return (
    <Paper sx={{ py: 4 }}>
      <DateCalendar
        views={["day"]}
        onMonthChange={handleMonthChange}
        sx={{
          ...styles.calendar,
          "&.MuiDateCalendar-root": {
            height: calendarHeight,
            transition: "height 0.35s ease",
            maxHeight: "unset",
            width: "100%",
          },
          ".MuiPickersSlideTransition-root": {
            height: calendarHeight,
            transition: "height 0.35s ease",
            minHeight: "unset",
          },
          ".MuiPickersArrowSwitcher-spacer": {
            width: arrowSwitcherWidth,
          },
          ".MuiPickersCalendarHeader-labelContainer > .MuiPickersFadeTransitionGroup-root": {
            position: "absolute",
            textAlign: "center",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
          },
        }}
        slots={{
          day: (props) =>
            ProgressCalendarDay({
              props,
              symptomScores,
              todayDateString,
              currentlyInViewMonthNumber: currentMonthInView,
              locallyStoredDate,
              isLoading,
              handleClick: handleCalendarDayClick,
            }),
          leftArrowIcon: () => (
            <AppIcon
              name="LeftChevron"
              size="sm"
              containerStyles={{
                display: "flex",
              }}
            />
          ),
          rightArrowIcon: () => <AppIcon name="RightChevron" size="sm" containerStyles={{ display: "flex" }} />,
        }}
        loading={isSymptomScoresLoading}
        renderLoading={() => <CircularProgress />}
        dayOfWeekFormatter={(date) => date.format("ddd")}
        referenceDate={locallyStoredDate}
      />
      <ProgressCalendarInfoBar />
    </Paper>
  );
};

const styles = {
  calendar: {
    ".MuiPickersCalendarHeader-root": {
      position: "relative",
      minHeight: "unset",
      mt: 0,
    },
    ".MuiPickersCalendarHeader-root:first-of-type": {
      padding: 0,
    },
    ".MuiPickersCalendarHeader-label": {
      ...typography.heading4,
      color: color.text.strong,
    },
    ".MuiDayCalendar-weekDayLabel": {
      ...typography.bodyDense,
      color: color.text.subtle,
      width: "32px",
      margin: `${SPACING_4_PX} 0 ${SPACING_8_PX} 0`,
    },
    ".MuiPickersArrowSwitcher-root": { width: "100%", justifyContent: "space-between" },
    ".MuiPickersArrowSwitcher-button": {
      padding: 2,
      border: `1px solid ${color.border.action.default}`,
      borderRadius: RADIUS_FULL_PX,
    },
    ".MuiDayCalendar-header": {
      display: "flex",
      justifyContent: "space-between",
      gap: 2,
    },
    ".MuiDayCalendar-weekContainer": {
      justifyContent: "space-between",
    },
    ".MuiDayCalendar-weekContainer > .MuiBox-root": {
      margin: `0 0 ${SPACING_16_PX} 0`,
    },
    ".MuiIconButton-root": {
      "&:hover": {
        backgroundColor: "transparent",
        border: `1px solid ${color.border.action.hover}`,
        color: color.text.action.default,
      },
    },
  },
};
