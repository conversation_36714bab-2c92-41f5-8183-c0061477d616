import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ClickStreamActivityEventType, Note, NoteCategory } from "@vivantehealth/vivante-core";
import { Backdrop, Box, CircularProgress, Paper, TextField, Typography } from "@mui/material";
import { color, typography } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { RADIUS_0_PX, RADIUS_16_PX, SPACING_0_PX, SPACING_16_PX } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { sendAnalytics } from "@Features/analytics/store/analyticsEpics";

import { ProgressStateSlice, progressStateSelector } from "../store/progressStateSlice";

const { upsertNote } = ProgressStateSlice.actions;

const PROGRESS_STRINGS = appStrings.features.progress;
const NOTES_DEBOUNCE_TIME = 1000;

const notesUpdateTimer: Record<string, NodeJS.Timeout> = {};

type CachedNote = Note & { isDirty?: boolean };
type ProgressNotesContainerProps = Readonly<{
  children: React.ReactNode;
  noteSaving: boolean;
}>;

const ProgressNotesContainer = ({ children, noteSaving }: ProgressNotesContainerProps) => {
  return (
    <Paper sx={{ py: 4 }}>
      <Typography variant="h4" mb={4}>
        {PROGRESS_STRINGS.notesHeader}
      </Typography>

      <Box position="relative">
        <Backdrop
          open={noteSaving}
          sx={{
            position: "absolute",
            zIndex: 999,
            // Use negative margin to ensure backdrop covers the entire container
            m: `-${SPACING_16_PX}`,
            borderRadius: `${RADIUS_0_PX} ${RADIUS_0_PX} ${RADIUS_16_PX} ${RADIUS_16_PX}`,
          }}
        >
          <Box display="flex" flexDirection="column" alignItems="center">
            <CircularProgress />
            <Typography variant="h4" color={color.text.action.onFill} mt={2}>
              {PROGRESS_STRINGS.noteSaving}
            </Typography>
          </Box>
        </Backdrop>

        <Box display="flex" flexDirection="column" gap={4}>
          {children}
        </Box>
      </Box>
    </Paper>
  );
};

type ProgressNotesProps = Readonly<{
  currentDate: Date | null;
  isLoading?: boolean;
}>;

export const ProgressNotes = ({ currentDate, isLoading }: ProgressNotesProps) => {
  const dispatch = useDispatch();
  const notes = useSelector(progressStateSelector("notes"));
  const noteCategories = useSelector(progressStateSelector("noteCategories"));
  const noteSaving = useSelector(progressStateSelector("noteSaving"));
  const [notesCache, setNotesCache] = useState<Record<string, CachedNote>>({});
  const [openCategories, setOpenCategories] = useState<Record<string, boolean | undefined>>({});

  if (isLoading && Object.keys(openCategories).length > 0) {
    setOpenCategories({});
  }

  useEffect(() => {
    setNotesCache(
      noteCategories.reduce(
        (acc, next) => ({
          ...acc,
          [next.id]: notes.find((n) => n.noteCategoryId === next.id),
        }),
        {},
      ),
    );
  }, [noteCategories, notes]);

  const onCategoryOpenClick = (noteCategory: NoteCategory) => {
    const categoryTitle: string = noteCategory.title;
    const newCategoryState = !openCategories[categoryTitle];

    if (newCategoryState) {
      dispatch(
        sendAnalytics({
          eventType: ClickStreamActivityEventType.NOTE_CATEGORY_SELECTED,
          activityContextExtra: {
            noteCategoryId: noteCategory.id,
          },
        }),
      );
    }

    setOpenCategories({
      ...openCategories,
      [categoryTitle]: newCategoryState,
    });
  };

  const saveNote = (note: CachedNote, shouldSetNoteSaving = false) => {
    if (note && note.isDirty) {
      dispatch(
        upsertNote({
          id: note.id,
          body: note.body,
          day: note.day,
          noteCategoryId: note.noteCategoryId,
          shouldSetNoteSaving,
        }),
      );
    }
  };

  const updateNote = (category: NoteCategory, value: string) => {
    const currentNote: Note = notesCache[category.id] || {
      id: "",
      body: "",
      day: currentDate,
      noteCategoryId: category.id,
    };
    const updatedNote: Note = {
      ...currentNote,
      body: value,
    };
    const update = {
      ...notesCache,
      [category.id]: { ...updatedNote, isDirty: true },
    };

    setNotesCache(update);
    clearTimeout(notesUpdateTimer[category.id]);

    notesUpdateTimer[category.id] = setTimeout(() => {
      saveNote(updatedNote);
    }, NOTES_DEBOUNCE_TIME);
  };

  const closedHeight = typography.actionDense.lineHeight;
  const closedTransformation = "rotate(90deg)";
  const openTransformation = "rotate(0deg)";

  if (isLoading) {
    return (
      <ProgressNotesContainer noteSaving={false}>
        <Box display="flex" justifyContent="center">
          <CircularProgress />
        </Box>
      </ProgressNotesContainer>
    );
  }

  return (
    <ProgressNotesContainer noteSaving={noteSaving}>
      {!noteCategories?.length
        ? null
        : noteCategories.map((category) => {
            const isOpen = openCategories[category.title];
            const value = notesCache[category.id]?.body || "";

            return (
              <Box
                sx={{
                  ...styles.categoryContainer,
                  height: isOpen ? "100%" : closedHeight,
                }}
                key={category.id}
              >
                <Paper
                  sx={{
                    ...styles.categoryHeaderContainer,
                    mb: 1,
                  }}
                  component="button"
                  onClick={() => onCategoryOpenClick(category)}
                  aria-label={appStrings.a11y.progressNoteCategory(category.title)}
                >
                  <Box display="flex" alignItems="center">
                    <AppIcon
                      name="Ellipse"
                      size="xs"
                      color={value ? color.background.brand.complete : color.background.surface.secondary}
                      svgStyles={styles.categoryDot}
                    />

                    <Typography variant="actionDense" ml={1} tabIndex={-1}>
                      {category.title}
                    </Typography>
                  </Box>

                  <AppIcon
                    name="RightChevron"
                    size="sm"
                    svgStyles={{
                      ...styles.openChevron,
                      transform: !isOpen ? openTransformation : closedTransformation,
                    }}
                  />
                </Paper>

                <TextField
                  type="text"
                  onChange={(event) => {
                    // Disable editting a note while it or another note is saving
                    if (!noteSaving) {
                      updateNote(category, event.target.value);
                    }
                  }}
                  value={value}
                  onFocus={() => {
                    if (!isOpen) {
                      onCategoryOpenClick(category);
                    }
                  }}
                  onBlur={() => {
                    // We clear the timeout to prevent the note from being saved twice
                    clearTimeout(notesUpdateTimer[category.id]);
                    saveNote(notesCache[category.id], true);
                  }}
                  placeholder={category.placeholder}
                  multiline
                  fullWidth
                />
              </Box>
            );
          })}
    </ProgressNotesContainer>
  );
};

const styles = {
  categoryContainer: {
    width: "100%",
    overflow: "hidden",
    transition: "height 250ms ease-in-out",
  },
  categoryHeaderContainer: {
    display: "flex",
    width: "100%",
    justifyContent: "space-between",
    alignItems: "center",
    padding: SPACING_0_PX,
    border: "none",
    cursor: "pointer",
  },
  categoryDot: { display: "flex", alignItems: "center", transition: "color 250ms ease-in-out" },
  openChevron: {
    verticalAlign: "middle",
    transition: "transform 250ms ease-in-out",
  },
} as const;
