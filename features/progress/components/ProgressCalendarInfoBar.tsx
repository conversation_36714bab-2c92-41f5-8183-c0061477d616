import { useState } from "react";
import { Box, IconButton, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";

import { ProgressCalendarInfoModal } from "./ProgressCalendarInfoModal";

export const ProgressCalendarInfoBar = () => {
  const [showInfoModal, setShowInfoModal] = useState<boolean>(false);

  return (
    <>
      <Box display="flex" alignItems="center">
        <AppIcon name="ProgressCircleGroup" />

        <Typography variant="caption" mx={1} color={color.text.subtle}>
          {appStrings.features.progress.calendarInfoBarMessage}
        </Typography>

        <IconButton onClick={() => setShowInfoModal(true)} disableRipple sx={{ p: 0 }}>
          <AppIcon name="Help" size="sm" color={color.icon.subtle} containerStyles={{ display: "flex" }} />
        </IconButton>
      </Box>

      <ProgressCalendarInfoModal isOpen={showInfoModal} onClose={() => setShowInfoModal(false)} />
    </>
  );
};
