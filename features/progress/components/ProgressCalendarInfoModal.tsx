import { Button, Typography } from "@mui/material";
import { Box } from "@mui/system";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { BaseModal } from "@Components/BaseModal/BaseModal";

const PROGRESS_STRINGS = appStrings.features.progress;

type ProgressCalendarInfoModalProps = Readonly<{
  isOpen: boolean;
  onClose: () => void;
}>;

export const ProgressCalendarInfoModal = ({ isOpen, onClose }: ProgressCalendarInfoModalProps) => {
  return (
    <BaseModal
      title={PROGRESS_STRINGS.infoModalHeader}
      isModalOpen={isOpen}
      onClose={onClose}
      bodyContent={
        <Box display="flex" flexDirection="column" gap={5}>
          {infoItems.map((infoItem) => (
            <InfoSection {...infoItem} key={infoItem.dotColor} />
          ))}
        </Box>
      }
      actions={
        <Button variant="primary" onClick={onClose} fullWidth>
          {appStrings.buttonText.gotIt}
        </Button>
      }
    />
  );
};

type InfoItem = {
  dotColor: string;
  message: string;
};

const InfoSection = ({ dotColor, message }: InfoItem) => (
  <Box display="flex" alignItems="flex-start">
    <AppIcon name="Ellipse" size="sm" color={dotColor} containerStyles={{ marginRight: 2, marginTop: 0.5 }} />
    <Typography
      variant="body"
      sx={{
        maxWidth: "calc(100% - 44px)",
      }}
    >
      {message}
    </Typography>
  </Box>
);

type InfoItemsType = InfoItem[];

const infoItems: InfoItemsType = [
  {
    dotColor: color.background.severity.severe,
    message: PROGRESS_STRINGS.infoModalSevere,
  },
  {
    dotColor: color.background.severity.mild,
    message: PROGRESS_STRINGS.infoModalModerate,
  },
  {
    dotColor: color.background.severity.noSymptoms,
    message: PROGRESS_STRINGS.infoModalNone,
  },
];
