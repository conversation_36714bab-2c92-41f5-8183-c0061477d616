import { SymptomGroupHistoryItem } from "@vivantehealth/vivante-core";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { RADIUS_FULL_PX, SPACING_8_PX } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { IconMenu } from "@Components/IconMenu/IconMenu";

const BUTTON_STRINGS = appStrings.buttonText;

type SymptomEditMenuProps = Readonly<{
  symptomItem: SymptomGroupHistoryItem;
  onDelete: (symptomGroupHistoryItem: SymptomGroupHistoryItem) => void;
  onEdit: (symptomGroupHistoryItem: SymptomGroupHistoryItem) => void;
}>;

export const SymptomEditMenu = ({ symptomItem, onDelete, onEdit }: SymptomEditMenuProps) => {
  return (
    <IconMenu
      id="symptom-edit"
      iconButtonStyles={{
        border: `1px solid ${color.border.action.default}`,
        borderRadius: RADIUS_FULL_PX,
        ":hover": {
          backgroundColor: "transparent",
          border: `1px solid ${color.border.action.hover}`,
          color: color.text.action.default,
        },
      }}
      ariaLabel="Symptom edit dropdown"
      disableRipple
      menuItems={[
        {
          text: BUTTON_STRINGS.edit,
          onClick: () => onEdit(symptomItem),
          leftIcon: "Edit",
          iconStyles: { marginRight: SPACING_8_PX },
        },
        {
          text: BUTTON_STRINGS.delete,
          onClick: () => onDelete(symptomItem),
          leftIcon: "Trash",
          iconStyles: { marginRight: SPACING_8_PX },
        },
      ]}
    >
      <AppIcon name="More" size="sm" containerStyles={{ display: "flex" }} />
    </IconMenu>
  );
};
