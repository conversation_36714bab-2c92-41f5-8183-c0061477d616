import {
  HistoryItem,
  isFoodHistoryItem,
  isStoolHistoryItem,
  isSymptomGroupHistoryItem,
} from "@vivantehealth/vivante-core";
import { Chip } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

import { SelectedFoodItem } from "./ProgressScreen";

type ProgressItemChipsProps = Readonly<{
  historyItem: HistoryItem;
  handleOnFoodClick: (props: SelectedFoodItem) => void;
}>;

export const ProgressItemChips = ({ historyItem, handleOnFoodClick }: ProgressItemChipsProps) => {
  if (isFoodHistoryItem(historyItem)) {
    return historyItem.foods.map((food) => {
      return (
        <Chip
          key={food.id}
          label={food.name}
          onClick={() => {
            handleOnFoodClick({
              foodItem: food,
              numberOfServings: historyItem.numberOfServings,
              servingSize: historyItem.servingSize,
              foodLogTime: historyItem.dateTime,
              foodLogId: historyItem.foodLogId,
            });
          }}
        />
      );
    });
  }

  if (isSymptomGroupHistoryItem(historyItem)) {
    const filteredSymptoms = historyItem.symptomLogs.filter(
      (s) => !["none", "no discomfort at all"].includes(s.severity.toLowerCase()),
    );

    return filteredSymptoms.length ? (
      filteredSymptoms.map((symptom) => {
        return <Chip key={symptom.id} label={`${symptom.symptom}, ${symptom.severity.toLowerCase()}`} />;
      })
    ) : (
      <Chip label={appStrings.features.progress.severityStrings.noSymptoms} />
    );
  }

  if (isStoolHistoryItem(historyItem)) {
    return <Chip label={historyItem.stoolType} />;
  }

  return <Chip label={historyItem.value} />;
};
