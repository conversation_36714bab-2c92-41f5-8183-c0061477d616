import { PartOfDayBucketedHistoryItems, SymptomGroupHistoryItem } from "@vivantehealth/vivante-core";
import { Box, CircularProgress, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";

import { SelectedFoodItem } from "./ProgressScreen";
import { ProgressTrackingPartOfDay } from "./ProgressTrackingPartOfDay";

const PROGRESS_STRINGS = appStrings.features.progress;

type ProgressTrackingProps = Readonly<{
  currentDate: Date | null;
  history: PartOfDayBucketedHistoryItems[];
  onSymptomDelete: (symptomGroupHistoryItem: SymptomGroupHistoryItem) => void;
  onSymptomEdit: (symptomGroupHistoryItem: SymptomGroupHistoryItem) => void;
  handleOnFoodClick: (props: SelectedFoodItem) => void;
  isLoading: boolean;
}>;

export const ProgressTracking = ({
  currentDate,
  history,
  onSymptomDelete,
  onSymptomEdit,
  handleOnFoodClick,
  isLoading,
}: ProgressTrackingProps) => {
  const currentDateString = currentDate?.toLocaleDateString("en-US", {
    month: "long",
    day: "numeric",
  });
  const symptomsExist = history?.some((h) => h.items?.length > 0);
  const formattedCurrentDateString =
    currentDateString && `${currentDateString.split(" ")[0]} ${currentDateString.split(" ")[1]}`;

  return (
    <Paper sx={{ py: 4 }}>
      <Box display="flex" flexDirection="column" gap={3}>
        <Typography variant="h4" mb={1} color={color.text.strong}>
          {PROGRESS_STRINGS.trackerHeader}
        </Typography>

        {isLoading ? (
          <Box display="flex" justifyContent="center">
            <CircularProgress />
          </Box>
        ) : (
          <>
            <Typography variant="h4" mb={1} color={color.text.strong}>
              {formattedCurrentDateString ?? ""}
            </Typography>

            {!symptomsExist ? (
              <Typography variant="bodyDense">{PROGRESS_STRINGS.emptyTrackerHistory}</Typography>
            ) : (
              <Box display="flex" flexDirection="column" gap={6}>
                {history.map((bucketedHistory, index) => (
                  <ProgressTrackingPartOfDay
                    key={index}
                    bucketedHistory={bucketedHistory}
                    onSymptomDelete={onSymptomDelete}
                    onSymptomEdit={onSymptomEdit}
                    handleOnFoodClick={handleOnFoodClick}
                  />
                ))}
              </Box>
            )}
          </>
        )}
      </Box>
    </Paper>
  );
};
