import { FormData, Order, OrderStatus } from "@vivantehealth/vivante-core";

import { FormSubmission } from "@Components/form/FormFields";

import { OrderingScreenState } from "../store/orderStateSlice";

export const mapOrderStatusToScreenState = (order: Order): OrderingScreenState => {
  switch (order.status) {
    case OrderStatus.INITIALIZED:
      return "eligibility_form";
    case OrderStatus.ELIGIBILITY_STATUS_CHECKED:
      if (order.memberEligibleToOrder && !order.shouldShowEligibilityResults) {
        return "shipping_form";
      }

      return "eligibility_result";

    case OrderStatus.SHIPPING_ADDRESS_CONFIRMED:
      return "order_review";
    case OrderStatus.ORDER_PLACED:
      return "order_confirmed";
    default:
      return "eligibility_form";
  }
};

export const formSubmissionToData = (data: FormSubmission): FormData => {
  const formData: FormData = Object.entries(data).reduce((acc, [key, value]) => {
    const valueToUse = typeof value === "object" && "id" in value ? value?.id : value;

    return {
      ...acc,
      [key]: valueToUse,
    };
  }, {});

  return formData;
};
