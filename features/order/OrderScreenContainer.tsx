import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";

import { FormSubmission } from "@Components/form/FormFields";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { OrderStateSlice, orderSelector } from "@Features/order/store/orderStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { Routes } from "@Types";

import { OrderScreen } from "./components/OrderScreen";

const {
  loadOrder,
  updateScreenState,
  goBack,
  goContinue,
  submitShippingFormData,
  submitEligibilityFormData,
  resetOrderState,
  submitOrderConfirmation,
} = OrderStateSlice.actions;

type OrderScreenContainerProps = Readonly<{
  orderId: string;
}>;

export const OrderScreenContainer = ({ orderId }: OrderScreenContainerProps) => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();

  useEffect(() => {
    dispatch(loadOrder(orderId));
    return () => {
      dispatch(resetOrderState());
    };
  }, [orderId, dispatch]);

  const loading = useSelector(orderSelector("loadState")) === "loading";
  const screenState = useSelector(orderSelector("screenState"));
  const order = useSelector(orderSelector("order"));

  const onEditShippingFormClick = () => dispatch(updateScreenState("shipping_form"));

  const onGoBack = () => dispatch(goBack());
  const onGoContinue = () => dispatch(goContinue());
  const onSubmitShippingForm = (data: FormSubmission) => {
    dispatch(submitShippingFormData(data));
    sendEventAnalytics(ClickStreamActivityEventType.LAB_ORDERING_SHIPPING_ADDRESS_SUBMITTED);
  };
  const onSubmitEligibilityForm = (data: FormSubmission) => {
    dispatch(submitEligibilityFormData(data));
    sendEventAnalytics(ClickStreamActivityEventType.LAB_ORDERING_INSURANCE_SUBMITTED);
  };
  const onSubmitOrderConfirmation = () => {
    sendEventAnalytics(ClickStreamActivityEventType.LAB_ORDERING_ORDER_CONFIRMED);
    dispatch(submitOrderConfirmation());
  };
  const onDoneConfirming = () => {
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: Routes.HOME,
        screenName: "Home",
      }),
    );
  };

  return (
    <OrderScreen
      screenState={screenState}
      loading={loading}
      order={order}
      onEditShippingFormClick={onEditShippingFormClick}
      onGoBack={onGoBack}
      onGoContinue={onGoContinue}
      onSubmitShippingForm={onSubmitShippingForm}
      onSubmitEligibilityForm={onSubmitEligibilityForm}
      onSubmitOrderConfirmation={onSubmitOrderConfirmation}
      onDoneConfirming={onDoneConfirming}
    />
  );
};
