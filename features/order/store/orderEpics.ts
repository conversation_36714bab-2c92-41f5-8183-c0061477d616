import { Order, FormData } from "@vivantehealth/vivante-core";
import { PayloadAction } from "@reduxjs/toolkit";
import { Epic, ofType } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { catchError, map, switchMap, withLatestFrom } from "rxjs/operators";

import { FormSubmission } from "@Components/form/FormFields";
import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { RootState } from "@Store/store";

import { OrderStateSlice } from "./orderStateSlice";
import { formSubmissionToData, mapOrderStatusToScreenState } from "../utils/orderState.util";

const {
  loadOrder,
  loadOrderFailure,
  loadOrderSuccess,
  updateScreenState,
  submitShippingFormData,
  submitShippingFormDataFailure,
  submitEligibilityFormData,
  submitEligibilityFormDataFailure,
  submitOrderConfirmation,
  submitOrderConfirmationSuccess,
  submitOrderConfirmationFailure,
} = OrderStateSlice.actions;

const loadOrderEpic: Epic = (actions$: Observable<PayloadAction<string>>) => {
  return actions$.pipe(
    ofType(loadOrder.type),
    switchMap((action: PayloadAction<string>) => {
      return from(
        vivanteCoreContainer.getOrderingUseCaseFactory().createGetOrderUseCase().execute(action.payload),
      ).pipe(
        map((order) => loadOrderSuccess(order)),
        catchError((error) => {
          return of(loadOrderFailure(error));
        }),
      );
    }),
  );
};

const loadOrderSuccessEpic: Epic = (actions$: Observable<PayloadAction<Order>>) =>
  actions$.pipe(
    ofType(loadOrderSuccess.type),
    switchMap((action: PayloadAction<Order>) => [updateScreenState(mapOrderStatusToScreenState(action.payload))]),
  );

const submitShippingFormDataEpic: Epic = (
  actions$: Observable<PayloadAction<FormSubmission>>,
  state$: Observable<RootState>,
) => {
  return actions$.pipe(
    ofType(submitShippingFormData.type),
    withLatestFrom(state$),
    switchMap(([action, state]: [PayloadAction<FormSubmission>, RootState]) => {
      const data: FormSubmission = action.payload;
      const formData: FormData = formSubmissionToData(data);
      const { order } = state.orderState;

      if (!order) {
        throw new Error("Order not found");
      }

      return from(
        vivanteCoreContainer
          .getOrderingUseCaseFactory()
          .createSubmitShippingAddressFormUseCase()
          .execute({ orderId: order.id, formData }),
      ).pipe(
        map((order) => loadOrderSuccess(order)),
        catchError((error) => {
          return of(submitShippingFormDataFailure(error));
        }),
      );
    }),
  );
};

const submitEligibilityFormDataEpic: Epic = (
  actions$: Observable<PayloadAction<FormSubmission>>,
  state$: Observable<RootState>,
) => {
  return actions$.pipe(
    ofType(submitEligibilityFormData.type),
    withLatestFrom(state$),
    switchMap(([action, state]: [PayloadAction<FormSubmission>, RootState]) => {
      const data: FormSubmission = action.payload;
      const formData: FormData = formSubmissionToData(data);
      const { order } = state.orderState;

      if (!order) {
        throw new Error("Order not found");
      }

      return from(
        vivanteCoreContainer
          .getOrderingUseCaseFactory()
          .createSubmitEligibilityFormUseCase()
          .execute({ orderId: order.id, formData }),
      ).pipe(
        map((order) => loadOrderSuccess(order)),
        catchError((error) => {
          return of(submitEligibilityFormDataFailure(error));
        }),
      );
    }),
  );
};

const submitOrderConfirmationEpic: Epic = (
  actions$: Observable<PayloadAction<FormSubmission>>,
  state$: Observable<RootState>,
) => {
  return actions$.pipe(
    ofType(submitOrderConfirmation.type),
    withLatestFrom(state$),
    switchMap((args: [PayloadAction<FormSubmission>, RootState]) => {
      const state = args[1];
      const { order } = state.orderState;

      if (!order) {
        throw new Error("Order not found");
      }

      return from(vivanteCoreContainer.getOrderingUseCaseFactory().createConfirmOrderUseCase().execute(order.id)).pipe(
        map(() => submitOrderConfirmationSuccess()),
        catchError((error) => {
          return of(submitOrderConfirmationFailure(error));
        }),
      );
    }),
  );
};

export const orderEpics = [
  loadOrderEpic,
  loadOrderSuccessEpic,
  submitShippingFormDataEpic,
  submitEligibilityFormDataEpic,
  submitOrderConfirmationEpic,
];
