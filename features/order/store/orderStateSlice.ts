/* eslint-disable @typescript-eslint/no-unused-vars */
import { DisplayableVivanteException, Order, VivanteException } from "@vivantehealth/vivante-core";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import Router from "next/router";

import { FormSubmission } from "@Components/form/FormFields";
import { LoadState } from "@Types";
import { buildSliceStateSelector, processError } from "@Utils/slice.util";

export type OrderingScreenState =
  | "eligibility_form"
  | "shipping_form"
  | "order_review"
  | "order_confirmed"
  | "eligibility_result"
  | "work_with_doctor";

/// //////////////////////////////////////////////////////
/// state

export type OrderState = Readonly<{
  loadState: LoadState;
  order: Order | null;
  screenState: OrderingScreenState | null;
}>;

export const initialState: OrderState = {
  loadState: null,
  order: null,
  screenState: "eligibility_form",
};

/// //////////////////////////////////////////////////////
/// slice

export const OrderStateSlice = createSlice({
  name: "orderState",
  initialState,
  reducers: {
    loadOrder: (state, _: PayloadAction<string>) => ({ ...state, loadState: "loading" }),
    loadOrderSuccess: (state, action: PayloadAction<Order>) => ({
      ...state,
      loadState: "loaded",
      order: action.payload,
    }),
    loadOrderFailure: (state, action: PayloadAction<VivanteException | DisplayableVivanteException>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
    updateScreenState: (state, action: PayloadAction<OrderingScreenState>) => ({
      ...state,
      screenState: action.payload,
    }),
    editShippingForm: (state) => state,
    goBack: (state) => {
      const currentScreenState = state.screenState;

      if (currentScreenState === "order_review") {
        return {
          ...state,
          screenState: "shipping_form",
        };
      }

      if (currentScreenState === "shipping_form") {
        return {
          ...state,
          screenState: "eligibility_form",
        };
      }

      if (currentScreenState === "eligibility_form") {
        Router.back();
        return state;
      }

      return state;
    },
    goContinue: (state) => {
      const currentScreenState = state.screenState;

      if (currentScreenState === "eligibility_form") {
        return {
          ...state,
          screenState: "shipping_form",
        };
      }

      if (currentScreenState === "shipping_form") {
        return {
          ...state,
          screenState: "order_review",
        };
      }

      return state;
    },
    submitShippingFormData: (state, _: PayloadAction<FormSubmission>) => ({ ...state, loadState: "loading" }),
    submitShippingFormDataFailure: (state, action: PayloadAction<VivanteException | DisplayableVivanteException>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
    submitEligibilityFormData: (state, _: PayloadAction<FormSubmission>) => ({ ...state, loadState: "loading" }),
    submitEligibilityFormDataFailure: (
      state,
      action: PayloadAction<VivanteException | DisplayableVivanteException>,
    ) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
    resetOrderState: () => initialState,
    submitOrderConfirmation: (state) => state,
    submitOrderConfirmationSuccess: (state) => ({ ...state, loadState: "loaded", screenState: "order_confirmed" }),
    submitOrderConfirmationFailure: (state, action: PayloadAction<VivanteException | DisplayableVivanteException>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
  },
});

/// //////////////////////////////////////////////////////
/// selectors

export const orderSelector = buildSliceStateSelector("orderState");

export const orderStateReducer = OrderStateSlice.reducer;
