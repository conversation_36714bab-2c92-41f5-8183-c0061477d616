import { useState } from "react";
import { FormField, Order } from "@vivantehealth/vivante-core";
import { Paper, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { FormActions } from "@Components/form/FormActions";
import { FormSubmission } from "@Components/form/FormFields";
import { ShippingForm } from "@Features/shippingForm/components/ShippingForm";
import { vivanteCoreContainer } from "@Lib/vivanteCore";

import { EditInfoHeader } from "./EditInfoHeader";
import { OrderShell } from "./OrderShell";

const ORDERING_STRINGS = appStrings.features.ordering;
const BUTTON_STRINGS = appStrings.buttonText;

type ShippingPageProps = Readonly<{
  order: Order;
  onGoBack: () => void;
  onGoContinue: () => void;
  onSubmitShippingForm: (data: FormSubmission) => void;
}>;

export const ShippingPage = ({ order, onGoBack, onGoContinue, onSubmitShippingForm }: ShippingPageProps) => {
  const wasSubmitted = Object.keys(order?.shippingAddress?.submissionForm?.submittedData || {}).length > 0;
  const [edit, setEdit] = useState<boolean>(!wasSubmitted);

  return edit ? (
    <RenderShippingForm
      order={order}
      onGoBack={() => {
        if (wasSubmitted) {
          setEdit(false);
        } else {
          onGoBack();
        }
      }}
      onSubmitShippingForm={onSubmitShippingForm}
    />
  ) : (
    <RenderShippingConfirmation
      order={order}
      onGoBack={onGoBack}
      onGoContinue={() => {
        onGoContinue();
      }}
      onEditShippingFormClick={() => {
        setEdit(true);
      }}
    />
  );
};

type RenderShippingFormProps = Readonly<{
  order: Order;
  onGoBack: () => void;
  onSubmitShippingForm: (data: FormSubmission) => void;
}>;

const RenderShippingForm = ({ order, onGoBack, onSubmitShippingForm }: RenderShippingFormProps) => {
  const { shippingAddress } = order;
  const { submissionForm: shippingForm } = shippingAddress;

  if (!order || !shippingForm) {
    return null;
  }

  const shippingFormFormFields: Record<string, FormField> = shippingForm.updateAndGetForm();
  const previouslyInputData = Object.keys(shippingFormFormFields).reduce((acc, key) => {
    return {
      ...acc,
      [key]: shippingFormFormFields[key].value,
    };
  }, {});

  const formData = shippingForm.jsonSchemaForm;

  return (
    <ShippingForm
      onBackClick={() => onGoBack()}
      onSubmit={(data: FormSubmission) => {
        onSubmitShippingForm(data);
      }}
      formData={formData}
      prefilledData={previouslyInputData}
    />
  );
};

type RenderShippingConfirmationProps = Readonly<{
  order: Order;
  onEditShippingFormClick: () => void;
  onGoBack: () => void;
  onGoContinue: () => void;
}>;

const RenderShippingConfirmation = ({
  order,
  onEditShippingFormClick,
  onGoBack,
  onGoContinue,
}: RenderShippingConfirmationProps) => {
  const { shippingAddress } = order;
  const { submissionForm: shippingForm } = shippingAddress;

  if (!order || !shippingForm) {
    return null;
  }

  const shippingFormFormFields = shippingForm.updateAndGetForm();

  const shippingInformation = vivanteCoreContainer
    .getOrderingUseCaseFactory()
    .createFormatOrderFormFieldsPreviewUseCase()
    .execute({
      fields: shippingFormFormFields,
      formType: "shipping",
    });

  return (
    <OrderShell
      header={ORDERING_STRINGS.shippingConfirmation}
      subHeader={ORDERING_STRINGS.shippingConfirmationSubheader}
    >
      <Paper sx={{ p: 4 }}>
        <EditInfoHeader header={ORDERING_STRINGS.shippingAddress} onEdit={() => onEditShippingFormClick()} />
        {shippingInformation.map((item) => (
          <Typography variant="body" key={item}>
            {item}
          </Typography>
        ))}
      </Paper>
      <FormActions
        primaryText={BUTTON_STRINGS.continue}
        secondaryText={BUTTON_STRINGS.back}
        secondaryCallback={() => onGoBack()}
        primaryCallback={() => onGoContinue()}
      />
    </OrderShell>
  );
};
