import { Box, Button, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";

const ORDERING_STRINGS = appStrings.features.ordering;
const SUPPORT_EMAIL = appStrings.cylinderLinks.contactUsEmail;

type RenderOrderConfirmationPageProps = Readonly<{
  onDoneConfirming: () => void;
}>;

export const OrderConfirmationPage = ({ onDoneConfirming }: RenderOrderConfirmationPageProps) => {
  const { formWidth } = useResponsiveStylesHook();

  return (
    <Box display="flex" justifyContent="center">
      <Paper sx={{ maxWidth: formWidth }}>
        <Box display="flex" flexDirection="column" gap={2}>
          <AppIcon name="Completed" size="lg" />
          <Typography variant="h3" color={color.text.strong}>
            {ORDERING_STRINGS.orderConfirmation}
          </Typography>
          <Typography variant="body" color={color.text.strong}>
            {ORDERING_STRINGS.orderConfirmationSubheaderOne}
          </Typography>
          <Box display="flex" flexWrap="wrap" mb={5}>
            <Typography variant="body" color={color.text.strong}>
              {ORDERING_STRINGS.orderConfirmationSubheaderTwo}
            </Typography>
            <Typography component="a" variant="link" href={`mailto:${SUPPORT_EMAIL}`} color={color.text.strong}>
              {SUPPORT_EMAIL}
            </Typography>
          </Box>
        </Box>
        <Button variant="primary" onClick={() => onDoneConfirming()} fullWidth>
          {ORDERING_STRINGS.confimrationContinue}
        </Button>
      </Paper>
    </Box>
  );
};
