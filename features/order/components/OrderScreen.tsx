import { Order } from "@vivantehealth/vivante-core";

import { FormSubmission } from "@Components/form/FormFields";
import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { OrderingScreenState } from "@Features/order/store/orderStateSlice";

import { OrderConfirmationPage } from "./ConfirmationPage";
import { EligibilityPage } from "./EligibilityPage";
import { OrderReviewPage } from "./OrderReviewPage";
import { ShippingPage } from "./ShippingPage";

type OrderScreenProps = Readonly<{
  screenState: OrderingScreenState | null;
  loading: boolean;
  order: Order | null;
  onEditShippingFormClick: () => void;
  onGoBack: () => void;
  onGoContinue: () => void;
  onSubmitShippingForm: (data: FormSubmission) => void;
  onSubmitEligibilityForm: (data: FormSubmission) => void;
  onSubmitOrderConfirmation: () => void;
  onDoneConfirming: () => void;
}>;

export const OrderScreen = ({
  loading,
  screenState,
  order,
  onEditShippingFormClick,
  onGoBack,
  onGoContinue,
  onSubmitShippingForm,
  onSubmitEligibilityForm,
  onSubmitOrderConfirmation,
  onDoneConfirming,
}: OrderScreenProps) => {
  return (
    <>
      <LoadingSpinner open={loading} overlayDrawer={false} overlayHeader={false} />
      {order && screenState === "order_review" && (
        <OrderReviewPage
          order={order}
          onEditShippingFormClick={onEditShippingFormClick}
          onGoBack={onGoBack}
          onConfirmOrder={onSubmitOrderConfirmation}
        />
      )}
      {screenState === "order_confirmed" && <OrderConfirmationPage onDoneConfirming={onDoneConfirming} />}
      {order && screenState === "shipping_form" && (
        <ShippingPage
          order={order}
          onGoBack={onGoBack}
          onGoContinue={onGoContinue}
          onSubmitShippingForm={onSubmitShippingForm}
        />
      )}
      {order && (screenState === "eligibility_form" || screenState === "eligibility_result") && (
        <EligibilityPage
          order={order}
          onGoBack={onGoBack}
          onGoContinue={onGoContinue}
          onSubmitEligibilityForm={onSubmitEligibilityForm}
        />
      )}
    </>
  );
};
