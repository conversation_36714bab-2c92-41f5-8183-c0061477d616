import { Box, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { OutlinedIconButton } from "@Components/OutlinedIconButton/OutlinedIconButton";

type EditInfoHeaderProps = Readonly<{
  header: string;
  onEdit?: () => void;
}>;

export const EditInfoHeader = ({ header, onEdit }: EditInfoHeaderProps) => (
  <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
    <Typography variant="h4" color={color.text.strong}>
      {header}
    </Typography>
    {onEdit ? <OutlinedIconButton icon="Edit" onClick={onEdit} /> : null}
  </Box>
);
