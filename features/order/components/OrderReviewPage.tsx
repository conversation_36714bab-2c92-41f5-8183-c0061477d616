import { Order } from "@vivantehealth/vivante-core";
import { Box, Paper, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { FormActions } from "@Components/form/FormActions";
import { vivanteCoreContainer } from "@Lib/vivanteCore";

import { EditInfoHeader } from "./EditInfoHeader";
import { OrderShell } from "./OrderShell";

const BUTTON_STRINGS = appStrings.buttonText;
const ORDERING_STRINGS = appStrings.features.ordering;

type OrderReviewPageProps = Readonly<{
  order: Order;
  onEditShippingFormClick: () => void;
  onGoBack: () => void;
  onConfirmOrder: () => void;
}>;

export const OrderReviewPage = ({ order, onEditShippingFormClick, onGoBack, onConfirmOrder }: OrderReviewPageProps) => {
  const { financialResponsibilityDisclaimer, eligibilityCheck, shippingAddress } = order;
  const { submissionForm: eligibilityForm } = eligibilityCheck;
  const { submissionForm: shippingForm } = shippingAddress;

  const eligibilityFormFields = eligibilityForm?.updateAndGetForm();
  const shippingFormFormFields = shippingForm?.updateAndGetForm();

  const insuranceInformation = eligibilityFormFields
    ? vivanteCoreContainer.getOrderingUseCaseFactory().createFormatOrderFormFieldsPreviewUseCase().execute({
        fields: eligibilityFormFields,
        formType: "eligibility",
      })
    : [];

  const shippingInformation = shippingFormFormFields
    ? vivanteCoreContainer.getOrderingUseCaseFactory().createFormatOrderFormFieldsPreviewUseCase().execute({
        fields: shippingFormFormFields,
        formType: "shipping",
      })
    : [];

  return (
    <OrderShell
      header={ORDERING_STRINGS.shippingAddress}
      financialResponsibilityDisclaimer={financialResponsibilityDisclaimer}
    >
      <Box display="grid" gap={4}>
        <Paper sx={{ p: 4 }}>
          {insuranceInformation.length > 0 ? (
            <>
              <EditInfoHeader header={ORDERING_STRINGS.insuranceInformation} />
              {insuranceInformation.map((item) => (
                <Typography variant="body" key={item}>
                  {item}
                </Typography>
              ))}
            </>
          ) : (
            <Typography variant="body">{ORDERING_STRINGS.noInsuranceInformation}</Typography>
          )}
        </Paper>
        <Paper sx={{ p: 4 }}>
          {shippingInformation.length > 0 ? (
            <>
              <EditInfoHeader header={ORDERING_STRINGS.reviewAddressHeader} onEdit={() => onEditShippingFormClick()} />
              {shippingInformation.map((item) => (
                <Typography variant="body" key={item}>
                  {item}
                </Typography>
              ))}
            </>
          ) : (
            <Typography variant="body">{ORDERING_STRINGS.noShippingAddress}</Typography>
          )}
        </Paper>
      </Box>
      <FormActions
        primaryText={BUTTON_STRINGS.continue}
        secondaryText={BUTTON_STRINGS.back}
        secondaryCallback={() => onGoBack()}
        primaryCallback={() => onConfirmOrder()}
      />
    </OrderShell>
  );
};
