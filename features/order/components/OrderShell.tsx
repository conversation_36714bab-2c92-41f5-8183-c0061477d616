import { Alert, Box, Typography } from "@mui/material";

import { AppIcon } from "@Components/AppIcon/AppIcon";
import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";

type OrderShellProps = Readonly<{
  hasCustomHeader?: boolean;
  header?: string;
  subHeader?: string;
  financialResponsibilityDisclaimer?: string;
  children: React.ReactNode;
}>;

export const OrderShell = ({
  header,
  hasCustomHeader,
  subHeader,
  financialResponsibilityDisclaimer,
  children,
}: OrderShellProps) => {
  const { formWidth } = useResponsiveStylesHook();

  return (
    <Box display="flex" justifyContent="center">
      <Box display="flex" flexDirection="column" justifyContent="center" gap={5} width={formWidth}>
        {hasCustomHeader ? null : (
          <Box display="grid" gap={2}>
            <Typography variant="h3">{header}</Typography>
            {subHeader ? <Typography variant="body">{subHeader}</Typography> : null}
            {financialResponsibilityDisclaimer ? (
              <Alert
                color="warning"
                action={
                  <AppIcon
                    name="Warning"
                    containerStyles={{
                      width: "20px",
                      height: "20px",
                      m: 1,
                    }}
                  />
                }
                icon={false}
                sx={{
                  "&.MuiAlert-root": { alignItems: "center" },
                  "& .MuiAlert-action": { padding: 0 },
                }}
              >
                {financialResponsibilityDisclaimer}
              </Alert>
            ) : null}
          </Box>
        )}
        {children}
      </Box>
    </Box>
  );
};
