import { useState } from "react";
import { ClickStreamActivityEventType, Order, OrderStatus } from "@vivantehealth/vivante-core";
import { Box, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import Router from "next/router";

import { appStrings } from "@Assets/app_strings";
import { BackButton } from "@Components/BackButton/BackButton";
import { Form } from "@Components/form/Form";
import { FormActions } from "@Components/form/FormActions";
import { FormFields, FormSubmission } from "@Components/form/FormFields";
import { formatInputData } from "@Components/form/utils/formatInputData";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { Routes } from "@Types";
import { isOfType } from "@Utils/isOfType";

import { EditInfoHeader } from "./EditInfoHeader";
import { OrderShell } from "./OrderShell";

const BUTTON_STRINGS = appStrings.buttonText;
const ORDERING_STRINGS = appStrings.features.ordering;

type EligibilityPageProps = Readonly<{
  order: Order;
  onGoBack: () => void;
  onGoContinue: () => void;
  onSubmitEligibilityForm: (data: FormSubmission) => void;
}>;

export const EligibilityPage = ({ order, onGoBack, onGoContinue, onSubmitEligibilityForm }: EligibilityPageProps) => {
  const { sendEventAnalytics } = useAnalyticsHook();
  const wasSubmitted = Object.keys(order?.eligibilityCheck?.submissionForm?.submittedData || {}).length > 0;
  const [edit, setEdit] = useState<boolean>(!wasSubmitted);

  return edit ? (
    <RenderEligibilityForm
      order={order}
      onGoBack={() => {
        if (order.status === OrderStatus.INITIALIZED) {
          sendEventAnalytics(ClickStreamActivityEventType.LAB_ORDERING_NOT_NOW);
          Router.back();
        } else {
          setEdit(false);
        }
      }}
      onSubmitEligibilityForm={onSubmitEligibilityForm}
    />
  ) : (
    <RenderEligibilityConfirmation
      order={order}
      onGoBack={onGoBack}
      onGoContinue={() => {
        onGoContinue();
      }}
      onEditEligibilityFormClick={() => {
        setEdit(true);
      }}
    />
  );
};

type RenderEligibilityFormProps = Readonly<{
  order: Order;
  onGoBack: () => void;
  onSubmitEligibilityForm: (data: FormSubmission) => void;
}>;

const RenderEligibilityForm = ({ order, onGoBack, onSubmitEligibilityForm }: RenderEligibilityFormProps) => {
  const { eligibilityCheck } = order;
  const { submissionForm: eligibilityForm } = eligibilityCheck;

  if (!order || !eligibilityForm) {
    return null;
  }

  const eligibilityFormFields = eligibilityForm.updateAndGetForm();
  const previouslyInputData = Object.keys(eligibilityFormFields).reduce((acc, key) => {
    return {
      ...acc,
      [key]: eligibilityFormFields[key].value,
    };
  }, {});

  const formData = eligibilityForm.jsonSchemaForm;

  const { inputProperties, defaultValues } = formatInputData(formData, previouslyInputData);
  const hardcodedRequiredFields = Object.keys(defaultValues || {});

  return (
    <OrderShell hasCustomHeader>
      <Paper>
        <Form
          defaultValues={defaultValues}
          onSubmit={(data) => {
            if (isOfType<FormSubmission>(data, [])) {
              onSubmitEligibilityForm(data);
            }
          }}
          formHeader={formData.title}
          formSubheader={formData.description}
        >
          <Box my={5}>
            <FormFields inputProperties={inputProperties} requiredFields={hardcodedRequiredFields} />
          </Box>
          <FormActions
            primaryText={BUTTON_STRINGS.continue}
            secondaryText={BUTTON_STRINGS.back}
            secondaryCallback={() => onGoBack()}
          />
        </Form>
      </Paper>
    </OrderShell>
  );
};

type RenderEligibilityConfirmationProps = Readonly<{
  order: Order;
  onEditEligibilityFormClick: () => void;
  onGoBack: () => void;
  onGoContinue: () => void;
}>;

const RenderEligibilityConfirmation = ({
  order,
  onEditEligibilityFormClick,
  onGoBack,
  onGoContinue,
}: RenderEligibilityConfirmationProps) => {
  const { eligibilityCheck } = order;
  const { submissionForm: eligibilityForm } = eligibilityCheck;

  if (!order || !eligibilityForm) {
    return null;
  }

  if (eligibilityCheck?.resultTitle) {
    return (
      <>
        <BackButton onClick={() => Router.push(Routes.HOME)}>{BUTTON_STRINGS.back}</BackButton>
        <Paper sx={{ p: 4, mt: 4 }}>
          <Typography variant="h3" color={color.text.strong} mb={4}>
            {eligibilityCheck?.resultTitle}
          </Typography>
          <Typography variant="body">{eligibilityCheck?.resultMessage}</Typography>
        </Paper>
      </>
    );
  }

  const eligibilityFormFields = eligibilityForm.updateAndGetForm();
  const insuranceInformation = vivanteCoreContainer
    .getOrderingUseCaseFactory()
    .createFormatOrderFormFieldsPreviewUseCase()
    .execute({
      fields: eligibilityFormFields,
      formType: "eligibility",
    });

  return (
    <OrderShell
      header={ORDERING_STRINGS.insuranceInformationConfirmation}
      subHeader={ORDERING_STRINGS.insuranceInformationConfirmationSubheader}
    >
      <Paper sx={{ p: 4 }}>
        <EditInfoHeader header={ORDERING_STRINGS.insuranceInformation} onEdit={() => onEditEligibilityFormClick()} />
        {insuranceInformation.map((item) => (
          <Typography variant="body" key={item}>
            {item}
          </Typography>
        ))}
      </Paper>
      <FormActions
        primaryText={BUTTON_STRINGS.continue}
        secondaryText={BUTTON_STRINGS.back}
        secondaryCallback={() => onGoBack()}
        primaryCallback={() => onGoContinue()}
      />
    </OrderShell>
  );
};
