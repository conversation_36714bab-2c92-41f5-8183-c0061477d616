import { useEffect, useState } from "react";
import { Appointment, CareTeamUser, ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/router";

import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { CareGuideReschedulingModal } from "@Features/careGuide/components/CareGuideReschedulingModal";
import { CareTeamStateSlice } from "@Features/careTeam/store/careTeamStateSlice";
import { setCurrentRoomType } from "@Features/chat/store/chatStateSlice";
import { loadChat } from "@Features/chat/store/chatThunks";
import { NavOptions, NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { CalendarProviderOption } from "@Lib/infrastructure/calendar/calendar-provider-option";
import { useAppDispatch } from "@Store/hooks";
import { Routes } from "@Types";

import { CareTeamSchedulingModalContainer } from "./components/CareTeamSchedulingModalContainer";
import { CareTeamScreen } from "./components/CareTeamScreen";
import { EditAppointmentConfirmationModal } from "./components/EditAppointmentConfirmationModal";
import { useCareTeamHook } from "./hooks/careTeamHook";
import { getCareTeamUserByRole } from "./utils/careteam.utils";

export type AppointmentAction = "reschedule" | "cancel";
export type AppointmentActionData = {
  appointment: Appointment;
  action: AppointmentAction;
};
export type CareTeamSessionType = "rd" | "hc";

export const isCareTeamSessionType = (role: string): role is CareTeamSessionType => {
  return role === "rd" || role === "hc";
};

type CareTeamScreenContainerProps = Readonly<{
  role?: CareTeamSessionType;
}>;

export const CareTeamScreenContainer = ({ role }: CareTeamScreenContainerProps) => {
  const dispatch = useAppDispatch();
  const params = useSearchParams();
  const router = useRouter();
  const { sendEventAnalytics } = useAnalyticsHook();

  useEffect(() => {
    dispatch(CareTeamStateSlice.actions.loadCareTeam());
    dispatch(CareTeamStateSlice.actions.refreshAppointments());
    dispatch(NavigationStateSlice.actions.setActiveNavOption(NavOptions.CARE_TEAM));
    dispatch(loadChat({ displayError: false, batchSize: 1 }));
  }, [dispatch]);

  const [rescheduledAppointment, setRescheduledAppointment] = useState<Appointment>();
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [isMemberModalOpen, setIsMemberModalOpen] = useState<boolean>(false);
  const [confirmationType, setConfirmationType] = useState<AppointmentAction>();
  const [currentlySelectedMember, setCurrentlySelectedMember] = useState<CareTeamUser>();
  const {
    isLoading,
    careTeamUsers,
    upcomingAppointments,
    showSchedulingModal,
    setShowSchedulingModal,
    setAppointmentToReschedule,
    cancelAppointment,
    selectedMemberId,
    setSelectedMemberId,
    showCareGuideSchedulingModal,
    setShowCareGuideSchedulingModal,
  } = useCareTeamHook();

  useEffect(() => {
    if (role) {
      const careTeamMember = getCareTeamUserByRole(role, careTeamUsers);

      setCurrentlySelectedMember(careTeamMember);
      setIsMemberModalOpen(true);
    }
  }, [careTeamUsers, role, setShowSchedulingModal]);

  const clearSchedulingModalData = () => {
    setRescheduledAppointment(undefined);
    setSelectedMemberId(undefined);
  };

  const closeConfirmation = () => {
    setShowConfirmationModal(false);
    setRescheduledAppointment(undefined);
  };

  const handleUpcomingAppointmentEditClick = (data: AppointmentActionData) => {
    setConfirmationType(data.action);
    setRescheduledAppointment(data.appointment);
    setShowConfirmationModal(true);
  };

  const handleNavigateToConversation = (user: CareTeamUser) => {
    dispatch(setCurrentRoomType(user.userType));
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: "/chat",
        screenName: "Chat",
      }),
    );
  };

  const onAddToCalendarClick = (data: { calendarType: CalendarProviderOption; appointment: Appointment }) => {
    dispatch(
      CareTeamStateSlice.actions.createAppointmentCalendarEvent({
        appointment: data.appointment,
        extras: data.calendarType,
      }),
    );
    dispatch(sendEventAnalytics(ClickStreamActivityEventType.APPOINTMENT_ADD_TO_CALENDAR));
  };

  if (!careTeamUsers) {
    return <LoadingSpinner open />;
  }

  const confirmRescheduleAppointment = () => {
    const practitionerRole = rescheduledAppointment?.practitioner.role;

    if (rescheduledAppointment && (practitionerRole === "MD" || practitionerRole === "PA")) {
      setAppointmentToReschedule(rescheduledAppointment);
      router.push(
        `${Routes.APPOINTMENTS}?role=${rescheduledAppointment?.requestedRole ?? "gi"}&appointmentToRescheduleId=${rescheduledAppointment.id}`,
      );
      return;
    }

    if (rescheduledAppointment?.practitioner.role === "CG") {
      setShowConfirmationModal(false);
      return setShowCareGuideSchedulingModal(true);
    }

    setShowConfirmationModal(false);
    setSelectedMemberId(rescheduledAppointment?.practitioner.id);
    return setShowSchedulingModal(true);
  };

  const handleCancelAppointment = () => {
    if (rescheduledAppointment) {
      cancelAppointment(rescheduledAppointment.id);
    }
  };

  const confirmCancelAppointment = () => {
    setShowConfirmationModal(false);
    handleCancelAppointment();

    clearSchedulingModalData?.();
  };

  const closeCareGuideReschedulingModal = () => {
    setShowCareGuideSchedulingModal(false);
    clearSchedulingModalData();
  };

  if (params.get("showScheduling") === "true" && !showSchedulingModal) {
    setShowSchedulingModal(true);
    // Replace the URL to remove the query param to ensure the modal doesn't open unexpectedly again
    router.replace(Routes.CARE_TEAM);
  }

  return (
    <>
      <CareTeamSchedulingModalContainer
        isModalOpen={showSchedulingModal}
        onToggleSchedulingModal={setShowSchedulingModal}
        selectedMemberId={selectedMemberId}
        setSelectedMemberId={setSelectedMemberId}
        rescheduledAppointment={rescheduledAppointment}
        clearSchedulingModalData={clearSchedulingModalData}
      />

      <CareGuideReschedulingModal
        isModalOpen={showCareGuideSchedulingModal}
        existingAppointment={rescheduledAppointment}
        closeSchedulingModal={closeCareGuideReschedulingModal}
        key={String(showCareGuideSchedulingModal)}
      />

      <EditAppointmentConfirmationModal
        showConfirmationModal={showConfirmationModal}
        confirmationType={confirmationType}
        closeConfirmation={closeConfirmation}
        setSelectedMemberId={setSelectedMemberId}
        confirmRescheduleAppointment={confirmRescheduleAppointment}
        confirmCancelAppointment={confirmCancelAppointment}
      />

      <CareTeamScreen
        careTeamUsers={careTeamUsers}
        isLoading={isLoading}
        isAppointmentListError={false}
        isMemberModalOpen={isMemberModalOpen}
        setIsMemberModalOpen={setIsMemberModalOpen}
        openScheduleModal={() => setShowSchedulingModal(true)}
        setCurrentMemberId={(memberId: string) => setSelectedMemberId(memberId)}
        onNavigateToConversation={handleNavigateToConversation}
        upcomingAppointments={upcomingAppointments}
        handleUpcomingAppointmentEditClick={handleUpcomingAppointmentEditClick}
        onAddToCalendarClick={onAddToCalendarClick}
        currentlySelectedMember={currentlySelectedMember}
        setCurrentlySelectedMember={(member: CareTeamUser) => setCurrentlySelectedMember(member)}
      />
    </>
  );
};
