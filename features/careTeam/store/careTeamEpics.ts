import {
  AddAppointmentToCalendarUseCaseParams,
  Appointment,
  CareTeamUser,
  SessionSlot,
} from "@vivantehealth/vivante-core";
import { Action, PayloadAction } from "@reduxjs/toolkit";
import { Epic, ofType } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { switchMap, catchError, map, ignoreElements } from "rxjs/operators";

import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { formatVivanteException } from "@Utils/formatVivanteException";

import { CareTeamStateSlice, ScheduleRequest } from "./careTeamStateSlice";

const {
  loadCareTeamFailure,
  loadCareTeamSuccess,
  loadCareTeam,
  refreshAppointments,
  loadAppointmentsSuccess,
  loadAppointmentsFailure,
  cancelAppointment,
  loadSlotPages,
  loadSlotPagesSuccess,
  loadSlotPagesFailure,
  submitScheduling,
  submitSchedulingSuccess,
  submitSchedulingFail,
  createAppointmentCalendarEvent,
} = CareTeamStateSlice.actions;

const loadCareTeamEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(loadCareTeam.type),
    switchMap(() => {
      return from(vivanteCoreContainer.getCareTeamUseCaseFactory().createGetCareTeamUseCase().execute()).pipe(
        map((careTeamUsers: CareTeamUser[]) => loadCareTeamSuccess(careTeamUsers)),
        catchError((error) => {
          return of(loadCareTeamFailure(error));
        }),
      );
    }),
  );
};

const cancelAppointmentEpic: Epic = (actions$: Observable<PayloadAction<string>>) => {
  return actions$.pipe(
    ofType(cancelAppointment.type),
    switchMap((action: PayloadAction<string>) => {
      return from(
        vivanteCoreContainer.getAppointmentsUseCaseFactory().createCancelAppointmentUseCase().execute(action.payload),
      ).pipe(
        switchMap(() => [refreshAppointments()]),
        catchError((error) => {
          return of(loadCareTeamFailure(error));
        }),
      );
    }),
  );
};

const refreshAppointmentsEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(refreshAppointments.type),
    switchMap(() => {
      return from(vivanteCoreContainer.getAppointmentsUseCaseFactory().createGetAppointmentsUseCase().execute()).pipe(
        map((appointments: Appointment[]) => loadAppointmentsSuccess(appointments)),
        catchError((error) => {
          return of(loadAppointmentsFailure(error));
        }),
      );
    }),
  );
};

const loadSlotPagesEpic: Epic = (actions$: Observable<PayloadAction<string>>) => {
  return actions$.pipe(
    ofType(loadSlotPages.type),
    switchMap((action: PayloadAction<string>) => {
      return from(
        vivanteCoreContainer
          .getSessionsUseCaseFactory()
          .createGetAvailableSlotsByCareTeamUserId()
          .execute(action.payload),
      ).pipe(
        map((slots: SessionSlot[]) => loadSlotPagesSuccess(slots)),
        catchError((error) => {
          return of(loadSlotPagesFailure(error));
        }),
      );
    }),
  );
};

const createCalendarEventEpic: Epic = (actions$: Observable<PayloadAction<AddAppointmentToCalendarUseCaseParams>>) => {
  return actions$.pipe(
    ofType(createAppointmentCalendarEvent.type),
    map((action: PayloadAction<AddAppointmentToCalendarUseCaseParams>) =>
      vivanteCoreContainer
        .getAppointmentsUseCaseFactory()
        .createAddAppointmentToCalendarUseCase()
        .execute(action.payload),
    ),
    ignoreElements(),
  );
};

const submitSchedulingEpic: Epic = (actions$: Observable<PayloadAction<ScheduleRequest>>) => {
  return actions$.pipe(
    ofType(submitScheduling.type),

    switchMap((action: PayloadAction<ScheduleRequest>) => {
      return from(
        !action.payload.rescheduledSessionId
          ? vivanteCoreContainer
              .getSessionsUseCaseFactory()
              .createScheduleSessionUseCaseByCareTeamUserId()
              .execute(action.payload)
          : action.payload.rescheduledForCareTeamUserType
            ? vivanteCoreContainer.getSessionsUseCaseFactory().createRescheduleSessionUseCase().execute({
                slot: action.payload.slot,
                careTeamUserType: action.payload.rescheduledForCareTeamUserType,
                communicationMethod: action.payload.communicationMethod,
                sessionId: action.payload.rescheduledSessionId,
              })
            : Promise.reject(
                new Error("Reschedule Session ID or Care team user is required when rescheduling a session"),
              ),
      ).pipe(
        switchMap(() => [submitSchedulingSuccess(), refreshAppointments()]),
        catchError((error) => {
          return of(submitSchedulingFail(formatVivanteException(error) ?? "An error occurred"));
        }),
      );
    }),
  );
};

export const careTeamEpics = [
  cancelAppointmentEpic,
  createCalendarEventEpic,
  loadCareTeamEpic,
  loadSlotPagesEpic,
  refreshAppointmentsEpic,
  submitSchedulingEpic,
];
