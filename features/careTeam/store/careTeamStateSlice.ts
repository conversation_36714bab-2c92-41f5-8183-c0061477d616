// Expect this error as the action is actually used in epic and not in the reducer
/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  Appointment,
  CareTeamUserType,
  CareTeamUser,
  ScheduleSessionByCareTeamUserIdParams,
  SessionSlot,
  AddAppointmentToCalendarUseCaseParams,
  VivanteApiError,
} from "@vivantehealth/vivante-core";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

import { LoadState } from "@Types";
import { buildSliceStateSelector, processError } from "@Utils/slice.util";

import { paginateSlots } from "../utils/careTeamState.util";

export type SlotPage = Readonly<{
  title?: string;
  slots: SessionSlot[];
}>;

export type SlotPages = SlotPage[];

export type ScheduleRequest = ScheduleSessionByCareTeamUserIdParams & {
  rescheduledSessionId?: string;
  rescheduledForCareTeamUserType?: CareTeamUserType;
};

export /// //////////////////////////////////////////////////////
/// state

type CareTeamState = Readonly<{
  loadState: LoadState;
  slotPagesLoadState: LoadState;
  schedulingLoadState: LoadState;
  careTeamUsers: CareTeamUser[] | undefined;
  upcomingAppointments: Appointment[] | undefined;
  currentSlotPages: SlotPage[];
  errorMessage: string;
}>;

export const initialState: CareTeamState = {
  loadState: null,
  slotPagesLoadState: null,
  schedulingLoadState: null,
  careTeamUsers: undefined,
  upcomingAppointments: undefined,
  currentSlotPages: [],
  errorMessage: "",
};

/// //////////////////////////////////////////////////////
/// slice

export const CareTeamStateSlice = createSlice({
  name: "careTeamState",
  initialState,
  reducers: {
    loadCareTeam: (state) => ({ ...state, loadState: "loading" }),
    loadCareTeamFailure: (state, action: PayloadAction<VivanteApiError>) => {
      processError({
        error: action.payload,
        /** This is a semi-frequent occuring error but doesn't seem to actually be an issue */
        errorDisplayType: action.payload.detail.includes("phpfastcache") ? undefined : "modal",
      });

      return { ...state, loadState: "failure" };
    },
    loadCareTeamSuccess: (state, action: PayloadAction<CareTeamUser[]>) => ({
      ...state,
      loadState: "loaded",
      careTeamUsers: action.payload,
    }),
    refreshAppointments: (state) => ({ ...state, loadState: "loading", upcomingAppointments: undefined }),
    loadAppointmentsSuccess: (state, action: PayloadAction<Appointment[]>) => ({
      ...state,
      loadState: "loaded",
      upcomingAppointments: action.payload,
    }),
    loadAppointmentsFailure: (state, action: PayloadAction<VivanteApiError>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
    cancelAppointment: (state, _: PayloadAction<string>) => ({ ...state, loadState: "loading" }),
    loadSlotPages: (state, _: PayloadAction<string>) => ({ ...state, slotPagesLoadState: "loading" }),
    loadSlotPagesSuccess: (state, action: PayloadAction<SessionSlot[]>) => ({
      ...state,
      slotPagesLoadState: "loaded",
      currentSlotPages: paginateSlots(action.payload),
    }),
    loadSlotPagesFailure: (state, action: PayloadAction<VivanteApiError>) => {
      processError({ error: action.payload });

      return { ...state, slotPagesLoadState: "failure" };
    },
    cancelScheduling: (state) => ({ ...state, slotPagesLoadState: null, currentSlotPages: [] }),
    submitScheduling: (state, _: PayloadAction<ScheduleRequest>) => ({ ...state, schedulingLoadState: "loading" }),
    submitSchedulingSuccess: (state) => ({ ...state, schedulingLoadState: "loaded" }),
    submitSchedulingFail: (state, action: PayloadAction<string>) => {
      processError({ error: action.payload });

      return { ...state, schedulingLoadState: "failure", errorMessage: action.payload };
    },
    clearSchedulingError: (state) => ({ ...state, errorMessage: "" }),
    createAppointmentCalendarEvent: (state, _: PayloadAction<AddAppointmentToCalendarUseCaseParams>) => state,
  },
});

/// //////////////////////////////////////////////////////
/// selectors

export const careTeamStateSelector = buildSliceStateSelector("careTeamState");

export const careTeamStateReducer = CareTeamStateSlice.reducer;
