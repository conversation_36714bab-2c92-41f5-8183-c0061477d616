import { useSelector } from "react-redux";
import { CareTeamUser } from "@vivantehealth/vivante-core";
import { Box, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { CareTeamAvatar } from "@Components/CareTeamAvatar/CareTeamAvatar";
import { careTeamStateSelector } from "@Features/careTeam/store/careTeamStateSlice";
import { chatStateSelector } from "@Features/chat/store/chatStateSlice";
import { getLatestConversation } from "@Features/chat/utils/chat.utils";

import { CareTeamConversationCardText } from "./CareTeamConversationCardText";
import { getUserType } from "../utils/careteam.utils";

type CareTeamConversationTabProps = Readonly<{
  handleNavigateToConversation: (user: CareTeamUser) => void;
}>;

export const CareTeamConversationTab = ({ handleNavigateToConversation }: CareTeamConversationTabProps) => {
  const careTeamUsers = useSelector(careTeamStateSelector("careTeamUsers"));
  const healthCoachChat = useSelector(chatStateSelector("healthCoach"));
  const dietitianChat = useSelector(chatStateSelector("dietitian"));
  const latestConversationCoach = getLatestConversation(healthCoachChat);
  const latestConversationDietitian = getLatestConversation(dietitianChat);
  const shouldDisplayEmptyState = !latestConversationCoach && !latestConversationDietitian;

  return (
    <Box>
      {shouldDisplayEmptyState ? (
        <Typography variant="body">{appStrings.features.careTeam.conversationsEmptyState}</Typography>
      ) : (
        careTeamUsers?.map((user, index) => {
          const latestConversation = user.userType === "SHERPA" ? latestConversationCoach : latestConversationDietitian;

          if (!latestConversation) return null;

          const dateOfLastMessage = dayjs(latestConversation.createdAt).format("MMM DD");

          return (
            <Paper
              sx={{
                display: "flex",
                width: "calc(100% - 32px)",
                padding: 4,
                justifyContent: "center",
                alignItems: "center",
                marginBottom: 4,
                cursor: "pointer",
                ":hover": {
                  borderColor: color.border.action.hover,
                },
              }}
              component="button"
              onClick={() => handleNavigateToConversation(user)}
              key={index}
              aria-label={appStrings.a11y.conversationCard(user.firstName, latestConversation.text, dateOfLastMessage)}
            >
              <CareTeamAvatar
                avatarLink={user.avatarLink}
                altText={appStrings.a11y.careTeamMemberAvatarText(getUserType(user).toLowerCase())}
                hasUnreadMessages={!latestConversation.memberRead}
              />
              <CareTeamConversationCardText
                careTeamMember={user.firstName}
                lastConversationMessage={latestConversation.textHTML.replace(/(<([^>]+)>)/gi, "")}
                dateOfLastMessage={dateOfLastMessage}
              />
            </Paper>
          );
        })
      )}
    </Box>
  );
};
