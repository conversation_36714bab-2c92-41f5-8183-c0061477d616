import { Button } from "@mui/material";
import { Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { BaseModal } from "@Components/BaseModal/BaseModal";

const CARE_TEAM_STRINGS = appStrings.features.careTeam;

type CallModalProps = Readonly<{
  isOpen: boolean;
  onExitClick: () => void;
}>;

export const CallModal = ({ isOpen, onExitClick }: CallModalProps) => {
  return (
    <BaseModal
      isModalOpen={isOpen}
      onClose={onExitClick}
      title={CARE_TEAM_STRINGS.callModalHeader}
      bodyContent={<SubtitleText />}
      actions={
        <Button variant="primary" onClick={onExitClick} fullWidth>
          {appStrings.buttonText.gotIt}
        </Button>
      }
    />
  );
};

const SubtitleText = () => (
  <Typography variant="body">
    {CARE_TEAM_STRINGS.callModalSubheaderPartOne}{" "}
    <Typography component="a" variant="link" href={`tel:${CARE_TEAM_STRINGS.callModalPhoneNumber}`}>
      {CARE_TEAM_STRINGS.callModalSubheaderPartTwo}
    </Typography>{" "}
    {CARE_TEAM_STRINGS.callModalSubheaderPartThree}
  </Typography>
);
