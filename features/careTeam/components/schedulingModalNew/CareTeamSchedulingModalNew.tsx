import { useState } from "react";
import { CareTeamUser, SessionCommunicationMethod } from "@vivantehealth/vivante-core";
import { Button, CircularProgress } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { BaseModal } from "@Components/BaseModal/BaseModal";
import { AppointmentTimeSlot, AppointmentTimeSlots } from "@Features/appointmentsNew/types/appointments.types";

import { AppointmentConfirmationContentSectionNew } from "./AppointmentConfirmationContentSectionNew";
import { SchedulingContentSectionNew } from "./SchedulingContentSectionNew";

const BUTTON_STRINGS = appStrings.buttonText;

export type SchedulingScreens = "scheduling" | "appointmentConfirmation";

type CareTeamSchedulingModalProps = Readonly<{
  isModalOpen: boolean;
  careTeamUsers?: CareTeamUser[];
  handleSelectMember: (memberId: string) => void;
  onSelectSlotClick: (slot: AppointmentTimeSlot) => void;
  selectedCareTeamMemberId?: string;
  timeSlots?: AppointmentTimeSlots;
  timeSlotsLoading: boolean;
  onCommunicationMethodClick: (communicationMethod: SessionCommunicationMethod) => void;
  selectedCommunicationMethod?: SessionCommunicationMethod;
  closeSchedulingModal: () => void;
  nextBtnRef: React.RefObject<HTMLButtonElement | null>;
  isSubmitting: boolean;
  onSubmit: () => void;
  selectedScreen?: SchedulingScreens;
  selectedAppointment: AppointmentTimeSlot | undefined;
  handleSelectedAppointmentSlot: (slot: AppointmentTimeSlot | undefined) => void;
}>;

export const CareTeamSchedulingModalNew = ({
  isModalOpen,
  careTeamUsers,
  selectedCareTeamMemberId,
  handleSelectMember,
  timeSlots,
  timeSlotsLoading,
  isSubmitting,
  handleSelectedAppointmentSlot,
  selectedCommunicationMethod,
  onCommunicationMethodClick,
  closeSchedulingModal,
  nextBtnRef,
  onSubmit,
  selectedScreen,
  selectedAppointment,
}: CareTeamSchedulingModalProps) => {
  const [currentScreen, setCurrentScreen] = useState<SchedulingScreens>(selectedScreen ?? "scheduling");
  const isSubmitEnabled = !!selectedCareTeamMemberId && selectedAppointment && !!selectedCommunicationMethod;
  const isSchedulingScreen = currentScreen === "scheduling";
  const primaryButtonText = isSchedulingScreen ? BUTTON_STRINGS.next : BUTTON_STRINGS.confirm;
  const careTeamMember = careTeamUsers?.find((member) => member.id === selectedCareTeamMemberId);

  return (
    <BaseModal
      isModalOpen={isModalOpen}
      onClose={closeSchedulingModal}
      displayCloseButton={false}
      bodyContent={
        <>
          {isSchedulingScreen && careTeamUsers ? (
            <SchedulingContentSectionNew
              careTeamUsers={careTeamUsers}
              selectedCareTeamMemberId={selectedCareTeamMemberId}
              handleSelectMember={handleSelectMember}
              selectedCommunicationMethod={selectedCommunicationMethod}
              onCommunicationMethodClick={onCommunicationMethodClick}
              timeSlotsLoading={timeSlotsLoading}
              timeSlots={timeSlots}
              onSelectSlotClick={handleSelectedAppointmentSlot}
              selectedAppointment={selectedAppointment}
              handleSelectedAppointmentSlot={handleSelectedAppointmentSlot}
            />
          ) : null}

          {!isSchedulingScreen && selectedAppointment && careTeamMember ? (
            <AppointmentConfirmationContentSectionNew
              communicationMethod={selectedCommunicationMethod}
              slot={selectedAppointment}
              member={careTeamMember}
            />
          ) : null}
        </>
      }
      actions={
        <>
          <Button
            variant="secondary"
            onClick={isSchedulingScreen ? closeSchedulingModal : () => setCurrentScreen("scheduling")}
            fullWidth
            disabled={isSubmitting}
          >
            {isSchedulingScreen ? BUTTON_STRINGS.cancel : BUTTON_STRINGS.edit}
          </Button>

          <Button
            variant="primary"
            onClick={isSchedulingScreen ? () => setCurrentScreen("appointmentConfirmation") : onSubmit}
            fullWidth
            disabled={!isSubmitEnabled || isSubmitting}
            ref={nextBtnRef}
          >
            {isSubmitting ? <CircularProgress size={24} color="inherit" /> : primaryButtonText}
          </Button>
        </>
      }
    />
  );
};
