import { CareTeamUserType } from "@vivantehealth/vivante-core";
import { Box, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { AppointmentTimeSlot, AppointmentTimeSlots } from "@Features/appointmentsNew/types/appointments.types";
import { getCareTeamMemberType } from "@Features/careTeam//utils/careteam.utils";

import { SchedulingModalSlotsNew } from "./SchedulingModalSlotsNew";
const CARE_TEAM_STRINGS = appStrings.features.careTeam;

type ConditionalTextProps = Readonly<{
  renderConditionalsContainer: boolean;
  renderSelectReachByMethodText: boolean;
  renderNoAvailableAppointmentsText: boolean;
  careTeamMemberType?: CareTeamUserType;
}>;

const getConditionalText = ({
  renderConditionalsContainer,
  renderSelectReachByMethodText,
  renderNoAvailableAppointmentsText,
  careTeamMemberType,
}: ConditionalTextProps) => {
  if (renderConditionalsContainer) {
    return CARE_TEAM_STRINGS.schedulingModal.pleaseSelectMember;
  }

  if (renderSelectReachByMethodText) {
    return CARE_TEAM_STRINGS.schedulingModal.pleaseSelectAMethod;
  }

  if (renderNoAvailableAppointmentsText) {
    return CARE_TEAM_STRINGS.schedulingModal.noAvailableAppointments(getCareTeamMemberType(careTeamMemberType));
  }

  return undefined;
};

type SchedulingAppointmentSection = Readonly<{
  renderConditionalsContainer: boolean;
  renderSelectReachByMethodText: boolean;
  renderNoAvailableAppointmentsText: boolean;
  renderSlotSection: boolean;
  careTeamMemberType?: CareTeamUserType;
  timeSlots?: AppointmentTimeSlots;
  onSelectSlotClick: (slot: AppointmentTimeSlot | undefined) => void;
  selectedAppointment: AppointmentTimeSlot | undefined;
}>;

export const SchedulingAppointmentSectionNew = ({
  renderConditionalsContainer,
  renderSelectReachByMethodText,
  renderNoAvailableAppointmentsText,
  renderSlotSection,
  careTeamMemberType,
  timeSlots,
  onSelectSlotClick,
  selectedAppointment,
}: SchedulingAppointmentSection) => {
  const conditionalText = getConditionalText({
    renderConditionalsContainer,
    renderSelectReachByMethodText,
    renderNoAvailableAppointmentsText,
    careTeamMemberType,
  });

  if (conditionalText) {
    return (
      <Typography variant="body" color={color.text.strong}>
        {conditionalText}
      </Typography>
    );
  }

  return (
    <>
      {renderSlotSection && timeSlots ? (
        <Paper>
          {Object.entries(timeSlots)?.map(([day, daySlots], dayIndex, { length }) => {
            const isFirstDay = dayIndex === 0;
            const isLastDay = dayIndex === length - 1;

            return (
              <Box
                key={day}
                paddingTop={isFirstDay ? 0 : 5}
                paddingBottom={isLastDay ? 0 : 5}
                borderBottom={isLastDay ? "none" : `1px solid ${color.border.surface.default}`}
              >
                <Typography variant="h4" pb={4}>
                  {dayjs(day).format("dddd, MMMM D, YYYY")}
                </Typography>

                <SchedulingModalSlotsNew
                  day={day}
                  timeSlots={daySlots}
                  onClick={onSelectSlotClick}
                  selectedAppointment={selectedAppointment}
                />
              </Box>
            );
          })}
        </Paper>
      ) : null}
    </>
  );
};
