import { CareTeamUser, SessionCommunicationMethod } from "@vivantehealth/vivante-core";
import { Box, Paper, Typography } from "@mui/material";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { AppointmentDuration } from "@Components/AppointmentDuration/AppointmentDuration";
import { AppointmentGetReady } from "@Components/AppointmentGetReady/AppointmentGetReady";
import { AppointmentInformation } from "@Components/AppointmentInformation/AppointmentInformation";
import { GutCheckResultsNote } from "@Components/GutCheckResultsNote/GutCheckResultsNote";
import { PractitionerInformation } from "@Components/PractitionerInformation/PractitionerInformation";
import { AppointmentTimeSlot } from "@Features/appointmentsNew/types/appointments.types";

import { getUserType } from "../../utils/careteam.utils";

const APPOINTMENT_CONFIRMATION_STRINGS = appStrings.features.careTeam.appointmentConfirmationModal;

type AppointmentConfirmationContentSectionProps = Readonly<{
  communicationMethod?: SessionCommunicationMethod;
  slot: AppointmentTimeSlot;
  member: CareTeamUser;
}>;

export const AppointmentConfirmationContentSectionNew = ({
  communicationMethod,
  slot,
  member,
}: AppointmentConfirmationContentSectionProps) => {
  const slotDuration = dayjs(slot.end).diff(dayjs(slot.start), "minute");
  const practitioner = {
    ...member,
    type: "care_team",
    role: member?.userType,
    displayableRole: getUserType(member),
  };

  return (
    <>
      <Paper>
        <Box display="flex" justifyContent="space-between" alignItems="start">
          <PractitionerInformation practitioner={practitioner} />

          <AppointmentDuration duration={slotDuration} />
        </Box>

        <Box mt={4}>
          <AppointmentInformation startTime={new Date(slot.start)} communicationMethod={communicationMethod} />
        </Box>
      </Paper>

      <Box pt={5}>
        <AppointmentGetReady headingSize="h4" />

        <GutCheckResultsNote />

        <Typography variant="body" mt={4}>
          {APPOINTMENT_CONFIRMATION_STRINGS.locationNote}
        </Typography>
      </Box>
    </>
  );
};
