import { Box, Chip } from "@mui/material";
import dayjs from "dayjs";

import { AppointmentTimeSlot, ClinicianWithTimeSlots } from "@Features/appointmentsNew/types/appointments.types";

type SchedulingModalSlotsProps = Readonly<{
  timeSlots: ClinicianWithTimeSlots[];
  day: string;
  onClick: (slot: AppointmentTimeSlot) => void;
  selectedAppointment: AppointmentTimeSlot | undefined;
}>;

export const SchedulingModalSlotsNew = ({
  timeSlots,
  day,
  onClick,
  selectedAppointment,
}: SchedulingModalSlotsProps) => {
  return (
    <Box display="flex" gap={2} flexWrap="wrap">
      {timeSlots[0].availableSlots.map((sessionSlot) => {
        const key = `${day}-${sessionSlot.start}-${sessionSlot.end}`;
        const startTime = dayjs(sessionSlot.start).format("h:mm");
        const endTime = dayjs(sessionSlot.end).format("h:mm A");
        const text = `${startTime} - ${endTime}`;

        return (
          <Chip
            key={key}
            label={text}
            onClick={() => onClick(sessionSlot)}
            variant={
              sessionSlot.start === selectedAppointment?.start && sessionSlot.end === selectedAppointment?.end
                ? "active"
                : "inactive"
            }
          />
        );
      })}
    </Box>
  );
};
