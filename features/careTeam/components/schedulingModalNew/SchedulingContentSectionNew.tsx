import { CareTeamUser, SessionCommunicationMethod } from "@vivantehealth/vivante-core";
import { Box, CircularProgress } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { AppointmentTimeSlot, AppointmentTimeSlots } from "@Features/appointmentsNew/types/appointments.types";

import { SchedulingAppointmentSectionNew } from "./SchedulingAppointmentSectionNew";
import { SchedulingCareTeamMembers } from "../schedulingModal/SchedulingCareTeamMembers";
import { SchedulingReachByMethod } from "../schedulingModal/SchedulingReachByMethod";
import { SchedulingSectionHeader } from "../schedulingModal/SchedulingSectionHeader";

const CARE_TEAM_STRINGS = appStrings.features.careTeam;
/**
 * We set the minimum height of the modal content to 420px to prevent an issue where we would get a
 * flickering scrollbar when the loading spinner was showing
 */
const MODAL_CONTENT_MIN_HEIGHT = "420px";

type SchedulingContentSectionProps = Readonly<{
  careTeamUsers: CareTeamUser[];
  handleSelectMember: (memberId: string) => void;
  onSelectSlotClick: (slot: AppointmentTimeSlot) => void;
  selectedCareTeamMemberId?: string;
  timeSlots?: AppointmentTimeSlots;
  timeSlotsLoading: boolean;
  onCommunicationMethodClick: (communicationMethod: SessionCommunicationMethod) => void;
  selectedCommunicationMethod?: SessionCommunicationMethod;
  selectedAppointment: AppointmentTimeSlot | undefined;
  handleSelectedAppointmentSlot: (slot: AppointmentTimeSlot | undefined) => void;
}>;

export const SchedulingContentSectionNew = ({
  careTeamUsers,
  selectedCareTeamMemberId,
  handleSelectMember,
  selectedCommunicationMethod,
  onCommunicationMethodClick,
  timeSlotsLoading,
  timeSlots,
  selectedAppointment,
  handleSelectedAppointmentSlot,
}: SchedulingContentSectionProps) => {
  const timeSlotsLength = Object.keys(timeSlots ?? {}).length;
  const renderConditionalsContainer = timeSlotsLoading || !selectedCareTeamMemberId;
  const renderSelectReachByMethodText = !renderConditionalsContainer && !selectedCommunicationMethod;
  const renderSlotSection =
    !!timeSlotsLength && !timeSlotsLoading && !!selectedCareTeamMemberId && !renderSelectReachByMethodText;
  const renderNoAvailableAppointmentsText =
    !timeSlotsLength && !timeSlotsLoading && !!selectedCareTeamMemberId && !renderSelectReachByMethodText;
  const careTeamMemberType = careTeamUsers?.find((member) => member.id === selectedCareTeamMemberId)?.userType;

  return (
    <Box minHeight={MODAL_CONTENT_MIN_HEIGHT}>
      <SchedulingSectionHeader
        number={CARE_TEAM_STRINGS.schedulingModal.one}
        text={CARE_TEAM_STRINGS.schedulingModal.promptOne}
      />

      <SchedulingCareTeamMembers
        careTeamUsers={careTeamUsers}
        selectedMemberId={selectedCareTeamMemberId}
        onSelectMemberClick={handleSelectMember}
      />

      <SchedulingSectionHeader
        number={CARE_TEAM_STRINGS.schedulingModal.two}
        text={CARE_TEAM_STRINGS.schedulingModal.promptTwo("Care Team")}
      />

      <SchedulingReachByMethod
        selectedCommunicationMethod={selectedCommunicationMethod}
        onCommunicationMethodClick={onCommunicationMethodClick}
      />

      <SchedulingSectionHeader
        number={CARE_TEAM_STRINGS.schedulingModal.three}
        text={CARE_TEAM_STRINGS.schedulingModal.promptThree}
      />

      {timeSlotsLoading ? (
        <Box width={"100%"} textAlign="center">
          <CircularProgress />
        </Box>
      ) : (
        <SchedulingAppointmentSectionNew
          renderConditionalsContainer={renderConditionalsContainer}
          renderSelectReachByMethodText={renderSelectReachByMethodText}
          renderNoAvailableAppointmentsText={renderNoAvailableAppointmentsText}
          renderSlotSection={renderSlotSection}
          careTeamMemberType={careTeamMemberType}
          timeSlots={timeSlots}
          onSelectSlotClick={handleSelectedAppointmentSlot}
          selectedAppointment={selectedAppointment}
        />
      )}
    </Box>
  );
};
