import { useCallback, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Appointment,
  CareTeamUserType,
  ClickStreamActivityEventType,
  SessionCommunicationMethod,
  SessionSlot,
} from "@vivantehealth/vivante-core";
import { Button, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { BaseModal } from "@Components/BaseModal/BaseModal";
import { AppointmentTimeSlot } from "@Features/appointmentsNew/types/appointments.types";
import { ActionPlansStateSlice, actionPlansStateSelector } from "@Features/carePlan/store/actionPlansStateSlice";
import { SlotPage } from "@Features/careTeam/store/careTeamStateSlice";
import { getCommunicationMethod } from "@Features/careTeam/utils/careTeamState.util";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { useSchedulingErrorModal } from "../hooks/useSchedulingErrorModal";
import { CareTeamSchedulingModalNew } from "./schedulingModalNew/CareTeamSchedulingModalNew";
import { useCareTeam } from "../hooks/useCareTeam";

const BUTTON_STRINGS = appStrings.buttonText;
const CARE_TEAM_STRINGS = appStrings.features.careTeam;

export type SelectedSlot = Readonly<{
  slotPage: SlotPage;
  sessionSlot: SessionSlot;
}>;

type CareTeamSchedulingModalContainerProps = Readonly<{
  isModalOpen: boolean;
  clearSchedulingModalData?: () => void;
  onToggleSchedulingModal: (toggle: boolean) => void;
  rescheduledAppointment?: Appointment;
}> &
  ReturnType<typeof useCareTeam>;

const CARE_TEAM_MEMBER_INTERVENTION_ID_MAP = {
  [CareTeamUserType.SHERPA]: "schedule_health_coach",
  [CareTeamUserType.DIETITIAN]: "schedule_dietitian",
} as const;

const getCareTeamInterventionId = (careTeamUserType?: CareTeamUserType) => {
  if (careTeamUserType !== CareTeamUserType.SHERPA && careTeamUserType !== CareTeamUserType.DIETITIAN) {
    return undefined;
  }

  return CARE_TEAM_MEMBER_INTERVENTION_ID_MAP[careTeamUserType];
};

export const CareTeamSchedulingModalContainerNew = ({
  isModalOpen,
  clearSchedulingModalData,
  onToggleSchedulingModal,
  selectedCareTeamMemberId,
  setSelectedCareTeamMemberId,
  rescheduledAppointment,
  careTeamUsers,
  selectedCommunicationMethod,
  timeSlots,
  timeSlotsLoading,
  handleSelectedCommunicationMethod,
  handleOnConfirmationModalClose,
  handleSelectedAppointmentSlot,
  handleSubmitScheduling,
  selectedAppointment,
  isSubmitting,
  modalKey,
}: CareTeamSchedulingModalContainerProps) => {
  const dispatch = useDispatch();
  const nextBtnRef = useRef<HTMLButtonElement | null>(null);
  const { isErrorModalOpen, schedulingErrorMessage, handleCloseErrorModal } = useSchedulingErrorModal();
  const { sendEventAnalytics } = useAnalyticsHook();

  const interventions = useSelector(actionPlansStateSelector("interventionEntities"));
  const careTeamMember = careTeamUsers?.find((user) => user.id === selectedCareTeamMemberId);
  const shouldSetCommunicationMethod =
    !selectedCommunicationMethod && !!rescheduledAppointment?.communicationMethod && isModalOpen;

  // When the modal is opened, we check if we are rescheduling an appointment and if so, we set the communication method
  if (shouldSetCommunicationMethod && selectedCareTeamMemberId) {
    handleSelectedCommunicationMethod(getCommunicationMethod(rescheduledAppointment?.communicationMethod));
  }

  const handleErrorClose = () => {
    handleCloseErrorModal();
    handleModalClose(false, true);
  };

  const handleModalClose = useCallback(
    (isClosedBeforeAppointmentMade = true, showSchedulingModal = false) => {
      setSelectedCareTeamMemberId(undefined);
      handleOnConfirmationModalClose(isClosedBeforeAppointmentMade);
      onToggleSchedulingModal(showSchedulingModal);

      if (clearSchedulingModalData) {
        clearSchedulingModalData();
      }
    },
    [clearSchedulingModalData, handleOnConfirmationModalClose, onToggleSchedulingModal, setSelectedCareTeamMemberId],
  );

  const handleSelectMember = useCallback(
    (memberId: string) => {
      if (selectedCareTeamMemberId !== memberId) {
        setSelectedCareTeamMemberId(memberId);
        handleSelectedAppointmentSlot(undefined);

        sendEventAnalytics(ClickStreamActivityEventType.SCHEDULING_SELECT_CARETEAM_MEMBER, {
          careTeamUserId: memberId,
        });
      }
    },
    [handleSelectedAppointmentSlot, selectedCareTeamMemberId, sendEventAnalytics, setSelectedCareTeamMemberId],
  );

  const onSelectSlotClick = (slot: AppointmentTimeSlot | undefined) => {
    handleSelectedAppointmentSlot(slot);
    // On slot selection, focus on the next button
    if (nextBtnRef.current) {
      // Adds a delay to ensure focus after button is changed from disabled to enabled
      setTimeout(() => nextBtnRef.current?.focus(), 1);
    }
  };

  const onCommunicationMethodClick = (communicationMethod: SessionCommunicationMethod) => {
    handleSelectedCommunicationMethod(communicationMethod);
  };

  const onSubmitClickHandler = async () => {
    await handleSubmitScheduling(rescheduledAppointment?.id);

    /**
     * Determine if there are any open interventions depending on the care team member user type
     * and update the intervention
     */
    const careTeamMemberInterventionId = getCareTeamInterventionId(careTeamMember?.userType);
    const intervention = careTeamMemberInterventionId ? interventions?.[careTeamMemberInterventionId] : undefined;

    if (intervention && intervention.state !== "COMPLETED") {
      dispatch(
        ActionPlansStateSlice.actions.setInterventionState({
          intervention,
          newState: "COMPLETED",
        }),
      );
    }
  };

  return (
    <>
      <CareTeamSchedulingModalNew
        isModalOpen={isModalOpen}
        handleSelectMember={handleSelectMember}
        careTeamUsers={careTeamUsers}
        selectedCareTeamMemberId={selectedCareTeamMemberId}
        timeSlots={timeSlots}
        timeSlotsLoading={timeSlotsLoading}
        handleSelectedAppointmentSlot={onSelectSlotClick}
        selectedAppointment={selectedAppointment}
        selectedCommunicationMethod={selectedCommunicationMethod}
        onCommunicationMethodClick={onCommunicationMethodClick}
        closeSchedulingModal={() => handleModalClose()}
        nextBtnRef={nextBtnRef}
        isSubmitting={isSubmitting}
        onSubmit={onSubmitClickHandler}
        onSelectSlotClick={onSelectSlotClick}
        key={modalKey}
      />

      <BaseModal
        title={CARE_TEAM_STRINGS.schedulingModal.errorTitle}
        isModalOpen={isErrorModalOpen}
        onClose={handleErrorClose}
        bodyContent={<Typography variant="body">{schedulingErrorMessage}</Typography>}
        actions={
          <Button variant="primary" onClick={handleErrorClose} fullWidth>
            {BUTTON_STRINGS.gotIt}
          </Button>
        }
      />
    </>
  );
};
