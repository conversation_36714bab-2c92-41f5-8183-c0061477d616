import { CareTeamUser } from "@vivantehealth/vivante-core";
import { Box, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { SPACING_12_PX, SPACING_16_PX } from "@Assets/style_constants";
import { CareTeamAvatar } from "@Components/CareTeamAvatar/CareTeamAvatar";

import { getUserType } from "../utils/careteam.utils";

type CareTeamMemberProps = Readonly<{
  member: CareTeamUser;
  onClick: () => void;
  isSmallWidth?: boolean;
  isSelected?: boolean;
}>;

export const CareTeamMemberCard = ({ member, onClick, isSmallWidth, isSelected }: CareTeamMemberProps) => {
  const careTeamMemberType = getUserType(member);

  return (
    <Paper
      component="button"
      sx={{
        display: "flex",
        justifyContent: "left",
        alignItems: "center",
        minWidth: isSmallWidth ? "177px" : "213px",
        padding: isSmallWidth ? SPACING_12_PX : SPACING_16_PX,
        borderColor: isSelected ? color.border.action.hover : color.border.default,
        cursor: "pointer",
        ":hover": {
          borderColor: color.border.action.hover,
        },
      }}
      onClick={onClick}
      aria-label={appStrings.a11y.careTeamMember(careTeamMemberType, member.firstName)}
    >
      <CareTeamAvatar
        avatarLink={member.avatarLink}
        altText={appStrings.a11y.careTeamMemberAvatarText(careTeamMemberType.toLowerCase())}
      />
      <Box display="flex" flexDirection="column" alignItems="flex-start" ml={SPACING_12_PX}>
        <Typography variant="h4" color={color.text.strong} tabIndex={-1}>
          {member.firstName}
        </Typography>
        <Typography variant="bodyDense" tabIndex={-1}>
          {careTeamMemberType}
        </Typography>
      </Box>
    </Paper>
  );
};
