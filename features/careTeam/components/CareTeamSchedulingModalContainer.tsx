import { useCallback, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Appointment,
  CareTeamUserType,
  ClickStreamActivityEventType,
  SessionCommunicationMethod,
  SessionSlot,
} from "@vivantehealth/vivante-core";
import { Button, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { BaseModal } from "@Components/BaseModal/BaseModal";
import { ActionPlansStateSlice, actionPlansStateSelector } from "@Features/carePlan/store/actionPlansStateSlice";
import { SlotPage } from "@Features/careTeam/store/careTeamStateSlice";
import { getCommunicationMethod } from "@Features/careTeam/utils/careTeamState.util";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { CareTeamSchedulingModal } from "./schedulingModal/CareTeamSchedulingModal";
import { useCareTeamHook } from "../hooks/careTeamHook";
import { useSchedulingErrorModal } from "../hooks/useSchedulingErrorModal";

const BUTTON_STRINGS = appStrings.buttonText;
const CARE_TEAM_STRINGS = appStrings.features.careTeam;

export type SelectedSlot = Readonly<{
  slotPage: SlotPage;
  sessionSlot: SessionSlot;
}>;

type CareTeamSchedulingModalContainerProps = Readonly<{
  isModalOpen: boolean;
  clearSchedulingModalData?: () => void;
  onToggleSchedulingModal: (toggle: boolean) => void;
  selectedMemberId?: string;
  setSelectedMemberId: (memberId?: string) => void;
  rescheduledAppointment?: Appointment;
}>;

const CARE_TEAM_MEMBER_INTERVENTION_ID_MAP = {
  [CareTeamUserType.SHERPA]: "schedule_health_coach",
  [CareTeamUserType.DIETITIAN]: "schedule_dietitian",
} as const;

const getCareTeamInverventionId = (careTeamUserType?: CareTeamUserType) => {
  if (!careTeamUserType) {
    return undefined;
  }

  return careTeamUserType === CareTeamUserType.SHERPA || careTeamUserType === CareTeamUserType.DIETITIAN
    ? CARE_TEAM_MEMBER_INTERVENTION_ID_MAP[careTeamUserType]
    : undefined;
};

export const CareTeamSchedulingModalContainer = ({
  isModalOpen,
  clearSchedulingModalData,
  onToggleSchedulingModal,
  selectedMemberId,
  setSelectedMemberId,
  rescheduledAppointment,
}: CareTeamSchedulingModalContainerProps) => {
  const dispatch = useDispatch();
  const nextBtnRef = useRef<HTMLButtonElement | null>(null);
  const {
    isSchedulingLoading,
    careTeamUsers,
    slotPages,
    slotsLoading,
    selectedSlotId,
    selectedCommunicationMethod,
    setSelectedSlot,
    setSelectedCommunicationMethod,
    submitScheduling,
    loadSlotPages,
    onConfirmationModalClose,
  } = useCareTeamHook();
  const { isErrorModalOpen, schedulingErrorMessage, handleCloseErrorModal } = useSchedulingErrorModal();
  const { sendEventAnalytics } = useAnalyticsHook();
  const prepareToCloseAppointmentModal = useRef(false);
  // We maintain a key for the modal in state so as to force reset the modal when it is closed
  const [modalKey, setModalKey] = useState(0);

  const interventions = useSelector(actionPlansStateSelector("interventionEntities"));
  const careTeamMember = careTeamUsers?.find((user) => user.id === selectedMemberId);
  const shouldSetCommunicationMethod =
    !selectedCommunicationMethod && !!rescheduledAppointment?.communicationMethod && isModalOpen;
  const shouldLoadSlotPages = selectedMemberId && selectedCommunicationMethod && slotPages === null && !slotsLoading;

  // When the modal is opened, we check if we are rescheduling an appointment and if so, we set the communication method
  if (shouldSetCommunicationMethod && selectedMemberId) {
    setSelectedCommunicationMethod(getCommunicationMethod(rescheduledAppointment?.communicationMethod));
    loadSlotPages(selectedMemberId);
  }
  // When the modal is opened, we check if we are rescheduling an appointment and if we have all of the necessary data to
  // load the slot pages
  if (shouldLoadSlotPages) {
    loadSlotPages(selectedMemberId);
  }

  const handleErrorClose = () => {
    handleCloseErrorModal();
    handleModalClose(false, true);
  };

  const handleModalClose = useCallback(
    (isClosedBeforeAppointmentMade = true, showSchedulingModal = false) => {
      setSelectedMemberId(undefined);
      onConfirmationModalClose();
      setModalKey((prevKey) => prevKey + 1);
      onToggleSchedulingModal(showSchedulingModal);

      if (clearSchedulingModalData) {
        clearSchedulingModalData();
      }

      if (isClosedBeforeAppointmentMade) {
        sendEventAnalytics(ClickStreamActivityEventType.SCHEDULING_ABORT);
      }
    },
    [
      clearSchedulingModalData,
      onConfirmationModalClose,
      onToggleSchedulingModal,
      sendEventAnalytics,
      setSelectedMemberId,
    ],
  );

  /**
   * We have this useEffect as a side effect of submitting the scheduling
   * This is so the modal stays open until submission completes OR the error modal shows
   * Otherwise, if we get the error modal it can be jarring to have it pop in when it does
   * Hopefully in the future we can switch to using RTK Query and use the isLoading state from there
   */
  useEffect(() => {
    if (prepareToCloseAppointmentModal.current && !isSchedulingLoading && !schedulingErrorMessage) {
      handleModalClose(false);
      prepareToCloseAppointmentModal.current = false;
    }
  }, [prepareToCloseAppointmentModal, isSchedulingLoading, schedulingErrorMessage, handleModalClose]);

  const onSelectMemberClick = useCallback(
    (memberId: string) => {
      if (selectedMemberId !== memberId) {
        setSelectedMemberId(memberId);
        setSelectedSlot(undefined);

        if (selectedCommunicationMethod) {
          loadSlotPages(memberId);
        }

        sendEventAnalytics(ClickStreamActivityEventType.SCHEDULING_SELECT_CARETEAM_MEMBER, {
          careTeamUserId: memberId,
        });
      }
    },
    [
      loadSlotPages,
      selectedCommunicationMethod,
      selectedMemberId,
      sendEventAnalytics,
      setSelectedMemberId,
      setSelectedSlot,
    ],
  );

  const onSelectSlotClick = (slot: SelectedSlot) => {
    setSelectedSlot(slot);
    // On slot selection, focus on the next button
    if (nextBtnRef.current) {
      // Adds a delay to ensure focus after button is changed from disabled to enabled
      setTimeout(() => nextBtnRef.current?.focus(), 1);
    }
  };

  const onCommunicationMethodClick = (communicationMethod: SessionCommunicationMethod) => {
    setSelectedCommunicationMethod(communicationMethod);
    // Only load slots if we haven't already selected a communicationMethod and have a selectedMemberId
    if (selectedCommunicationMethod === undefined && selectedMemberId) {
      loadSlotPages(selectedMemberId);
    }
  };

  const onSubmitClickHandler = () => {
    if (selectedMemberId) {
      submitScheduling(rescheduledAppointment?.id, selectedMemberId);
    }

    prepareToCloseAppointmentModal.current = true;
    /**
     * Determine if there are any open interventions depending on the care team member user type
     * and update the intervention
     */
    const careTeamMemberInterventionId = getCareTeamInverventionId(careTeamMember?.userType);
    const intervention = careTeamMemberInterventionId ? interventions?.[careTeamMemberInterventionId] : undefined;

    if (intervention && intervention.state !== "COMPLETED") {
      dispatch(
        ActionPlansStateSlice.actions.setInterventionState({
          intervention,
          newState: "COMPLETED",
        }),
      );
    }
  };

  return (
    <>
      <CareTeamSchedulingModal
        isModalOpen={isModalOpen}
        onSelectMemberClick={onSelectMemberClick}
        careTeamUsers={careTeamUsers}
        selectedMemberId={selectedMemberId}
        slotPages={slotPages}
        slotsLoading={slotsLoading}
        onSelectSlotClick={onSelectSlotClick}
        selectedSlotId={selectedSlotId}
        selectedCommunicationMethod={selectedCommunicationMethod}
        onCommunicationMethodClick={onCommunicationMethodClick}
        closeSchedulingModal={() => handleModalClose()}
        nextBtnRef={nextBtnRef}
        isSchedulingLoading={isSchedulingLoading}
        onSubmit={onSubmitClickHandler}
        key={modalKey}
      />

      <BaseModal
        title={CARE_TEAM_STRINGS.schedulingModal.errorTitle}
        isModalOpen={isErrorModalOpen}
        onClose={handleErrorClose}
        bodyContent={<Typography variant="body">{schedulingErrorMessage}</Typography>}
        actions={
          <Button variant="primary" onClick={handleErrorClose} fullWidth>
            {BUTTON_STRINGS.gotIt}
          </Button>
        }
      />
    </>
  );
};
