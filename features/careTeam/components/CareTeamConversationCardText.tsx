import { Box, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { SPACING_12_PX } from "@Assets/style_constants";

type CareTeamConversationCardTextProps = Readonly<{
  careTeamMember: string;
  lastConversationMessage: string;
  dateOfLastMessage: string;
}>;

export const CareTeamConversationCardText = ({
  careTeamMember,
  lastConversationMessage,
  dateOfLastMessage,
}: CareTeamConversationCardTextProps) => {
  return (
    <Box sx={styles.textContainer}>
      <Box display="flex" flexDirection="row" justifyContent="space-between" width={"100%"}>
        <Typography variant="h4" tabIndex={-1} color={color.text.strong}>
          {careTeamMember}
        </Typography>
        <Typography variant="bodyDense" tabIndex={-1} color={color.text.subtle}>
          {dateOfLastMessage}
        </Typography>
      </Box>
      <Typography variant="bodyDense" sx={styles.textLastConversation} tabIndex={-1}>
        {lastConversationMessage}
      </Typography>
    </Box>
  );
};

const styles = {
  textContainer: {
    display: "flex",
    flexDirection: "column",
    width: "100%",
    marginLeft: SPACING_12_PX,
  },
  textLastConversation: {
    width: "500px",
    whiteSpace: "nowrap",
    textOverflow: "ellipsis",
    overflow: "hidden",
    textAlign: "left",
  },
} as const;
