import { useEffect, useState } from "react";
import { Appointment, ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { Box, Button, CircularProgress, Paper, Typography } from "@mui/material";

import { bffApi } from "@Api/bffApi";
import { appStrings } from "@Assets/app_strings";
import { RADIUS_12_PX, SPACING_12_PX, SPACING_16_PX } from "@Assets/style_constants";
import { AppointmentDuration } from "@Components/AppointmentDuration/AppointmentDuration";
import { AppointmentInformation } from "@Components/AppointmentInformation/AppointmentInformation";
import { BasicMenu } from "@Components/BasicMenu/BasicMenu";
import { PractitionerInformation } from "@Components/PractitionerInformation/PractitionerInformation";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { CalendarProviderOption } from "@Lib/infrastructure/calendar/calendar-provider-option";
import { useAppDispatch } from "@Store/hooks";

import { MINUTES_BEFORE_SESSION_TO_SHOW_LINK } from "../assets/constants";
import { AppointmentActionData } from "../CareTeamScreenContainer";
import { appointmentData, calendarTypes } from "../utils/careteam.utils";

const CARE_TEAM_STRINGS = appStrings.features.careTeam;

type CarePlanAppointmentsTabProps = Readonly<{
  appointments?: Appointment[];
  isLoading: boolean;
  isAppointmentListError: boolean;
  handleUpcomingAppointmentEditClick: (appointment: AppointmentActionData) => void;
  onAddToCalendarClick: (data: { calendarType: CalendarProviderOption; appointment: Appointment }) => void;
}>;

export const CareTeamAppointmentsTab = ({
  appointments,
  isLoading,
  isAppointmentListError,
  handleUpcomingAppointmentEditClick,
  onAddToCalendarClick,
}: CarePlanAppointmentsTabProps) => {
  const dispatch = useAppDispatch();
  const [currentTime, setCurrentTime] = useState(new Date());
  const { sendEventAnalytics } = useAnalyticsHook();

  useEffect(() => {
    // check every minute for conditionally enabling appointment buttons
    const updateCurrentTime = () => {
      setCurrentTime(new Date());
    };

    const intervalId = setInterval(updateCurrentTime, 60000);

    return () => {
      clearInterval(intervalId);
    };
  }, []);

  const handleRescheduleAppointment = (appointment: Appointment) => {
    handleUpcomingAppointmentEditClick({ appointment, action: "reschedule" });
  };

  const handleCancelAppointment = (appointment: Appointment) => {
    handleUpcomingAppointmentEditClick({ appointment, action: "cancel" });
  };

  const handleCalendarClick = (calendarType: CalendarProviderOption, appointment: Appointment) => {
    onAddToCalendarClick({ calendarType, appointment });
  };

  const handleRetryAppointmentList = () => {
    dispatch(bffApi.util.invalidateTags(["Appointment"]));
  };

  if (isAppointmentListError) {
    return (
      <Box display="flex" flexDirection="column" gap={4} alignItems="flex-start">
        <Typography variant="body">{CARE_TEAM_STRINGS.appointmentListError}</Typography>

        <Button variant="secondary" size="small" onClick={handleRetryAppointmentList}>
          {appStrings.buttonText.tryAgain}
        </Button>
      </Box>
    );
  }

  if (!appointments || isLoading) {
    return (
      <Box mt={8} width={"100%"} textAlign="center">
        <CircularProgress />
      </Box>
    );
  }

  const orderedAppointments = [...appointments].sort(({ slot: timeSlotA }, { slot: timeSlotB }) => {
    return timeSlotA.start.toISOString().localeCompare(timeSlotB.start.toISOString());
  });

  return (
    <Box display="flex" flexDirection="column">
      {orderedAppointments.length === 0 ? (
        <Typography variant="body">{CARE_TEAM_STRINGS.appointmentsEmptyState}</Typography>
      ) : null}
      {orderedAppointments.map((appointment, index) => {
        const { practitioner, appointmentDuration, isPhoneAppointment, minutesUntilAppointment } = appointmentData(
          appointment,
          currentTime,
        );

        return (
          <Paper key={appointment.id} sx={{ mb: 4 }}>
            <Box display="flex" justifyContent="space-between" alignItems="start">
              <PractitionerInformation practitioner={practitioner} />
              <AppointmentDuration duration={appointmentDuration} />
            </Box>

            <Box my={4}>
              <AppointmentInformation
                startTime={appointment.slot.start}
                communicationMethod={appointment.communicationMethod}
              />
            </Box>
            <Box display="flex">
              {isPhoneAppointment ? null : (
                <Button
                  variant="primary"
                  disabled={minutesUntilAppointment > MINUTES_BEFORE_SESSION_TO_SHOW_LINK}
                  onClick={() => window.open(appointment.meetingLink, "_blank", "noopener,noreferrer")}
                  sx={{ mr: 4 }}
                >
                  {CARE_TEAM_STRINGS.joinMeetingButton}
                </Button>
              )}
              <BasicMenu
                id={`appointment-menu-${index}`}
                ariaLabel={appStrings.a11y.editAppointmentMenu}
                buttonVariant="secondary"
                menuIconRight="DownChevron"
                menuItems={[
                  {
                    type: "nested",
                    text: CARE_TEAM_STRINGS.menuItems.addToCalendar,
                    menuItems: calendarTypes.map((calendar) => ({
                      text: calendar.text,
                      onClick: () => handleCalendarClick(calendar.value, appointment),
                    })),
                  },
                  {
                    text: CARE_TEAM_STRINGS.menuItems.rescheduleAppointment,
                    onClick: () => handleRescheduleAppointment(appointment),
                    isDisabled: currentTime >= appointment.canEditUntil,
                  },
                  {
                    text: CARE_TEAM_STRINGS.menuItems.cancelAppointment,
                    onClick: () => handleCancelAppointment(appointment),
                    isDisabled: currentTime >= appointment.canEditUntil,
                  },
                ]}
                buttonStyles={{ borderRadius: RADIUS_12_PX, padding: `${SPACING_12_PX} ${SPACING_16_PX}` }}
                onMenuClick={() => {
                  sendEventAnalytics(ClickStreamActivityEventType.APPOINTMENT_MENU_OPENED);
                }}
              >
                {CARE_TEAM_STRINGS.editAppointmentButton}
              </BasicMenu>
            </Box>
            {isPhoneAppointment ? (
              <Typography variant="body" mt={4}>
                {CARE_TEAM_STRINGS.phoneCallFootnote}
              </Typography>
            ) : null}
            {!isPhoneAppointment && minutesUntilAppointment > MINUTES_BEFORE_SESSION_TO_SHOW_LINK && (
              <Typography variant="body" mt={4}>
                {CARE_TEAM_STRINGS.videoCallFootnote(MINUTES_BEFORE_SESSION_TO_SHOW_LINK)}
              </Typography>
            )}
          </Paper>
        );
      })}
    </Box>
  );
};
