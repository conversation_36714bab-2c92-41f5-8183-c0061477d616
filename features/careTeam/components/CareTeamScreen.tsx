import { Dispatch, SetStateAction, useState } from "react";
import { Appointment, CareTeamUser } from "@vivantehealth/vivante-core";
import { Box, Button, Tab, Tabs, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { TabsPanel } from "@Components/TabsPanel/TabsPanel";
import { tabsA11yProps } from "@Components/TabsPanel/utils/tabsA11yProps";
import { CalendarProviderOption } from "@Lib/infrastructure/calendar/calendar-provider-option";

import { CallModal } from "./CallModal";
import { CareTeamAppointmentsTab } from "./CareTeamAppointmentTab";
import { CareTeamConversationTab } from "./CareTeamConversationTab";
import { CareTeamMemberCard } from "./CareTeamMemberCard";
import { CareTeamMemberModal } from "./CareTeamMemberModal";
import { AppointmentActionData } from "../CareTeamScreenContainer";

const BUTTON_STRINGS = appStrings.buttonText;
const CARE_TEAM_STRINGS = appStrings.features.careTeam;

type CareTeamScreenProps = Readonly<{
  careTeamUsers: CareTeamUser[];
  isLoading: boolean;
  isAppointmentListError: boolean;
  isMemberModalOpen: boolean;
  upcomingAppointments?: Appointment[];
  currentlySelectedMember?: CareTeamUser;
  openScheduleModal: () => void;
  setCurrentMemberId: (memberId: string) => void;
  onNavigateToConversation: (user: CareTeamUser) => void;
  handleUpcomingAppointmentEditClick: (appointment: AppointmentActionData) => void;
  onAddToCalendarClick: (data: { calendarType: CalendarProviderOption; appointment: Appointment }) => void;
  setIsMemberModalOpen: Dispatch<SetStateAction<boolean>>;
  setCurrentlySelectedMember: (member: CareTeamUser) => void;
}>;

export const CareTeamScreen = ({
  careTeamUsers,
  isLoading,
  isAppointmentListError,
  isMemberModalOpen,
  upcomingAppointments,
  currentlySelectedMember,
  openScheduleModal,
  setCurrentMemberId,
  onNavigateToConversation,
  handleUpcomingAppointmentEditClick,
  onAddToCalendarClick,
  setIsMemberModalOpen,
  setCurrentlySelectedMember,
}: CareTeamScreenProps) => {
  const [isCallModalOpen, setIsCallModalOpen] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<"appointments" | "conversations">("appointments");

  const handleMemberModalScheduleClick = () => {
    openScheduleModal();

    if (currentlySelectedMember) {
      setCurrentMemberId(currentlySelectedMember.id);
    }

    setIsMemberModalOpen(false);
  };

  const handleMemberCardClick = (member: CareTeamUser) => {
    setCurrentlySelectedMember(member);
    setIsMemberModalOpen(true);
  };

  return (
    <>
      <CallModal isOpen={isCallModalOpen} onExitClick={() => setIsCallModalOpen(false)} />
      <CareTeamMemberModal
        isModalOpen={isMemberModalOpen}
        member={currentlySelectedMember}
        handleExitModalClick={() => setIsMemberModalOpen(false)}
        handleScheduleClick={handleMemberModalScheduleClick}
        handleNavigateToConversation={onNavigateToConversation}
      />
      <Box sx={{ mb: 4, display: "flex", alignItems: "center" }}>
        <Typography variant="h1Serif" sx={{ mr: 5 }}>
          {CARE_TEAM_STRINGS.pageHeader}
        </Typography>
        <Button variant="secondary" onClick={() => setIsCallModalOpen(true)} startIcon={<AppIcon name="Phone" />}>
          {BUTTON_STRINGS.callNow}
        </Button>
      </Box>
      <Typography variant="body">{CARE_TEAM_STRINGS.pageSubheader}</Typography>
      <Box display="flex" gap={4} my={5}>
        {!careTeamUsers?.length ? (
          <Box />
        ) : (
          careTeamUsers.map((member: CareTeamUser) => (
            <CareTeamMemberCard key={member.id} member={member} onClick={() => handleMemberCardClick(member)} />
          ))
        )}
      </Box>

      <Button onClick={openScheduleModal} variant="primary" sx={{ mb: 7 }}>
        {BUTTON_STRINGS.scheduleAnAppointment}
      </Button>

      <Tabs value={activeTab} onChange={(_, targetTab) => setActiveTab(targetTab)}>
        <Tab
          label={CARE_TEAM_STRINGS.appointments}
          value={CARE_TEAM_STRINGS.appointments.toLowerCase()}
          {...tabsA11yProps(CARE_TEAM_STRINGS.appointments.toLowerCase())}
        />
        <Tab
          label={CARE_TEAM_STRINGS.conversations}
          value={CARE_TEAM_STRINGS.conversations.toLowerCase()}
          {...tabsA11yProps(CARE_TEAM_STRINGS.conversations.toLowerCase())}
        />
      </Tabs>
      <TabsPanel tabName={CARE_TEAM_STRINGS.appointments.toLowerCase()} currentSelectedTabName={activeTab}>
        <CareTeamAppointmentsTab
          appointments={upcomingAppointments}
          handleUpcomingAppointmentEditClick={handleUpcomingAppointmentEditClick}
          onAddToCalendarClick={onAddToCalendarClick}
          isLoading={isLoading}
          isAppointmentListError={isAppointmentListError}
        />
      </TabsPanel>
      <TabsPanel tabName={CARE_TEAM_STRINGS.conversations.toLowerCase()} currentSelectedTabName={activeTab}>
        <CareTeamConversationTab handleNavigateToConversation={onNavigateToConversation} />
      </TabsPanel>
    </>
  );
};
