import { Button, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { BaseModal } from "@Components/BaseModal/BaseModal";

import { AppointmentAction } from "../CareTeamScreenContainer";

const BUTTON_STRINGS = appStrings.buttonText;
const CARE_TEAM_STRINGS = appStrings.features.careTeam;

type EditAppointmentConfirmationModalProps = Readonly<{
  showConfirmationModal: boolean;
  confirmationType?: AppointmentAction;
  closeConfirmation: () => void;
  setSelectedMemberId: (id?: string) => void;
  confirmRescheduleAppointment: () => void;
  confirmCancelAppointment: () => void;
}>;

export const EditAppointmentConfirmationModal = ({
  showConfirmationModal,
  confirmationType,
  closeConfirmation,
  setSelectedMemberId,
  confirmRescheduleAppointment,
  confirmCancelAppointment,
}: EditAppointmentConfirmationModalProps) => {
  const isRescheduleConfirmation = confirmationType === "reschedule";

  return (
    <BaseModal
      isModalOpen={showConfirmationModal || false}
      onClose={() => {
        closeConfirmation();
        setSelectedMemberId(undefined);
      }}
      title={CARE_TEAM_STRINGS.editConfirmationTitle(confirmationType)}
      bodyContent={<Typography variant="body">{CARE_TEAM_STRINGS.editConfirmation(confirmationType)}</Typography>}
      actions={
        <>
          <Button
            variant="secondary"
            onClick={() => {
              closeConfirmation();
              setSelectedMemberId(undefined);
            }}
            fullWidth
          >
            {isRescheduleConfirmation ? BUTTON_STRINGS.cancel : BUTTON_STRINGS.no}
          </Button>
          <Button
            variant="primary"
            onClick={isRescheduleConfirmation ? confirmRescheduleAppointment : confirmCancelAppointment}
            fullWidth
          >
            {isRescheduleConfirmation ? CARE_TEAM_STRINGS.menuItems.rescheduleAppointment : BUTTON_STRINGS.yesCancel}
          </Button>
        </>
      }
    />
  );
};
