import { useState } from "react";
import { CareTeamUser, SessionCommunicationMethod } from "@vivantehealth/vivante-core";
import { Button, CircularProgress } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { BaseModal } from "@Components/BaseModal/BaseModal";
import { SlotPage } from "@Features/careTeam/store/careTeamStateSlice";

import { AppointmentConfirmationContentSection } from "./AppointmentConfirmationContentSection";
import { SchedulingContentSection } from "./SchedulingContentSection";
import { keyToSelectedSlot } from "../../utils/careTeamState.util";
import { SelectedSlot } from "../CareTeamSchedulingModalContainer";

const BUTTON_STRINGS = appStrings.buttonText;

export type SchedulingScreens = "scheduling" | "appointmentConfirmation";

type CareTeamSchedulingModalProps = Readonly<{
  isModalOpen: boolean;
  careTeamUsers?: CareTeamUser[];
  onSelectMemberClick: (memberId: string) => void;
  onSelectSlotClick: (slot: SelectedSlot) => void;
  selectedMemberId?: string;
  slotPages: SlotPage[];
  slotsLoading: boolean;
  selectedSlotId?: string;
  onCommunicationMethodClick: (communicationMethod: SessionCommunicationMethod) => void;
  selectedCommunicationMethod?: SessionCommunicationMethod;
  closeSchedulingModal: () => void;
  nextBtnRef: React.RefObject<HTMLButtonElement | null>;
  isSchedulingLoading: boolean;
  onSubmit: () => void;
  selectedScreen?: SchedulingScreens;
}>;

export const CareTeamSchedulingModal = ({
  isModalOpen,
  careTeamUsers,
  selectedMemberId,
  onSelectMemberClick,
  slotPages,
  slotsLoading,
  onSelectSlotClick,
  selectedSlotId,
  selectedCommunicationMethod,
  onCommunicationMethodClick,
  closeSchedulingModal,
  nextBtnRef,
  isSchedulingLoading,
  onSubmit,
  selectedScreen,
}: CareTeamSchedulingModalProps) => {
  const [currentScreen, setCurrentScreen] = useState<SchedulingScreens>(selectedScreen ?? "scheduling");
  const isSubmitEnabled = !!selectedMemberId && selectedSlotId && !!selectedCommunicationMethod;
  const isSchedulingScreen = currentScreen === "scheduling";
  const primaryButtonText = isSchedulingScreen ? BUTTON_STRINGS.next : BUTTON_STRINGS.confirm;
  const careTeamMember = careTeamUsers?.find((member) => member.id === selectedMemberId);

  return (
    <BaseModal
      isModalOpen={isModalOpen}
      onClose={closeSchedulingModal}
      displayCloseButton={false}
      bodyContent={
        <>
          {isSchedulingScreen && careTeamUsers ? (
            <SchedulingContentSection
              careTeamUsers={careTeamUsers}
              selectedMemberId={selectedMemberId}
              onSelectMemberClick={onSelectMemberClick}
              selectedCommunicationMethod={selectedCommunicationMethod}
              onCommunicationMethodClick={onCommunicationMethodClick}
              slotsLoading={slotsLoading}
              slotPages={slotPages}
              onSelectSlotClick={onSelectSlotClick}
              selectedSlotId={selectedSlotId}
            />
          ) : null}

          {!isSchedulingScreen && selectedSlotId && careTeamMember ? (
            <AppointmentConfirmationContentSection
              communicationMethod={selectedCommunicationMethod}
              slot={keyToSelectedSlot(selectedSlotId)}
              member={careTeamMember}
            />
          ) : null}
        </>
      }
      actions={
        <>
          <Button
            variant="secondary"
            onClick={isSchedulingScreen ? closeSchedulingModal : () => setCurrentScreen("scheduling")}
            fullWidth
            disabled={isSchedulingLoading}
          >
            {isSchedulingScreen ? BUTTON_STRINGS.cancel : BUTTON_STRINGS.edit}
          </Button>

          <Button
            variant="primary"
            onClick={isSchedulingScreen ? () => setCurrentScreen("appointmentConfirmation") : onSubmit}
            fullWidth
            disabled={!isSubmitEnabled || isSchedulingLoading}
            ref={nextBtnRef}
          >
            {isSchedulingLoading ? <CircularProgress size={24} color="inherit" /> : primaryButtonText}
          </Button>
        </>
      }
    />
  );
};
