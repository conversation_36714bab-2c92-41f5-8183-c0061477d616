import { Box, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

type SchedulingSectionHeaderProps = Readonly<{
  number: string;
  text: string;
  typographyVariant?: "h3" | "h4";
}>;

export const SchedulingSectionHeader = ({ number, text, typographyVariant = "h3" }: SchedulingSectionHeaderProps) => (
  <Box display="flex" mb={5} tabIndex={0}>
    <Typography variant={typographyVariant} mr={2} tabIndex={-1} color={color.text.strong}>
      {number}
    </Typography>

    <Typography variant={typographyVariant} tabIndex={-1} color={color.text.strong}>
      {text}
    </Typography>
  </Box>
);
