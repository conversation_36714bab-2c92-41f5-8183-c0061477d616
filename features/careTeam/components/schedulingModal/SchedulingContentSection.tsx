import { CareTeamUser, SessionCommunicationMethod } from "@vivantehealth/vivante-core";
import { Box, CircularProgress } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { SlotPage } from "@Features/careTeam/store/careTeamStateSlice";

import { SchedulingAppointmentSection } from "./SchedulingAppointmentSection";
import { SchedulingCareTeamMembers } from "./SchedulingCareTeamMembers";
import { SchedulingReachByMethod } from "./SchedulingReachByMethod";
import { SchedulingSectionHeader } from "./SchedulingSectionHeader";
import { SelectedSlot } from "../CareTeamSchedulingModalContainer";

const CARE_TEAM_STRINGS = appStrings.features.careTeam;
/**
 * We set the minimum height of the modal content to 420px to prevent an issue where we would get a
 * flickering scrollbar when the loading spinner was showing
 */
const MODAL_CONTENT_MIN_HEIGHT = "420px";

type SchedulingContentSectionProps = Readonly<{
  careTeamUsers: CareTeamUser[];
  onSelectMemberClick: (memberId: string) => void;
  onSelectSlotClick: (slot: SelectedSlot) => void;
  selectedMemberId?: string;
  slotPages: SlotPage[];
  slotsLoading: boolean;
  selectedSlotId?: string;
  onCommunicationMethodClick: (communicationMethod: SessionCommunicationMethod) => void;
  selectedCommunicationMethod?: SessionCommunicationMethod;
}>;

export const SchedulingContentSection = ({
  careTeamUsers,
  selectedMemberId,
  onSelectMemberClick,
  selectedCommunicationMethod,
  onCommunicationMethodClick,
  slotsLoading,
  slotPages,
  onSelectSlotClick,
  selectedSlotId,
}: SchedulingContentSectionProps) => {
  const renderConditionalsContainer = slotsLoading || !selectedMemberId;
  const renderSelectReachByMethodText = !renderConditionalsContainer && !selectedCommunicationMethod;
  const renderSlotSection =
    !!slotPages?.length && !slotsLoading && !!selectedMemberId && !renderSelectReachByMethodText;
  const renderNoAvailableAppointmentsText =
    !slotPages?.length && !slotsLoading && !!selectedMemberId && !renderSelectReachByMethodText;
  const careTeamMemberType = careTeamUsers?.find((member) => member.id === selectedMemberId)?.userType;

  return (
    <Box minHeight={MODAL_CONTENT_MIN_HEIGHT}>
      <SchedulingSectionHeader
        number={CARE_TEAM_STRINGS.schedulingModal.one}
        text={CARE_TEAM_STRINGS.schedulingModal.promptOne}
      />

      <SchedulingCareTeamMembers
        careTeamUsers={careTeamUsers}
        selectedMemberId={selectedMemberId}
        onSelectMemberClick={onSelectMemberClick}
      />

      <SchedulingSectionHeader
        number={CARE_TEAM_STRINGS.schedulingModal.two}
        text={CARE_TEAM_STRINGS.schedulingModal.promptTwo("Care Team")}
      />

      <SchedulingReachByMethod
        selectedCommunicationMethod={selectedCommunicationMethod}
        onCommunicationMethodClick={onCommunicationMethodClick}
      />

      <SchedulingSectionHeader
        number={CARE_TEAM_STRINGS.schedulingModal.three}
        text={CARE_TEAM_STRINGS.schedulingModal.promptThree}
      />

      {slotsLoading ? (
        <Box width={"100%"} textAlign="center">
          <CircularProgress />
        </Box>
      ) : (
        <SchedulingAppointmentSection
          renderConditionalsContainer={renderConditionalsContainer}
          renderSelectReachByMethodText={renderSelectReachByMethodText}
          renderNoAvailableAppointmentsText={renderNoAvailableAppointmentsText}
          renderSlotSection={renderSlotSection}
          careTeamMemberType={careTeamMemberType}
          slotPages={slotPages}
          onSelectSlotClick={onSelectSlotClick}
          selectedSlotId={selectedSlotId}
        />
      )}
    </Box>
  );
};
