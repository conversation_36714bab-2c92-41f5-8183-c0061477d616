import { CareTeamUser } from "@vivantehealth/vivante-core";
import { Box } from "@mui/material";

import { SPACING_8_PX, SPACING_24_PX } from "@Assets/style_constants";

import { CareTeamMemberCard } from "../CareTeamMemberCard";

type SchedulingCareTeamMembersProps = Readonly<{
  careTeamUsers: CareTeamUser[];
  selectedMemberId?: string;
  onSelectMemberClick: (memberId: string) => void;
}>;

export const SchedulingCareTeamMembers = ({
  careTeamUsers,
  selectedMemberId,
  onSelectMemberClick,
}: SchedulingCareTeamMembersProps) => {
  return (
    <Box display="flex" gap={SPACING_8_PX} mb={SPACING_24_PX}>
      {careTeamUsers?.map((member) => (
        <CareTeamMemberCard
          key={member.id}
          member={member}
          onClick={() => onSelectMemberClick(member.id)}
          isSmallWidth
          isSelected={selectedMemberId === member.id}
        />
      ))}
    </Box>
  );
};
