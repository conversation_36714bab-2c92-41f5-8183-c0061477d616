import { SessionCommunicationMethod } from "@vivantehealth/vivante-core";
import { Box, Chip } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

const CARE_TEAM_STRINGS = appStrings.features.careTeam;

type SchedulingReachByMethodProps = Readonly<{
  selectedCommunicationMethod?: SessionCommunicationMethod;
  onCommunicationMethodClick: (communicationMethod: SessionCommunicationMethod) => void;
}>;

export const SchedulingReachByMethod = ({
  selectedCommunicationMethod,
  onCommunicationMethodClick,
}: SchedulingReachByMethodProps) => {
  return (
    <Box display="flex" gap={2} mb={5}>
      <Chip
        label={CARE_TEAM_STRINGS.schedulingModal.phoneCall}
        onClick={() => onCommunicationMethodClick(SessionCommunicationMethod.PHONE)}
        variant={selectedCommunicationMethod === SessionCommunicationMethod.PHONE ? "active" : "inactive"}
      />
      <Chip
        label={CARE_TEAM_STRINGS.schedulingModal.video}
        onClick={() => onCommunicationMethodClick(SessionCommunicationMethod.VIDEO)}
        variant={selectedCommunicationMethod === SessionCommunicationMethod.VIDEO ? "active" : "inactive"}
      />
    </Box>
  );
};
