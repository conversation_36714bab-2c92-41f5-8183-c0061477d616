import { CareTeamUser, SessionCommunicationMethod, SessionSlot } from "@vivantehealth/vivante-core";
import { Box, Paper, Typography } from "@mui/material";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { AppointmentDuration } from "@Components/AppointmentDuration/AppointmentDuration";
import { AppointmentGetReady } from "@Components/AppointmentGetReady/AppointmentGetReady";
import { AppointmentInformation } from "@Components/AppointmentInformation/AppointmentInformation";
import { GutCheckResultsNote } from "@Components/GutCheckResultsNote/GutCheckResultsNote";
import { PractitionerInformation } from "@Components/PractitionerInformation/PractitionerInformation";

import { getUserType } from "../../utils/careteam.utils";

const MILLISECONDS = 1000;
const APPOINTMENT_CONFIRMATION_STRINGS = appStrings.features.careTeam.appointmentConfirmationModal;

type AppointmentConfirmationContentSectionProps = Readonly<{
  communicationMethod?: SessionCommunicationMethod;
  slot: SessionSlot;
  member: CareTeamUser;
}>;

export const AppointmentConfirmationContentSection = ({
  communicationMethod,
  slot,
  member,
}: AppointmentConfirmationContentSectionProps) => {
  const startTime = dayjs(slot.start * MILLISECONDS).format("MM-DD-YYYY h:mm A");
  const endTime = dayjs(slot.end * MILLISECONDS).format("MM-DD-YYYY h:mm A");
  const slotDuration = dayjs(endTime).diff(dayjs(startTime), "minute");
  const practitioner = {
    ...member,
    type: "care_team",
    role: member?.userType,
    displayableRole: getUserType(member),
  };

  return (
    <>
      <Paper>
        <Box display="flex" justifyContent="space-between" alignItems="start">
          <PractitionerInformation practitioner={practitioner} />

          <AppointmentDuration duration={slotDuration} />
        </Box>

        <Box mt={4}>
          <AppointmentInformation startTime={new Date(startTime)} communicationMethod={communicationMethod} />
        </Box>
      </Paper>

      <Box pt={5}>
        <AppointmentGetReady headingSize="h4" />

        <GutCheckResultsNote />

        <Typography variant="body" mt={4}>
          {APPOINTMENT_CONFIRMATION_STRINGS.locationNote}
        </Typography>
      </Box>
    </>
  );
};
