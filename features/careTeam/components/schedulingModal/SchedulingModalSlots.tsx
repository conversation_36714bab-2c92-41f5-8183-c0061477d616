import { SessionSlot } from "@vivantehealth/vivante-core";
import { Box, Chip } from "@mui/material";
import dayjs from "dayjs";

import { SPACING_8_PX } from "@Assets/style_constants";
import { SlotPage } from "@Features/careTeam/store/careTeamStateSlice";
import { selectedSlotToKey } from "@Features/careTeam/utils/careTeamState.util";

import { SelectedSlot } from "../CareTeamSchedulingModalContainer";

type SchedulingModalSlotsProps = Readonly<{
  day: SlotPage;
  onClick: (slot: SelectedSlot) => void;
  selectedSlotId?: string;
  multiplier?: number;
}>;

export const SchedulingModalSlots = ({ day, onClick, selectedSlotId, multiplier = 1 }: SchedulingModalSlotsProps) => {
  return (
    <Box display="flex" gap={SPACING_8_PX} flexWrap="wrap">
      {day.slots.map((sessionSlot: SessionSlot) => {
        const key = selectedSlotToKey({
          slotPage: day,
          sessionSlot,
        });
        const startTime = dayjs(sessionSlot.start * multiplier).format("h:mm");
        const endTime = dayjs(sessionSlot.end * multiplier).format("h:mm A");
        const text = `${startTime} - ${endTime}`;

        return (
          <Chip
            key={key}
            label={text}
            onClick={() => onClick({ slotPage: day, sessionSlot })}
            variant={key === selectedSlotId ? "active" : "inactive"}
          />
        );
      })}
    </Box>
  );
};
