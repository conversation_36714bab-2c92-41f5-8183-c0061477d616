import { CareTeamUserType } from "@vivantehealth/vivante-core";
import { Box, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { SPACING_0_PX, SPACING_16_PX, SPACING_24_PX } from "@Assets/style_constants";
import { getCareTeamMemberType } from "@Features/careTeam//utils/careteam.utils";
import { SlotPage } from "@Features/careTeam/store/careTeamStateSlice";

import { SchedulingModalSlots } from "./SchedulingModalSlots";
import { SelectedSlot } from "../CareTeamSchedulingModalContainer";
const CARE_TEAM_STRINGS = appStrings.features.careTeam;

type ConditionalTextProps = Readonly<{
  renderConditionalsContainer: boolean;
  renderSelectReachByMethodText: boolean;
  renderNoAvailableAppointmentsText: boolean;
  careTeamMemberType?: CareTeamUserType;
}>;

const getConditionalText = ({
  renderConditionalsContainer,
  renderSelectReachByMethodText,
  renderNoAvailableAppointmentsText,
  careTeamMemberType,
}: ConditionalTextProps) => {
  if (renderConditionalsContainer) {
    return CARE_TEAM_STRINGS.schedulingModal.pleaseSelectMember;
  }

  if (renderSelectReachByMethodText) {
    return CARE_TEAM_STRINGS.schedulingModal.pleaseSelectAMethod;
  }

  if (renderNoAvailableAppointmentsText) {
    return CARE_TEAM_STRINGS.schedulingModal.noAvailableAppointments(getCareTeamMemberType(careTeamMemberType));
  }

  return undefined;
};

type SchedulingAppointmentSection = Readonly<{
  renderConditionalsContainer: boolean;
  renderSelectReachByMethodText: boolean;
  renderNoAvailableAppointmentsText: boolean;
  renderSlotSection: boolean;
  careTeamMemberType?: CareTeamUserType;
  slotPages: SlotPage[];
  onSelectSlotClick: (slot: SelectedSlot) => void;
  selectedSlotId?: string;
}>;

export const SchedulingAppointmentSection = ({
  renderConditionalsContainer,
  renderSelectReachByMethodText,
  renderNoAvailableAppointmentsText,
  renderSlotSection,
  careTeamMemberType,
  slotPages,
  onSelectSlotClick,
  selectedSlotId,
}: SchedulingAppointmentSection) => {
  const conditionalText = getConditionalText({
    renderConditionalsContainer,
    renderSelectReachByMethodText,
    renderNoAvailableAppointmentsText,
    careTeamMemberType,
  });

  if (conditionalText) {
    return (
      <Typography variant="body" color={color.text.strong}>
        {conditionalText}
      </Typography>
    );
  }

  return (
    <>
      {renderSlotSection ? (
        <Paper>
          {slotPages?.map((day, dayIndex) => {
            const isFirstDay = dayIndex === 0;
            const isLastDay = dayIndex === slotPages.length - 1;

            return (
              <Box
                key={day.title}
                paddingTop={isFirstDay ? SPACING_0_PX : SPACING_24_PX}
                paddingBottom={isLastDay ? SPACING_0_PX : SPACING_24_PX}
                borderBottom={isLastDay ? "none" : `1px solid ${color.border.surface.default}`}
              >
                <Typography variant="h4" pb={SPACING_16_PX}>
                  {day.title}
                </Typography>
                <SchedulingModalSlots
                  day={day}
                  onClick={onSelectSlotClick}
                  selectedSlotId={selectedSlotId}
                  multiplier={1000}
                />
              </Box>
            );
          })}
        </Paper>
      ) : null}
    </>
  );
};
