import { CareTeamUser } from "@vivantehealth/vivante-core";
import { Box, Button, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { SPACING_16_PX, SPACING_8_PX } from "@Assets/style_constants";
import { BaseModal } from "@Components/BaseModal/BaseModal";
import { CareTeamAvatar } from "@Components/CareTeamAvatar/CareTeamAvatar";

import { getUserType } from "../utils/careteam.utils";

const BUTTON_STRINGS = appStrings.buttonText;
const CARE_TEAM_STRINGS = appStrings.features.careTeam;

type CareTeamMemberModalProps = Readonly<{
  isModalOpen: boolean;
  member?: CareTeamUser;
  handleExitModalClick: () => void;
  handleScheduleClick: () => void;
  handleNavigateToConversation: (user: CareTeamUser) => void;
}>;

export const CareTeamMemberModal = ({
  isModal<PERSON><PERSON>,
  member,
  handleExitModalClick,
  handleScheduleClick,
  handleNavigateToConversation,
}: CareTeamMemberModalProps) => {
  const userType = getUserType(member);
  const subheaderText =
    userType === "Health Coach"
      ? CARE_TEAM_STRINGS.memberModalSubeader.healthCoach
      : CARE_TEAM_STRINGS.memberModalSubeader.dietitian;

  return (
    <BaseModal
      isModalOpen={isModalOpen}
      onClose={handleExitModalClick}
      title={CARE_TEAM_STRINGS.memberModalHeader(userType.toLowerCase())}
      bodyContent={
        <>
          <Typography variant="body" sx={{ pb: 5 }}>
            {subheaderText}
          </Typography>

          <Box display="flex" justifyContent="center" alignItems="center">
            <Paper
              sx={{ display: "flex", alignItems: "center", p: 4, mb: 6 }}
              aria-label={appStrings.a11y.careTeamMember(userType, member?.firstName)}
              tabIndex={0}
            >
              {member?.avatarLink ? (
                <CareTeamAvatar
                  avatarLink={member.avatarLink}
                  altText={appStrings.a11y.careTeamMemberAvatarText(userType.toLowerCase())}
                  width="96px"
                />
              ) : null}

              <Box display="flex" flexDirection="column" ml={3}>
                <Typography variant="h3" tabIndex={-1} color={color.text.strong}>
                  {member?.firstName}
                </Typography>

                {member && (
                  <Typography variant="body" tabIndex={-1} color={color.text.subtle}>
                    {userType}
                  </Typography>
                )}
              </Box>
            </Paper>
          </Box>
          {member?.summary && (
            <Box
              position="relative"
              p={4}
              borderRadius={2}
              bgcolor={color.background.surface.secondary}
              textAlign="center"
            >
              <Box
                sx={{
                  position: "absolute",
                  top: `-${SPACING_16_PX}`,
                  right: `calc(50% - ${SPACING_8_PX})`,
                  borderLeft: "8px solid transparent",
                  borderRight: "8px solid transparent",
                  borderBottom: `16px solid ${color.background.surface.secondary}`,
                }}
              />

              <Typography variant="body" color={color.text.strong}>
                {`"${member?.summary}"`}
              </Typography>
            </Box>
          )}
        </>
      }
      actions={
        <>
          <Button
            variant="secondary"
            onClick={() => {
              if (member) {
                handleNavigateToConversation(member);
              }
            }}
          >
            {BUTTON_STRINGS.sendMessage}
          </Button>
          {/** Added width of 57% to get as close to figma design as possible. If we go to 58% or higher OR use fullWidth variatn of the button
           * the button will be too wide and make the adjacent button text wrap to the next line
           */}
          <Button variant="primary" onClick={handleScheduleClick} sx={{ width: "57%" }}>
            {BUTTON_STRINGS.scheduleAppointment}
          </Button>
        </>
      }
    />
  );
};
