import { bffApi } from "@Api/bffApi";

import { ClinicianMap } from "../types/careTeam.types";
import { normalizeClinician } from "../utils/careteam.utils";

const CACHE_TIME = 60 * 60; // 1 hour

export const careTeamApi = bffApi.injectEndpoints({
  endpoints: (builder) => ({
    getClinicians: builder.query<ClinicianMap, GetClinicianRequestOptions>({
      query: (options) => ({
        url: `v1/clinicians`,
        method: "GET",
        params: {
          ids: options?.ids,
          specialty: options?.specialty,
          identifiers: options?.identifiers,
          usstate: options?.usState,
          enabled: options?.enabled,
          emails: options?.emails,
          role_code: options?.roleCode,
        },
      }),
      transformResponse: normalizeClinician,
      keepUnusedDataFor: CACHE_TIME,
    }),
  }),
});

export const { useGetCliniciansQuery } = careTeamApi;

type GetClinicianRequestOptions = {
  /** A list of clinician IDs */
  ids?: string[];
  /** Specialty code to filter on. eg: GI | INT */
  specialty?: string;
  /**
   * A list of identifiers in the format System/Identifier.
   * eg "GIThrive/671ded37b73345a09a04bb81b69f3bb7,5ecb1d4480154341bc98d0ffd36ef865"
   */
  identifiers?: string;
  /** US State code eg CA */
  usState?: string;
  /** Only return clinicians that are enabled */
  enabled?: boolean;
  /** A list of email addresses to search on */
  emails?: string[];
  /** Only return clinicians with the provided role. eg: "MD" | "PA" | "CG" | "HC" | "RD"; */
  roleCode?: string;
};
