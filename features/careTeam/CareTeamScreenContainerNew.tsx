import { useEffect, useState } from "react";
import { Appointment, CareTeamUser, ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/router";

import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { CareGuideReschedulingModal } from "@Features/careGuide/components/CareGuideReschedulingModal";
import { CareTeamStateSlice } from "@Features/careTeam/store/careTeamStateSlice";
import { setCurrentRoomType } from "@Features/chat/store/chatStateSlice";
import { loadChat } from "@Features/chat/store/chatThunks";
import { NavOptions, NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { CalendarProviderOption } from "@Lib/infrastructure/calendar/calendar-provider-option";
import { useAppDispatch } from "@Store/hooks";
import { Routes } from "@Types";

import { CareTeamSchedulingModalContainerNew } from "./components/CareTeamSchedulingModalContainerNew";
import { CareTeamScreen } from "./components/CareTeamScreen";
import { EditAppointmentConfirmationModal } from "./components/EditAppointmentConfirmationModal";
import { useCareTeam } from "./hooks/useCareTeam";
import { getCareTeamUserByRole } from "./utils/careteam.utils";

export type AppointmentAction = "reschedule" | "cancel";
export type AppointmentActionData = {
  appointment: Appointment;
  action: AppointmentAction;
};
export type CareTeamSessionType = "rd" | "hc";

export const isCareTeamSessionType = (role: string): role is CareTeamSessionType => {
  return role === "rd" || role === "hc";
};

type CareTeamScreenContainerProps = Readonly<{
  role?: CareTeamSessionType;
}>;

export const CareTeamScreenContainerNew = ({ role }: CareTeamScreenContainerProps) => {
  const dispatch = useAppDispatch();
  const params = useSearchParams();
  const router = useRouter();
  const { sendEventAnalytics } = useAnalyticsHook();

  useEffect(() => {
    dispatch(CareTeamStateSlice.actions.loadCareTeam());
    dispatch(NavigationStateSlice.actions.setActiveNavOption(NavOptions.CARE_TEAM));
    dispatch(loadChat({ displayError: false, batchSize: 1 }));
  }, [dispatch]);

  const [rescheduledAppointment, setRescheduledAppointment] = useState<Appointment>();
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [isMemberModalOpen, setIsMemberModalOpen] = useState<boolean>(false);
  const [confirmationType, setConfirmationType] = useState<AppointmentAction>();
  const [currentlySelectedMember, setCurrentlySelectedMember] = useState<CareTeamUser>();
  const careTeamProps = useCareTeam();
  const {
    careTeamUsers,
    isLoadingAppointments,
    isAppointmentListError,
    upcomingAppointments,
    showSchedulingModal,
    setShowSchedulingModal,
    handleCancelAppointment,
    setAppointmentToReschedule,
    setSelectedCareTeamMemberId,
    showCareGuideSchedulingModal,
    setShowCareGuideSchedulingModal,
  } = careTeamProps;

  useEffect(() => {
    if (role) {
      const careTeamMember = getCareTeamUserByRole(role, careTeamUsers);

      setCurrentlySelectedMember(careTeamMember);
      setIsMemberModalOpen(true);
    }
  }, [careTeamUsers, role, setShowSchedulingModal]);

  const clearSchedulingModalData = () => {
    setRescheduledAppointment(undefined);
    setSelectedCareTeamMemberId(undefined);
  };

  const closeConfirmation = () => {
    setShowConfirmationModal(false);
    setRescheduledAppointment(undefined);
  };

  const handleUpcomingAppointmentEditClick = (data: AppointmentActionData) => {
    setConfirmationType(data.action);
    setRescheduledAppointment(data.appointment);
    setShowConfirmationModal(true);
  };

  const handleNavigateToConversation = (user: CareTeamUser) => {
    dispatch(setCurrentRoomType(user.userType));
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: "/chat",
        screenName: "Chat",
      }),
    );
  };

  const onAddToCalendarClick = (data: { calendarType: CalendarProviderOption; appointment: Appointment }) => {
    dispatch(
      CareTeamStateSlice.actions.createAppointmentCalendarEvent({
        appointment: data.appointment,
        extras: data.calendarType,
      }),
    );
    dispatch(sendEventAnalytics(ClickStreamActivityEventType.APPOINTMENT_ADD_TO_CALENDAR));
  };

  if (!careTeamUsers) {
    return <LoadingSpinner open />;
  }

  const confirmRescheduleAppointment = () => {
    const practitionerRole = rescheduledAppointment?.practitioner.role;

    if (rescheduledAppointment && (practitionerRole === "MD" || practitionerRole === "PA")) {
      setAppointmentToReschedule(rescheduledAppointment);
      router.push(
        `${Routes.APPOINTMENTS}?role=${rescheduledAppointment.practitioner.type === "INT" ? "internist" : "gi"}&appointmentToRescheduleId=${rescheduledAppointment.id}`,
      );
      return;
    }

    setShowConfirmationModal(false);

    if (rescheduledAppointment?.practitioner.role === "CG") {
      return setShowCareGuideSchedulingModal(true);
    }

    setSelectedCareTeamMemberId(rescheduledAppointment?.practitioner.id);
    return setShowSchedulingModal(true);
  };

  const handleCancelCareTeamAppointment = () => {
    if (rescheduledAppointment) {
      handleCancelAppointment(rescheduledAppointment.id);
    }
  };

  const confirmCancelAppointment = () => {
    setShowConfirmationModal(false);
    handleCancelCareTeamAppointment();

    clearSchedulingModalData?.();
  };

  const closeCareGuideReschedulingModal = () => {
    setShowCareGuideSchedulingModal(false);
    clearSchedulingModalData();
  };

  if (params.get("showScheduling") === "true" && !showSchedulingModal) {
    setShowSchedulingModal(true);
    // Replace the URL to remove the query param to ensure the modal doesn't open unexpectedly again
    router.replace(Routes.CARE_TEAM);
  }

  return (
    <>
      <CareTeamSchedulingModalContainerNew
        isModalOpen={showSchedulingModal}
        onToggleSchedulingModal={setShowSchedulingModal}
        rescheduledAppointment={rescheduledAppointment}
        clearSchedulingModalData={clearSchedulingModalData}
        {...careTeamProps}
      />

      <CareGuideReschedulingModal
        isModalOpen={showCareGuideSchedulingModal}
        existingAppointment={rescheduledAppointment}
        closeSchedulingModal={closeCareGuideReschedulingModal}
        key={String(showCareGuideSchedulingModal)}
      />

      <EditAppointmentConfirmationModal
        showConfirmationModal={showConfirmationModal}
        confirmationType={confirmationType}
        closeConfirmation={closeConfirmation}
        setSelectedMemberId={setSelectedCareTeamMemberId}
        confirmRescheduleAppointment={confirmRescheduleAppointment}
        confirmCancelAppointment={confirmCancelAppointment}
      />

      <CareTeamScreen
        careTeamUsers={careTeamUsers}
        isLoading={isLoadingAppointments}
        isAppointmentListError={isAppointmentListError}
        isMemberModalOpen={isMemberModalOpen}
        setIsMemberModalOpen={setIsMemberModalOpen}
        openScheduleModal={() => setShowSchedulingModal(true)}
        setCurrentMemberId={setSelectedCareTeamMemberId}
        onNavigateToConversation={handleNavigateToConversation}
        upcomingAppointments={upcomingAppointments}
        handleUpcomingAppointmentEditClick={handleUpcomingAppointmentEditClick}
        onAddToCalendarClick={onAddToCalendarClick}
        currentlySelectedMember={currentlySelectedMember}
        setCurrentlySelectedMember={setCurrentlySelectedMember}
      />
    </>
  );
};
