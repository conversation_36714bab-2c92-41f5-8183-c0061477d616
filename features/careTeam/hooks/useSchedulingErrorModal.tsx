import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

import { CareTeamStateSlice, careTeamStateSelector } from "@Features/careTeam/store/careTeamStateSlice";

export const useSchedulingErrorModal = () => {
  const dispatch = useDispatch();
  const [isErrorModalOpen, setIsErrorModalOpen] = useState<boolean>(false);
  const schedulingErrorMessage: string = useSelector(careTeamStateSelector("errorMessage"));

  useEffect(() => {
    if (schedulingErrorMessage) {
      setIsErrorModalOpen(true);
    }
  }, [schedulingErrorMessage]);

  const handleCloseErrorModal = () => {
    setIsErrorModalOpen(false);
    dispatch(CareTeamStateSlice.actions.clearSchedulingError());
  };

  return {
    schedulingErrorMessage,
    isErrorModalOpen,
    handleCloseErrorModal,
  };
};
