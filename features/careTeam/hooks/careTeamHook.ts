import { useCallback, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Appointment, ClickStreamActivityEventType, SessionCommunicationMethod } from "@vivantehealth/vivante-core";

import { appStrings } from "@Assets/app_strings";
import { AppointmentsStateSlice } from "@Features/appointments/store/appointmentsStateSlice";
import { useCancelAppointmentMutation } from "@Features/appointmentsNew/api/appointmentApi";
import { APPOINTMENT_API_FEATURE_FLAG } from "@Features/appointmentsNew/assets/constants";
import { isAppointmentSchedulingError } from "@Features/appointmentsNew/utils/processAppointmentSchedulingErrors";
import { CareTeamStateSlice, careTeamStateSelector } from "@Features/careTeam/store/careTeamStateSlice";
import { keyToSelectedSlot, selectedSlotToKey } from "@Features/careTeam/utils/careTeamState.util";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { useFeatureFlag } from "@Hooks/useFeatureFlag";
import { useAppSelector } from "@Store/hooks";

import { SelectedSlot } from "../components/CareTeamSchedulingModalContainer";

export const useCareTeamHook = () => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const [selectedMemberId, setSelectedMemberId] = useState<string | undefined>();
  const [selectedSlotId, setSelectedSlotId] = useState<string | undefined>(undefined);
  const [showSchedulingModal, setShowSchedulingModal] = useState(false);
  const [showCareGuideSchedulingModal, setShowCareGuideSchedulingModal] = useState(false);
  const [selectedCommunicationMethod, setCommunicationMethod] = useState<SessionCommunicationMethod | undefined>();

  const isSchedulingLoading = useSelector(careTeamStateSelector("schedulingLoadState")) === "loading";
  const isLoading: boolean = useSelector(careTeamStateSelector("loadState")) === "loading";
  const careTeamUsers = useSelector(careTeamStateSelector("careTeamUsers"));
  const slotPages = useSelector(careTeamStateSelector("currentSlotPages"));
  const slotsLoading = useSelector(careTeamStateSelector("slotPagesLoadState")) === "loading";
  const upcomingAppointments = useSelector(careTeamStateSelector("upcomingAppointments"));
  const memberId = useAppSelector((state) => state.memberState.member)?.id ?? "";
  const { isReady, treatment } = useFeatureFlag(APPOINTMENT_API_FEATURE_FLAG);
  const [invokeCancelAppointment, { isLoading: isCancelling }] = useCancelAppointmentMutation();

  const setSelectedCommunicationMethod = (communicationMethod?: SessionCommunicationMethod) => {
    setCommunicationMethod(communicationMethod);
    sendEventAnalytics(ClickStreamActivityEventType.SCHEDULING_SELECT_COMMUNICATION_METHOD, {
      communicationMethod: "",
    });
  };

  const onConfirmationModalClose = () => {
    setSelectedSlotId(undefined);
    setSelectedCommunicationMethod(undefined);
    dispatch(CareTeamStateSlice.actions.cancelScheduling());
  };

  const submitScheduling = (appointmentId: string | undefined, selectedMemberId: string) => {
    if (selectedSlotId && selectedCommunicationMethod) {
      dispatch(
        CareTeamStateSlice.actions.submitScheduling({
          rescheduledSessionId: appointmentId,
          rescheduledForCareTeamUserType: careTeamUsers?.find((n) => n.id === selectedMemberId)?.userType,
          slot: keyToSelectedSlot(selectedSlotId),
          careTeamUserId: selectedMemberId,
          communicationMethod: selectedCommunicationMethod,
        }),
      );
    }
  };

  const setSelectedSlot = (slot: SelectedSlot | undefined) => {
    const slotKey = selectedSlotToKey(slot);

    setSelectedSlotId(slotKey);
    // If slot is a undefined, that means we have cleared the selection and don't want to send analytics
    if (slot) {
      sendEventAnalytics(ClickStreamActivityEventType.SCHEDULING_SELECT_SESSION_SLOT, {
        endTs: slot.sessionSlot.start,
        startTs: slot.sessionSlot.end,
      });
    }
  };

  const cancelAppointment = async (appointmentId: string) => {
    if (isReady && treatment === "on") {
      try {
        await invokeCancelAppointment({ appointmentId, memberId }).unwrap();
        dispatch(CareTeamStateSlice.actions.refreshAppointments());
      } catch (error) {
        // Appointment was created using the old scheduling API, so we need to use the old cancel appointment API
        if (isAppointmentSchedulingError(error) && error.status === 404) {
          return dispatch(CareTeamStateSlice.actions.cancelAppointment(appointmentId));
        }

        dispatch(
          SnackbarStateSlice.actions.toggleSnackbar({
            isOpen: true,
            message: appStrings.features.careTeam.cancellingAppointmentError,
            variant: "error",
          }),
        );
      }

      return;
    }

    dispatch(CareTeamStateSlice.actions.cancelAppointment(appointmentId));
  };

  const loadSlotPages = useCallback(
    (memberId: string) => {
      dispatch(CareTeamStateSlice.actions.loadSlotPages(memberId));
    },
    [dispatch],
  );

  const setAppointmentToReschedule = (appointment: Appointment) => {
    dispatch(AppointmentsStateSlice.actions.setAppointmentToReschedule(appointment));
  };

  return {
    isSchedulingLoading,
    careTeamUsers,
    slotPages,
    slotsLoading,
    isLoading: isLoading || isCancelling,
    upcomingAppointments,
    selectedSlotId,
    selectedCommunicationMethod,
    showSchedulingModal,
    setShowSchedulingModal,
    setSelectedCommunicationMethod,
    setSelectedSlot,
    onConfirmationModalClose,
    submitScheduling,
    cancelAppointment,
    loadSlotPages,
    setAppointmentToReschedule,
    selectedMemberId,
    setSelectedMemberId,
    showCareGuideSchedulingModal,
    setShowCareGuideSchedulingModal,
  };
};
