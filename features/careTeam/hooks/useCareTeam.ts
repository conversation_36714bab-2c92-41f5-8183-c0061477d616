import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Appointment, ClickStreamActivityEventType, SessionCommunicationMethod } from "@vivantehealth/vivante-core";
import { skipToken } from "@reduxjs/toolkit/query";

import { appStrings } from "@Assets/app_strings";
import { AppointmentsStateSlice } from "@Features/appointments/store/appointmentsStateSlice";
import {
  appointmentsApi,
  useCancelAppointmentMutation,
  useCreateAppointmentMutation,
  useGetAppointmentListQuery,
  useGetAppointmentTimeSlotsQuery,
  useRescheduleAppointmentMutation,
} from "@Features/appointmentsNew/api/appointmentApi";
import { AppointmentBaseRequest, AppointmentTimeSlot } from "@Features/appointmentsNew/types/appointments.types";
import {
  isAppointmentSchedulingError,
  processAppointmentSchedulingErrors,
} from "@Features/appointmentsNew/utils/processAppointmentSchedulingErrors";
import { CareTeamStateSlice, careTeamStateSelector } from "@Features/careTeam/store/careTeamStateSlice";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { useAppSelector } from "@Store/hooks";

import { useGetCliniciansQuery } from "../api/careTeamApi";

const checkForExistingAppointment = (careTeamMemberId: string | undefined, existingAppointments: Appointment[]) => {
  return existingAppointments.some((appointment) => appointment.practitioner.id === careTeamMemberId);
};

export const useCareTeam = () => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const [selectedCareTeamMemberId, setSelectedCareTeamMemberId] = useState<string | undefined>();
  const [selectedAppointment, setSelectedAppointment] = useState<AppointmentTimeSlot | undefined>(undefined);
  const [showSchedulingModal, setShowSchedulingModal] = useState(false);
  const [showCareGuideSchedulingModal, setShowCareGuideSchedulingModal] = useState(false);
  const [selectedCommunicationMethod, setCommunicationMethod] = useState<SessionCommunicationMethod | undefined>();
  // We maintain a key for the modal in state so as to force reset the modal when it is closed
  const [modalKey, setModalKey] = useState(0);

  const careTeamUsers = useSelector(careTeamStateSelector("careTeamUsers"));
  const {
    data: upcomingAppointments,
    isFetching: isLoadingAppointments,
    isError: isAppointmentListError,
  } = useGetAppointmentListQuery();
  const memberId = useAppSelector((state) => state.memberState.member)?.id ?? "";
  const careTeamUserIds = careTeamUsers?.map(({ id }) => id);
  const { data: clinicianInformation, isLoading: isClinicianInfoLoading } = useGetCliniciansQuery(
    careTeamUserIds && careTeamUserIds.length > 0
      ? { identifiers: `GIThrive/${careTeamUserIds.join(",")}` }
      : skipToken,
  );
  const selectedCareTeamMember =
    clinicianInformation && selectedCareTeamMemberId ? clinicianInformation[selectedCareTeamMemberId] : undefined;
  const { isFetching: isTimeSlotsLoading, data: timeSlots } = useGetAppointmentTimeSlotsQuery(
    selectedCareTeamMember
      ? {
          clinicianId: selectedCareTeamMember.id,
        }
      : skipToken,
  );

  const [invokeCancelAppointment, { isLoading: isCancelling }] = useCancelAppointmentMutation();
  const [invokeCreateAppointment, { isLoading: isSubmittingAppointment }] = useCreateAppointmentMutation();
  const [invokeRescheduleAppointment, { isLoading: isSubmittingReschedule }] = useRescheduleAppointmentMutation();

  const handleSelectedCommunicationMethod = (communicationMethod?: SessionCommunicationMethod) => {
    setCommunicationMethod(communicationMethod);
    sendEventAnalytics(ClickStreamActivityEventType.SCHEDULING_SELECT_COMMUNICATION_METHOD, {
      communicationMethod: communicationMethod || "",
    });
  };

  const handleOnConfirmationModalClose = (isClosedBeforeAppointmentMade = true) => {
    setSelectedAppointment(undefined);
    handleSelectedCommunicationMethod(undefined);
    setSelectedCareTeamMemberId(undefined);
    setShowSchedulingModal(false);
    setModalKey((prevKey) => prevKey + 1);

    if (isClosedBeforeAppointmentMade) {
      sendEventAnalytics(ClickStreamActivityEventType.SCHEDULING_ABORT);
    }
  };

  const handleSubmitScheduling = async (appointmentId: string | undefined) => {
    if (selectedAppointment && selectedCommunicationMethod && selectedCareTeamMember && timeSlots) {
      // Add check for existing appointment if new
      const appointmentInformation: AppointmentBaseRequest = {
        memberId,
        clinicianId: selectedCareTeamMember?.id ?? "",
        startTime: selectedAppointment.start,
        endTime: selectedAppointment.end,
        communicationMethod: selectedCommunicationMethod === "PHONE" ? "phone" : "video",
      };

      try {
        if (appointmentId) {
          await invokeRescheduleAppointment({
            ...appointmentInformation,
            appointmentId,
          }).unwrap();
        } else {
          if (checkForExistingAppointment(selectedCareTeamMemberId, upcomingAppointments ?? [])) {
            throw new Error(appStrings.features.careTeam.existingAppointmentError);
          }

          await invokeCreateAppointment({
            ...appointmentInformation,
            requestTrackingId: Object.values(timeSlots)[0][0]?.requestTrackingId,
          }).unwrap();
        }

        dispatch(
          SnackbarStateSlice.actions.toggleSnackbar({
            message: appStrings.features.careTeam.appointmentConfirmed,
            isOpen: true,
          }),
        );
        dispatch(CareTeamStateSlice.actions.refreshAppointments());
        handleOnConfirmationModalClose(false);
      } catch (error) {
        if (error instanceof Error) {
          dispatch(
            SnackbarStateSlice.actions.toggleSnackbar({
              message: error.message,
              isOpen: true,
              variant: "error",
            }),
          );
          return handleOnConfirmationModalClose(false);
        }

        const processedError = processAppointmentSchedulingErrors(error);

        // If the error is related to the request tracking id, invalidate the appointment api cache to get a new request_tracking_id
        if (processedError.type === "REQUEST_TRACKING_ID_ERROR") {
          dispatch(appointmentsApi.util.invalidateTags(["Appointment"]));
          dispatch(appointmentsApi.util.resetApiState());
        }

        dispatch(
          SnackbarStateSlice.actions.toggleSnackbar({
            message: processedError.message,
            isOpen: true,
            variant: "error",
          }),
        );
        // As the user can not reschedule within 1 minute of the appointment, we force close the modal
        if (processedError.type === "RESCHEDULING_WITHIN_1_MINUTE") {
          handleOnConfirmationModalClose(false);
        }
      }
    }
  };

  const handleSelectedAppointmentSlot = (selectedAppointmentSlot: AppointmentTimeSlot | undefined) => {
    setSelectedAppointment(selectedAppointmentSlot);

    // If slot is a undefined, that means we have cleared the selection and don't want to send analytics
    if (selectedAppointmentSlot) {
      sendEventAnalytics(ClickStreamActivityEventType.SCHEDULING_SELECT_SESSION_SLOT, {
        endTs: selectedAppointmentSlot.end,
        startTs: selectedAppointmentSlot.start,
      });
    }
  };

  const handleCareTeamMemberSelected = (careTeamMemberId: string) => {
    if (selectedCareTeamMemberId !== careTeamMemberId) {
      setSelectedCareTeamMemberId(careTeamMemberId);
      setSelectedAppointment(undefined);
    }
  };

  const handleCancelAppointment = async (appointmentId: string) => {
    try {
      await invokeCancelAppointment({ appointmentId, memberId }).unwrap();
      dispatch(CareTeamStateSlice.actions.refreshAppointments());
    } catch (error) {
      // Appointment was created using the old scheduling API, so we need to use the old cancel appointment API
      if (isAppointmentSchedulingError(error) && error.status === 404) {
        return dispatch(CareTeamStateSlice.actions.cancelAppointment(appointmentId));
      }

      dispatch(
        SnackbarStateSlice.actions.toggleSnackbar({
          isOpen: true,
          message: appStrings.features.careTeam.cancellingAppointmentError,
          variant: "error",
        }),
      );
    }
  };

  const setAppointmentToReschedule = (appointment: Appointment) => {
    dispatch(AppointmentsStateSlice.actions.setAppointmentToReschedule(appointment));
  };

  return {
    careTeamUsers,
    isLoading: isCancelling,
    isSubmitting: isSubmittingAppointment || isSubmittingReschedule,
    timeSlots,
    timeSlotsLoading: isTimeSlotsLoading || isClinicianInfoLoading,
    upcomingAppointments,
    isLoadingAppointments,
    isAppointmentListError,
    selectedCommunicationMethod,
    showSchedulingModal,
    setShowSchedulingModal,
    handleSelectedCommunicationMethod,
    handleOnConfirmationModalClose,
    handleSubmitScheduling,
    handleCancelAppointment,
    setAppointmentToReschedule,
    selectedCareTeamMemberId,
    setSelectedCareTeamMemberId,
    showCareGuideSchedulingModal,
    setShowCareGuideSchedulingModal,
    selectedAppointment,
    handleSelectedAppointmentSlot,
    handleCareTeamMemberSelected,
    modalKey,
  } as const;
};
