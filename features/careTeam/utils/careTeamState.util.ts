import { AppointmentCommunicationMethod, SessionCommunicationMethod, SessionSlot } from "@vivantehealth/vivante-core";
import { format, startOfDay } from "date-fns";

import { SelectedSlot } from "../components/CareTeamSchedulingModalContainer";
import { SlotPages } from "../store/careTeamStateSlice";

export const paginateSlots = (list: Array<SessionSlot>): SlotPages => {
  const dayMap: Record<number, Array<SessionSlot>> = list
    // sort
    .sort((a, b) => (a.start === b.start ? 0 : a.start > b.start ? 1 : -1))
    // bucket slots by day
    .reduce((acc: Record<number, SessionSlot[]>, next: SessionSlot) => {
      const dayTs = startOfDay(next.start * 1000).getTime();

      return {
        ...acc,
        [dayTs]: [...(acc[dayTs] || []), { ...next }],
      };
    }, {});

  return Object.keys(dayMap).map((key) => {
    const startOfDayTs = Number(key);

    return {
      title: format(startOfDayTs, "EEEE, MMMM d, yyyy"),
      slots: dayMap[startOfDayTs],
    };
  });
};

export const selectedSlotToKey = (slot: SelectedSlot | undefined) =>
  slot ? `${slot.slotPage.title};${slot.sessionSlot.start};${slot.sessionSlot.end}` : "";

export const keyToSelectedSlot = (key: string): SessionSlot => {
  const parts = key.split(";");

  return {
    start: Number(parts[1]),
    end: Number(parts[2]),
  };
};

const COMMUNICATION_METHOD_MAP = {
  [SessionCommunicationMethod.PHONE]: SessionCommunicationMethod.PHONE,
  [SessionCommunicationMethod.VIDEO]: SessionCommunicationMethod.VIDEO,
};

export const getCommunicationMethod = (communicationMethod: AppointmentCommunicationMethod) => {
  return COMMUNICATION_METHOD_MAP[communicationMethod];
};
