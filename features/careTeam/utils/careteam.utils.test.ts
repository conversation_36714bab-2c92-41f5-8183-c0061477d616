import { Appointment, CareTeamUser, CareTeamUserType, SessionCommunicationMethod } from "@vivantehealth/vivante-core";
import dayjs from "dayjs";
import { describe, test, expect } from "vitest";

import { Clinician } from "@Features/appointmentsNew/types/appointments.types";

import { CareTeamSessionType } from "../CareTeamScreenContainer";
import {
  getUserType,
  getCareTeamMemberType,
  appointmentData,
  calendarTypes,
  getCareTeamUserByRole,
  normalizeClinician,
} from "./careteam.utils";

describe("careteam.utils", () => {
  describe("getUserType", () => {
    const baseUser: CareTeamUser = {
      userType: CareTeamUserType.SHERPA,
      id: "1",
      firstName: "John",
      lastName: "Doe",
      avatarLink: "",
      careTeamExpertise: "",
    };

    test("Returns formatted type for SHERPA", () => {
      expect(getUserType(baseUser)).toBe("Health Coach");
    });

    test("Returns formatted type for DIETITIAN", () => {
      expect(getUserType({ ...baseUser, userType: CareTeamUserType.DIETITIAN })).toBe("Dietitian");
    });

    test("Returns empty string for undefined", () => {
      expect(getUserType(undefined)).toBe("");
    });

    test("Formats unknown type", () => {
      expect(getUserType({ ...baseUser, userType: "SOME_TYPE" as unknown as CareTeamUserType })).toBe("Some Type");
    });
  });

  describe("getCareTeamMemberType", () => {
    test("Returns Health Coach for SHERPA", () => {
      expect(getCareTeamMemberType(CareTeamUserType.SHERPA)).toBe("Health Coach");
    });

    test("Returns Dietitian for DIETITIAN", () => {
      expect(getCareTeamMemberType(CareTeamUserType.DIETITIAN)).toBe("Dietitian");
    });

    test("Returns Care Team Member for undefined", () => {
      expect(getCareTeamMemberType(undefined)).toBe("Care Team Member");
    });

    test("Returns Care Team Member for unknown type", () => {
      expect(getCareTeamMemberType("UNKNOWN" as unknown as CareTeamUserType)).toBe("Care Team Member");
    });
  });

  describe("appointmentData", () => {
    const now = new Date("2024-01-01T10:00:00Z");
    const appointment: Appointment = {
      id: "1",
      canEditUntil: new Date("2024-01-01T10:30:00Z"),
      practitioner: { id: "1", type: "care_team", firstName: "Dr.", lastName: "Smith", avatarLink: "", role: "SHERPA" },
      communicationMethod: SessionCommunicationMethod.PHONE,
      slot: {
        start: new Date("2024-01-01T11:00:00Z"),
        end: new Date("2024-01-01T11:30:00Z"),
      },
    };

    test("Returns correct appointment data", () => {
      const result = appointmentData(appointment, now);

      expect(result.practitioner).toEqual(appointment.practitioner);
      expect(result.appointmentDuration).toBe(30);
      expect(result.isPhoneAppointment).toBe(true);
      expect(result.minutesUntilAppointment).toBe(60);
    });

    test("Calculates minutesUntilAppointment correctly (round up)", () => {
      const appt = {
        ...appointment,
        slot: {
          start: dayjs(now).add(61, "minute").toDate(),
          end: dayjs(now).add(91, "minute").toDate(),
        },
      };
      const result = appointmentData(appt, now);

      expect(result.minutesUntilAppointment).toBe(61);
    });
  });

  describe("calendarTypes", () => {
    test("Contains all expected calendar types", () => {
      const values = calendarTypes.map((c) => c.value);

      expect(values).toContain("iCalendar");
      expect(values).toContain("GoogleCalendar");
      expect(values).toContain("YahooCalendar");
      expect(values).toContain("OutlookLive");
      expect(values).toContain("OfficeOutlook");
    });
  });

  describe("getCareTeamUserByRole", () => {
    const users: CareTeamUser[] = [
      {
        userType: CareTeamUserType.SHERPA,
        id: "1",
        firstName: "John",
        lastName: "Doe",
        avatarLink: "",
        careTeamExpertise: "",
      },
      {
        userType: CareTeamUserType.DIETITIAN,
        id: "2",
        firstName: "Jane",
        lastName: "Smith",
        avatarLink: "",
        careTeamExpertise: "",
      },
    ];

    test("Returns undefined if careTeamUserList is undefined", () => {
      expect(getCareTeamUserByRole("rd", undefined)).toBeUndefined();
    });

    test("Returns dietitian for 'rd' role", () => {
      expect(getCareTeamUserByRole("rd", users)).toEqual(users[1]);
    });

    test("Returns sherpa for 'hc' role", () => {
      expect(getCareTeamUserByRole("hc", users)).toEqual(users[0]);
    });

    test("Returns undefined for unknown role", () => {
      expect(getCareTeamUserByRole("other" as CareTeamSessionType, users)).toBeUndefined();
    });
  });

  describe("normalizeClinician", () => {
    test("Returns empty object for empty input", () => {
      expect(normalizeClinician([])).toEqual({});
    });

    test("Maps clinicians by GIThrive identifier", () => {
      const clinicians: Clinician[] = [
        {
          id: "1",
          first_name: "John",
          last_name: "Doe",
          clinician_role: { code: "MD", description: "" },
          specialty: { code: "GI", description: "" },
          avatar_link: "",
          enabled: true,

          identifiers: [
            { system: "GIThrive", identifier: "12345" },
            { system: "Other", identifier: "67890" },
          ],
        },
        {
          id: "2",
          first_name: "Jane",
          last_name: "Doe",
          clinician_role: { code: "MD", description: "" },
          specialty: { code: "GI", description: "" },
          avatar_link: "",
          enabled: true,

          identifiers: [
            { system: "GIThrive", identifier: "54321" },
            { system: "Other", identifier: "09876" },
          ],
        },
      ];

      const result = normalizeClinician(clinicians);

      expect(result).toHaveProperty("12345");
      expect(result).toHaveProperty("54321");
      expect(result["12345"].first_name).toBe("John");
      expect(result["54321"].first_name).toBe("Jane");

      const keys = Object.keys(result);

      expect(keys).not.toContain("67890");
      expect(keys).not.toContain("09876");
    });
  });
});
