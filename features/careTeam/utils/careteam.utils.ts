import { Appointment, CareTeamUser, CareTeamUserType, SessionCommunicationMethod } from "@vivantehealth/vivante-core";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { Clinician } from "@Features/appointmentsNew/types/appointments.types";
import { CalendarProviderOption } from "@Lib/infrastructure/calendar/calendar-provider-option";

import { CareTeamSessionType } from "../CareTeamScreenContainer";
import { ClinicianMap } from "../types/careTeam.types";

const CARE_TEAM_STRINGS = appStrings.features.careTeam;

export const getUserType = (careTeamUser?: CareTeamUser): string => {
  let type: string = "";

  if (careTeamUser?.userType === CareTeamUserType.SHERPA) {
    type = "HEALTH_COACH";
  } else {
    type = careTeamUser?.userType || "";
  }

  const displayUserType = type
    .replace(/[\W_]+/g, " ")
    .toLowerCase()
    .split(" ")
    .map((s: string) => s.charAt(0).toUpperCase() + s.substring(1))
    .join(" ");

  return displayUserType;
};

export const getCareTeamMemberType = (careTeamMemberType?: CareTeamUserType) => {
  switch (careTeamMemberType) {
    case CareTeamUserType.SHERPA:
      return "Health Coach";
    case CareTeamUserType.DIETITIAN:
      return "Dietitian";
    default:
      return "Care Team Member";
  }
};

export const appointmentData = (appointment: Appointment, currentTime: Date) => {
  const { practitioner, communicationMethod, slot } = appointment;
  const appointmentDuration = dayjs(slot.end).diff(dayjs(slot.start), "minute");
  const isPhoneAppointment = communicationMethod === SessionCommunicationMethod.PHONE;
  const appointmentStart = new Date(slot.start);
  const diff = appointmentStart.getTime() - currentTime.getTime();
  const minutesUntilAppointment = Math.ceil(diff / (1000 * 60));

  return {
    practitioner,
    appointmentDuration,
    isPhoneAppointment,
    minutesUntilAppointment,
  };
};

export const calendarTypes: { text: string; value: CalendarProviderOption }[] = [
  {
    text: CARE_TEAM_STRINGS.calendars.iCalendar,
    value: "iCalendar",
  },
  {
    text: CARE_TEAM_STRINGS.calendars.google,
    value: "GoogleCalendar",
  },
  {
    text: CARE_TEAM_STRINGS.calendars.yahoo,
    value: "YahooCalendar",
  },
  {
    text: CARE_TEAM_STRINGS.calendars.outlookLive,
    value: "OutlookLive",
  },
  {
    text: CARE_TEAM_STRINGS.calendars.officeOutlook,
    value: "OfficeOutlook",
  },
];

export const getCareTeamUserByRole = (
  sessionRole: CareTeamSessionType | undefined,
  careTeamUserList: CareTeamUser[] | undefined,
) => {
  if (!careTeamUserList) {
    return undefined;
  }

  if (sessionRole === "rd") {
    return careTeamUserList.find((careTeamUser) => careTeamUser.userType !== CareTeamUserType.SHERPA);
  }

  if (sessionRole === "hc") {
    return careTeamUserList.find((careTeamUser) => careTeamUser.userType === CareTeamUserType.SHERPA);
  }

  return undefined;
};

export const normalizeClinician = (response: Clinician[]) => {
  const normalizedClinicians = response.reduce<ClinicianMap>((clinicians, clinician) => {
    const { identifiers } = clinician;

    const giThriveId = identifiers.find((identifier) => identifier.system === "GIThrive")?.identifier;

    if (giThriveId) {
      clinicians[giThriveId] = clinician;
    }

    return clinicians;
  }, {});

  return normalizedClinicians;
};
