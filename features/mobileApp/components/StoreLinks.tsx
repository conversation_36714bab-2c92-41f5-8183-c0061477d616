import { createRef } from "react";
import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { Box } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

const MOBILE_APP_LINKS = appStrings.cylinderLinks.mobileApp;

export const StoreLinks = () => {
  const { sendEventAnalytics } = useAnalyticsHook();
  const linkRef = createRef<HTMLAnchorElement>();

  const openLink = (link: string) => {
    if (linkRef.current) {
      linkRef.current.href = link;
      linkRef.current.target = "_blank";
      linkRef.current.rel = "noopener noreferrer";
      linkRef.current.click();
    }
  };

  const onIOSLinkClicked = () => {
    sendEventAnalytics(ClickStreamActivityEventType.MOBILE_ON_WEB_IOS_CLICK);

    openLink(MOBILE_APP_LINKS.appleAppStore);
  };

  const onAndroidLinkClicked = () => {
    sendEventAnalytics(ClickStreamActivityEventType.MOBILE_ON_WEB_ANDROID_CLICK);

    openLink(MOBILE_APP_LINKS.androidAppStore);
  };

  return (
    <Box display="grid" gap={4} justifyContent="center">
      <a href="#" target="_blank" rel="noopener noreferrer" onClick={onAndroidLinkClicked}>
        <AppIcon name="GooglePlayLogo" containerStyles={{ width: "238px", height: "70px" }} />
      </a>
      <a href="#" target="_blank" rel="noopener noreferrer" onClick={onIOSLinkClicked}>
        <AppIcon name="AppStoreLogo" containerStyles={{ width: "238px", height: "70px" }} />
      </a>
      <a
        style={{
          display: "none",
          position: "absolute",
          top: "-1000px",
          left: "-1000px",
        }}
        ref={linkRef}
      >
        a
      </a>
    </Box>
  );
};
