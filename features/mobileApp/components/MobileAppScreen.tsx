import { useEffect } from "react";
import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { Box, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { StoreLinks } from "./StoreLinks";

const WELCOME_MOBILE_APP_STRINGS = appStrings.features.welcomeMobileApp;
const CYLINDER_LINKS = appStrings.cylinderLinks;

export const MobileAppScreen = () => {
  const { sendEventAnalytics } = useAnalyticsHook();

  useEffect(() => {
    sendEventAnalytics(ClickStreamActivityEventType.MOBILE_ON_WEB_SHOW);
  }, [sendEventAnalytics]);

  return (
    <Box padding={4} display="grid" gap={7}>
      <Box>
        <Box display="flex" justifyContent="center" mt={5} mb={4}>
          <AppIcon name="CompanyLogo" size="logo" />
        </Box>

        <Paper>
          <Typography variant="body" color={color.text.strong}>
            {WELCOME_MOBILE_APP_STRINGS.body}
          </Typography>
          <br />
          <Typography variant="body" color={color.text.strong}>
            {WELCOME_MOBILE_APP_STRINGS.download}
          </Typography>
        </Paper>
      </Box>

      <StoreLinks />

      <Box textAlign="center">
        <Typography variant="body" px={4} color={color.text.strong}>
          {WELCOME_MOBILE_APP_STRINGS.contact.one}
          <Typography component="a" variant="link" href={`mailto:${WELCOME_MOBILE_APP_STRINGS.contact.email}`}>
            {WELCOME_MOBILE_APP_STRINGS.contact.email}
          </Typography>
          {WELCOME_MOBILE_APP_STRINGS.contact.two}
          <Typography component="a" variant="link" href={`tel:${WELCOME_MOBILE_APP_STRINGS.contact.phone.number}`}>
            {WELCOME_MOBILE_APP_STRINGS.contact.phone.text}
          </Typography>
        </Typography>
      </Box>

      <Box display="grid" textAlign="center">
        <Typography variant="body" px={4} color={color.text.strong}>
          {WELCOME_MOBILE_APP_STRINGS.copyright(new Date().getFullYear())}
        </Typography>
        <Typography
          component="a"
          variant="link"
          href={CYLINDER_LINKS.termsAndConditions}
          target="_blank"
          rel="noopener noreferrer"
        >
          {WELCOME_MOBILE_APP_STRINGS.footerLinks.terms}
        </Typography>
        <Typography
          component="a"
          variant="link"
          href={CYLINDER_LINKS.privacyPolicy}
          target="_blank"
          rel="noopener noreferrer"
        >
          {WELCOME_MOBILE_APP_STRINGS.footerLinks.privacy}
        </Typography>
        <Typography
          component="a"
          variant="link"
          href={CYLINDER_LINKS.hipaaLink}
          target="_blank"
          rel="noopener noreferrer"
        >
          {WELCOME_MOBILE_APP_STRINGS.footerLinks.hipaa}
        </Typography>
      </Box>
    </Box>
  );
};
