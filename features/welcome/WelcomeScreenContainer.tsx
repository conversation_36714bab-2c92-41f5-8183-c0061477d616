import { useDispatch } from "react-redux";

import { letsGetStarted, loginNow } from "@Features/landing/store/onboardingEpics";
import { WelcomeScreen } from "@Features/welcome/components/WelcomeScreen";

export const WelcomeScreenContainer = () => {
  const dispatch = useDispatch();

  const handleGetStartedClick = () => {
    dispatch(letsGetStarted());
  };
  const handleLogInNowClick = () => {
    dispatch(loginNow());
  };

  return <WelcomeScreen onGetStartedClick={handleGetStartedClick} onLogInNowClick={handleLogInNowClick} />;
};
