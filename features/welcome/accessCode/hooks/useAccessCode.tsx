import { useState } from "react";
import { useDispatch } from "react-redux";
import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import * as Sentry from "@sentry/nextjs";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { useLazyGetRegistrationCodeConfigQuery } from "@Features/authentication/api/registrationApi";
import {
  LOCAL_STORAGE_ACCESS_CODE,
  LOCAL_STORAGE_MFA_METHOD,
  LOCAL_STORAGE_MFA_REQUIRED,
} from "@Features/authentication/assets/constants";
import { updateRegistrationConfig } from "@Features/eligibility/store/eligibilityProcessStateSlice";
import { loginNow } from "@Features/landing/store/onboardingEpics";
import { MFA_TYPE } from "@Features/multiFactorAuthentication/assets/constants";
import { LowerCaseMfaType, MfaType } from "@Features/multiFactorAuthentication/types/mfa.types";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { Routes } from "@Types";
import { isFetchFromBffError } from "@Utils/isFetchFromBffError";

const UNMATCHED_ACCESS_CODE_ERROR = ["not found", "invalid value provided for code"];
const ACCESS_CODE_STRINGS = appStrings.features.accessCode;
const MAP_MFA_SUPPORTED_METHODS = { email: MFA_TYPE.EMAIL, sms: MFA_TYPE.SMS } as const satisfies Record<
  LowerCaseMfaType,
  MfaType
>;

const getUnknownErrorMessage = (error: unknown) => {
  if (error instanceof Error) {
    return error.message;
  }

  return JSON.stringify(error);
};

export const useAccessCode = () => {
  const router = useRouter();
  const [registrationCodeError, setRegistrationCodeError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { sendEventAnalytics } = useAnalyticsHook();
  const [invokeGetRegistrationCodeConfig] = useLazyGetRegistrationCodeConfigQuery();
  const dispatch = useDispatch();

  const handleSubmit = async (data: { accessCode: string }) => {
    const trimmedAccessCode = data.accessCode.trim();

    if (trimmedAccessCode.length === 0) {
      return setRegistrationCodeError(ACCESS_CODE_STRINGS.errorMissingAccessCode);
    }

    if (registrationCodeError.length > 0) {
      setRegistrationCodeError("");
    }

    setIsSubmitting(true);
    sendEventAnalytics(ClickStreamActivityEventType.REGISTRATION_SUBMIT_COMPANY_CODE, {
      accessCode: trimmedAccessCode,
    });

    try {
      // fetch registration code (accessCode) config to determine if the company is using the new registration flow
      const response = await invokeGetRegistrationCodeConfig(trimmedAccessCode).unwrap();

      const {
        attributes: { registration_v2, passive_v2_eligibility_check, mfa_required, mfa_supported_methods },
      } = response.data;
      /**
       * The current implementation of MFA only supports a single method (email or sms) so we pick the first value from the array
       * but in the future we may support multiple methods hence mfa_supported_methods being an array of values
       * */
      const mfaSupportedMethod = mfa_supported_methods?.[0]
        ? MAP_MFA_SUPPORTED_METHODS[mfa_supported_methods?.[0]]
        : undefined;

      /**
       * Store the access code and mfa required flag in local storage so if the user refreshes the account creation page, we can still register them
       */
      localStorage.setItem(LOCAL_STORAGE_ACCESS_CODE, trimmedAccessCode);
      localStorage.setItem(LOCAL_STORAGE_MFA_REQUIRED, mfa_required.toString());
      if (mfaSupportedMethod) {
        localStorage.setItem(LOCAL_STORAGE_MFA_METHOD, mfaSupportedMethod);
      }

      dispatch(
        updateRegistrationConfig({
          accessCode: trimmedAccessCode,
          registration_v2,
          passiveV2EligibilityCheck: !!passive_v2_eligibility_check,
          mfaRequired: mfa_required,
          mfaSupportedMethod,
        }),
      );

      router.push(Routes.SIGN_UP);
    } catch (error) {
      const isExpectedRegistrationCodeError =
        isFetchFromBffError(error) && UNMATCHED_ACCESS_CODE_ERROR.includes(error.data.errors[0].detail.toLowerCase());

      sendEventAnalytics(
        isExpectedRegistrationCodeError
          ? ClickStreamActivityEventType.REGISTRATION_BAD_CODE
          : ClickStreamActivityEventType.REGISTRATION_UNEXPECTED_ERROR,
      );
      setRegistrationCodeError(
        isExpectedRegistrationCodeError
          ? ACCESS_CODE_STRINGS.errorWrongAccessCode
          : ACCESS_CODE_STRINGS.errorUnexpectedError,
      );
      setIsSubmitting(false);

      Sentry.captureMessage(
        isExpectedRegistrationCodeError ? error.data.errors[0].detail : getUnknownErrorMessage(error),
        "error",
      );
    }
  };

  const handleLogInNowClick = () => {
    dispatch(loginNow());
  };

  return { handleSubmit, handleLogInNowClick, registrationCodeError, isSubmitting } as const;
};
