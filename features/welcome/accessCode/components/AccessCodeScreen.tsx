import { Box, Button, CircularProgress, Grid, Paper } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { AuthFormContainer } from "@Components/AuthFormContainer/AuthFormContainer";
import { ErrorText } from "@Components/ErrorText/ErrorText";
import { FormInput } from "@Components/form/Fields";
import { Form } from "@Components/form/Form";
import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";
import { isOfType } from "@Utils/isOfType";

const SHARED_FORM_TEXT = appStrings.sharedFormText;
const BUTTON_TEXT = appStrings.buttonText;
const WECLOME_SCREEN_TEXT = appStrings.features.welcome;
const ACCESS_CODE_TEXT = appStrings.features.accessCode;
const FORM_FIELD_NAME = "accessCode";

type AccessCodeScreenProps = Readonly<{
  accessCode?: string;
  onSubmit: (data: { accessCode: string }) => void;
  onLogInClick: () => void;
  error?: string;
  errorMessageRef?: React.RefObject<HTMLDivElement | null>;
  isSubmitting: boolean;
}>;

export const AccessCodeScreen = ({
  accessCode = "",
  onSubmit,
  onLogInClick,
  error,
  errorMessageRef,
  isSubmitting,
}: AccessCodeScreenProps) => {
  const { formWidth } = useResponsiveStylesHook();

  return (
    <AuthFormContainer>
      <Paper sx={{ width: formWidth }}>
        <Form
          defaultValues={{ [FORM_FIELD_NAME]: accessCode }}
          onSubmit={(data) => {
            if (isOfType<Record<"accessCode", string>>(data, ["accessCode"])) {
              onSubmit(data);
            }
          }}
          formHeader={ACCESS_CODE_TEXT.accessCodeFormTitle}
          formSubheader={ACCESS_CODE_TEXT.accessCodePrompt}
        >
          <Grid item my={5}>
            <FormInput
              label={ACCESS_CODE_TEXT.inputLabel}
              id={FORM_FIELD_NAME}
              name={FORM_FIELD_NAME}
              type="text"
              required
              rules={{
                required: {
                  value: true,
                  message: SHARED_FORM_TEXT.requiredMessage,
                },
              }}
            />
          </Grid>

          <Grid item mt={5}>
            {error && (
              <Box mb={5}>
                <ErrorText errorTextRef={errorMessageRef} errorMessage={error} />
              </Box>
            )}

            <Button type="submit" variant="primary" aria-label={BUTTON_TEXT.continue} fullWidth disabled={isSubmitting}>
              {isSubmitting ? <CircularProgress size={24} color="inherit" /> : BUTTON_TEXT.continue}
            </Button>
          </Grid>
        </Form>

        <Button
          variant="tertiary"
          aria-label={WECLOME_SCREEN_TEXT.existingMemberButton}
          onClick={onLogInClick}
          sx={{ mt: 4 }}
          disabled={isSubmitting}
        >
          {WECLOME_SCREEN_TEXT.existingMemberButton}
        </Button>
      </Paper>
    </AuthFormContainer>
  );
};
