/* eslint-disable no-alert */
import type { Meta, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import { AccessCodeScreen } from "./AccessCodeScreen";

const meta: Meta<typeof AccessCodeScreen> = {
  decorators: cylinderThemeDecorator,
  title: "@Features/welcome/accessCode/AccessCodeScreen",
  component: AccessCodeScreen,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof AccessCodeScreen>;

export const Primary: Story = {
  args: {
    onSubmit: (data) => alert(JSON.stringify(data)),
    onLogInClick: () => alert("Log In Now was clicked"),
  },
};
