import { useEffect, useRef } from "react";

import { AccessCodeScreen } from "./components/AccessCodeScreen";
import { useAccessCode } from "./hooks/useAccessCode";

type AccessCodeScreenContainerProps = Readonly<{
  accessCode?: string;
}>;

export const AccessCodeScreenContainer = ({ accessCode }: AccessCodeScreenContainerProps) => {
  const { registrationCodeError, isSubmitting, handleSubmit, handleLogInNowClick } = useAccessCode();
  const errorMessageRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (registrationCodeError && errorMessageRef?.current) {
      errorMessageRef?.current.focus();
    }
  }, [registrationCodeError]);

  return (
    <AccessCodeScreen
      accessCode={accessCode}
      onSubmit={handleSubmit}
      onLogInClick={handleLogInNowClick}
      error={registrationCodeError}
      errorMessageRef={errorMessageRef}
      isSubmitting={isSubmitting}
    />
  );
};
