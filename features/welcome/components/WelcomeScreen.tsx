import { CSSProperties, useState, useEffect, useRef } from "react";
import { Box, Button, CardMedia, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { GC_VIDEO_ASSETS_PATH } from "@Assets/constants";
import { SPACING_16_PX, SPACING_32_PX, SPACING_48_PX } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { isMobileDevice } from "@Utils/isMobileDevice";

type WelcomeScreenProps = Readonly<{
  onGetStartedClick: () => void;
  onLogInNowClick: () => void;
}>;

const WELCOME_VIDEO_FILE = `${GC_VIDEO_ASSETS_PATH}/Cylinder_supergraphic_Cropped.mp4`;
const WELCOME_STRINGS = appStrings.features.welcome;

export const WelcomeScreen = ({ onGetStartedClick, onLogInNowClick }: WelcomeScreenProps) => {
  const [height, setHeight] = useState(0);
  const elementRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (!elementRef.current) return;

    if (isMobileDevice()) {
      elementRef.current.play();
    }

    const resizeObserver = new ResizeObserver(() => {
      if (elementRef?.current) {
        setHeight(elementRef.current.getBoundingClientRect().height);
      }
    });

    resizeObserver.observe(elementRef.current);
    return () => resizeObserver.disconnect(); // clean up
  }, []);

  return (
    <Box style={styles.welcomeScreenContainer}>
      <Box style={{ ...styles.contentContainer, height: `calc(100% - ${height}px)` }}>
        <AppIcon name="CompanyLogo" size="logo" includeInTabIndex includeContainer={false} />
        <Typography variant="h1Serif" sx={styles.largerSpacing}>
          {WELCOME_STRINGS.header}
        </Typography>
        <Button variant="primary" onClick={onGetStartedClick} sx={styles.signUpButton}>
          {WELCOME_STRINGS.newMemberButton}
        </Button>
        <div>
          <Button variant="tertiary" onClick={onLogInNowClick} id="loginBtn">
            {WELCOME_STRINGS.existingMemberButton}
          </Button>
        </div>
      </Box>
      <Box style={styles.bgVideoContainer}>
        <CardMedia ref={elementRef} component="video" autoPlay loop muted playsInline sx={styles.video}>
          <source src={WELCOME_VIDEO_FILE} type="video/mp4" />
        </CardMedia>
      </Box>
    </Box>
  );
};

const styles = {
  welcomeScreenContainer: {
    display: "flex",
    flexDirection: "column",
    flexWrap: "nowrap",
    justifyContent: "space-between",
    alignItems: "center",
    alignContent: "stretch",
    height: "100vh",
    background: "rgb(246,246,244)", // Need to use this value due to video background color being a different color
  },
  contentContainer: {
    display: "block",
    flexBasis: "auto",
    alignSelf: "auto",
    order: 0,
    textAlign: "center",
    width: "390px",
    alignContent: "center",
    minHeight: "400px",
    zIndex: 20,
  },
  bgVideoContainer: {
    overflow: "hidden",
    position: "relative",
    display: "flex",
    flexGrow: 1,
    flexShrink: 1,
    flexBasis: "auto",
    alignSelf: "auto",
    order: 0,
    maxWidth: "100%",
  },
  video: {
    position: "fixed",
    zIndex: 10,
    right: 0,
    bottom: 0,
    transform: "translateX(calc((100% - 100vw) / 2))",
    maxWidth: "1440px",
    minWidth: "1024px",
    maxHeight: "calc(100vh - 300px)",
    mixBlendMode: "darken",
  },
  signUpButton: {
    margin: SPACING_16_PX,
    marginTop: 0,
    width: `calc(100% - ${SPACING_48_PX})`,
  },
  largerSpacing: {
    margin: `${SPACING_32_PX} 0px`,
  },
  logoStyle: { height: "100%", width: "100%", maxHeight: "7vh", maxWidth: "204px" },
} as const satisfies Record<string, CSSProperties>;
