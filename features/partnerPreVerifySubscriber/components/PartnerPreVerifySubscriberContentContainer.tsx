import { Paper, Typography, Box, Button } from "@mui/material";

const CONTAINER_WIDTH = "448px";

type PartnerPreVerifySubscriberContentContainerProps = Readonly<{
  title: string;
  body: string;
  buttonText: string;
  handleButtonClick: () => void;
}>;

export const PartnerPreVerifySubscriberContentContainer = ({
  title,
  body,
  buttonText,
  handleButtonClick,
}: PartnerPreVerifySubscriberContentContainerProps) => {
  return (
    <Paper sx={{ width: CONTAINER_WIDTH, display: "flex", flexDirection: "column", gap: 2 }}>
      <Typography variant="h3">{title}</Typography>

      <Typography variant="body">{body}</Typography>

      {buttonText ? (
        <Box mt={4} display="flex" flexDirection={"column"} gap={3}>
          <Button variant={"primary"} onClick={handleButtonClick} fullWidth>
            {buttonText}
          </Button>
        </Box>
      ) : null}
    </Paper>
  );
};
