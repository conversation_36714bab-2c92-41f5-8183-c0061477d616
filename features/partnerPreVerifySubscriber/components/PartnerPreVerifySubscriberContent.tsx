import { appStrings } from "@Assets/app_strings";

import { PartnerPreVerifySubscriberContentContainer } from "./PartnerPreVerifySubscriberContentContainer";
import { PartnerPreVerifyRegistrationStatus } from "../types/partnerPreVerifySubscriber.types";

const PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS = appStrings.features.partnerPreVerifySubscriber;

type PartnerPreVerifySubscriberContentProps = Readonly<{
  isError: boolean;
  isUserRecoverableError: boolean;
  partnerName: string;
  registrationStatus: PartnerPreVerifyRegistrationStatus;
  handleRedirectToLogin: () => void;
  handleRedirectToCreateAccount: () => void;
  handleRedirectToOrigin: () => void;
}>;

export const PartnerPreVerifySubscriberContent = ({
  isError,
  isUserRecoverableError,
  partnerName,
  registrationStatus,
  handleRedirectToLogin,
  handleRedirectToCreateAccount,
  handleRedirectToOrigin,
}: PartnerPreVerifySubscriberContentProps) => {
  if (isError) {
    const errorStrings = isUserRecoverableError
      ? PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.userRecoverableError
      : PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.userNonRecoverableError;

    return (
      <PartnerPreVerifySubscriberContentContainer
        {...errorStrings}
        buttonText={
          isUserRecoverableError
            ? PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.userRecoverableError.buttonText(partnerName)
            : ""
        }
        handleButtonClick={handleRedirectToOrigin}
      />
    );
  }

  if (registrationStatus === "not_registered" || registrationStatus === "unknown") {
    return (
      <PartnerPreVerifySubscriberContentContainer
        title={PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.notRegistered.title}
        body={PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.notRegistered.body}
        buttonText={PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.notRegistered.buttonText}
        handleButtonClick={handleRedirectToCreateAccount}
      />
    );
  }

  return (
    <PartnerPreVerifySubscriberContentContainer
      title={PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.registered.title}
      body={PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.registered.body}
      buttonText={appStrings.buttonText.logIn}
      handleButtonClick={handleRedirectToLogin}
    />
  );
};
