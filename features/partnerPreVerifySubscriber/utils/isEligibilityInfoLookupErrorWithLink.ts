import { BffJsonApiError, isFetchFromBffError } from "@Utils/isFetchFromBffError";

import { EligibilityInfoLookupError } from "../types/partnerPreVerifySubscriber.types";

export const isEligibilityInfoLookupErrorWithError = (error: unknown): error is EligibilityInfoLookupError => {
  if (!isFetchFromBffError<BffJsonApiError>(error)) return false;

  const baseError = error.data;

  return (
    "links" in baseError &&
    baseError.links != null &&
    typeof baseError.links === "object" &&
    /** TODO: Update this to partner_preverified_registration_url when BE is updated */
    "parnter_preverified_registration_url" in baseError.links
  );
};
