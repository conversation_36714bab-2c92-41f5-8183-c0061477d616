import { describe, test, expect } from "vitest";

import { isEligibilityInfoLookupErrorWithError } from "./isEligibilityInfoLookupErrorWithLink";

describe("isEligibilityInfoLookupErrorWithLink", () => {
  test("Should return true if error contains a valid registration URL", () => {
    const error = {
      status: 200,
      data: {
        errors: [{ detail: "Some error", status: "400" }],
        jsonapi: { version: "1.0" },
        links: {
          parnter_preverified_registration_url: "https://example.com/register",
        },
      },
    };

    expect(isEligibilityInfoLookupErrorWithError(error)).toBe(true);
  });

  test("Should return false if error does not contain links", () => {
    const error = {
      status: 400,
      data: {
        errors: [],
        jsonapi: {
          version: "",
        },
      },
    };

    expect(isEligibilityInfoLookupErrorWithError(error)).toBe(false);
  });

  test("Should return false if links is null", () => {
    const error = {
      status: 400,
      data: {
        errors: [],
        jsonapi: { version: "1.0" },
        links: null,
      },
    };

    expect(isEligibilityInfoLookupErrorWithError(error)).toBe(false);
  });

  test("Should return false if links is not an object", () => {
    const error = {
      status: 400,
      data: {
        errors: [],
        jsonapi: { version: "1.0" },
        links: "invalid",
      },
    };

    expect(isEligibilityInfoLookupErrorWithError(error)).toBe(false);
  });

  test("Should return false if parnter_preverified_registration_url is missing", () => {
    const error = {
      status: 400,
      data: {
        errors: [],
        jsonapi: { version: "1.0" },
        links: {},
      },
    };

    expect(isEligibilityInfoLookupErrorWithError(error)).toBe(false);
  });

  test("Should return false if error is not a BffJsonApiFetchError", () => {
    const error = new Error("Some other error");

    expect(isEligibilityInfoLookupErrorWithError(error)).toBe(false);
  });
});
