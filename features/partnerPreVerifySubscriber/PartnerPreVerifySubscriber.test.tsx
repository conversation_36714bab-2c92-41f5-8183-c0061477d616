import { fireEvent, render, screen } from "@testing-library/react";
import { useRouter } from "next/router";
import { describe, beforeEach, test, expect, vi, Mock } from "vitest";

import { appStrings } from "@Assets/app_strings";
import { Routes } from "@Types";

import { PartnerPreVerifySubscriber } from "./PartnerPreVerifySubscriber";
import { PartnerPreVerifyEligibilityInfoLookupResponse } from "./types/partnerPreVerifySubscriber.types";

const PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS = appStrings.features.partnerPreVerifySubscriber;

vi.mock("next/router", () => ({
  useRouter: vi.fn(),
}));

vi.mock("next/navigation", () => ({
  useSearchParams: () => ({
    get: vi.fn(),
    has: vi.fn(),
    entries: vi.fn(),
    keys: vi.fn(),
    values: vi.fn(),
    toString: vi.fn(),
  }),
}));

vi.mock("./api/partnerPreVerifySubscriberApi", () => {
  const MOCKED_PARTNER_PRE_VERIFY_RESPONSE: PartnerPreVerifyEligibilityInfoLookupResponse = {
    registration_code: "uhc_store",
    registration_status: "not_registered",
    partner_eligibility_status: "eligible",
    eligibility_data: {
      birth_date: "1990-01-01",
      client_id: "12345",
      client_member_id: "67890",
      external_member_id: "abcde",
      first_name: "John",
      last_name: "Doe",
      phone_mobile: "************",
      hash_code: "hashcode123",
    },
  };

  return {
    useEligibilityInfoLookupQueryQuery: vi
      .fn()
      .mockReturnValueOnce({ isLoading: true, data: null, isError: false })
      .mockReturnValueOnce({
        isLoading: false,
        data: null,
        isError: true,
        error: { data: { errors: [], status: 403 } },
      })
      .mockReturnValueOnce({
        isLoading: false,
        data: null,
        isError: true,
        error: {
          data: {
            errors: [{ detail: "example error", status: 401 }],
            links: { parnter_preverified_registration_url: "http://example.com" },
          },
          status: 401,
        },
      })
      .mockReturnValueOnce({
        isLoading: false,
        data: { ...MOCKED_PARTNER_PRE_VERIFY_RESPONSE, registration_status: "unknown" },
        isError: false,
      })
      .mockReturnValueOnce({
        isLoading: false,
        data: MOCKED_PARTNER_PRE_VERIFY_RESPONSE,
        isError: false,
      })
      .mockReturnValueOnce({
        isLoading: false,
        data: { ...MOCKED_PARTNER_PRE_VERIFY_RESPONSE, registration_status: "registered" },
        isError: false,
      }),
  };
});

describe("PartnerPreVerifySubscriber", () => {
  const mockPush = vi.fn();
  const mockReplace = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useRouter as Mock).mockReturnValue({
      query: { partner: "uhc-store" },
      push: mockPush,
      replace: mockReplace,
    });
  });

  test("Renders loading spinner when retrieving eligibility data", () => {
    render(<PartnerPreVerifySubscriber code="0123-9503-a9453" />);

    expect(screen.getByRole("progressbar")).toBeInTheDocument();
  });

  test("Displays error message when the API returns a non-user recoverable error with NO CTA", () => {
    render(<PartnerPreVerifySubscriber code="0123-9503-a9453" />);

    expect(screen.getByText(PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.userNonRecoverableError.title)).toBeInTheDocument();
    expect(screen.getByText(PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.userNonRecoverableError.body)).toBeInTheDocument();
    expect(screen.queryByRole("button")).not.toBeInTheDocument();
  });

  test("Displays error message when the API returns a user recoverable error with CTA back to origin", () => {
    render(<PartnerPreVerifySubscriber code="0123-9503-a9453" />);

    expect(screen.getByText(PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.userRecoverableError.title)).toBeInTheDocument();
    expect(screen.getByText(PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.userRecoverableError.body)).toBeInTheDocument();
    const returnToOriginBtn = screen.getByRole("button", {
      name: PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.userRecoverableError.buttonText("UHC Store"),
    });

    expect(returnToOriginBtn).toBeInTheDocument();
    fireEvent.click(returnToOriginBtn);
    expect(mockReplace).toHaveBeenCalledWith("http://example.com");
  });

  test("Renders not registered content with CTA going to Sign up when registration status is 'unknown'", () => {
    render(<PartnerPreVerifySubscriber code="0123-9503-a9453" />);

    expect(screen.getByText(PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.notRegistered.title)).toBeInTheDocument();
    expect(screen.getByText(PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.notRegistered.body)).toBeInTheDocument();

    const createAcctBtn = screen.getByRole("button", {
      name: PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.notRegistered.buttonText,
    });

    expect(createAcctBtn).toBeInTheDocument();
    fireEvent.click(createAcctBtn);

    expect(mockPush).toHaveBeenCalledWith(Routes.SIGN_UP);
  });

  test("Renders not registered content with CTA going to Sign up when registration status is 'not_registered'", () => {
    render(<PartnerPreVerifySubscriber code="0123-9503-a9453" />);

    expect(screen.getByText(PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.notRegistered.title)).toBeInTheDocument();
    expect(screen.getByText(PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.notRegistered.body)).toBeInTheDocument();

    const createAcctBtn = screen.getByRole("button", {
      name: PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.notRegistered.buttonText,
    });

    expect(createAcctBtn).toBeInTheDocument();
    fireEvent.click(createAcctBtn);

    expect(mockPush).toHaveBeenCalledWith(Routes.SIGN_UP);
  });

  test("Renders registered content with CTA going to login when registration status is 'registered'", () => {
    render(<PartnerPreVerifySubscriber code="0123-9503-a9453" />);

    expect(screen.getByText(PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.registered.title)).toBeInTheDocument();
    expect(screen.getByText(PARTNER_PRE_VERIFY_SUBSCRIBER_STRINGS.registered.body)).toBeInTheDocument();

    const loginBtn = screen.getByRole("button", { name: appStrings.buttonText.logIn });

    expect(loginBtn).toBeInTheDocument();
    fireEvent.click(loginBtn);

    expect(mockPush).toHaveBeenCalledWith(Routes.LOGIN);
  });
});
