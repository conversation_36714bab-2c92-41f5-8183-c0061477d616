import { bffApi } from "@Api/bffApi";
import { mergePartnerPreVerifyData } from "@Features/eligibility/store/eligibilityProcessStateSlice";

import { PartnerPreVerifyEligibilityInfoLookupResponse } from "../types/partnerPreVerifySubscriber.types";

type PartnerPreVerifyEligibilityInfoLookupRequest = Readonly<{
  partner: string;
  code: string;
  redirectUri: string;
}>;

export const partnerPreVerifySubscriberApi = bffApi.injectEndpoints({
  endpoints: (builder) => ({
    eligibilityInfoLookupQuery: builder.query<
      PartnerPreVerifyEligibilityInfoLookupResponse,
      PartnerPreVerifyEligibilityInfoLookupRequest
    >({
      query: (body) => ({
        url: `/v1/partner/${body.partner}/eligibility_info_lookup`,
        method: "POST",
        body: {
          lookup_key: body.code,
          redirect_uri: body.redirectUri,
        },
      }),
      onQueryStarted: async (_, { dispatch, queryFulfilled }) => {
        const { data } = await queryFulfilled;

        dispatch(
          mergePartnerPreVerifyData({
            eligibilityData: data.eligibility_data,
            registrationCode: data.registration_code,
          }),
        );
      },
    }),
  }),
});

export const { useEligibilityInfoLookupQueryQuery } = partnerPreVerifySubscriberApi;
