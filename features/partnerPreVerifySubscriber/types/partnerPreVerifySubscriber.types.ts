import { BffFetchError, BffJsonApiError } from "@Utils/isFetchFromBffError";

export type PartnerPreVerifyRegistrationStatus = "not_registered" | "registered" | "unknown";

export type PartnerPreVerifyEligibilityInfoLookupResponse = {
  registration_code: string;
  registration_status: PartnerPreVerifyRegistrationStatus;
  partner_eligibility_status: "eligible" | "not_eligible";
  eligibility_data: {
    birth_date: string;
    client_id: string;
    client_member_id: string;
    external_member_id: string;
    first_name: string;
    last_name: string;
    phone_mobile: string;
    hash_code: string;
  };
};
/** TODO: Update this to partner_preverified_registration_url when BE is updated */
export type EligibilityInfoLookupError = BffFetchError<BffJsonApiError> & {
  data: { links: { parnter_preverified_registration_url: string } };
};
