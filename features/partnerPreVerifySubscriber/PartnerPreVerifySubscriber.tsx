import { Box, CircularProgress } from "@mui/material";
import { skipToken } from "@reduxjs/toolkit/query";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/router";

import { AuthenticationLayout } from "@Features/authentication/components/AuthenticationLayout";
import { Routes } from "@Types";

import { useEligibilityInfoLookupQueryQuery } from "./api/partnerPreVerifySubscriberApi";
import { SUBSCRIBER_PARTNERS } from "./assets/constants";
import { PartnerPreVerifySubscriberContent } from "./components/PartnerPreVerifySubscriberContent";
import { isEligibilityInfoLookupErrorWithError } from "./utils/isEligibilityInfoLookupErrorWithLink";

type PartnerPreVerifySubscriberProps = Readonly<{ code: string }>;

const isKeyofPartnerMap = (key: string): key is keyof typeof SUBSCRIBER_PARTNERS => {
  return Object.keys(SUBSCRIBER_PARTNERS).includes(key);
};

const getPartnerName = (partner: string | string[] | undefined) => {
  if (typeof partner === "string" && isKeyofPartnerMap(partner)) {
    return SUBSCRIBER_PARTNERS[partner];
  }

  return "";
};

const PARTNER_DISPLAYABLE_NAME = {
  "uhc-store": "UHC Store",
} as const satisfies Record<keyof typeof SUBSCRIBER_PARTNERS, string>;

export const PartnerPreVerifySubscriber = ({ code }: PartnerPreVerifySubscriberProps) => {
  const router = useRouter();
  const partner = getPartnerName(router.query.partner);
  const searchParams = useSearchParams();
  const partnerRedirectUrlParam = searchParams.get("redirectUrl");
  const partnerRedirectUrl =
    partnerRedirectUrlParam != null && partnerRedirectUrlParam.length > 0 ? partnerRedirectUrlParam : undefined;
  const redirectUri = `${window.location.origin}${window.location.pathname}`;
  const {
    isError: isEligibilityLookupError,
    isLoading,
    data,
    error,
  } = useEligibilityInfoLookupQueryQuery(partnerRedirectUrl ? skipToken : { code, redirectUri, partner });
  /** TODO: Update this to partner_preverified_registration_url when BE is updated */
  const errorRegistrationUrl = isEligibilityInfoLookupErrorWithError(error)
    ? error.data.links?.parnter_preverified_registration_url
    : "";
  const isError = isEligibilityLookupError || (partnerRedirectUrl != null && partnerRedirectUrl.length > 0);

  const handleRedirectToLogin = () => {
    router.push(Routes.LOGIN);
  };

  const handleRedirectToCreateAccount = () => {
    router.push(Routes.SIGN_UP);
  };

  const handleRedirectToOrigin = () => {
    if (errorRegistrationUrl) {
      router.replace(errorRegistrationUrl);
    }

    if (partnerRedirectUrl) {
      router.replace(partnerRedirectUrl);
    }
  };

  return (
    <AuthenticationLayout>
      <Box m={7} display="flex" flexDirection="column" justifyContent="center" alignItems="center">
        {isLoading ? (
          <CircularProgress />
        ) : (
          <PartnerPreVerifySubscriberContent
            isError={isError}
            isUserRecoverableError={isError && (!!errorRegistrationUrl || !!partnerRedirectUrl)}
            partnerName={isKeyofPartnerMap(partner) ? PARTNER_DISPLAYABLE_NAME[partner] : ""}
            registrationStatus={data?.registration_status ?? "registered"}
            handleRedirectToLogin={handleRedirectToLogin}
            handleRedirectToCreateAccount={handleRedirectToCreateAccount}
            handleRedirectToOrigin={handleRedirectToOrigin}
          />
        )}
      </Box>
    </AuthenticationLayout>
  );
};
