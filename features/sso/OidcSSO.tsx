import { useParams } from "next/navigation";

import { appStrings } from "@Assets/app_strings";

import { SsoContainer } from "./components/SsoContainer";
import { SsoError } from "./components/SsoError";
import { SSOError, useOidcSSO } from "./hooks/useOidcSSO";

const SSO_STRINGS = appStrings.features.oidcSSO;
const BUTTON_TEXT = appStrings.buttonText;
const NO_ACTIONS_ERRORS: SSOError[] = ["popupBlocked", "nonUserRecoverableError"];

export const OidcSSO = () => {
  const pathParameter = useParams<{ accessCode: string }>();
  const { ssoErrorType, signInWithPopup, redirectToLogin, redirectToRegister, registrationCode } = useOidcSSO(
    pathParameter?.accessCode,
  );
  const displayOidcError = ssoErrorType || registrationCode;
  const errorProperties = ssoErrorType ? SSO_STRINGS.authenticationErrors[ssoErrorType] : "";

  return (
    <SsoContainer displayError={!!displayOidcError}>
      {ssoErrorType && errorProperties && NO_ACTIONS_ERRORS.includes(ssoErrorType) && (
        <SsoError type="noActions" {...errorProperties} />
      )}

      {ssoErrorType === "popupClosedByUser" && errorProperties && (
        <SsoError
          type="singleAction"
          {...errorProperties}
          primaryActionText={BUTTON_TEXT.logIn}
          primaryActionOnClick={signInWithPopup}
        />
      )}

      {ssoErrorType === "failedMemberMatch" && errorProperties && (
        <SsoError
          type="multipleActions"
          {...errorProperties}
          primaryActionText={BUTTON_TEXT.logIn}
          primaryActionOnClick={redirectToLogin}
          secondaryActionText={BUTTON_TEXT.createAccount}
          secondaryActionOnClick={redirectToRegister}
        />
      )}

      {ssoErrorType === "unableToSignIn" && errorProperties && (
        <SsoError
          type="singleAction"
          {...errorProperties}
          primaryActionText={BUTTON_TEXT.logIn}
          primaryActionOnClick={redirectToLogin}
        />
      )}
    </SsoContainer>
  );
};
