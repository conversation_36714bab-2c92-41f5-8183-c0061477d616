import { useCallback } from "react";
import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { useRouter } from "next/router";

import { loadMemberAccount } from "@Features/member/store/memberStateSlice";
import { loadMemberPreferences } from "@Features/memberPreferences/store/memberPreferencesStateSlice";
import { addServerEventsSubscriptions } from "@Store/actions";
import { useAppDispatch } from "@Store/hooks";
import { Routes } from "@Types";

import { useAnalyticsHook } from "../../../hooks/analyticsHook";
import { useSignOutHook } from "../../../hooks/signOutHook";

type UseSSOProps = Readonly<{
  ssoType: "OIDC" | "SAML";
  ssoCode?: string;
  registrationCode?: string;
}>;

export const useSSO = ({ ssoType, ssoCode, registrationCode }: UseSSOProps) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { signOutOfApp } = useSignOutHook();
  const { sendEventAnalytics } = useAnalyticsHook();

  const initializeMemberAccount = useCallback(() => {
    dispatch(loadMemberAccount());
    dispatch(addServerEventsSubscriptions());
    dispatch(loadMemberPreferences());
  }, [dispatch]);

  const sendClickstreamEvent = useCallback(
    (eventType: ClickStreamActivityEventType, activityContextExtra?: Record<string, string>) => {
      sendEventAnalytics(eventType, { ...activityContextExtra, ssoType, ssoCode: ssoCode ?? "" });
    },
    [sendEventAnalytics, ssoCode, ssoType],
  );

  const signOutAndRedirect = (redirectPath: string) => {
    sendClickstreamEvent(ClickStreamActivityEventType.SSO_REDIRECT_INITIATED, { redirectTo: redirectPath });

    signOutOfApp();
    return router.push(redirectPath);
  };

  const redirectToLogin = () => {
    return signOutAndRedirect(Routes.LOGIN);
  };

  const redirectToRegister = () => {
    return signOutAndRedirect(`${Routes.REGISTER}/${registrationCode ?? ""}`);
  };

  return {
    initializeMemberAccount,
    redirectToLogin,
    redirectToRegister,
    sendClickstreamEvent,
    signOutAndRedirect,
  } as const;
};
