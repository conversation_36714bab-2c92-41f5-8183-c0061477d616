import { useState, useRef, useCallback, useEffect } from "react";
import { useDispatch } from "react-redux";
import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import * as Sentry from "@sentry/nextjs";
import { FirebaseError } from "firebase/app";
import { OAuthProvider, signInWithPopup } from "firebase/auth";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { getVivanteEnvironment } from "@Config/config";
import { useRegisteredMemberMatchMutation } from "@Features/authentication/api/registrationApi";
import { authenticationStateSlice } from "@Features/authentication/store/authenticationStateSlice";
import { useFirebaseAuth } from "@Hooks/firebaseAuthHook";
import { useSignOutHook } from "@Hooks/signOutHook";
import { Routes } from "@Types";
import { getFirebaseAuth } from "@Utils/getFirebaseAuth";

import { useSSO } from "./useSSO";

export type SSOError = keyof typeof appStrings.features.oidcSSO.authenticationErrors;
/** Map to convert access code to corresponding sso key */
const SSO_CODE_MAP: Record<string, string> = {
  accenture: "oidc.accenture-oauth",
  accolade: "oidc.accolade",
};

if (getVivanteEnvironment() === "dev4") {
  SSO_CODE_MAP["accolade3"] = "oidc.accolade3";
}

function mapAccessCodeToSSOCode(accessCode: string | undefined): string | undefined {
  return accessCode ? SSO_CODE_MAP[accessCode] : undefined;
}

export const mapSSOCodeToAccessCode = (ssoCode: string | undefined): string | undefined => {
  return Object.keys(SSO_CODE_MAP).find((key) => SSO_CODE_MAP[key] === ssoCode);
};

export const useOidcSSO = (accessCode: string | undefined) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [ssoErrorType, setSSOErrorType] = useState<SSOError | null>(null);
  const [registrationCode, setRegistrationCode] = useState<string | undefined>();
  const { isAuthenticated, isFirebaseAuthLoading } = useFirebaseAuth();
  const [invokeRegisteredMemberMatch, { isUninitialized }] = useRegisteredMemberMatchMutation();
  const { signOutOfApp } = useSignOutHook();
  /** This ref ensures we do not encounter a case of infinite popups. We don't need it to cause a re-render hence being a ref */
  const hasCompletedAuth = useRef(false);
  const ssoCode = mapAccessCodeToSSOCode(accessCode);
  const shouldDisplaySignInPopup = !isFirebaseAuthLoading && !isAuthenticated && !hasCompletedAuth.current;
  const shouldInvokeRegisteredMemberMatch = isAuthenticated && isUninitialized;
  const { initializeMemberAccount, redirectToLogin, redirectToRegister, sendClickstreamEvent } = useSSO({
    ssoType: "OIDC",
    registrationCode,
    ssoCode,
  });

  const signInWithPopupCallback = useCallback(async () => {
    setSSOErrorType(null);
    sendClickstreamEvent(ClickStreamActivityEventType.SSO_LOGIN_STARTED);

    try {
      const firebaseAuth = await getFirebaseAuth();
      const ssoProvider = new OAuthProvider(ssoCode ?? "");

      ssoProvider.addScope("profile");
      ssoProvider.addScope("email");

      const result = await signInWithPopup(firebaseAuth, ssoProvider);

      if (result.user) {
        dispatch(authenticationStateSlice.actions.initializeFirebaseAuth());
      }
    } catch (error) {
      if (error instanceof FirebaseError) {
        sendClickstreamEvent(ClickStreamActivityEventType.SSO_LOGIN_FAILED, { failureReason: error.code });
        Sentry.withScope((scope) => {
          scope.setLevel("error");
          Sentry.captureException(error);
        });

        if (error.code === "auth/popup-closed-by-user") {
          return setSSOErrorType("popupClosedByUser");
        }

        if (error.code === "auth/popup-blocked") {
          return setSSOErrorType("popupBlocked");
        }

        return setSSOErrorType("nonUserRecoverableError");
      }

      sendClickstreamEvent(ClickStreamActivityEventType.SSO_LOGIN_FAILED, {
        failureReason: "nonUserRecoverableError",
      });

      Sentry.withScope((scope) => {
        scope.setLevel("error");
        Sentry.captureException(error);
      });
      setSSOErrorType("nonUserRecoverableError");
    }
  }, [dispatch, sendClickstreamEvent, ssoCode]);

  const registerMemberMatch = useCallback(async () => {
    const handleReauthenticatUser = () => {
      signOutOfApp();
      hasCompletedAuth.current = false;
    };

    try {
      const firebaseAuth = await getFirebaseAuth();
      const currentUser = firebaseAuth.currentUser;
      const currentUserToken = await currentUser?.getIdTokenResult();

      /**
       * We determine if the provider ID is in our provider list, if not this most likely indicates they're currently
       * logged in via username/password instead of SSO, so we signout the user and start the SSO flow
       * */
      if (!mapSSOCodeToAccessCode(currentUser?.providerData?.[0].providerId)) {
        return handleReauthenticatUser();
      }

      hasCompletedAuth.current = true;
      const response = await invokeRegisteredMemberMatch().unwrap();

      /**
       * Handle the case where the SSO user has been linked to a Traditional user but this is the first login
       * after linking the accounts and they do not have the cylinder person id in the claims yet. We force refresh the token to get the updated claims
       */
      if (response.matched_to_registered_member && currentUserToken?.claims?.cylinder_person_id === undefined) {
        await currentUser?.getIdToken(true);
        initializeMemberAccount();
      }

      if (response.matched_to_registered_member) {
        sendClickstreamEvent(ClickStreamActivityEventType.SSO_MEMBER_MATCH_SUCCESSFUL);
        sendClickstreamEvent(ClickStreamActivityEventType.SSO_REDIRECT_INITIATED, { redirectTo: Routes.HOME });

        return router.push(Routes.HOME);
      }

      /**
       * This indicates that this is either the first time the user has logged in and has not created a Cylinder account
       * OR they created a Cylinder account but did not complete the registration process
       * We need to display the appropriate copy with options to either log in or create an account
       */
      sendClickstreamEvent(ClickStreamActivityEventType.SSO_MEMBER_MATCH_FAILED);
      setRegistrationCode(response.traditional_registration_code);
      setSSOErrorType("failedMemberMatch");
    } catch (error) {
      Sentry.withScope((scope) => {
        scope.setLevel("error");
        Sentry.captureException(error);
      });

      setSSOErrorType("nonUserRecoverableError");
    }
  }, [initializeMemberAccount, invokeRegisteredMemberMatch, router, sendClickstreamEvent, signOutOfApp]);

  useEffect(() => {
    async function authenticateUser() {
      if (shouldDisplaySignInPopup) {
        hasCompletedAuth.current = true;
        return await signInWithPopupCallback();
      }

      if (shouldInvokeRegisteredMemberMatch) {
        return await registerMemberMatch();
      }
    }
    /** When we successfully get an SSO Code based on the provided path parameter accessCode */
    if (ssoCode !== undefined) {
      authenticateUser();
    }
    /** Failure route when we have an accessCode but it is not an accepted SSO provider. */
    if (accessCode !== undefined && ssoCode === undefined) {
      setSSOErrorType("unableToSignIn");
    }
  }, [
    accessCode,
    registerMemberMatch,
    shouldDisplaySignInPopup,
    shouldInvokeRegisteredMemberMatch,
    signInWithPopupCallback,
    ssoCode,
  ]);

  return {
    ssoErrorType,
    signInWithPopup: signInWithPopupCallback,
    redirectToLogin,
    redirectToRegister,
    registrationCode,
  } as const;
};
