import { useCallback, useEffect, useState } from "react";
import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import * as Sentry from "@sentry/nextjs";
import { FirebaseError } from "firebase/app";
import Cookies from "js-cookie";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { useRegisteredMemberMatchMutation } from "@Features/authentication/api/registrationApi";
import { useAuthentication } from "@Features/authentication/hooks/useAuthentication";
import { initializeFirebaseAuth } from "@Features/authentication/store/authenticationEpics";
import { useSSO } from "@Features/sso/hooks/useSSO";
import { useFirebaseAuth } from "@Hooks/firebaseAuthHook";
import { useSignOutHook } from "@Hooks/signOutHook";
import { useAppDispatch } from "@Store/hooks";
import { Routes } from "@Types";
import { getFirebaseAuth } from "@Utils/getFirebaseAuth";

const SIGN_IN_TOKEN_KEY = "cylinderhealth_s_token";

export type SsoError = keyof typeof appStrings.features.samlSSO.authenticationErrors;

export const useSamlSSO = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const cookieSigninToken = Cookies.get(SIGN_IN_TOKEN_KEY);
  const [ssoErrorType, setSsoErrorType] = useState<SsoError | undefined>();
  const [registrationCode, setRegistrationCode] = useState<string | undefined>();
  const [partnerRedirectUrl, setPartnerRedirectUrl] = useState<string | undefined>();
  const { isAuthenticated, isFirebaseAuthLoading } = useFirebaseAuth();
  const [invokeRegisteredMemberMatch, { isUninitialized }] = useRegisteredMemberMatchMutation();
  const { signOutOfApp } = useSignOutHook();
  const shouldAuthenticate = !isAuthenticated && !isFirebaseAuthLoading;
  const shouldInvokeRegisteredMemberMatch = isAuthenticated && isUninitialized;
  const { initializeMemberAccount, redirectToLogin, redirectToRegister, sendClickstreamEvent, signOutAndRedirect } =
    useSSO({
      ssoType: "SAML",
      registrationCode,
    });
  const { authenticateUserWithCustomToken } = useAuthentication();

  const registerMemberMatch = useCallback(async () => {
    const handleReauthenticatUser = () => {
      signOutOfApp();
    };

    try {
      const firebaseAuth = await getFirebaseAuth();
      const currentUser = firebaseAuth.currentUser;
      const currentUserToken = await currentUser?.getIdTokenResult();

      /**
       * We determine if the user is logged in via username/password and if so, we need to sign them out
       */
      if (currentUser?.providerData?.[0].providerId === "password") {
        return handleReauthenticatUser();
      }

      const response = await invokeRegisteredMemberMatch().unwrap();

      /**
       * Handle the case where the SSO user has been linked to a Traditional user but this is the first login
       * after linking the accounts and they do not have the cylinder person id in the claims yet. We force refresh the token to get the updated claims
       */
      if (response.matched_to_registered_member && currentUserToken?.claims?.cylinder_person_id === undefined) {
        await currentUser?.getIdToken(true);
        initializeMemberAccount();
      }

      if (response.matched_to_registered_member) {
        sendClickstreamEvent(ClickStreamActivityEventType.SSO_MEMBER_MATCH_SUCCESSFUL);
        sendClickstreamEvent(ClickStreamActivityEventType.SSO_REDIRECT_INITIATED, { redirectTo: Routes.HOME });

        return router.push(Routes.HOME);
      }

      /**
       * This indicates that the user needs to complete the registation process on the partner side before continuing
       */
      if (response.redirect_to_partner_preverified_registration) {
        sendClickstreamEvent(ClickStreamActivityEventType.SSO_MEMBER_MATCH_FAILED);

        setSsoErrorType("redirectToPartner");
        setPartnerRedirectUrl(response.partner_preverified_registration_url);
        return;
      }

      /**
       * This indicates that this is either the first time the user has logged in and has not created a Cylinder account
       * OR they created a Cylinder account but did not complete the registration process
       * We need to display the appropriate copy with options to either log in or create an account
       */
      sendClickstreamEvent(ClickStreamActivityEventType.SSO_MEMBER_MATCH_FAILED);
      setRegistrationCode(response.traditional_registration_code);
      setSsoErrorType("failedMemberMatch");
    } catch (error) {
      Sentry.withScope((scope) => {
        scope.setLevel("error");
        Sentry.captureException(error);
      });

      setSsoErrorType("failedSignin");
    }
  }, [initializeMemberAccount, invokeRegisteredMemberMatch, router, sendClickstreamEvent, signOutOfApp]);

  const authenticateAndMatchUser = useCallback(
    async (token: string) => {
      try {
        await authenticateUserWithCustomToken(token, false);
        dispatch(initializeFirebaseAuth());

        await registerMemberMatch();
      } catch (error) {
        sendClickstreamEvent(ClickStreamActivityEventType.SSO_LOGIN_FAILED, {
          failureReason: error instanceof FirebaseError ? error.code : "badToken",
        });
        Sentry.withScope((scope) => {
          scope.setLevel("error");
          Sentry.captureException(error);
        });

        return setSsoErrorType("failedSignin");
      }
    },
    [authenticateUserWithCustomToken, dispatch, registerMemberMatch, sendClickstreamEvent],
  );

  useEffect(() => {
    const authenticateUser = async () => {
      if (!cookieSigninToken) {
        return setSsoErrorType("failedSignin");
      }

      if (shouldAuthenticate) {
        // attempt to sign in with the token and match member
        return await authenticateAndMatchUser(cookieSigninToken);
      }

      if (shouldInvokeRegisteredMemberMatch) {
        return await registerMemberMatch();
      }
    };

    authenticateUser();
  }, [
    authenticateAndMatchUser,
    cookieSigninToken,
    registerMemberMatch,
    shouldAuthenticate,
    shouldInvokeRegisteredMemberMatch,
  ]);

  const redirectToPartner = () => {
    if (partnerRedirectUrl) {
      signOutAndRedirect(partnerRedirectUrl);
    }
  };

  return { ssoErrorType, redirectToLogin, redirectToRegister, redirectToPartner } as const;
};
