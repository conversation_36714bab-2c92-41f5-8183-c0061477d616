import { appStrings } from "@Assets/app_strings";

import { SsoContainer } from "./components/SsoContainer";
import { SsoError } from "./components/SsoError";
import { useSamlSSO } from "./hooks/useSamlSSO";

const SAML_SSO_STRINGS = appStrings.features.samlSSO;
const BUTTON_TEXT = appStrings.buttonText;

export const SamlSSO = () => {
  const { ssoErrorType, redirectToLogin, redirectToPartner, redirectToRegister } = useSamlSSO();
  const errorProperties = ssoErrorType ? SAML_SSO_STRINGS.authenticationErrors[ssoErrorType] : "";

  return (
    <SsoContainer displayError={!!ssoErrorType}>
      {ssoErrorType === "failedSignin" && errorProperties && <SsoError type="noActions" {...errorProperties} />}

      {ssoErrorType === "redirectToPartner" && errorProperties && (
        <SsoError
          type="singleAction"
          {...errorProperties}
          primaryActionText={SAML_SSO_STRINGS.finishRegistrationBtnText}
          primaryActionOnClick={redirectToPartner}
        />
      )}

      {ssoErrorType === "failedMemberMatch" && errorProperties && (
        <SsoError
          type="multipleActions"
          {...errorProperties}
          primaryActionText={BUTTON_TEXT.logIn}
          primaryActionOnClick={redirectToLogin}
          secondaryActionText={BUTTON_TEXT.createAccount}
          secondaryActionOnClick={redirectToRegister}
        />
      )}
    </SsoContainer>
  );
};
