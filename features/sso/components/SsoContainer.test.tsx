import { render, screen } from "@testing-library/react";
import { describe, test, expect } from "vitest";

import { SPACING_56_PX } from "@Assets/style_constants";

import { SsoContainer } from "./SsoContainer";

describe("SsoContainer", () => {
  test("Renders CircularProgress when displayError is false and proper margin", () => {
    const { container } = render(
      <SsoContainer displayError={false}>
        <div>Child Content</div>
      </SsoContainer>,
    );

    expect(screen.getByRole("progressbar")).toBeInTheDocument();
    expect(screen.queryByText("Child Content")).not.toBeInTheDocument();
    expect(container.firstChild).toHaveStyle("margin: auto");
  });

  test("Renders children when displayError is true", () => {
    const { container } = render(
      <SsoContainer displayError>
        <div>Child Content</div>
      </SsoContainer>,
    );

    expect(screen.getByText("Child Content")).toBeInTheDocument();
    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();
    expect(container.firstChild).toHaveStyle(`margin: ${SPACING_56_PX}`);
  });
});
