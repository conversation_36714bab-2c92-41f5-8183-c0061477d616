import { Box, Button, Paper, Typography } from "@mui/material";
/** Width from Figma https://www.figma.com/design/L236LT0gcSWVsrwxo9fsr9/SSO?node-id=5192-4102&t=4AktOydq12IbdrFw-4 */
const SSO_ERROR_WIDTH = "448px";

type SsoErrorBaseProps = Readonly<{
  title: string;
  textBody: string;
  additionalTextBody?: string;
  steps?: ReadonlyArray<string>;
  footerText?: string;
}>;

type SsoErrorActions =
  | {
      type: "noActions";
      primaryActionText?: never;
      primaryActionOnClick?: never;
      secondaryActionText?: never;
      secondaryActionOnClick?: never;
    }
  | {
      type: "singleAction";
      primaryActionText: string;
      primaryActionOnClick: () => void;
      secondaryActionText?: never;
      secondaryActionOnClick?: never;
    }
  | {
      type: "multipleActions";
      primaryActionText: string;
      primaryActionOnClick: () => void;
      secondaryActionText: string;
      secondaryActionOnClick: () => void;
    };

type SsoErrorProps = SsoErrorBaseProps & SsoErrorActions;

export const SsoError = ({
  title,
  textBody,
  additionalTextBody,
  steps,
  footerText,
  type,
  primaryActionText,
  primaryActionOnClick,
  secondaryActionText,
  secondaryActionOnClick,
}: SsoErrorProps) => {
  return (
    <Paper sx={{ width: SSO_ERROR_WIDTH, display: "flex", flexDirection: "column", gap: 2 }}>
      <Typography variant="h3">{title}</Typography>

      <Typography variant="body">{textBody}</Typography>

      {additionalTextBody ? <Typography variant="body">{additionalTextBody}</Typography> : null}

      {steps ? (
        <ol>
          {steps.map((step) => (
            <li key={step}>
              <Typography variant="body">{step}</Typography>
            </li>
          ))}
        </ol>
      ) : null}

      {footerText ? <Typography variant="body">{footerText}</Typography> : null}

      {type !== "noActions" ? (
        <Box mt={4} display="flex" flexDirection={type === "multipleActions" ? "row" : "column"} gap={3}>
          <Button
            variant={type === "multipleActions" ? "secondary" : "primary"}
            onClick={primaryActionOnClick}
            fullWidth
          >
            {primaryActionText}
          </Button>

          {type === "multipleActions" ? (
            <Button variant="secondary" onClick={secondaryActionOnClick} fullWidth>
              {secondaryActionText}
            </Button>
          ) : null}
        </Box>
      ) : null}
    </Paper>
  );
};
