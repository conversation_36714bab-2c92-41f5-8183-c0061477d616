import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, test, vi, expect } from "vitest";

import { SsoError } from "./SsoError";

describe("SsoError Component", () => {
  const user = userEvent.setup();

  test("Renders the title and text body", () => {
    render(<SsoError title="Error Title" textBody="This is the error message." type="noActions" />);

    expect(screen.getByText("Error Title")).toBeInTheDocument();
    expect(screen.getByText("This is the error message.")).toBeInTheDocument();
  });

  test("Renders additional text body if provided", () => {
    render(
      <SsoError
        title="Error Title"
        textBody="This is the error message."
        additionalTextBody="Additional details here."
        type="noActions"
      />,
    );

    expect(screen.getByText("Additional details here.")).toBeInTheDocument();
  });

  test("Renders steps if provided", () => {
    const steps = ["Step 1", "Step 2", "Step 3"];

    render(<SsoError title="Error Title" textBody="This is the error message." steps={steps} type="noActions" />);

    steps.forEach((step) => {
      expect(screen.getByText(step)).toBeInTheDocument();
    });
  });

  test("Renders footer text if provided", () => {
    render(
      <SsoError
        title="Error Title"
        textBody="This is the error message."
        footerText="Footer details here."
        type="noActions"
      />,
    );

    expect(screen.getByText("Footer details here.")).toBeInTheDocument();
  });

  test("Renders a single action button and handles click", async () => {
    const handleClick = vi.fn();

    render(
      <SsoError
        title="Error Title"
        textBody="This is the error message."
        type="singleAction"
        primaryActionText="Retry"
        primaryActionOnClick={handleClick}
      />,
    );

    const button = screen.getByText("Retry");

    expect(button).toBeInTheDocument();

    await user.click(button);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test("Renders multiple action buttons and handles clicks", async () => {
    const handlePrimaryClick = vi.fn();
    const handleSecondaryClick = vi.fn();

    render(
      <SsoError
        title="Error Title"
        textBody="This is the error message."
        type="multipleActions"
        primaryActionText="Retry"
        primaryActionOnClick={handlePrimaryClick}
        secondaryActionText="Cancel"
        secondaryActionOnClick={handleSecondaryClick}
      />,
    );

    const primaryButton = screen.getByText("Retry");
    const secondaryButton = screen.getByText("Cancel");

    expect(primaryButton).toBeInTheDocument();
    expect(secondaryButton).toBeInTheDocument();

    await user.click(primaryButton);
    expect(handlePrimaryClick).toHaveBeenCalledTimes(1);

    await user.click(secondaryButton);
    expect(handleSecondaryClick).toHaveBeenCalledTimes(1);
  });
});
