import { ReactNode } from "react";
import { Box, CircularProgress } from "@mui/material";

type SsoContainerProps = Readonly<{
  displayError: boolean;
  children: ReactNode;
}>;

export const SsoContainer = ({ displayError, children }: SsoContainerProps) => {
  return (
    <Box
      m={displayError ? 7 : "auto"}
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
    >
      {!displayError ? <CircularProgress thickness={3} size={48} /> : null}

      {displayError ? children : null}
    </Box>
  );
};
