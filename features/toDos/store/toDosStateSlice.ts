import { ToDo, VivanteApiError } from "@vivantehealth/vivante-core";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

import { LoadState } from "@Types";
import { buildSliceStateSelector, processError } from "@Utils/slice.util";

export type ToDosState = Readonly<{
  loadState: LoadState;
  todos: ToDo[] | null;
}>;

export const initialState: ToDosState = {
  loadState: null,
  todos: null,
};

export const ToDosStateSlice = createSlice({
  name: "toDosState",
  initialState,
  reducers: {
    loadToDos: (state) => ({ ...state, loadState: "loading" }),
    loadToDosSuccess: (state, action: PayloadAction<ToDo[]>) => ({
      ...state,
      loadState: "loaded",
      todos: action.payload,
    }),
    loadToDosFailure: (state, action: PayloadAction<Error | VivanteApiError>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
  },
});

export const toDosSelectors = buildSliceStateSelector("toDosState");

export const toDosStateReducer = ToDosStateSlice.reducer;
