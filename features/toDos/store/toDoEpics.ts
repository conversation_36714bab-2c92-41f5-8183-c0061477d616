import { Action } from "@reduxjs/toolkit";
import { Epic, ofType } from "redux-observable";
import { from, Observable, of } from "rxjs";
import { switchMap, map, catchError } from "rxjs/operators";

import { vivanteCoreContainer } from "@Lib/vivanteCore";

import { ToDosStateSlice } from "./toDosStateSlice";

const { loadToDos, loadToDosFailure, loadToDosSuccess } = ToDosStateSlice.actions;

const loadToDosEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(loadToDos.type),
    switchMap(() =>
      from(vivanteCoreContainer.getToDosUseCaseFactory().createGetCurrentMemberTodosUseCase().execute()).pipe(
        map((toDos) => loadToDosSuccess(toDos)),
        catchError((error) => of(loadToDosFailure(error))),
      ),
    ),
  );
};

export const todoEpics = [loadToDosEpic];
