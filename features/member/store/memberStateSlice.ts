import { Member, VivanteApiError } from "@vivantehealth/vivante-core";
import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import * as Sentry from "@sentry/nextjs";

import { LoadState } from "@Types";
import { buildSliceStateSelector, processError } from "@Utils/slice.util";

export type MemberState = Readonly<{
  member?: Member;
  loadState: LoadState;
}>;

export const initialState: MemberState = {
  member: undefined,
  loadState: null,
};

export const memberStateSlice = createSlice({
  name: "memberState",
  initialState,
  reducers: {
    loadMemberAccount: (state) => ({ ...state, loadState: "loading" }),
    loadMemberAccountSuccess: (state: MemberState, action: PayloadAction<Member>) => {
      Sentry.setUser({ id: action.payload.id });

      return {
        ...state,
        member: action.payload,
        loadState: "loaded",
      } as const;
    },
    loadMemberAccountFailure: (state, action: PayloadAction<Error | VivanteApiError>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
  },
});

export const { loadMemberAccount, loadMemberAccountSuccess, loadMemberAccountFailure } = memberStateSlice.actions;

export const memberStateSelector = buildSliceStateSelector("memberState");

export const memberStateReducer = memberStateSlice.reducer;
