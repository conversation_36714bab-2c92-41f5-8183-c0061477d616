import { Action } from "@reduxjs/toolkit";
import { Epic, ofType } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { switchMap, map, catchError } from "rxjs/operators";

import {
  loadMemberAccount,
  loadMemberAccountFailure,
  loadMemberAccountSuccess,
} from "@Features/member/store/memberStateSlice";
import { vivanteCoreContainer } from "@Lib/vivanteCore";

export const loadMemberEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(loadMemberAccount.type),
    switchMap(() => {
      return from(vivanteCoreContainer.getMemberUseCaseFactory().createGetMemberUseCase().execute()).pipe(
        map((member) => loadMemberAccountSuccess(member)),
        catchError((error) => {
          return of(loadMemberAccountFailure(error));
        }),
      );
    }),
  );
};

export const loadMemberEpics = [loadMemberEpic];
