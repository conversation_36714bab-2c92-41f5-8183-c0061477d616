import {
  EditSymptomLogScreenProperties,
  Severity,
  Symptom,
  SymptomGroupHistoryItem,
  UpdateSymptomLogProps,
} from "@vivantehealth/vivante-core";

import {
  PrepareEditSymptomLogProps,
  PrepareSymptomLogQuestionSuccessProps,
  SymptomLoggingState,
  SymptomWithSeverity,
  initialState,
} from "../store/symptomLoggingStateSlice";

export const updateStateFromClose = (state: SymptomLoggingState): SymptomLoggingState => {
  return {
    ...state,
    loadState: null,
    symptoms: initialState.symptoms,
    severities: initialState.severities,
    symptomIdToSeverityList: initialState.symptomIdToSeverityList,
    symptomSeverities: initialState.symptomSeverities,
    symptomToSymptomLogId: initialState.symptomToSymptomLogId,
    isEditing: initialState.isEditing,
    showSymptomLoggingControl: false,
    initDate: null,
    currentSymptomGroupHistoryItem: null,
  };
};
export const prepareForCreatingSymptomLog = (
  state: SymptomLoggingState,
  prepareSymptomLogQuestionSuccessProps: PrepareSymptomLogQuestionSuccessProps,
): SymptomLoggingState => {
  const { symptomLogScreenProperties, date } = prepareSymptomLogQuestionSuccessProps;
  const { symptoms: _symptoms } = symptomLogScreenProperties;
  const symptoms = _symptoms.sort((a, b) => (a.title < b.title ? -1 : 1)); // sort alphabetically

  const severities: Severity[] = [...symptomLogScreenProperties.severities];

  const symptomIdToSeverityList = symptoms.reduce((acc, next) => {
    return {
      ...acc,
      [next.value]: severities,
    };
  }, {});
  const minSeverity = severities.sort((a, b) => a.ranking - b.ranking)[0];
  const symptomSeverities = symptoms.reduce((acc, next) => {
    return {
      ...acc,
      [next.value]: minSeverity.value,
    };
  }, {});

  const initDate = date || null;

  return {
    ...state,
    symptoms,
    severities,
    symptomSeverities,
    symptomIdToSeverityList,
    initDate,
  };
};

export const prepareForEditingSymptomLog = (
  state: SymptomLoggingState,
  prepareEditSymptomLogProps: PrepareEditSymptomLogProps,
): SymptomLoggingState => {
  const symptomSeverities: Record<string, string> = {};
  const symptomToSymptomLogId: Record<string, string> = {};
  const symptomIdToSeverityList: Record<string, Severity[]> = {};
  const selectedDate = prepareEditSymptomLogProps.editSymptomLogScreenProperties[0]?.selectedDate;
  // hydrate the historical questions being edited
  const symptoms: Symptom[] = [];
  const { symptomLogs } = prepareEditSymptomLogProps.symptomGroupHistoryItem;

  for (let i = 0; i < symptomLogs.length; i++) {
    const symptomLogId = symptomLogs[i].id;
    const editScreenForSymptom: EditSymptomLogScreenProperties =
      prepareEditSymptomLogProps.editSymptomLogScreenProperties[i];
    const symptom = editScreenForSymptom.selectedSymptom;
    const symptomId = symptom.value;

    symptomSeverities[symptomId] = editScreenForSymptom.selectedSeverity.value;
    symptomToSymptomLogId[symptomId] = symptomLogId;
    symptomIdToSeverityList[symptomId] = [...(editScreenForSymptom.severities || [])].sort(
      (a, b) => a.ranking - b.ranking,
    );
    symptoms.push(symptom);
  }

  symptoms.sort((a, b) => a.value.localeCompare(b.value));

  return {
    ...state,
    symptoms,
    symptomSeverities,
    symptomToSymptomLogId,
    symptomIdToSeverityList,
    loadState: "loaded",
    initDate: new Date(selectedDate),
    isEditing: true,
    showSymptomLoggingControl: true,
  };
};

export const createUpdateSymptomLogProps = (
  date: Date,
  symptomGroupHistoryItem: SymptomGroupHistoryItem,
  symptoms: Symptom[],
  symptomsWithSeverities: SymptomWithSeverity[],
): UpdateSymptomLogProps[] => {
  const symptomValues: { [symptom: string]: string } = symptomsWithSeverities.reduce((acc, next) => {
    return {
      ...acc,
      [next.symptomValue]: next.severityValue,
    };
  }, {});

  return symptoms.reduce<UpdateSymptomLogProps[]>((accumulator, symptom) => {
    return [
      ...accumulator,
      {
        date,
        symptomLogId:
          symptomGroupHistoryItem.symptomLogs.find((symptomLog) => symptomLog.symptom === symptom.title)?.id ?? "",
        severityValue: symptomValues[symptom.value],
      },
    ];
  }, []);
};
