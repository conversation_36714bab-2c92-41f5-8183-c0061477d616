import { useEffect, useState } from "react";
import { Box, Chip } from "@mui/material";
import { TimePicker } from "@mui/x-date-pickers/TimePicker";
import { color, typography } from "@vivantehealth/design-tokens";
import dayjs, { Dayjs } from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { SPACING_12_PX, SPACING_24_PX } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";

const PROGRESS_STRINGS = appStrings.features.progress;
const AM_MIN = 0;
const AM_MAX = 11;
const PM_MIN = 12;

type TimePickerProps = Readonly<{
  date: number;
  setTime: (time: number) => void;
}>;

export const TimePickerInput = ({ date, setTime }: TimePickerProps) => {
  const initialDay = new Date(date).getDate();
  const [newDate, setNewDate] = useState(new Date(date));
  const [minutes, setMinutes] = useState(newDate.getMinutes());
  const [hours, setHours] = useState(newDate.getHours());
  const [format, setFormat] = useState<"AM" | "PM">(newDate.getHours() > AM_MAX ? "PM" : "AM");

  useEffect(() => {
    const newDate = new Date(date);

    setMinutes(newDate.getMinutes());
    setHours(newDate.getHours());
    setFormat(newDate.getHours() > AM_MAX ? "PM" : "AM");
    setNewDate(newDate);
  }, [date]);

  const setNewHours = (newHours: number) => {
    setHours(newHours);
    newDate.setHours(newHours);
    newDate.setDate(initialDay);
    return setTime(newDate.getTime());
  };

  const setNewMinutes = (newMinutes: number) => {
    setMinutes(newMinutes);
    newDate.setMinutes(newMinutes);
    newDate.setDate(initialDay);
    return setTime(newDate.getTime());
  };

  const handleChangeFormat = (formatVal: "AM" | "PM") => {
    if (format === formatVal) {
      return null;
    }

    setFormat(formatVal);
    if (formatVal === "AM") {
      return setNewHours(hours - PM_MIN);
    }

    const setHours = hours !== PM_MIN ? hours + PM_MIN : hours;

    return setNewHours(setHours);
  };

  const handleInputChange = (val: number) => {
    if (format === "AM") {
      const setHours = val > AM_MAX ? AM_MIN : val;

      return setNewHours(setHours);
    }

    const setHours = val !== PM_MIN ? val + PM_MIN : val;

    return setNewHours(setHours);
  };

  return (
    <Box display="flex" alignItems="center" gap={4}>
      <TimeInput
        onChangeCallback={(newVal) => handleInputChange(newVal?.hour() || 1)}
        variant="hours"
        value={dayjs().hour(hours)}
      />
      <AppIcon name="SemiColon" />
      <TimeInput
        onChangeCallback={(newVal) => setNewMinutes(newVal?.minute() || 0)}
        variant="minutes"
        value={dayjs().minute(minutes)}
      />
      <Box display="flex" gap={2}>
        <TimeFormatChip
          text={PROGRESS_STRINGS.amButtonText}
          onClickCallback={() => handleChangeFormat("AM")}
          selected={format === "AM"}
        />
        <TimeFormatChip
          text={PROGRESS_STRINGS.pmButtonText}
          onClickCallback={() => handleChangeFormat("PM")}
          selected={format === "PM"}
        />
      </Box>
    </Box>
  );
};

type TimeInputProps = Readonly<{
  onChangeCallback: (newVal: Dayjs | null) => void;
  value: Dayjs;
  variant: "hours" | "minutes";
}>;

const TimeInput = ({ onChangeCallback, value, variant }: TimeInputProps) => {
  const isHoursVariant = variant === "hours";

  return (
    <TimePicker
      value={value}
      views={isHoursVariant ? ["hours"] : ["minutes"]}
      format={isHoursVariant ? "hh" : "mm"}
      onChange={onChangeCallback}
      disableOpenPicker
      sx={{ minWidth: "64px" }}
      slotProps={{
        textField: {
          error: false,
          "aria-label": `Time ${variant} input`,
        },
      }}
    />
  );
};

type TimeFormatChip = Readonly<{
  text: string;
  onClickCallback: () => void;
  selected: boolean;
}>;

const TimeFormatChip = ({ text, onClickCallback, selected }: TimeFormatChip) => {
  return (
    <Chip
      variant={selected ? "active" : "inactive"}
      onClick={onClickCallback}
      label={text}
      sx={{
        ...typography.action,
        color: selected ? color.text.action.onFill : color.text.action.default,
        minHeight: "48px",
        "&.MuiChip-root.MuiChip-sizeMedium > .MuiChip-label": { padding: `${SPACING_12_PX} ${SPACING_24_PX}` },
      }}
    />
  );
};
