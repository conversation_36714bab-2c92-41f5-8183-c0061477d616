import { useEffect, useMemo, useRef, useState } from "react";
import { ClickStreamActivityEventType, Severity, Symptom } from "@vivantehealth/vivante-core";
import { Box, Button, CircularProgress, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import dayjs, { Dayjs } from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { SPACING_16_PX } from "@Assets/style_constants";
import { BaseDrawer } from "@Components/BaseDrawer/BaseDrawer";
import { DateTimeField } from "@Components/form/DateTimeField";
import { SwitchSelect } from "@Components/SwitchSelect/SwitchSelect";
import { SymptomLogSaveProps } from "@Features/symptomLogging/store/symptomLoggingStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { SymptomItem } from "./SymptomItem";

const BUTTON_STRINGS = appStrings.buttonText;
const PROGRESS_STRINGS = appStrings.features.progress;

const SYMPTOM_TABS: string[] = [
  PROGRESS_STRINGS.symptomsTabs.withSymptoms,
  PROGRESS_STRINGS.symptomsTabs.withoutSymptoms,
];

type SymptomLoggingDrawerProps = Readonly<{
  isOpen: boolean;
  closeCallback: () => void;
  severityChangeCallback: () => void;
  timeChangeCallback: (time: number) => void;
  symptoms: Symptom[];
  severities: Severity[];
  symptomSeverities: Record<string, string>;
  symptomIdToSeverityList: Record<string, Severity[]>;
  saveCallback: (symptomLog: SymptomLogSaveProps) => void;
  time?: number;
  isLoading?: boolean;
  isDrawerOpen: boolean;
}>;

export const SymptomLoggingDrawer = ({
  isOpen,
  closeCallback,
  severityChangeCallback,
  timeChangeCallback,
  saveCallback,
  symptoms,
  severities,
  symptomSeverities,
  symptomIdToSeverityList,
  time,
  isLoading,
  isDrawerOpen,
}: SymptomLoggingDrawerProps) => {
  const focusRef = useRef<HTMLDivElement | null>(null);
  const [localSeverities, setLocalSeverities] = useState<Record<string, string>>(symptomSeverities);
  const [chosenTime, setChosenTime] = useState<number>();
  const { sendEventAnalytics } = useAnalyticsHook();
  const [selectedTab, setSelectedTab] = useState<(typeof SYMPTOM_TABS)[number]>(SYMPTOM_TABS[0]);
  const [symptomsUpdated, setSymptomsUpdated] = useState(false);

  useEffect(() => {
    if (isOpen && focusRef.current) {
      focusRef.current.focus();
    }
    if (!isOpen) {
      // reset tab and symptomUpdated flag when drawer is closed
      setSelectedTab(SYMPTOM_TABS[0]);
      setSymptomsUpdated(false);
    }
  }, [isOpen]);

  useEffect(() => {
    setChosenTime(time || new Date().getTime());
  }, [time]);

  useEffect(() => {
    setLocalSeverities(symptomSeverities);
  }, [symptomSeverities]);

  const symptomSeveritySelectedCallback = () => {
    sendEventAnalytics(ClickStreamActivityEventType.SYMPTOM_LOGGING_SEVERITY_SELECTED);
  };
  // store the base symptoms with the no severity selected to be used for no symptoms
  const baseSymptoms = useMemo(() => {
    return symptoms.map((symptom) => {
      const severitiesToUse = symptomIdToSeverityList[symptom.value];

      return {
        symptomValue: symptom.value,
        severityValue: severitiesToUse[0].value,
      };
    });
  }, [symptoms, symptomIdToSeverityList]);

  const onSave = () => {
    const symptomsWithSeverities =
      selectedTab === PROGRESS_STRINGS.symptomsTabs.withSymptoms
        ? symptoms.map((symptom) => ({
            symptomValue: symptom.value,
            severityValue: localSeverities[symptom.value],
          }))
        : baseSymptoms;

    if (chosenTime === undefined) {
      return;
    }

    saveCallback({
      date: new Date(chosenTime),
      symptomsWithSeverities,
      isNoSymptoms: selectedTab === PROGRESS_STRINGS.symptomsTabs.withoutSymptoms,
    });
  };

  const setTime = (time: string | Dayjs | null) => {
    setChosenTime(dayjs(time).valueOf());
    if (time) {
      // has severities evaluation is equivalent to state if the symptoms were updated when changing the date, since we shouldn't update the `save` button on date change when there are no severities
      const hasSeverities = Object.values(localSeverities).some((severity) => severity !== "none");

      setSymptomsUpdated(hasSeverities);
      timeChangeCallback(dayjs(time).valueOf());
    }
  };

  return (
    <BaseDrawer
      isDrawerOpen={isDrawerOpen}
      onClose={() => {
        setSelectedTab(SYMPTOM_TABS[0]);
        closeCallback();
      }}
      header={PROGRESS_STRINGS.symptomDrawerHeader}
      actions={
        <Box display="flex" gap={2}>
          <Button onClick={() => closeCallback()} variant="secondary" fullWidth disabled={isLoading}>
            {BUTTON_STRINGS.cancel}
          </Button>
          <Button
            onClick={() => onSave()}
            variant="primary"
            fullWidth
            disabled={
              isLoading ||
              !dayjs(chosenTime).isValid() ||
              (!symptomsUpdated && selectedTab === PROGRESS_STRINGS.symptomsTabs.withSymptoms)
            }
          >
            {isLoading ? <CircularProgress size={24} color="inherit" /> : BUTTON_STRINGS.save}
          </Button>
        </Box>
      }
    >
      <Box display="flex" flexDirection="column" gap={4}>
        <Typography variant="h3" color={color.text.strong}>
          {PROGRESS_STRINGS.symptomDrawerSubHeader}
        </Typography>
        <DateTimeField
          maxDateTime={dayjs().add(1, "day")}
          name="symptomLogTime"
          required
          onAccept={setTime}
          onBlur={setTime}
          value={dayjs(chosenTime)}
        />

        <SwitchSelect
          options={SYMPTOM_TABS}
          value={selectedTab}
          onChange={(option: (typeof SYMPTOM_TABS)[number]) => {
            if (option !== selectedTab) {
              setSelectedTab(option);
            }
          }}
        />
        {selectedTab === PROGRESS_STRINGS.symptomsTabs.withSymptoms ? (
          <>
            <Typography variant="h4">{PROGRESS_STRINGS.symptomDrawerSliderHeader}</Typography>

            <Box display="flex" flexDirection="column" gap={4}>
              {symptoms.map((symptom) => {
                const symptomId = symptom.value;
                const severitiesToUse = symptomIdToSeverityList[symptomId] || severities;
                const value = localSeverities[symptom.value] || severitiesToUse[0].value;

                return (
                  <SymptomItem
                    key={symptom.value}
                    symptom={symptom}
                    severities={severitiesToUse}
                    value={value}
                    setValue={(value) => {
                      const updatedSeverities = {
                        ...localSeverities,
                        [symptom.value]: value,
                      };

                      setSymptomsUpdated(true);

                      setLocalSeverities(updatedSeverities);
                      severityChangeCallback();
                    }}
                    symptomSeveritySelectedCallback={symptomSeveritySelectedCallback}
                  />
                );
              })}
            </Box>
          </>
        ) : (
          <>
            <Typography variant="h4">{PROGRESS_STRINGS.noSympomsTitle}</Typography>
            <Box display="flex" flexDirection="column" gap={SPACING_16_PX}>
              <Typography variant="bodyDense">{PROGRESS_STRINGS.noSymptomsBody1}</Typography>
              <Typography variant="bodyDense">{PROGRESS_STRINGS.noSymptomsBody2}</Typography>
            </Box>
          </>
        )}
        <Typography variant="bodyDense">{PROGRESS_STRINGS.symptomDrawerDisclaimer} </Typography>
      </Box>
    </BaseDrawer>
  );
};
