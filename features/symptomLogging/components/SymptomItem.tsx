import { Severity, Symptom } from "@vivantehealth/vivante-core";
import { Paper, Slider, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

type SymptomItemProps = Readonly<{
  symptom: Symptom;
  value: string;
  severities: ReadonlyArray<Severity>;
  setValue: (value: string) => void;
  symptomSeveritySelectedCallback: () => void;
}>;

const getAriaValueText = (marks: ReadonlyArray<Severity>, value: number) => {
  return marks.find((m) => m.ranking === value)?.title ?? value.toString();
};

export const SymptomItem = ({ symptom, severities, value, setValue }: SymptomItemProps) => {
  const onChange = (updatedValue: number | number[]) => {
    const severity = severities.find((s) => s.ranking === updatedValue);

    if (severity?.value) {
      setValue(severity.value);
    }
  };
  const curVal = severities.find((s) => s.value === value);
  const marks = severities.map(({ ranking, value: severityValue, title }) => ({
    value: ranking,
    label: value === severityValue ? title : "",
  }));

  return curVal ? (
    <Paper
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: 4,
      }}
    >
      <Typography variant="actionDense" mb={2} color={color.text.strong}>
        {symptom.title}
      </Typography>
      <Slider
        value={curVal.ranking}
        title={curVal.title}
        marks={marks}
        onChange={(_, updatedValue) => onChange(updatedValue)}
        min={0}
        max={severities.length - 1}
        step={1}
        aria-label={`slider-label-${curVal.title}`}
        getAriaValueText={(ariaValue) => getAriaValueText(severities, ariaValue)}
      />
    </Paper>
  ) : null;
};
