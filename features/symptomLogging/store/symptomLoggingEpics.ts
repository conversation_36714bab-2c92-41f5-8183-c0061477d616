import { ClickStreamActivityEventType, SymptomGroupHistoryItem } from "@vivantehealth/vivante-core";
import { Action, PayloadAction } from "@reduxjs/toolkit";
import { format } from "date-fns";
import { Epic, StateObservable, ofType } from "redux-observable";
import { Observable, forkJoin, from, of } from "rxjs";
import { switchMap, map, catchError, withLatestFrom, mergeMap } from "rxjs/operators";

import { appStrings } from "@Assets/app_strings";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";
import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { RootState } from "@Store/store";

import { SymptomLogSaveProps, SymptomLoggingStateSlice } from "./symptomLoggingStateSlice";
import { createSimpleAnalyticsEpic } from "../../analytics/store/analyticsEpics";
import { ProgressStateSlice } from "../../progress/store/progressStateSlice";
import { createUpdateSymptomLogProps } from "../utils/symptomLogging.util";

const { toggleSnackbar } = SnackbarStateSlice.actions;
const PROGRESS_STRINGS = appStrings.features.progress;

const {
  loadSymptomLogQuestion,
  loadSymptomLogQuestionSuccess,
  loadSymptomLogQuestionFailure,
  prepareSymptomLogQuestion,
  prepareSymptomLogQuestionSuccess,
  closeSymptomLoggingControl,
  createSymptomLogs,
  createSymptomLogsSuccess,
  updateSymptomLogs,
  updateSymptomLogsSuccess,
  prepareCreateSymptomLogQuestion,
  prepareEditSymptomLogQuestion,
  prepareEditSymptomLogQuestionSuccess,
  symptomSeverityChanged,
  symptomTimeChanged,
} = SymptomLoggingStateSlice.actions;

const loadSymptomLogQuestionEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(loadSymptomLogQuestion.type),

    // fetch the symptom log question
    switchMap(() => {
      return from(
        vivanteCoreContainer.getMonitoringUseCaseFactory().createGetSymptomLogQuestionObservableUseCase().execute(),
      ).pipe(
        map(
          (monitorQuestion) => {
            return loadSymptomLogQuestionSuccess(monitorQuestion);
          },
          catchError((error) => {
            return of(loadSymptomLogQuestionFailure(error));
          }),
        ),
      );
    }),
  );
};

const prepareSymptomLogQuestionEpic: Epic = (actions$: Observable<Action>, state$: StateObservable<RootState>) => {
  return actions$.pipe(
    ofType(prepareSymptomLogQuestion.type),

    withLatestFrom(state$),

    // using the symptom log question in state, prepare the symptom field metadatas
    map((args) => {
      const state = args[1];

      if (state.symptomLoggingState.symptomLogQuestion) {
        return vivanteCoreContainer
          .getMonitoringUseCaseFactory()
          .createGetSymptomLogScreenPropertiesUseCase()
          .execute(state.symptomLoggingState.symptomLogQuestion);
      }
    }),
    withLatestFrom(state$),
    map(([properties, state]) => {
      state.progressState.currentDate?.setMinutes(new Date().getMinutes());
      state.progressState.currentDate?.setHours(new Date().getHours());

      if (properties && state.progressState.currentDate) {
        return prepareSymptomLogQuestionSuccess({
          symptomLogScreenProperties: properties,
          date: state.progressState.currentDate,
        });
      }
    }),
  );
};

const prepareCreateSymptomLogQuestionEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(prepareCreateSymptomLogQuestion.type),
    // dispatch the prep action to invoke the epic which creates field metadata
    // that will be used for creating new symptom questions
    switchMap(() => [prepareSymptomLogQuestion()]),
  );
};

const createSymptomLogsEpic: Epic = (
  actions$: Observable<PayloadAction<SymptomLogSaveProps>>,
  state$: StateObservable<RootState>,
) => {
  return actions$.pipe(
    ofType(createSymptomLogs.type),

    withLatestFrom(state$),

    // write a batch of new symptom log answers
    mergeMap(([action, state]: [PayloadAction<SymptomLogSaveProps>, RootState]) => {
      const isNoSymptoms = action.payload.isNoSymptoms;

      return from(
        state.symptomLoggingState.symptomLogQuestion
          ? vivanteCoreContainer
              .getMonitoringUseCaseFactory()
              .createBatchSaveSymptomLogsUseCase()
              .execute({
                ...action.payload,
                monitorQuestion: state.symptomLoggingState.symptomLogQuestion,
              })
          : Promise.reject("Symptom log question is not available"),
      ).pipe(
        map(() => isNoSymptoms), // Pass the parameter to the next step
      );
    }),

    mergeMap((isNoSymptoms) => [
      closeSymptomLoggingControl(),
      createSymptomLogsSuccess(),
      ProgressStateSlice.actions.reloadProgress(),
      toggleSnackbar({
        message: isNoSymptoms
          ? PROGRESS_STRINGS.trackedNoSymptomsSuccessSnackbar
          : PROGRESS_STRINGS.trackedSymptomsSuccessSnackbar,
        isOpen: true,
      }),
    ]),
  );
};

const prepareEditSymptomLogQuestionEpic: Epic = (actions$: Observable<PayloadAction<SymptomGroupHistoryItem>>) => {
  return actions$.pipe(
    ofType(prepareEditSymptomLogQuestion.type),
    // dispatch the prep action to invoke the epic which creates field metadata
    // that will be used for editing old symptom questions
    switchMap(() => [prepareSymptomLogQuestion()]),
  );
};

const prepareEditSymptomLogQuestionImplEpic: Epic = (
  actions$: Observable<PayloadAction<SymptomGroupHistoryItem>>,
  state$: Observable<RootState>,
) => {
  return actions$.pipe(
    ofType(prepareEditSymptomLogQuestion.type),
    withLatestFrom(state$),

    // hydrate historical symptom questions
    switchMap((args) => {
      const state = args[1];
      const requests = state.symptomLoggingState.currentSymptomGroupHistoryItem?.symptomLogs?.map((item) =>
        from(
          vivanteCoreContainer
            .getMonitoringUseCaseFactory()
            .createGetEditSymptomLogScreenPropertiesUseCase()
            .execute(item.id),
        ),
      );

      return forkJoin(requests);
    }),

    withLatestFrom(state$),

    // dispatch the action which ingresses these historical symptom questions
    // in a ready-to-update form in state
    switchMap(([properties, state]) => {
      if (properties && state.symptomLoggingState.currentSymptomGroupHistoryItem && state.progressState.currentDate) {
        return [
          prepareEditSymptomLogQuestionSuccess({
            symptomGroupHistoryItem: state.symptomLoggingState.currentSymptomGroupHistoryItem,
            editSymptomLogScreenProperties: properties,
            date: state.progressState.currentDate,
          }),
        ];
      }

      return [];
    }),
  );
};

const updateSymptomLogsEpic: Epic = (
  actions$: Observable<PayloadAction<SymptomLogSaveProps>>,
  state$: StateObservable<RootState>,
) => {
  return actions$.pipe(
    ofType(updateSymptomLogs.type),

    withLatestFrom(state$),

    // transform updated historical symptom answers
    // into a form that is readily consumed by the backend, and
    // fire them off in parallel
    mergeMap(([action, state]: [PayloadAction<SymptomLogSaveProps>, RootState]) => {
      const symptomLogSaveProps = action.payload;
      const isNoSymptoms = symptomLogSaveProps.isNoSymptoms;
      const { date, symptomsWithSeverities } = symptomLogSaveProps;
      const { currentSymptomGroupHistoryItem, symptoms } = state.symptomLoggingState;
      const updateSymptomLogProps = currentSymptomGroupHistoryItem
        ? createUpdateSymptomLogProps(date, currentSymptomGroupHistoryItem, symptoms, symptomsWithSeverities)
        : [];

      const requests = updateSymptomLogProps.map((props) =>
        from(vivanteCoreContainer.getMonitoringUseCaseFactory().createUpdateSymptomLogUseCase().execute(props)),
      );

      return forkJoin(requests).pipe(
        map(() => isNoSymptoms),
        catchError((error) => {
          // todo
          throw error;
        }),
      );
    }),

    // close the symptom log control and reload the progress page data
    mergeMap((isNoSymptoms) => [
      closeSymptomLoggingControl(),
      updateSymptomLogsSuccess(),
      ProgressStateSlice.actions.reloadProgress(),
      toggleSnackbar({
        message: isNoSymptoms
          ? PROGRESS_STRINGS.trackedNoSymptomsSuccessSnackbar
          : PROGRESS_STRINGS.trackedSymptomsEditSuccessSnackbar,
        isOpen: true,
      }),
    ]),
  );
};

const analyticsEpics: Epic[] = [
  createSimpleAnalyticsEpic(prepareCreateSymptomLogQuestion.type, ClickStreamActivityEventType.SYMPTOM_LOGGING_OPENED),
  createSimpleAnalyticsEpic(prepareEditSymptomLogQuestion.type, ClickStreamActivityEventType.SYMPTOM_LOGGING_OPENED),
  createSimpleAnalyticsEpic(closeSymptomLoggingControl.type, ClickStreamActivityEventType.SYMPTOM_LOGGING_CLOSED),
  createSimpleAnalyticsEpic(
    symptomSeverityChanged.type,
    ClickStreamActivityEventType.SYMPTOM_LOGGING_SEVERITY_SELECTED,
  ),
  createSimpleAnalyticsEpic(
    createSymptomLogsSuccess.type,
    ClickStreamActivityEventType.SYMPTOM_LOGGING_LOG_SAVE_PERFORMED,
  ),
  createSimpleAnalyticsEpic(
    updateSymptomLogsSuccess.type,
    ClickStreamActivityEventType.SYMPTOM_LOGGING_LOG_EDIT_PERFORMED,
  ),
  createSimpleAnalyticsEpic<number>(symptomTimeChanged.type, (payload) => ({
    eventType: ClickStreamActivityEventType.SYMPTOM_LOGGING_TIME_SELECTED,
    activityContextExtra: {
      time: payload?.valueOf() ? format(payload.valueOf(), "MM/dd/yyyy hh:mm aa") : "",
    },
  })),
];

export const symptomLoggingEpics = [
  createSymptomLogsEpic,
  prepareEditSymptomLogQuestionEpic,
  loadSymptomLogQuestionEpic,
  prepareSymptomLogQuestionEpic,
  updateSymptomLogsEpic,
  prepareCreateSymptomLogQuestionEpic,
  prepareEditSymptomLogQuestionImplEpic,
  ...analyticsEpics,
];
