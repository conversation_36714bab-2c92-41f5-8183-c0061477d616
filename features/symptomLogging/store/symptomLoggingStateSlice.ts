// Expect this error as the action is actually used in epic and not in the reducer
/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  EditSymptomLogScreenProperties,
  MonitorQuestion,
  Severity,
  Symptom,
  SymptomGroupHistoryItem,
  SymptomLogScreenProperties,
  VivanteApiError,
} from "@vivantehealth/vivante-core";
import { PayloadAction, createSlice } from "@reduxjs/toolkit";

import { LoadState } from "@Types";
import { buildSliceStateSelector, processError } from "@Utils/slice.util";

import {
  prepareForCreatingSymptomLog,
  prepareForEditingSymptomLog,
  updateStateFromClose,
} from "../utils/symptomLogging.util";

/// //////////////////////////////////////////////////////
/// state

export interface SymptomLoggingState {
  loadState: LoadState;
  symptomLogQuestion: MonitorQuestion | undefined;
  symptoms: Symptom[];
  severities: Severity[];
  symptomSeverities: Record<string, string>;
  symptomIdToSeverityList: Record<string, Severity[]>;
  symptomToSymptomLogId: Record<string, string>;
  isEditing: boolean;
  showSymptomLoggingControl: boolean;
  initDate: Date | null;
  currentSymptomGroupHistoryItem: SymptomGroupHistoryItem | null;
}

export const initialState: SymptomLoggingState = {
  loadState: "loading",
  symptomLogQuestion: undefined,
  symptoms: [],
  severities: [],
  symptomIdToSeverityList: {},
  symptomSeverities: {},
  symptomToSymptomLogId: {},
  isEditing: false,
  showSymptomLoggingControl: false,
  initDate: null,
  currentSymptomGroupHistoryItem: null,
};

export type SymptomWithSeverity = Readonly<{
  symptomValue: string;
  severityValue: string;
}>;

export type SymptomLogSaveProps = Readonly<{
  date: Date;
  symptomsWithSeverities: SymptomWithSeverity[];
  isNoSymptoms?: boolean;
}>;

export type PrepareSymptomLogQuestionSuccessProps = Readonly<{
  symptomLogScreenProperties: SymptomLogScreenProperties;
  date: Date;
}>;

export type PrepareEditSymptomLogProps = Readonly<{
  symptomGroupHistoryItem: SymptomGroupHistoryItem;
  editSymptomLogScreenProperties: EditSymptomLogScreenProperties[];
  date: Date;
}>;

/// //////////////////////////////////////////////////////
/// slice

export const SymptomLoggingStateSlice = createSlice({
  name: "symptomLoggingState",
  initialState,
  reducers: {
    loadSymptomLogQuestion: (state) => state,
    loadSymptomLogQuestionSuccess: (state, action: PayloadAction<MonitorQuestion | undefined>) => ({
      ...state,
      loadState: "loaded",
      symptomLogQuestion: action.payload,
    }),
    loadSymptomLogQuestionFailure: (state, action: PayloadAction<Error | VivanteApiError>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
    prepareSymptomLogQuestion: (state: SymptomLoggingState) => state,
    prepareSymptomLogQuestionSuccess: (
      state: SymptomLoggingState,
      action: PayloadAction<PrepareSymptomLogQuestionSuccessProps>,
    ) => ({
      ...prepareForCreatingSymptomLog(state, action.payload),
    }),
    prepareCreateSymptomLogQuestion: (state) => ({
      ...state,
      symptomToSymptomLogId: initialState.symptomToSymptomLogId,
      initDate: initialState.initDate,
      symptomLogQuestion: state.symptomLogQuestion,
      currentSymptomGroupHistoryItem: state.currentSymptomGroupHistoryItem,
      isEditing: false,
      showSymptomLoggingControl: true,
      loadState: "loaded",
    }),

    prepareEditSymptomLogQuestion: (state, action: PayloadAction<SymptomGroupHistoryItem>) => ({
      ...state,
      loadState: "loading",
      currentSymptomGroupHistoryItem: action.payload,
    }),
    prepareEditSymptomLogQuestionSuccess: (
      state: SymptomLoggingState,
      action: PayloadAction<PrepareEditSymptomLogProps>,
    ) => prepareForEditingSymptomLog(state, action.payload),

    closeSymptomLoggingControl: (state: SymptomLoggingState) => updateStateFromClose(state),
    createSymptomLogs: (state, _: PayloadAction<SymptomLogSaveProps>) => ({ ...state, loadState: "loading" }),
    createSymptomLogsSuccess: (state) => state,
    updateSymptomLogs: (state, _: PayloadAction<SymptomLogSaveProps>) => ({ ...state, loadState: "loading" }),
    updateSymptomLogsSuccess: (state) => state,
    symptomSeverityChanged: (state) => state,
    symptomTimeChanged: (state, _: PayloadAction<number>) => state,
  },
});

/// //////////////////////////////////////////////////////
/// selectors

export const symptomLoggingStateSelector = buildSliceStateSelector("symptomLoggingState");

export const symptomLoggingStateReducer = SymptomLoggingStateSlice.reducer;
