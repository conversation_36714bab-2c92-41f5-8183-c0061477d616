import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";

import { actionPlansStateSelector } from "@Features/carePlan/store/actionPlansStateSlice";
import { ProgressStateSlice, progressStateSelector } from "@Features/progress/store/progressStateSlice";
import {
  SymptomLogSaveProps,
  symptomLoggingStateSelector,
  SymptomLoggingStateSlice,
} from "@Features/symptomLogging/store/symptomLoggingStateSlice";

import { SymptomLoggingDrawer } from "./components/SymptomLoggingDrawer";

const { closeSymptomLoggingControl, createSymptomLogs, updateSymptomLogs, symptomSeverityChanged, symptomTimeChanged } =
  SymptomLoggingStateSlice.actions;

const { toggleSymptomLoggingDrawer, toggleReturnToCarePlan } = ProgressStateSlice.actions;

export const SymptomLoggingDrawerContainer = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const showSymptomLoggingControl = useSelector(symptomLoggingStateSelector("showSymptomLoggingControl"));
  const isOpen = useSelector(progressStateSelector("showSymptomLoggingDrawer"));
  const originatingCarePlan = useSelector(actionPlansStateSelector("selectedActionPlanModuleId"));
  const shouldReturnToCarePlan = useSelector(progressStateSelector("returnToCarePlan"));

  const onClose = () => {
    dispatch(toggleSymptomLoggingDrawer(false));

    if (shouldReturnToCarePlan && originatingCarePlan) {
      dispatch(toggleReturnToCarePlan(false));
      router.push(`/care-plan/${originatingCarePlan}`);
    }
  };

  const closeCallback = () => {
    dispatch(closeSymptomLoggingControl());
    onClose();
  };

  const severityChangeCallback = () => {
    dispatch(symptomSeverityChanged());
  };

  const timeChangeCallback = (time: number) => {
    dispatch(symptomTimeChanged(time));
  };

  const symptoms = useSelector(symptomLoggingStateSelector("symptoms"));
  const severities = useSelector(symptomLoggingStateSelector("severities"));
  const isLoading = useSelector(symptomLoggingStateSelector("loadState")) === "loading";
  const symptomSeverities = useSelector(symptomLoggingStateSelector("symptomSeverities"));
  const symptomIdToSeverityList = useSelector(symptomLoggingStateSelector("symptomIdToSeverityList"));
  const isEditingSymptoms = useSelector(symptomLoggingStateSelector("isEditing"));
  const initDate = useSelector(symptomLoggingStateSelector("initDate")) as Date | null;
  const time = initDate?.getTime();

  const { updateCurrentDay } = ProgressStateSlice.actions;

  const saveCallback = (response: SymptomLogSaveProps) => {
    if (isEditingSymptoms) {
      dispatch(updateSymptomLogs(response));
    } else {
      dispatch(createSymptomLogs(response));
    }

    dispatch(updateCurrentDay(new Date(response.date)));
    onClose();
  };

  return (
    <SymptomLoggingDrawer
      isOpen={isOpen || showSymptomLoggingControl}
      closeCallback={closeCallback}
      severityChangeCallback={severityChangeCallback}
      timeChangeCallback={timeChangeCallback}
      symptoms={symptoms}
      severities={severities}
      symptomSeverities={symptomSeverities}
      symptomIdToSeverityList={symptomIdToSeverityList}
      saveCallback={saveCallback}
      time={time}
      isLoading={isLoading}
      isDrawerOpen={isOpen || showSymptomLoggingControl}
    />
  );
};
