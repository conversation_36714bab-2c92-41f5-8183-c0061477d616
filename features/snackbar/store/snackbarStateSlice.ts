import { PayloadAction, createSlice } from "@reduxjs/toolkit";

import { OpenSnackbarProperties, SnackbarOptionalProperties } from "@Features/snackbar/components/BaseSnackbar";
import { RootState } from "@Store/store";

export type SnackbarToggleState = { isOpen: false } | (OpenSnackbarProperties & SnackbarOptionalProperties);

export type SnackbarState = Readonly<{
  snackbarState: SnackbarToggleState;
}>;

const initialState: SnackbarState = {
  snackbarState: { isOpen: false },
};

export const SnackbarStateSlice = createSlice({
  name: "snackbarState",
  initialState,
  reducers: {
    toggleSnackbar: (state, action: PayloadAction<SnackbarToggleState>) => {
      return {
        ...state,
        snackbarState: action.payload,
      };
    },
  },
});

export const snackbarSelector = (state: RootState) => state.snackbarState.snackbarState;

export const snackbarStateReducer = SnackbarStateSlice.reducer;
