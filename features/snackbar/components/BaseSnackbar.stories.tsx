import type { Meta, StoryObj } from "@storybook/nextjs";

import { BaseSnackbar } from "./BaseSnackbar";
import { cylinderThemeDecorator } from "../../../components/stories/cylinderThemeDecorator";

const meta: Meta<typeof BaseSnackbar> = {
  title: "@Features/snackbar/BaseSnackbar",
  component: BaseSnackbar,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    ...cylinderThemeDecorator,
    (Story) => (
      <div style={{ marginTop: "40px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof BaseSnackbar>;

export const Primary: Story = {
  args: {
    snackbarProperties: {
      isOpen: true,
      message: "This is a success message",
      duration: 30000,
    },
    variant: "success",
    sx: { width: "600px" },
  },
};
