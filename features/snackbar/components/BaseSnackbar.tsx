import { CSSProperties, useEffect, useRef } from "react";
import { Member } from "@vivantehealth/vivante-core";
import { IconButton, Snackbar } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { Routes } from "@Types";

export type BaseSnackbarVariants = "success" | "info" | "warning" | "error";

export type SnackbarOptionalProperties = {
  /** The variant of the snackbar, which will determine the color scheme, default is success */
  variant?: BaseSnackbarVariants;
  /** Where to position the snackbar on screen, default is top center */
  anchorOrigin?: {
    vertical: "top" | "bottom";
    horizontal: "left" | "center" | "right";
  };
  /**
   * The snackbar by default will center on the screen. If you want to position it differently, you can use the sx prop to override the default styles.
   * Example: sx={{ width: "684px", marginLeft: navDrawerOpen ? "240px" : "80px" }}
   * The above will make the snackbar 684px wide and position it 240px from the left if the navDrawer is open, otherwise it will be 80px from the left.
   */
  sx?: CSSProperties;
};

export type OpenSnackbarProperties = {
  isOpen: true;
  message: string;
  duration?: number;
};

type SnackbarProperties = { isOpen: false } | OpenSnackbarProperties;

type BaseSnackbarProps = Readonly<
  {
    snackbarProperties: SnackbarProperties;
    onClose: () => void;
    navDrawerOpen: boolean;
    member: Member | undefined | null;
  } & SnackbarOptionalProperties
>;

const TRANSITION_DURATION = 300;
const DEFAULT_SNACKBAR_DURATION = 4000;
const SNACKBAR_FOCUS_TIMEOUT = 10;
const SNACKBAR_MARGIN_LEFT_NAV_DRAWER_OPEN = "120px";
const SNACKBAR_MARGIN_LEFT_NAV_DRAWER_CLOSED = "45px";
const SNACKBAR_MARGIN_LEFT_NO_NAV_DRAWER = "auto";

const getMarginLeft = (navDrawerOpen: boolean, currentPageHasNoNavBar: boolean) => {
  // If the current page has no nav bar we should not adjust the margin
  if (currentPageHasNoNavBar) {
    return SNACKBAR_MARGIN_LEFT_NO_NAV_DRAWER;
  }
  // If the member is successfully logged in and not onboarding, we adjust the margin based on the nav drawer state
  if (navDrawerOpen) {
    return SNACKBAR_MARGIN_LEFT_NAV_DRAWER_OPEN;
  }

  return SNACKBAR_MARGIN_LEFT_NAV_DRAWER_CLOSED;
};
/**
 * Usage: Use SnackbarStateSlice.actions.toggleSnackbar to toggle opening and closing of the snackbar component.
 * This will trigger the snackbar within the ScreenShell component which will be displayed on whichever page the user is on.
 */

export const BaseSnackbar = ({
  snackbarProperties,
  onClose,
  anchorOrigin = { vertical: "top", horizontal: "center" },
  variant = "success",
  sx,
  navDrawerOpen,
  member,
}: BaseSnackbarProps) => {
  const router = useRouter();
  const snackbarRef = useRef<HTMLDivElement>(null);
  /** If we are using the default top center anchor point, we need to further push the snackbar down 16px to have a total of 40px from top  */
  const snackbarRootStyleAttributes = {
    ...(anchorOrigin.vertical === "top" && anchorOrigin.horizontal === "center" && { marginTop: 4 }),
    marginLeft: getMarginLeft(
      navDrawerOpen,
      member == null || member.setting?.onboardingPending || router.pathname === Routes.CARE_GUIDE,
    ),
    transition: "margin 0.3s ease-in-out",
  };

  const { iconColor, ...variantAttributes } = snackbarVariants[variant];
  const styleAttributes = { ...variantAttributes, ...sx };

  /**
   * Automatically focus on the snackbar when it shows for a11y purposes
   * We require a timeout because the snackbar is not in the DOM when the snackbar is first opened
   */
  useEffect(() => {
    if (snackbarProperties.isOpen && snackbarRef?.current) {
      setTimeout(() => snackbarRef.current?.focus(), SNACKBAR_FOCUS_TIMEOUT);
      /** Blur the snackbar after the duration has passed to prevent the snackbar from staying open */
      setTimeout(() => snackbarRef.current?.blur(), snackbarProperties?.duration ?? DEFAULT_SNACKBAR_DURATION);
    }
  }, [snackbarProperties]);

  if (!snackbarProperties.isOpen) return null;

  return (
    <Snackbar
      open={snackbarProperties.isOpen}
      onClose={onClose}
      anchorOrigin={anchorOrigin}
      message={snackbarProperties.message}
      autoHideDuration={snackbarProperties?.duration ?? DEFAULT_SNACKBAR_DURATION}
      sx={snackbarRootStyleAttributes}
      ContentProps={{ sx: styleAttributes }}
      action={
        <IconButton
          size="small"
          onClick={onClose}
          aria-label={appStrings.a11y.snackbarClose}
          sx={{ color: iconColor, p: 1 }}
        >
          <AppIcon name="Close" containerStyles={{ width: "20px", height: "20px" }} />
        </IconButton>
      }
      transitionDuration={TRANSITION_DURATION}
      ref={snackbarRef}
      tabIndex={0}
      role="alert"
    />
  );
};

type BaseSnackbarStyleAttributes = Readonly<{
  backgroundColor: string;
  border: string;
  color: string;
  iconColor: string;
}>;

const snackbarVariants: Record<BaseSnackbarVariants, BaseSnackbarStyleAttributes> = {
  success: {
    backgroundColor: color.background.success,
    border: `1px solid ${color.border.success}`,
    color: color.text.success,
    iconColor: color.icon.success,
  },
  info: {
    backgroundColor: color.background.information,
    border: `1px solid ${color.border.information}`,
    color: color.text.information,
    iconColor: color.icon.information,
  },
  warning: {
    backgroundColor: color.background.warning,
    border: `1px solid ${color.border.warning}`,
    color: color.text.warning,
    iconColor: color.icon.warning,
  },
  error: {
    backgroundColor: color.background.error,
    border: `1px solid ${color.border.error}`,
    color: color.text.error,
    iconColor: color.icon.error,
  },
};
