import { ShippingAddress } from "@vivantehealth/vivante-core";
import { Action, PayloadAction } from "@reduxjs/toolkit";
import { Epic, ofType } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { catchError, map, switchMap } from "rxjs/operators";

import { vivanteCoreContainer } from "@Lib/vivanteCore";

import { ShippingFormStateSlice } from "./shippingFormStateSlice";

const {
  submitShippingForm,
  submitShippingFormSuccess,
  submitShippingFormFailure,
  loadLatestShippingFormData,
  loadLatestShippingFormDataSuccess,
  loadLatestShippingFormDataFailure,
} = ShippingFormStateSlice.actions;

const loadLatestShippingFormDataEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(loadLatestShippingFormData.type),
    switchMap(() =>
      from(
        vivanteCoreContainer.getShippingAddressUseCaseFactory().createGetLatestShippingFormDataUseCase().execute(),
      ).pipe(
        map((shippingAddress: ShippingAddress[]) => {
          if (shippingAddress.length > 0) {
            return loadLatestShippingFormDataSuccess(shippingAddress[0]);
          }

          return loadLatestShippingFormDataSuccess(null);
        }),
        catchError((error) => of(loadLatestShippingFormDataFailure(error))),
      ),
    ),
  );
};

const submitShippingFormEpic: Epic = (actions$: Observable<PayloadAction<ShippingAddress>>) => {
  return actions$.pipe(
    ofType(submitShippingForm.type),
    switchMap((action: PayloadAction<ShippingAddress>) =>
      from(
        vivanteCoreContainer
          .getShippingAddressUseCaseFactory()
          .createPostShippingAddressUseCase()
          .execute(action.payload),
      ).pipe(
        map(() => submitShippingFormSuccess()),
        catchError((error) => of(submitShippingFormFailure(error))),
      ),
    ),
  );
};

export const shippingFormEpics = [submitShippingFormEpic, loadLatestShippingFormDataEpic];
