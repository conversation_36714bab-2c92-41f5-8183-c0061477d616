/* eslint-disable @typescript-eslint/no-unused-vars */
import { ShippingAddress, VivanteApiError } from "@vivantehealth/vivante-core";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

import { LoadState } from "@Types";
import { buildSliceStateSelector, processError } from "@Utils/slice.util";

/// //////////////////////////////////////////////////////
/// state
export interface ShippingFormState {
  loadState: LoadState;
  shippingAddressLoadState: LoadState;
  shippingAddress: ShippingAddress | null;
}

export const initialState: ShippingFormState = {
  loadState: null,
  shippingAddressLoadState: null,
  shippingAddress: null,
};

/// //////////////////////////////////////////////////////
/// slice

export const ShippingFormStateSlice = createSlice({
  name: "shippingFormState",
  initialState,
  reducers: {
    submitShippingForm: (state, _: PayloadAction<ShippingAddress>) => ({ ...state, loadState: "loading" }),
    submitShippingFormSuccess: (state) => ({ ...state, loadState: "loaded" }),
    submitShippingFormFailure: (state, action: PayloadAction<VivanteApiError | Error>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
    loadLatestShippingFormData: (state) => ({ ...state, shippingAddressLoadState: "loading" }),
    loadLatestShippingFormDataSuccess: (state, action: PayloadAction<ShippingAddress | null>) => ({
      ...state,
      shippingAddressLoadState: "loaded",
      shippingAddress: action.payload,
    }),
    loadLatestShippingFormDataFailure: (state, action: PayloadAction<VivanteApiError | Error>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
  },
});

/// //////////////////////////////////////////////////////
/// selectors

export const shippingSelector = buildSliceStateSelector("shippingFormState");

export const shippingFormStateReducer = ShippingFormStateSlice.reducer;
