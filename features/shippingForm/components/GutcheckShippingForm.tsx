import { ShippingAddress, JsonSchemaForm } from "@vivantehealth/vivante-core";

import { FormSubmission } from "@Components/form/FormFields";
import { vivanteCoreContainer } from "@Lib/vivanteCore";

import { ShippingForm } from "./ShippingForm";
import { injectStates } from "../utils/shippingForm.util";

type GutcheckShippingFormProps = Readonly<{
  onBackClick: () => void;
  onSubmit: (shippingAddress: FormSubmission) => void;
  shippingAddress: ShippingAddress | null;
}>;

export const GutcheckShippingForm = ({ onBackClick, onSubmit, shippingAddress }: GutcheckShippingFormProps) => {
  const formData: JsonSchemaForm = vivanteCoreContainer
    .getShippingAddressUseCaseFactory()
    .createGetShippingFormUseCase()
    .execute();

  injectStates(formData);

  return (
    <ShippingForm
      onBackClick={onBackClick}
      onSubmit={onSubmit}
      formData={formData}
      prefilledData={{ ...shippingAddress }}
    />
  );
};
