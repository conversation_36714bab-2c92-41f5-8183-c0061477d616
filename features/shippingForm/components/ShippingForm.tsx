import { JsonSchemaForm } from "@vivantehealth/vivante-core";
import { Box, Button, Grid, Paper } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { Form } from "@Components/form/Form";
import { FormFields, FormSubmission } from "@Components/form/FormFields";
import { formatInputData } from "@Components/form/utils/formatInputData";
import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";
import { isOfType } from "@Utils/isOfType";

type ShippingFormProps = Readonly<{
  onBackClick: () => void;
  onSubmit: (data: FormSubmission) => void;
  formData: JsonSchemaForm;
  prefilledData: Record<string, string>;
}>;

export const ShippingForm = ({ onBackClick, onSubmit, formData, prefilledData }: ShippingFormProps) => {
  const { formWidth } = useResponsiveStylesHook();
  const { inputProperties, defaultValues, requiredFields } = formatInputData(formData, prefilledData);

  return (
    <Box display="flex" justifyContent="center">
      <Paper sx={{ width: formWidth, display: "flex", justifyContent: "center" }}>
        <Form
          defaultValues={defaultValues}
          onSubmit={(data) => {
            if (isOfType<FormSubmission>(data, [])) {
              onSubmit({
                ...data,
                ...(data?.phone_mobile ? { phone_mobile: String(data.phone_mobile).replace(/\D/g, "") } : {}),
              });
            }
          }}
          formHeader={formData.title}
          formSubheader={formData.description}
        >
          <Grid item my={5}>
            <FormFields inputProperties={inputProperties} requiredFields={requiredFields} />
          </Grid>
          <Box display="flex">
            <Button variant="secondary" aria-label={appStrings.buttonText.logOut} onClick={onBackClick} fullWidth>
              {appStrings.buttonText.back}
            </Button>
            <Box ml={3} />
            <Button type="submit" variant="primary" aria-label={appStrings.buttonText.next} fullWidth>
              {appStrings.buttonText.continue}
            </Button>
          </Box>
        </Form>
      </Paper>
    </Box>
  );
};
