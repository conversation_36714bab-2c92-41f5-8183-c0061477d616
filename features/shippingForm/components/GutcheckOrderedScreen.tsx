import { appStrings } from "@Assets/app_strings";
import { InformationCard } from "@Components/InformationCard/InformationCard";

type GutcheckOrderedProps = Readonly<{ onContinue: () => void }>;

export const GutcheckOrderedScreen = ({ onContinue }: GutcheckOrderedProps) => {
  const { header, orderPlacedSubheader, keepTrackingSubheader } = appStrings.features.courses.gutCheckOrdered;

  return (
    <InformationCard
      header={header}
      subheaders={[orderPlacedSubheader, keepTrackingSubheader]}
      buttonText={appStrings.buttonText.continue}
      onClick={onContinue}
    />
  );
};
