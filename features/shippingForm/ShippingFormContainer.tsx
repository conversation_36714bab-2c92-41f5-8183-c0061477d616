import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ActionPlanTargetState } from "@vivantehealth/vivante-core";

import { FormSubmission } from "@Components/form/FormFields";
import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { actionPlansStateSelector, setActionPlanTargetState } from "@Features/carePlan/store/actionPlansStateSlice";
import { GUTCHECK_ACTION_TITLE, identifyCarePlanActionByTitle } from "@Features/carePlan/utils/markAsDone.util";
import { ShippingFormStateSlice, shippingSelector } from "@Features/shippingForm/store/shippingFormStateSlice";
import { useCheckAndUpdateParentActionState } from "@Hooks/useCheckAndUpdateParentActionState";

import { GutcheckOrderedScreen } from "./components/GutcheckOrderedScreen";
import { GutcheckShippingForm } from "./components/GutcheckShippingForm";

type ShippingFormContainerProps = Readonly<{
  onBackClick: () => void;
  onSubmit: (shippingAddress: FormSubmission) => void;
  onContinue: () => void;
}>;

export const ShippingFormContainer = ({ onBackClick, onSubmit, onContinue }: ShippingFormContainerProps) => {
  const dispatch = useDispatch();
  const driverEntitiesState = useSelector(actionPlansStateSelector("driverEntities"));

  const checkAndUpdateParentActionState = useCheckAndUpdateParentActionState();

  useEffect(() => {
    dispatch(ShippingFormStateSlice.actions.loadLatestShippingFormData());
  }, [dispatch]);

  const shippingAddress = useSelector(shippingSelector("shippingAddress"));
  const shippingFormLoadState = useSelector(shippingSelector("loadState"));
  const loading =
    useSelector(shippingSelector("shippingAddressLoadState")) === "loading" || shippingFormLoadState === "loading";
  const submissionSucceeded = shippingFormLoadState === "loaded";

  if (submissionSucceeded) {
    // In case of successful gutcheck submission, mark the gutcheck action as completed, and the parent if it qualifies
    const gutCheckAction = driverEntitiesState
      ? identifyCarePlanActionByTitle(GUTCHECK_ACTION_TITLE, driverEntitiesState)
      : null;

    if (gutCheckAction && gutCheckAction.state !== ActionPlanTargetState.COMPLETED) {
      // mark parent/target item as completed if this is the last uncompleted driver
      checkAndUpdateParentActionState(
        gutCheckAction?.targetId ?? "",
        gutCheckAction.id,
        ActionPlanTargetState.COMPLETED,
      );
      // mark this action as completed
      dispatch(
        setActionPlanTargetState({
          targetId: gutCheckAction.id,
          newTargetState: ActionPlanTargetState.COMPLETED,
        }),
      );
    }
  }

  if (loading) {
    return <LoadingSpinner open />;
  }

  return submissionSucceeded ? (
    <GutcheckOrderedScreen onContinue={onContinue} />
  ) : (
    <GutcheckShippingForm shippingAddress={shippingAddress} onBackClick={onBackClick} onSubmit={onSubmit} />
  );
};
