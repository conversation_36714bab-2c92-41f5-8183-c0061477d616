import { JsonSchemaForm, USState } from "@vivantehealth/vivante-core";

export const injectStates = (formData: JsonSchemaForm) => {
  const properties = Object.values(formData.properties);
  const stateFieldIdx = properties.findIndex((p) => p?.meta?.display === "select");
  const stateField = properties[stateFieldIdx];

  const states = Object.entries(USState).map((v) => ({ key: v[0], label: v[1] }));
  const updated = {
    ...stateField,
    enum: states.map((s) => s.key),
    meta: {
      ...stateField.meta,
      "enum-titles": states.reduce(
        (acc, next) => ({
          ...acc,
          [next.key]: next.label,
        }),
        {},
      ),
    },
  };
  const key = Object.keys(formData.properties)[stateFieldIdx];

  formData.properties[key] = updated;
};
