import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { createAction } from "@reduxjs/toolkit";
import { Epic } from "redux-observable";

import { Routes } from "@Types";

import { createSimpleAnalyticsEpic } from "../../analytics/store/analyticsEpics";
import { createNavigateToEpic } from "../../navigation/store/navigationEpics";

export const letsGetStarted = createAction("letsGetStarted");
export const loginNow = createAction("loginNow");

const navigateToAccessCode = createNavigateToEpic(letsGetStarted.type, () => ({
  path: Routes.ACCESS_CODE,
  screenName: "WelcomeRegistrationCodeSubmission",
}));

const navigateToLogin = createNavigateToEpic(loginNow.type, () => ({
  path: Routes.LOGIN,
  screenName: "SignIn",
}));

const analyticsEpics: Epic[] = [
  createSimpleAnalyticsEpic(letsGetStarted.type, ClickStreamActivityEventType.REGISTRATION_START),
  createSimpleAnalyticsEpic(loginNow.type, ClickStreamActivityEventType.LOGIN_START),
];

export const onboardingEpics = [navigateToAccessCode, navigateToLogin, ...analyticsEpics];
