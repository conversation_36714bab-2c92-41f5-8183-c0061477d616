import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

import { MicrobiomeStateSlice, microbiomeSelector } from "@Features/microbiome/store/microbiomeStateSlice";
import { NavOptions, setActiveNavOption } from "@Features/navigation/store/navigationStateSlice";

import { MicrobiomeScreen } from "./components/MicrobiomeScreen";

const { loadMicrobiomeSamples } = MicrobiomeStateSlice.actions;

export const MicrobiomeScreenContainer = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(setActiveNavOption(NavOptions.GUT_CHECK));
    dispatch(loadMicrobiomeSamples());
  }, [dispatch]);

  const samples = useSelector(microbiomeSelector("samples"));
  const loading = useSelector(microbiomeSelector("loadState")) === "loading";

  return <MicrobiomeScreen samples={samples} loading={loading} />;
};
