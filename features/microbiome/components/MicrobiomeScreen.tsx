import { useState } from "react";
import { Sample } from "@vivantehealth/vivante-core";
import { Paper, Tab, Tabs } from "@mui/material";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { BackButton } from "@Components/BackButton/BackButton";
import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { TabsPanel } from "@Components/TabsPanel/TabsPanel";
import { tabsA11yProps } from "@Components/TabsPanel/utils/tabsA11yProps";

import { NoMicrobiomeResults } from "./NoMicrobiomeResults";
import { OverviewTab } from "./OverviewTab";
import { ReportDetailsTab } from "./ReportDetailsTab";
import { SampleSection } from "./SampleSection";

type MicrobiomeScreenProps = Readonly<{
  loading: boolean;
  samples: Sample[] | null;
  selectedSampleIndex?: number | null;
  onSampleSelect?: (sampleIndex: number) => void;
}>;

type TabData = Readonly<{
  tabText: string;
  Content: React.ReactNode;
  name: string;
  onClick?: () => void;
}>;

const MICROBIOME_STRINGS = appStrings.features.microbiome;
const MICROBIOME_TABS_STRINGS = MICROBIOME_STRINGS.tabs;

type MicrobiomeTabs = (typeof MICROBIOME_TABS_STRINGS)[keyof typeof MICROBIOME_TABS_STRINGS];

export const MicrobiomeScreen = ({ samples, loading, selectedSampleIndex, onSampleSelect }: MicrobiomeScreenProps) => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<MicrobiomeTabs>(MICROBIOME_TABS_STRINGS.overview);
  const [activeSampleIndex, setActiveSampleIndex] = useState<number>(selectedSampleIndex ?? 0);

  if (loading || !samples) {
    return <LoadingSpinner open={loading} />;
  }
  if (!loading && !samples?.length) {
    return <NoMicrobiomeResults />;
  }

  const dropdownMenuItems = samples.map((_, i) => ({
    label: `${MICROBIOME_STRINGS.report} ${samples.length - i}`,
    index: i,
  }));

  const activeSample = samples[activeSampleIndex];
  const hasReportDetailsTab = !!activeSample?.symptomAndBacteria?.length;

  const overviewTabData = {
    tabText: MICROBIOME_TABS_STRINGS.overview,
    Content: <OverviewTab activeSample={activeSample} />,
    onClick: () => setActiveTab(MICROBIOME_TABS_STRINGS.overview),
    name: MICROBIOME_TABS_STRINGS.overview,
  };

  const reportDetailsTabData = {
    tabText: MICROBIOME_TABS_STRINGS.reportDetails,
    Content: <ReportDetailsTab activeSample={activeSample} />,
    onClick: () => setActiveTab(MICROBIOME_TABS_STRINGS.reportDetails),
    name: MICROBIOME_TABS_STRINGS.reportDetails,
  };

  const tabData: TabData[] = [overviewTabData];

  if (hasReportDetailsTab) {
    tabData.push(reportDetailsTabData);
  }

  return (
    <>
      <BackButton onClick={() => router.back()}>{appStrings.buttonText.back}</BackButton>
      <Paper sx={{ p: 5, mt: 5 }}>
        <SampleSection
          defaultSelectedIndex={activeSampleIndex}
          sampleId={activeSample.id}
          menuItems={dropdownMenuItems}
          onMenuChange={(index) => {
            setActiveTab(MICROBIOME_TABS_STRINGS.overview);
            setActiveSampleIndex(index);
            onSampleSelect?.(index);
          }}
        />
        <Tabs value={activeTab} onChange={(_, targetTab) => setActiveTab(targetTab)}>
          {tabData.map((tab) => (
            <Tab key={tab.name} label={tab.tabText} value={tab.name} {...tabsA11yProps(tab.name)} />
          ))}
        </Tabs>
        {tabData.map((tab) => (
          <TabsPanel key={tab.name} currentSelectedTabName={activeTab} tabName={tab.name} sx={{ marginTop: 6 }}>
            {tab.Content}
          </TabsPanel>
        ))}
      </Paper>
    </>
  );
};
