import { useState } from "react";
import { Box, MenuItem, Select, SelectChangeEvent, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";

type SampleSectionProps = Readonly<{
  menuItems: { label: string; index: number }[];
  sampleId: string;
  onMenuChange: (index: number) => void;
  defaultSelectedIndex?: number;
}>;

export const SampleSection = ({ menuItems, sampleId, onMenuChange, defaultSelectedIndex }: SampleSectionProps) => {
  const [selectedSample, setSelectedSample] = useState(defaultSelectedIndex ?? menuItems[0].index);

  const handleChange = (event: SelectChangeEvent<number>) => {
    const activeSample = Number(event.target.value);

    setSelectedSample(activeSample);
    onMenuChange(activeSample);
  };

  return (
    <Box display="flex" justifyContent="space-between" alignItems="center" mb={6}>
      <Select
        value={selectedSample}
        onChange={handleChange}
        sx={(theme) => {
          return {
            width: "200px",
            [theme.breakpoints.down("sm")]: {
              width: "120px",
            },
          };
        }}
      >
        {menuItems.map((menuItem) => (
          <MenuItem key={menuItem.index} value={menuItem.index}>
            {menuItem.label}
          </MenuItem>
        ))}
      </Select>
      <Box ml={4} textAlign="right">
        <Typography variant="h3" color={color.text.strong}>
          {appStrings.features.microbiome.sampleId}
        </Typography>
        <Typography variant="body" color={color.text.strong}>
          {sampleId}
        </Typography>
      </Box>
    </Box>
  );
};
