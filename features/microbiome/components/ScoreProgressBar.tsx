import { Box, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { RADIUS_FULL_PX } from "@Assets/style_constants";

type ScoreProgressBarProps = Readonly<{
  value: number;
  referenceRange: { min: number; max: number };
}>;

export const ScoreProgressBar = ({ value, referenceRange }: ScoreProgressBarProps) => {
  const percentage = `${Math.ceil(value * 100)}%`;
  const referenceRangeWidth = `${Math.ceil((referenceRange.max - referenceRange.min) * 100)}%`;
  const referenceRangeLeft = `${Math.ceil(referenceRange.min * 100)}%`;

  return (
    <Paper sx={{ p: 4 }}>
      <Box
        width="100%"
        height="32px"
        borderRadius={RADIUS_FULL_PX}
        bgcolor={color.background.surface.secondary}
        padding="2px"
        mb={2}
      >
        <Box
          display="flex"
          alignItems="center"
          height="28px"
          width={percentage}
          borderRadius={RADIUS_FULL_PX}
          bgcolor={color.background.brand.default}
        >
          <Typography variant="h4" ml={4}>
            {percentage}
          </Typography>
        </Box>
      </Box>
      <Box height="36px">
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          height="14px"
          width={referenceRangeWidth}
          ml={referenceRangeLeft}
          borderBottom={`1px solid ${color.border.default}`}
          borderLeft={`1px solid ${color.border.default}`}
          borderRight={`1px solid ${color.border.default}`}
        >
          <Typography variant="caption" position="relative" top="20px" whiteSpace="nowrap" tabIndex={-1}>
            {appStrings.features.microbiome.referenceRange}
          </Typography>
        </Box>
      </Box>
    </Paper>
  );
};
