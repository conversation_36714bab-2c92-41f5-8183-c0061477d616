import { Box, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";

const MICROBIOME_STRINGS = appStrings.features.microbiome;

export const NoMicrobiomeResults = () => (
  <Paper
    sx={{
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
      px: 5,
    }}
  >
    <Box display="flex" flexDirection="column" gap={5}>
      <AppIcon name="InProgress" size="lg" />
      <Box display="flex" flexDirection="column" gap={3}>
        <Typography variant="h3" color={color.text.strong}>
          {MICROBIOME_STRINGS.noContentHeader}
        </Typography>
        <Typography variant="h4" color={color.text.strong}>
          {MICROBIOME_STRINGS.noContentSubHeader}
        </Typography>
        <Typography variant="body" whiteSpace="pre-wrap">
          {MICROBIOME_STRINGS.noContentBody1}
        </Typography>
        <Typography variant="body" whiteSpace="pre-wrap">
          {MICROBIOME_STRINGS.noContentBody2}
        </Typography>
        <Typography variant="body" whiteSpace="pre-wrap">
          {MICROBIOME_STRINGS.noContentBody3}
        </Typography>
      </Box>
    </Box>
  </Paper>
);
