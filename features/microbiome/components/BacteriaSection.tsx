import { Bacterium } from "@vivantehealth/vivante-core";
import { Box, Button, Paper, Tooltip, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";

const MICROBIOME_STRINGS = appStrings.features.microbiome;

const LEVEL_LABEL = {
  "Not in sample": MICROBIOME_STRINGS.notDetected,
  Low: MICROBIOME_STRINGS.low,
  Normal: MICROBIOME_STRINGS.normal,
  High: MICROBIOME_STRINGS.high,
} as const;

type BacteriaSectionProps = Readonly<{
  bacteria: Bacterium[];
}>;

export const BacteriaSection = ({ bacteria }: BacteriaSectionProps) => (
  <Box display="flex" flexDirection="column" gap={4}>
    {bacteria.map((bacterium) => {
      const toolTipMessage = `${MICROBIOME_STRINGS.abundance}: ${(bacterium.abundance * 100).toFixed(2)}%`;

      return (
        <Paper
          key={bacterium.key}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            py: 4,
          }}
        >
          <Box width="70%">
            <Typography variant="h4" color={color.text.strong}>
              {bacterium.key}
            </Typography>
            <Typography variant="bodyDense">{bacterium?.details ?? ""}</Typography>
          </Box>
          <Tooltip
            title={toolTipMessage}
            placement="top"
            aria-label={`${LEVEL_LABEL[bacterium.levelMap]} - ${toolTipMessage}`}
          >
            <Button
              variant="tertiary"
              endIcon={<AppIcon name={bacterium.levelEffect === "good" ? "Completed" : "Alert"} />}
              sx={{ borderBottom: "unset", color: color.text.default }}
              disableRipple
            >
              {LEVEL_LABEL[bacterium.levelMap]}
            </Button>
          </Tooltip>
        </Paper>
      );
    })}
  </Box>
);
