import { Sample } from "@vivantehealth/vivante-core";
import { Box, Grid, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import Image from "next/image";

import { appStrings } from "@Assets/app_strings";
import GutCheckCard from "@Assets/images/GutCheckCard.jpg";
import { RADIUS_16_PX } from "@Assets/style_constants";

import { ScoreProgressBar } from "./ScoreProgressBar";

type OverviewTabProps = Readonly<{
  activeSample: Sample;
}>;

const MICROBIOME_STRINGS = appStrings.features.microbiome;

const isMicrobiomeScore = (score: string): score is keyof typeof MICROBIOME_STRINGS.scores => {
  return score in MICROBIOME_STRINGS.scores;
};

export const OverviewTab = ({ activeSample }: OverviewTabProps) => (
  <>
    <Typography variant="h3" mb={4}>
      {MICROBIOME_STRINGS.overviewTitle}
    </Typography>
    <Box display="flex" flexDirection="column" gap={6}>
      <Box position="relative" height="296px">
        <Image
          src={GutCheckCard.src}
          fill
          alt={MICROBIOME_STRINGS.microbiomeImg}
          priority
          style={{ borderRadius: RADIUS_16_PX, objectFit: "cover" }}
        />
      </Box>
      <Box>
        <Typography variant="h3" color={color.text.strong} mb={5}>
          {MICROBIOME_STRINGS.howYouCompare}
        </Typography>
        <Grid container spacing={5} alignItems="stretch">
          {activeSample.microbiomeScores?.map((score) => {
            const { title, subtitle } = isMicrobiomeScore(score.title)
              ? MICROBIOME_STRINGS.scores[score.title]
              : { title: "", subtitle: "" };
            const referenceRange = score.definition.find((def) => def.label === MICROBIOME_STRINGS.referenceRange);

            return (
              <Grid item xs={12} sm={6} key={score.key} display="flex" flexDirection="column">
                <Typography variant="h4" color={color.text.strong} mb={1}>
                  {title}
                </Typography>
                <Typography variant="bodyDense" mb={4}>
                  {subtitle}
                </Typography>
                <Box marginTop="auto">
                  {referenceRange && <ScoreProgressBar value={score.value} referenceRange={referenceRange} />}
                </Box>
              </Grid>
            );
          })}
        </Grid>
      </Box>
      <Typography variant="caption" color={color.text.subtle}>
        {MICROBIOME_STRINGS.disclaimer}
      </Typography>
    </Box>
  </>
);
