import { Bacterium, Sample } from "@vivantehealth/vivante-core";
import { Box, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";

import { BacteriaSection } from "./BacteriaSection";

const MICROBIOME_STRINGS = appStrings.features.microbiome;

type ReportDetailsTabProps = Readonly<{
  activeSample: Sample;
}>;

type Bacteriums = Readonly<{
  protectiveBacteria: Bacterium[];
  inflammatoryBacteria: Bacterium[];
}>;

const SECTIONS_TO_REMOVE = ["GI tissue strength", "Mucin regulation", "GI protection", "Histamine production"];

export const ReportDetailsTab = ({ activeSample }: ReportDetailsTabProps) => (
  <Box display="flex" flexDirection="column" gap={6}>
    <Box display="flex" flexDirection="column" gap={2}>
      <Typography variant="h3" color={color.text.strong}>
        {MICROBIOME_STRINGS.reportDetailsTitle}
      </Typography>
      <Typography variant="body">{MICROBIOME_STRINGS.reportDetailsSubtitle}</Typography>
    </Box>
    {activeSample.symptomAndBacteria
      .filter(({ title }) => !SECTIONS_TO_REMOVE.includes(title))
      .map((data) => {
        const { protectiveBacteria, inflammatoryBacteria } = data.bacteria.reduce<Bacteriums>(
          (bacteriums, bacterium) => {
            if (bacterium.effect === "Protective") {
              bacteriums.protectiveBacteria.push(bacterium);
            } else {
              bacteriums.inflammatoryBacteria.push(bacterium);
            }

            return bacteriums;
          },
          {
            protectiveBacteria: [],
            inflammatoryBacteria: [],
          },
        );

        return (
          <Box key={data.title} display="flex" flexDirection="column" gap={2}>
            <Typography variant="h4" color={color.text.strong}>
              {data.title}
            </Typography>
            <Typography variant="body" mb={4}>
              {data.description}
            </Typography>

            {inflammatoryBacteria.length ? <BacteriaSection bacteria={inflammatoryBacteria} /> : null}
            {!!protectiveBacteria.length && data.title !== "Inflammation" ? (
              <BacteriaSection bacteria={protectiveBacteria} />
            ) : null}
          </Box>
        );
      })}
  </Box>
);
