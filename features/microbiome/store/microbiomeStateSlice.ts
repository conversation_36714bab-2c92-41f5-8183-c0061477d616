import { Sample } from "@vivantehealth/vivante-core";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

import { LoadState } from "@Types";
import { buildSliceStateSelector, processError } from "@Utils/slice.util";

/// //////////////////////////////////////////////////////
/// state

export interface MicrobiomeState {
  loadState: LoadState;
  samples: Sample[] | null;
}

export const initialState: MicrobiomeState = {
  loadState: null,
  samples: null,
};

/// //////////////////////////////////////////////////////
/// slice

export const MicrobiomeStateSlice = createSlice({
  name: "microbiomeState",
  initialState,
  reducers: {
    loadMicrobiomeSamples: (state) => ({ ...state, loadState: "loading" }),
    loadMicrobiomeSamplesSuccess: (state, action: PayloadAction<Sample[]>) => ({
      ...state,
      loadState: "loaded",
      samples: action.payload,
    }),
    loadMicrobiomeSamplesFailed: (state, action: PayloadAction<Error>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
  },
});

/// //////////////////////////////////////////////////////
/// selectors

export const microbiomeSelector = buildSliceStateSelector("microbiomeState");

export const microbiomeStateReducer = MicrobiomeStateSlice.reducer;
