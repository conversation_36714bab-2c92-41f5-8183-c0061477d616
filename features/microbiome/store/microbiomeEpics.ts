/* eslint-disable */
import { Epic, ofType } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { MicrobiomeStateSlice } from "./microbiomeStateSlice";
import { catchError, map, mergeMap } from "rxjs/operators";
import { Action } from "@reduxjs/toolkit";
import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { Sample } from "@vivantehealth/vivante-core";

const { loadMicrobiomeSamples, loadMicrobiomeSamplesSuccess, loadMicrobiomeSamplesFailed } =
  MicrobiomeStateSlice.actions;

export const getMicrobiomeSamplesEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(loadMicrobiomeSamples.type),
    mergeMap((_) =>
      from(vivanteCoreContainer.getGutcheckUseCaseFactory().createGetGutCheckSampleUseCase().execute()).pipe(
        map((samples: Sample[]) => {
          return loadMicrobiomeSamplesSuccess(samples);
        }),
        catchError((error) => {
          return of(loadMicrobiomeSamplesFailed(error));
        }),
      ),
    ),
  );
};

export const microbiomeEpics = [getMicrobiomeSamplesEpic];
