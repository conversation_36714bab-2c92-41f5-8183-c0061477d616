import { ReactNode } from "react";
import { Member } from "@vivantehealth/vivante-core";
import { Box, Button, Link, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
// Uncomment the below when enabling the device section
// import { AppIcon, IconVariant } from "@Components/AppIcon/AppIcon";

const CYLINDER_LINKS = appStrings.cylinderLinks;
const SETTINGS_STRINGS = appStrings.features.settings;

type SettingsScreenProps = Readonly<{
  member?: Member;
  onChangePasswordClick: () => void;
}>;

export const SettingsScreen = ({ member, onChangePasswordClick }: SettingsScreenProps) => (
  <Box display="flex" flexDirection="column" gap={5}>
    <Typography variant="h1Serif" mb={2}>
      {SETTINGS_STRINGS.settings}
    </Typography>

    <SettingsCard headerText={SETTINGS_STRINGS.account}>
      <Typography variant="h4" mb={2} color={color.text.subtle}>
        {SETTINGS_STRINGS.email}
      </Typography>

      <Typography variant="body">{member?.email ?? ""}</Typography>
    </SettingsCard>

    <SettingsCard headerText={SETTINGS_STRINGS.password}>
      <Typography variant="body" mb={2} color={color.text.subtle}>
        {SETTINGS_STRINGS.passwordPlaceholder}
      </Typography>

      <Button variant="secondary" size="small" onClick={onChangePasswordClick}>
        {SETTINGS_STRINGS.changePasswordButtonText}
      </Button>
    </SettingsCard>

    {/* Uncomment this section when device management is implemented
    <SettingsCard headerText={SETTINGS_STRINGS.trustedDevices}>
      {MOCKED_DEVICE.map((device, index) => (
        <div style={{ paddingTop: index > 0 ? "16px" : "" }} key={device.deviceName}>
          <DeviceItem
            deviceName={device.deviceName}
            addedDate={device.addedDate}
            deviceVariant={device.deviceVariant}
          />
        </div>
      ))}
    </SettingsCard> */}

    <SettingsCard headerText={SETTINGS_STRINGS.support}>
      <Typography variant="h4" mt={4} mb={1} color={color.text.subtle}>
        {SETTINGS_STRINGS.contactUs}
      </Typography>

      <Link href={`mailto:${CYLINDER_LINKS.contactUsEmail}`} variant="link" color={color.text.strong}>
        {CYLINDER_LINKS.contactUsEmail}
      </Link>
    </SettingsCard>

    <SettingsCard headerText={SETTINGS_STRINGS.about}>
      <Box display="flex" flexDirection="column" gap={1} alignItems="flex-start">
        <SettingsLink href={CYLINDER_LINKS.privacyPolicy} linkText={SETTINGS_STRINGS.privacyPolicy} />

        <SettingsLink href={CYLINDER_LINKS.termsAndConditions} linkText={SETTINGS_STRINGS.termsOfUse} />

        <SettingsLink href={CYLINDER_LINKS.hipaaLink} linkText={SETTINGS_STRINGS.hipaaTerms} />
      </Box>
    </SettingsCard>
  </Box>
);

type SettingsCardProps = Readonly<{
  headerText: string;
  children: ReactNode;
}>;

const SettingsCard = ({ headerText, children }: SettingsCardProps) => (
  <Paper sx={{ p: 4 }}>
    <Typography variant="h3" mb={4} color={color.text.strong}>
      {headerText}
    </Typography>

    {children}
  </Paper>
);

type SettingsLinkProps = Readonly<{
  href: string;
  linkText: string;
}>;

const SettingsLink = ({ href, linkText }: SettingsLinkProps) => {
  return (
    <Link href={href} target="_blank" rel="noreferrer" variant="link" color={color.text.strong}>
      {linkText}
    </Link>
  );
};
// Uncomment this section when device management is implemented
// const MOCKED_DEVICE = [
//   {
//     deviceName: "iPhone 14",
//     addedDate: "June 20, 2023",
//     deviceVariant: "Cellphone",
//   },
//   {
//     deviceName: "Macbook Pro",
//     addedDate: "April 11, 2023",
//     deviceVariant: "Laptop",
//   },
// ] as const;

// type DeviceItemProps = Readonly<{
//   deviceName: string;
//   addedDate: string;
//   deviceVariant: Extract<IconVariant, "Cellphone" | "Laptop">;
// }>;

// const DeviceItem = ({ deviceName, addedDate, deviceVariant }: DeviceItemProps) => {
//   return (
//     <>
//       <Box display="flex" justifyContent="space-between" mb={1}>
//         <Box display="flex" alignItems="center" gap={2}>
//           <AppIcon name={deviceVariant} includeContainer={false} color={color.icon.default} />
//           <Typography variant="h4">{deviceName}</Typography>
//         </Box>

//         <Button variant="tertiary" size="small">
//           {SETTINGS_STRINGS.remove}
//         </Button>
//       </Box>

//       <Typography variant="bodyDense" pl={6}>{`${SETTINGS_STRINGS.deviceAdded} ${addedDate}`}</Typography>
//     </>
//   );
// };
