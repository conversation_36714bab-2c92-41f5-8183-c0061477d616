import { useDispatch, useSelector } from "react-redux";

import { memberStateSelector } from "@Features/member/store/memberStateSlice";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { Routes } from "@Types";

import { SettingsScreen } from "./components/SettingsScreen";

export const SettingsScreenContainer = () => {
  const dispatch = useDispatch();
  const member = useSelector(memberStateSelector("member"));

  const onChangePasswordClick = () => {
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: Routes.CHANGE_PASSWORD,
        screenName: "ChangePassword",
      }),
    );
  };

  return <SettingsScreen member={member} onChangePasswordClick={onChangePasswordClick} />;
};
