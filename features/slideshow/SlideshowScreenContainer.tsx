import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";

import { SlideshowScreen } from "@Features/slideshow/components/SlideshowScreen";
import { loadSlideshow, selectSlideshow, clearSlideshow } from "@Features/slideshow/store/slideshowStateSlice";

type SlideshowScreenContainerProps = Readonly<{
  slideshowId: string;
  actionTargetId?: string;
}>;

export const SlideshowScreenContainer = ({ slideshowId, actionTargetId }: SlideshowScreenContainerProps) => {
  const dispatch = useDispatch();
  const router = useRouter();

  useEffect(() => {
    dispatch(loadSlideshow(slideshowId));
    return () => {
      dispatch(clearSlideshow());
    };
  }, [slideshowId, dispatch]);

  const slideshow = useSelector(selectSlideshow);

  const onDoneClick = () => {
    router.back();
  };

  return <SlideshowScreen slideshow={slideshow} actionTargetId={actionTargetId} onDoneClick={onDoneClick} />;
};
