/* eslint-disable @typescript-eslint/no-unused-vars */
import { Slideshow, VivanteApiError } from "@vivantehealth/vivante-core";
import { createSlice } from "@reduxjs/toolkit";
import { PayloadAction } from "@reduxjs/toolkit";
import Router from "next/router";

import { RootState } from "@Store/store";
import { LoadState, Routes } from "@Types";
import { processError } from "@Utils/slice.util";

/// //////////////////////////////////////////////////////
/// state

export type SlideshowState = Readonly<{
  loadState: LoadState;
  currentSlideshow: Slideshow | null;
}>;

export const initialState: SlideshowState = {
  loadState: null,
  currentSlideshow: null,
};

/// //////////////////////////////////////////////////////
/// slice

export const SlideshowStateSlice = createSlice({
  name: "slideshowState",
  initialState,
  reducers: {
    loadSlideshow: (state, _: PayloadAction<string>) => ({ ...state, loadState: "loading" }),
    loadSlideshowFailure: (state, action: PayloadAction<VivanteApiError>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
    loadSlideshowSuccess: (state, action: PayloadAction<Slideshow>) => ({
      ...state,
      loadState: "loaded",
      currentSlideshow: action.payload,
    }),
    navigateToSlideshow: (_, action: PayloadAction<string>) => {
      Router.push(`${Routes.SLIDESHOW}/${action.payload}`);

      return { ...initialState };
    },
    clearSlideshow: () => ({ ...initialState }),
  },
});

/// //////////////////////////////////////////////////////
/// actions

export const { loadSlideshow, navigateToSlideshow, loadSlideshowSuccess, loadSlideshowFailure, clearSlideshow } =
  SlideshowStateSlice.actions;

/// //////////////////////////////////////////////////////
/// selectors

export const selectSlideshow = (state: RootState) => state.slideshowState?.currentSlideshow;

export const slideshowStateReducer = SlideshowStateSlice.reducer;
