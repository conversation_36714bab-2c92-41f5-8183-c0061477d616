import { PayloadAction } from "@reduxjs/toolkit";
import { Epic, ofType } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { switchMap, map, catchError } from "rxjs/operators";

import { vivanteCoreContainer } from "@Lib/vivanteCore";

import { loadSlideshowFailure, loadSlideshowSuccess, loadSlideshow } from "./slideshowStateSlice";

const loadSlideshowEpic: Epic = (actions$: Observable<PayloadAction<string>>) => {
  return actions$.pipe(
    ofType(loadSlideshow.type),
    switchMap((action: PayloadAction<string>) => {
      return from(
        vivanteCoreContainer.getSlideshowUseCaseFactory().createGetSlideshowUseCase().execute(action.payload),
      ).pipe(
        map((slideshow) => loadSlideshowSuccess(slideshow)),
        catchError((error) => {
          return of(loadSlideshowFailure(error));
        }),
      );
    }),
  );
};

export const slideshowEpics = [loadSlideshowEpic];
