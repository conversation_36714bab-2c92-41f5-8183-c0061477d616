import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ActionPlanTargetState, Slideshow } from "@vivantehealth/vivante-core";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import CardMedia from "@mui/material/CardMedia";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";

import { appStrings } from "@Assets/app_strings";
import { RADIUS_16_PX, SPACING_16_PX } from "@Assets/style_constants";
import { BackButton } from "@Components/BackButton/BackButton";
import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { actionPlansStateSelector, setActionPlanTargetState } from "@Features/carePlan/store/actionPlansStateSlice";
import { useCheckAndUpdateParentActionState } from "@Hooks/useCheckAndUpdateParentActionState";

type SlideshowScreenProps = Readonly<{
  slideshow: Slideshow | null;
  actionTargetId?: string;
  onDoneClick: () => void;
}>;

export const SlideshowScreen = ({ slideshow, actionTargetId, onDoneClick }: SlideshowScreenProps) => {
  const [slidePos, setSlidePos] = useState(0);
  const dispatch = useDispatch();

  const driverEntitiesState = useSelector(actionPlansStateSelector("driverEntities"));

  const checkAndUpdateParentActionState = useCheckAndUpdateParentActionState();

  const markActionAsDone = (actionId: string) => {
    if (driverEntitiesState[actionId]?.targetId) {
      checkAndUpdateParentActionState(
        driverEntitiesState[actionId]?.targetId,
        actionId,
        ActionPlanTargetState.COMPLETED,
      );
    }

    dispatch(
      setActionPlanTargetState({
        targetId: actionId,
        newTargetState: ActionPlanTargetState.COMPLETED,
      }),
    );
  };

  if (!slideshow) {
    return <LoadingSpinner open overlayDrawer={false} overlayHeader={false} />;
  }

  const currentSlide = slideshow.slides[slidePos];
  const onNextClick = () => {
    if (slidePos + 1 < slideshow.slides.length) {
      setSlidePos((pos) => pos + 1);
    } else {
      if (actionTargetId && actionTargetId?.length > 0) {
        markActionAsDone(actionTargetId);
      }

      onDoneClick();
    }
  };

  const onPrevClick = () => {
    setSlidePos((pos) => Math.max(pos - 1, 0));
  };

  const nextLabel = currentSlide?.nextTitle || appStrings.buttonText.next;

  return (
    <>
      <BackButton onClick={onDoneClick}>{appStrings.buttonText.back}</BackButton>

      <Paper sx={{ mt: 5, mb: 5, p: 5 }}>
        {currentSlide.imageUrl && (
          <CardMedia
            image={currentSlide.imageUrl}
            title={appStrings.a11y.slideshowImage}
            tabIndex={0}
            sx={{ height: "312px", borderRadius: RADIUS_16_PX, marginBottom: SPACING_16_PX }}
          />
        )}
        <Typography variant="body">{currentSlide.body}</Typography>
      </Paper>

      <Box display="flex" justifyContent="space-between">
        {slidePos > 0 ? (
          <Button onClick={onPrevClick} variant="secondary" sx={{ width: "212px" }}>
            {appStrings.buttonText.previous}
          </Button>
        ) : null}
        <Button onClick={onNextClick} variant="primary" sx={{ width: "212px", ml: "auto" }}>
          {nextLabel}
        </Button>
      </Box>
    </>
  );
};
