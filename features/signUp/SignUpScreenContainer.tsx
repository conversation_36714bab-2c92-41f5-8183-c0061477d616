import { useEffect, useRef } from "react";
import { useSelector } from "react-redux";

import { selectMfaSupportedMethod } from "@Features/eligibility/store/eligibilityProcessStateSlice";
import { useSignUpUser } from "@Features/signUp/hooks/useSignUpUser";
import { isOfType } from "@Utils/isOfType";

import { SignUpScreen } from "./components/SignUpScreen";
import { UserSignUp } from "./types/signUp.types";

export const SignUpScreenContainer = () => {
  const mfaType = useSelector(selectMfaSupportedMethod);
  const { initiateUserSignUp, isSubmitting, signUpError: errorMessage } = useSignUpUser();

  const errorMessageRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (errorMessage && errorMessageRef?.current) {
      errorMessageRef?.current.focus();
    }
  }, [errorMessage]);

  const handleSubmit = async (data: UserSignUp) => {
    await initiateUserSignUp({
      ...data,
      mfaSupportedMethod: mfaType,
    });
  };

  return (
    <SignUpScreen
      onSubmit={(data) => {
        if (isOfType<UserSignUp>(data, ["email", "password"])) {
          handleSubmit(data);
        }
      }}
      error={errorMessage}
      errorMessageRef={errorMessageRef}
      isSubmitting={isSubmitting}
      mfaType={mfaType}
    />
  );
};
