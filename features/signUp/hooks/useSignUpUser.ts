import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import * as Sentry from "@sentry/nextjs";

import { appStrings } from "@Assets/app_strings";
import { useLazyCreateUserQuery } from "@Features/authentication/api/registrationApi";
import { LOCAL_STORAGE_ACCESS_CODE } from "@Features/authentication/assets/constants";
import { useAuthentication } from "@Features/authentication/hooks/useAuthentication";
import { initializeFirebaseAuth, signInSuccess } from "@Features/authentication/store/authenticationEpics";
import { authenticationStateSlice } from "@Features/authentication/store/authenticationStateSlice";
import { selectAccessCode } from "@Features/eligibility/store/eligibilityProcessStateSlice";
import { useLogin } from "@Features/login/hooks/useLogin";
import { SCREEN_VARIANT } from "@Features/multiFactorAuthentication/assets/constants";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { Routes } from "@Types";
import { isFetchFromBffError } from "@Utils/isFetchFromBffError";

import { UserSignUp } from "../types/signUp.types";

const SIGN_UP_STRINGS = appStrings.features.signUp;

export const useSignUpUser = () => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const { signInWithFirstFactor, storeMFAMobilePhone } = useLogin();
  const { authenticateNewlyCreatedUser } = useAuthentication();
  const [invokeCreateUser] = useLazyCreateUserQuery();
  const registrationCodeFromState = useSelector(selectAccessCode);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [signUpError, setSignUpError] = useState("");
  const registrationCode = registrationCodeFromState || localStorage.getItem(LOCAL_STORAGE_ACCESS_CODE) || "";

  const handleSignUpError = (errorMessage: string) => {
    setIsSubmitting(false);
    setSignUpError(errorMessage);

    sendEventAnalytics(ClickStreamActivityEventType.REGISTRATION_ERROR);
    Sentry.captureMessage(`Auth Error: ${errorMessage}`, "error");
  };

  const firstFactorUserSignIn = async ({ email, password, mobilePhone }: UserSignUp) => {
    dispatch(authenticationStateSlice.actions.storeMFACredentials({ email, password }));

    try {
      const signInToken = await signInWithFirstFactor({
        email,
        password,
        authenticationType: SCREEN_VARIANT.REGISTRATION,
        mobilePhone,
      });

      // MFA not enabled, sign in user using custom token.
      await authenticateNewlyCreatedUser(signInToken);

      dispatch(initializeFirebaseAuth());
      dispatch(signInSuccess());
    } catch (error) {
      if (error instanceof Error && error.message === "MFA_REQUIRED") {
        // If the error is MFA_REQUIRED, we do not need to display anything as the page should navigate to the code verification screen
        return;
      }

      /** Account was created but failed to login. Navigate to login page and display error snackbar with message */
      dispatch(
        SnackbarStateSlice.actions.toggleSnackbar({
          isOpen: true,
          message: SIGN_UP_STRINGS.successfulSignUpFailedLogin,
          variant: "error",
        }),
      );
      dispatch(NavigationStateSlice.actions.navigateTo({ path: Routes.LOGIN, screenName: "SignIn" }));
    }
  };

  const initiateUserSignUp = async ({ email, password, mobilePhone, mfaSupportedMethod }: UserSignUp) => {
    setSignUpError("");
    setIsSubmitting(true);
    sendEventAnalytics(ClickStreamActivityEventType.REGISTRATION_SUBMITTED);

    if (mobilePhone) {
      storeMFAMobilePhone(mobilePhone);
    }

    try {
      await invokeCreateUser({ email, password, mfaPhoneNumber: mobilePhone, registrationCode }).unwrap();
    } catch (error) {
      if (isFetchFromBffError(error)) {
        const shouldDisplaySignUpError = error.data.errors[0]?.title?.includes("email already exists");

        return handleSignUpError(
          shouldDisplaySignUpError ? SIGN_UP_STRINGS.signUpError : SIGN_UP_STRINGS.genericSignUpError,
        );
      }

      return handleSignUpError(SIGN_UP_STRINGS.genericSignUpError);
    }

    await firstFactorUserSignIn({ email, password, mobilePhone, mfaSupportedMethod });

    setIsSubmitting(false);
  };

  return {
    initiateUserSignUp,
    isSubmitting,
    signUpError,
  };
};
