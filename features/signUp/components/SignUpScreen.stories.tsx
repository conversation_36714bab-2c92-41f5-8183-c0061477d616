import type { Meta, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import { SignUpScreen } from "./SignUpScreen";

const meta: Meta<typeof SignUpScreen> = {
  decorators: cylinderThemeDecorator,
  title: "@Features/signUp/SignUpScreen",
  component: SignUpScreen,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof SignUpScreen>;

export const Primary: Story = {
  args: {
    // eslint-disable-next-line no-alert
    onSubmit: (data) => alert(JSON.stringify(data)),
  },
};
