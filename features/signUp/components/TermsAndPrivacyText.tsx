import React from "react";
import { Typo<PERSON>, <PERSON> } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

const SIGN_UP_STRINGS = appStrings.features.signUp;
const CYLINDER_LINKS = appStrings.cylinderLinks;

export const TermsAndPrivacyText = () => {
  return (
    <Typography variant="body">
      {SIGN_UP_STRINGS.termsTextPart1}{" "}
      <Link variant="link" href={CYLINDER_LINKS.termsAndConditions} target="_blank" rel="noopener noreferrer">
        {SIGN_UP_STRINGS.termsAndConditions}
      </Link>{" "}
      {SIGN_UP_STRINGS.termsTextPart2}{" "}
      <Link variant="link" href={CYLINDER_LINKS.privacyPolicy} target="_blank" rel="noopener noreferrer">
        {SIGN_UP_STRINGS.privacyPolicy}
      </Link>
    </Typography>
  );
};
