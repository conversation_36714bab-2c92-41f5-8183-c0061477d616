import { ComponentProps } from "react";
import { useSelector } from "react-redux";
import { SignInUseCase, CreateMemberUseCase } from "@vivantehealth/vivante-core";
import { Box, Button, CircularProgress, FormControlLabel, Paper, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { AuthFormContainer } from "@Components/AuthFormContainer/AuthFormContainer";
import { ErrorText } from "@Components/ErrorText/ErrorText";
import { FormInput, FormCheckbox } from "@Components/form/Fields";
import { Form } from "@Components/form/Form";
import { authenticationStateSelector } from "@Features/authentication/store/authenticationStateSlice";
import { MFA_TYPE } from "@Features/multiFactorAuthentication/assets/constants";
import { MfaType } from "@Features/multiFactorAuthentication/types/mfa.types";
import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";
import { formatPhoneNumber } from "@Utils/formatPhoneNumber";

import { TermsAndPrivacyText } from "./TermsAndPrivacyText";

const SIGN_UP_STRINGS = appStrings.features.signUp;
const SHARED_FORM_STRINGS = appStrings.sharedFormText;
const AUTHENTICATION_STRINGS = appStrings.features.authentication;

type SignUpScreenProps = Readonly<{
  onSubmit: ComponentProps<typeof Form>["onSubmit"];
  error?: string;
  errorMessageRef?: React.RefObject<HTMLDivElement | null>;
  isSubmitting: boolean;
  mfaType?: MfaType;
}>;

const termsAndPrivacyAriaText = `${SIGN_UP_STRINGS.termsTextPart1} ${SIGN_UP_STRINGS.termsAndConditions} ${SIGN_UP_STRINGS.termsTextPart2} ${SIGN_UP_STRINGS.privacyPolicy}`;

export const SignUpScreen = ({ onSubmit, error, errorMessageRef, isSubmitting, mfaType }: SignUpScreenProps) => {
  const { formWidth } = useResponsiveStylesHook();
  const userCredentials = useSelector(authenticationStateSelector("mfaCredentials"));
  const isSmsMfa = mfaType === MFA_TYPE.SMS;

  return (
    <AuthFormContainer>
      <Paper sx={{ width: formWidth }}>
        <Form
          defaultValues={{
            email: userCredentials.email,
            password: userCredentials.password,
            passwordConfirmation: userCredentials.password,
            mobilePhone: isSmsMfa ? "" : undefined,
            terms: false,
          }}
          onSubmit={onSubmit}
          formHeader={SIGN_UP_STRINGS.formHeader}
          formSubheader={SIGN_UP_STRINGS.formPrompt}
        >
          <Box display="flex" flexDirection="column" gap={5}>
            <Box display="flex" flexDirection="column" gap={4} mt={5}>
              <FormInput
                name={SHARED_FORM_STRINGS.email}
                label={SHARED_FORM_STRINGS.email}
                type="text"
                required
                rules={{
                  required: {
                    value: true,
                    message: SHARED_FORM_STRINGS.requiredMessage,
                  },
                  pattern: {
                    value: SignInUseCase.EmailValidationRegex,
                    message: AUTHENTICATION_STRINGS.invalidEmailError,
                  },
                }}
              />

              <FormInput
                name={SHARED_FORM_STRINGS.password}
                label={SHARED_FORM_STRINGS.password}
                type="password"
                required
                rules={{
                  required: {
                    value: true,
                    message: SHARED_FORM_STRINGS.requiredMessage,
                  },
                  pattern: {
                    value: CreateMemberUseCase.PasswordValidationRegex,
                    message: AUTHENTICATION_STRINGS.invalidPasswordError,
                  },
                }}
              />

              <FormInput
                label={SIGN_UP_STRINGS.confirmPasswordLabel}
                name="passwordConfirmation"
                type="password"
                required
                rules={{
                  watchData: {
                    inputName: SHARED_FORM_STRINGS.password,
                    message: AUTHENTICATION_STRINGS.passwordConfirmationError,
                  },
                  required: {
                    value: true,
                    message: SHARED_FORM_STRINGS.requiredMessage,
                  },
                }}
              />

              {isSmsMfa ? (
                <FormInput
                  label={SHARED_FORM_STRINGS.mobilePhone}
                  name="mobilePhone"
                  type="telephone"
                  required
                  rules={{
                    required: {
                      value: true,
                      message: SHARED_FORM_STRINGS.requiredMessage,
                    },
                    pattern: {
                      value: /^\d{3}-\d{3}-\d{4}$/,
                      message: SHARED_FORM_STRINGS.invalidMobilePhone,
                    },
                  }}
                  onChange={formatPhoneNumber}
                />
              ) : null}
            </Box>

            <Typography variant="bodyDense">{SIGN_UP_STRINGS.formDisclaimer}</Typography>

            <FormControlLabel
              control={
                <FormCheckbox
                  name="terms"
                  ariaLabel={termsAndPrivacyAriaText}
                  required
                  rules={{
                    required: {
                      value: true,
                      message: SHARED_FORM_STRINGS.requiredMessage,
                    },
                  }}
                  tabIndex={0}
                />
              }
              label={<TermsAndPrivacyText />}
              sx={{
                ".MuiStack-root": {
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                },
              }}
            />

            {error && <ErrorText errorMessage={error} errorTextRef={errorMessageRef} />}

            <Button
              type="submit"
              variant="primary"
              aria-label={appStrings.buttonText.continue}
              fullWidth
              disabled={isSubmitting}
            >
              {isSubmitting ? <CircularProgress size={24} color="inherit" /> : appStrings.buttonText.continue}
            </Button>
          </Box>
        </Form>
      </Paper>
    </AuthFormContainer>
  );
};
