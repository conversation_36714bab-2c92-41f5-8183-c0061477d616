import { fetchBaseQuery, createApi, retry } from "@reduxjs/toolkit/query/react";

import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { getBaseApiUrl } from "@Utils/getBaseApiUrl";
/**
 * The retry utility will automatically retry 5 times (or the number of times specified in the options) if the request fails
 * with an exponental backoff delay between each retry.
 * https://redux-toolkit.js.org/rtk-query/usage/customizing-queries#automatic-retries
 */
const retryBaseQuery = retry(
  fetchBaseQuery({
    baseUrl: getBaseApiUrl("MAIN_API_URL"),
    prepareHeaders: async (headers) => {
      const firebaseToken = await vivanteCoreContainer.authClient.getValidAccessToken();

      headers.set("Authorization", `Bearer ${firebaseToken}`);
      headers.set("Content-Type", "application/vnd.api+json");

      return headers;
    },
  }),
);

export const signUpApi = createApi({
  reducerPath: "signUpApi",
  baseQuery: retryBaseQuery,
  endpoints: (builder) => ({
    associateTimezone: builder.query<void, string>({
      query: (timezone) => {
        const body = JSON.stringify({
          data: {
            type: "user_setting",
            attributes: {
              timezone,
            },
          },
        });

        return {
          url: `/member/user_setting`,
          method: "POST",
          body,
        };
      },
    }),
    associateRegistrationCode: builder.query<void, string>({
      query: (registrationCode) => {
        const body = JSON.stringify({
          data: {
            type: "registration_code",
            id: registrationCode,
          },
        });

        return {
          url: "/member/account/relationships/registration_code",
          method: "POST",
          body,
        };
      },
    }),
  }),
});

export const { useLazyAssociateTimezoneQuery, useLazyAssociateRegistrationCodeQuery } = signUpApi;
