import { styled } from "@mui/material";
import { color, typography } from "@vivantehealth/design-tokens";

import { SPACING_0_PX, SPACING_8_PX, SPACING_12_PX, SPACING_16_PX, RADIUS_12_PX } from "@Assets/style_constants";

export const StyledChatContainer = styled("div")(() => ({
  position: "relative",
  flexGrow: 1,
  "& .scrollbar-container": {
    padding: `${SPACING_0_PX} ${SPACING_16_PX}`,
  },
  "& .cs-main-container": {
    border: "none",
    borderLeft: `1px solid ${color.border.default}`,
  },
  "& .cs-message-input__content-editor": {
    ...typography.body,
    color: color.text.subtle,
    background: "none !important",
  },
  "& .cs-message-input__content-editor-container, .cs-message-input__content-editor-wrapper": {
    background: "none",
    padding: 0,
  },
  "& .cs-chat-container .cs-message-input": {
    border: `1px solid ${color.border.input.default}`,
    margin: `${SPACING_16_PX} ${SPACING_16_PX} ${SPACING_0_PX} ${SPACING_16_PX}`,
    borderRadius: `${RADIUS_12_PX}`,
    padding: `${SPACING_12_PX}`,
    "& .cs-message-input__content-editor-wrapper:first-of-type": {
      margin: 0,
    },
  },
  "& .cs-button--send": {
    display: "flex",
    padding: 0,
    marginLeft: `${SPACING_8_PX} !important`,
    color: color.icon.subtle,
  },
  "& .svg-inline--fa": {
    height: "20px",
  },
  "& .ps__rail-y:hover>.ps__thumb-y, .ps__rail-y:focus>.ps__thumb-y, .ps__rail-y.ps--clicking .ps__thumb-y, .ps__thumb-y":
    {
      backgroundColor: "#C7C7C7", // color is not part of our DS
    },
  "& .ps .ps__rail-x:hover, .ps .ps__rail-y:hover, .ps .ps__rail-x:focus, .ps .ps__rail-y:focus, .ps .ps__rail-x.ps--clicking, .ps .ps__rail-y.ps--clicking":
    {
      backgroundColor: color.palette.neutral[100],
    },
  ".cs-message-input--disabled .cs-message-input__content-editor-wrapper": {
    background: "none",
  },
  ".cs-loader": {
    "&::before": {
      borderColor: color.background.surface.primary,
    },
    "&::after": {
      borderWidth: "2px",
      borderColor: color.background.brand.default,
      borderLeftColor: "transparent",
    },
  },
}));
