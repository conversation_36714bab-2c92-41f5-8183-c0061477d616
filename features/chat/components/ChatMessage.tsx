import { Message } from "@chatscope/chat-ui-kit-react";
import { Box, Typography, styled } from "@mui/material";
import { visuallyHidden } from "@mui/utils";
import { color } from "@vivantehealth/design-tokens";
import dayjs from "dayjs";
import Image from "next/image";

import { appStrings } from "@Assets/app_strings";
import { SPACING_8_PX, RADIUS_16_PX } from "@Assets/style_constants";
import { ChatEvent } from "@Features/chat/types/chat.types";
import { linkifyText } from "@Utils/linkify";

const DEFAULT_AVATAR = "/images/default_member_avatar.webp";

type ChatMessageProps = Readonly<{
  message: ChatEvent;
  userId: string;
}>;

export const ChatMessage = ({ message, userId }: ChatMessageProps) => {
  const sentAtStr = dayjs(message.createdAt).format("h:mm A");
  const isIncoming = message.sender !== userId;

  return (
    <Box display="flex" alignItems="flex-end" mb={2}>
      {isIncoming && (
        <Image
          src={message?.senderAvatar || DEFAULT_AVATAR}
          height={32}
          width={32}
          alt={appStrings.features.careTeam.profileImageAltText}
          style={{
            marginRight: SPACING_8_PX,
            borderRadius: "50%",
          }}
        />
      )}
      <StyledMessage
        model={{
          sentTime: sentAtStr,
          sender: message.sender,
          direction: isIncoming ? "incoming" : "outgoing",
          position: "single",
        }}
        tabIndex={0}
      >
        <Message.CustomContent>
          <Typography
            color={isIncoming ? color.text.strong : color.text.action.disabledOnFill}
            variant="body"
            tabIndex={-1}
            display="flex"
            flexDirection="column"
            sx={{
              "& > *": { mb: 0 },
              "& > *:first-child": { mt: 0 },
              "& ul, ol": { display: "grid" },
              "& blockquote": { "& p": { m: 0 }, display: "grid" },
            }}
          >
            {linkifyText(message.textHTML)}
          </Typography>
          <Typography
            variant="caption"
            color={isIncoming ? color.text.subtle : color.text.action.disabledOnFill}
            mt={1}
            tabIndex={-1}
            display="inherit"
            textAlign="right"
          >
            {sentAtStr}
          </Typography>
          <Typography variant="caption" sx={visuallyHidden} tabIndex={-1}>
            {appStrings.a11y.chat.messageFrom(
              message.senderType && message.senderName ? message.senderName : appStrings.features.chat.you,
            )}
          </Typography>
        </Message.CustomContent>
      </StyledMessage>
    </Box>
  );
};

const StyledMessage = styled(Message)(() => ({
  marginTop: "0 !important",
  maxWidth: "268px",
  "&.cs-message--incoming .cs-message__content": {
    backgroundColor: color.background.surface.primary,
    color: color.text.strong,
    border: `1px solid ${color.border.default}`,
  },
  "&.cs-message--outgoing .cs-message__content": {
    backgroundColor: color.palette.maroon[200],
    color: color.text.action.disabledOnFill,
  },
  ".cs-message__content": {
    borderRadius: `${RADIUS_16_PX} !important`,
  },
}));
