import { Box, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { getCareTeamMemberType } from "@Features/careTeam//utils/careteam.utils";
import { ChatEvent } from "@Features/chat/types/chat.types";

type RenderMessageNameProps = Readonly<{
  message: ChatEvent;
  userId: string;
}>;

export const RenderMessageName = ({ userId, message }: RenderMessageNameProps) => {
  let memberType = "";

  if (message.senderType !== "MEMBER" && message.senderType !== "UNKNOWN") {
    memberType = getCareTeamMemberType(message.senderType);
  }

  return message.sender === userId ? (
    <Typography variant="caption" tabIndex={-1}>
      {appStrings.features.chat.you}
    </Typography>
  ) : (
    <Box display="flex">
      <Typography variant="caption" tabIndex={-1}>
        {message.senderName}
      </Typography>
      <Typography variant="caption" color={color.text.subtle} ml={1} tabIndex={-1}>
        {memberType}
      </Typography>
    </Box>
  );
};
