import { Box, Typography, CircularProgress } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { LoadState } from "@Types";

const CHAT_STRINGS = appStrings.features.chat;

type NoConversationYetProps = Readonly<{ loadState: LoadState }>;

export const NoConversationYet = ({ loadState }: NoConversationYetProps) => {
  if (loadState === "loading") {
    return (
      <Box display="grid" justifyItems="center">
        <CircularProgress size={24} />
      </Box>
    );
  }

  return (
    <>
      {loadState === "failure" ? (
        <Box display="grid" justifyItems="center" gap={2}>
          <AppIcon name="Warning" />
          <Typography variant="body" maxWidth="300px" align="center">
            {CHAT_STRINGS.errorLoading}
          </Typography>
        </Box>
      ) : (
        <>
          <Typography variant="h4" pb={2} color={color.text.strong} align="center">
            {CHAT_STRINGS.emptyMessagesHeader}
          </Typography>
          <Typography variant="body" maxWidth="300px" align="center">
            {CHAT_STRINGS.emptyMessagesBody}
          </Typography>
        </>
      )}
    </>
  );
};
