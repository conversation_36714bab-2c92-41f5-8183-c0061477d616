import { useEffect } from "react";
import { useSelector } from "react-redux";
import { Box, Button, Paper, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";

import { ChatMemberOverview } from "./ChatMemberOverview";
import { ChatMessages } from "./ChatMessages";
import { chatStateSelector } from "../store/chatStateSlice";

const CHAT_STRINGS = appStrings.features.chat;
const SEND_SVG_PATH =
  "M19.9978 0.932884C19.9822 0.699771 19.8853 0.471076 19.7071 0.292893C19.4207 0.00644564 19.0037 -0.0698941 18.6476 0.063874L0.669655 6.35617C0.282654 6.49162 0.0175501 6.84959 0.000836285 7.25926C-0.0158775 7.66894 0.219186 8.04731 0.593867 8.21384L8.3424 11.6576L11.7862 19.4062C11.9527 19.7808 12.3311 20.0159 12.7408 19.9992C13.1504 19.9825 13.5084 19.7174 13.6439 19.3304L19.9367 1.35081C19.9868 1.2167 20.0072 1.07398 19.9978 0.932884ZM15.1943 3.39151L3.71108 7.41063L8.87852 9.70727L15.1943 3.39151ZM10.2927 11.1215L16.6085 4.80567L12.5894 16.289L10.2927 11.1215Z";

type ChatScreenProps = Readonly<{
  onScheduleClick: () => void;
}>;

export const ChatScreen = ({ onScheduleClick }: ChatScreenProps) => {
  const currentRoomType = useSelector(chatStateSelector("currentRoomType"));

  // chatscope does not allow for customizing the send button icon so we need to do it manually here
  useEffect(() => {
    function updateSvg() {
      const svgElement = document.querySelector(".cs-button--send svg");
      const pathElement = document.querySelector(".cs-button--send svg path");

      if (svgElement && pathElement) {
        svgElement.setAttribute("viewBox", "0 0 24 24");
        pathElement.setAttribute("d", SEND_SVG_PATH);
        pathElement.setAttribute("fill-rule", "evenodd");
        pathElement.setAttribute("clip-rule", "evenodd");
      }
    }

    const observer = new MutationObserver(updateSvg);

    observer.observe(document, { childList: true, subtree: true });
    return () => observer.disconnect();
  }, []);

  return (
    <Box display="flex" flexDirection="column" height="100%">
      <Box display="flex" alignItems="center" mb={6} gap={5}>
        <Typography variant="h1Serif">{CHAT_STRINGS.pageHeader}</Typography>
        <Button
          variant="secondary"
          onClick={() => onScheduleClick()}
          size="small"
          startIcon={<AppIcon name="Calendar" />}
        >
          {appStrings.buttonText.scheduleAnAppointment}
        </Button>
      </Box>
      <Paper
        sx={{
          display: "flex",
          flexGrow: 1,
          p: 5,
          height: "500px",
        }}
      >
        <Box minWidth="284px" pr={4} display="flex" flexDirection="column" gap={5}>
          <ChatMemberOverview currentRoomType={currentRoomType} />
        </Box>
        <ChatMessages key={currentRoomType} currentRoomType={currentRoomType} />
      </Paper>
    </Box>
  );
};
