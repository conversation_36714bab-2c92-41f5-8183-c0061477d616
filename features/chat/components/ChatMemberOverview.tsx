import { useDispatch, useSelector } from "react-redux";
import { Box, Paper, Skeleton, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { CareTeamAvatar } from "@Components/CareTeamAvatar/CareTeamAvatar";
import { careTeamStateSelector } from "@Features/careTeam/store/careTeamStateSlice";
import { getUserType } from "@Features/careTeam/utils/careteam.utils";
import { chatStateSelector } from "@Features/chat/store/chatStateSlice";
import { getLatestConversation } from "@Features/chat/utils/chat.utils";

import { ROOM_TO_MEMBER } from "../assets/constants";
import { setCurrentRoomType } from "../store/chatStateSlice";
import { ChatRoom } from "../types/chat.types";

const A11Y_STRINGS = appStrings.a11y;
const CHAT_STRINGS = appStrings.features.chat;
const CARD_HEIGHT = 72;
const CARD_WIDTH = 268;

type ChatMemberOverviewProps = Readonly<{
  currentRoomType: ChatRoom;
}>;

export const ChatMemberOverview = ({ currentRoomType }: ChatMemberOverviewProps) => {
  const dispatch = useDispatch();
  const careTeamUsers = useSelector(careTeamStateSelector("careTeamUsers"));
  const careTeamIsLoading = useSelector(careTeamStateSelector("loadState")) === "loading";
  const healthCoachChat = useSelector(chatStateSelector("healthCoach"));
  const dietitianChat = useSelector(chatStateSelector("dietitian"));
  const latestConversationCoach = getLatestConversation(healthCoachChat);
  const latestConversationDietitian = getLatestConversation(dietitianChat);

  return (
    <Box>
      <Typography variant="h4" color={color.text.strong}>
        {CHAT_STRINGS.careTeamHeader}
      </Typography>
      <Box display="flex" flexDirection="column" mt={4} gap={2}>
        {careTeamIsLoading ? (
          <>
            <Skeleton variant="rectangular" width={CARD_WIDTH} height={CARD_HEIGHT} sx={{ borderRadius: 4 }} />
            <Skeleton variant="rectangular" width={CARD_WIDTH} height={CARD_HEIGHT} sx={{ borderRadius: 4 }} />
          </>
        ) : (
          careTeamUsers?.map((member) => {
            const isSelected = ROOM_TO_MEMBER[currentRoomType] === member.userType;
            const careTeamMemberType = getUserType(member);
            const latestConversation =
              member.userType === "SHERPA" ? latestConversationCoach : latestConversationDietitian;

            return (
              <Paper
                component="button"
                key={member.id}
                sx={{
                  display: "flex",
                  justifyContent: "left",
                  alignItems: "center",
                  border: "unset",
                  backgroundColor: isSelected ? color.border.surface.subtle : color.background.surface.primary,
                  cursor: "pointer",
                  ":hover": {
                    backgroundColor: color.border.surface.subtle,
                  },
                  p: 3,
                  minHeight: CARD_HEIGHT,
                }}
                onClick={() => {
                  dispatch(setCurrentRoomType(member.userType));
                }}
                aria-label={A11Y_STRINGS.careTeamMember(careTeamMemberType, member.firstName)}
              >
                <Box display="flex" alignItems="center">
                  <CareTeamAvatar
                    avatarLink={member.avatarLink}
                    altText={A11Y_STRINGS.careTeamMemberAvatarText(careTeamMemberType.toLowerCase())}
                    hasUnreadMessages={latestConversation?.memberRead === false}
                  />
                  <Box ml={3} display="flex" flexDirection="column" alignItems="flex-start">
                    <Typography variant="h4" tabIndex={-1}>
                      {member.firstName}
                    </Typography>
                    <Typography variant="bodyDense" tabIndex={-1}>
                      {careTeamMemberType}
                    </Typography>
                  </Box>
                </Box>
              </Paper>
            );
          })
        )}
      </Box>
    </Box>
  );
};
