import "@chatscope/chat-ui-kit-styles/dist/default/styles.min.css";
import { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Main<PERSON>ontainer, Chat<PERSON>ontainer, MessageList, MessageInput } from "@chatscope/chat-ui-kit-react";
import { Box } from "@mui/material";
import * as Sentry from "@sentry/nextjs";

import { appStrings } from "@Assets/app_strings";
import { memberStateSelector } from "@Features/member/store/memberStateSlice";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";

import { MessagesGenerator } from "./MessagesGenerator";
import { NoConversationYet } from "./NoConversationYet";
import { StyledChatContainer } from "./StyledChatContainer";
import { postMessage, fetchMessages } from "../api/chatApi";
import {
  chatStateSelector,
  submitReplyOptimistic,
  submitReplyFailed,
  loadingMoreSuccess,
  loadingMoreFailed,
} from "../store/chatStateSlice";
import { ChatRoom } from "../types/chat.types";
import { getDraftDocument, mergeDocumentsEvents } from "../utils/chat.utils";

const CHAT_STRINGS = appStrings.features.chat;

type ChatMessagesProps = Readonly<{
  currentRoomType: ChatRoom;
}>;

export const ChatMessages = ({ currentRoomType }: ChatMessagesProps) => {
  const dispatch = useDispatch();
  const currentRoom = useSelector(chatStateSelector(currentRoomType));
  const member = useSelector(memberStateSelector("member"));
  const currentConversation = mergeDocumentsEvents(currentRoom);
  const areAllMessagesLoaded = useSelector(chatStateSelector("allMessagesLoaded"));
  const loadState = useSelector(chatStateSelector("loadState"));
  const [isInputDisabled, setIsInputDisabled] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const fetchMoreMessages = useCallback(() => {
    setIsLoadingMore(true);
    fetchMessages(currentRoomType, currentRoom[0]?.id)
      .then((messages) => dispatch(loadingMoreSuccess(messages)))
      .catch((e) => dispatch(loadingMoreFailed(e)))
      .finally(() => setIsLoadingMore(false));
  }, [currentRoomType, currentRoom, dispatch]);

  useEffect(() => {
    // Load more messages if there are not enough to trigger the scroll
    const messageListElement = document.querySelector(".cs-message-list");
    const messagesGeneratorElement = document.querySelector(".messages-generator");
    const shouldFetchMoreMessages =
      loadState !== "failure" &&
      !isLoadingMore &&
      !areAllMessagesLoaded &&
      messagesGeneratorElement &&
      messageListElement &&
      messagesGeneratorElement?.clientHeight <= messageListElement?.clientHeight;

    if (shouldFetchMoreMessages) {
      fetchMoreMessages();
    }

    // Mark only the last message in the document as read if it's not read yet
    if (currentRoom.length > 0) {
      const documentData = structuredClone(currentRoom[currentRoom.length - 1].data);
      const lastEvent = documentData.events[documentData.events.length - 1];

      if (lastEvent.memberRead) return;

      lastEvent.memberRead = true;
      dispatch(submitReplyOptimistic(documentData));
      // don't display any error if update was not successful
      postMessage({ currentRoomType, draftDocument: documentData }).catch(() => {});
    }
  }, [currentRoom, currentRoomType, dispatch, fetchMoreMessages, loadState, isLoadingMore, areAllMessagesLoaded]);

  const onYReachStart = () => {
    const shouldNotLoadMore = areAllMessagesLoaded || isLoadingMore;

    if (shouldNotLoadMore) return;

    fetchMoreMessages();
  };

  const saveMessage = (message: string) => {
    // Get the latest possible state to avoid overwriting with stale state
    fetchMessages(currentRoomType, undefined, 1)
      .then((currentRoomLatest) => {
        if (!member) {
          throw new Error("Member not available");
        }

        const draftDocument = getDraftDocument({
          conversations: currentRoomLatest,
          member: member,
          message: message,
        });

        dispatch(submitReplyOptimistic(draftDocument));
        postMessage({ currentRoomType, draftDocument }).catch((error: Error) => {
          dispatch(submitReplyFailed(error));
        });
      })
      .catch((e) => {
        dispatch(
          SnackbarStateSlice.actions.toggleSnackbar({
            isOpen: true,
            message: CHAT_STRINGS.errorSubmitting,
            variant: "error",
          }),
        );
        Sentry.captureMessage(e, "error");
      });
  };

  return (
    <StyledChatContainer>
      <MainContainer>
        <ChatContainer>
          <MessageList loadingMore={isLoadingMore} onYReachStart={onYReachStart}>
            <MessageList.Content
              style={{
                display: "flex",
                flexDirection: "column",
                justifyContent: currentConversation?.length ? "flex-end" : "center",
                minHeight: "100%",
              }}
            >
              {currentConversation?.length ? (
                <Box mt={3} className="messages-generator">
                  <MessagesGenerator currentConversation={currentConversation} />
                </Box>
              ) : (
                <NoConversationYet loadState={loadState} />
              )}
            </MessageList.Content>
          </MessageList>
          <MessageInput
            attachButton={false}
            placeholder={CHAT_STRINGS.placeHolder}
            aria-label={CHAT_STRINGS.placeHolder}
            onChange={(_, __, value) => {
              setIsInputDisabled(value?.trim() === "");
            }}
            onSend={(_, __, value) => {
              saveMessage(value?.trim());
            }}
            sendDisabled={isInputDisabled}
            sendOnReturnDisabled={isInputDisabled}
          />
        </ChatContainer>
      </MainContainer>
    </StyledChatContainer>
  );
};
