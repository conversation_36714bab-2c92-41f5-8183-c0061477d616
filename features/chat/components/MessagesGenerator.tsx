import { useSelector } from "react-redux";
import { Box, Divider, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import dayjs from "dayjs";

import { ChatEvent } from "@Features/chat/types/chat.types";
import { memberStateSelector } from "@Features/member/store/memberStateSlice";

import { ChatMessage } from "./ChatMessage";
import { RenderMessageName } from "./RenderMessageName";

type MessagesGeneratorProps = Readonly<{
  currentConversation: ChatEvent[];
}>;

export const MessagesGenerator = ({ currentConversation }: MessagesGeneratorProps) => {
  const member = useSelector(memberStateSelector("member"));
  const userId = member?.id ?? "";

  return currentConversation.map((message, messageIndex) => {
    const thisDate = dayjs(message.createdAt).format("MMM D, YYYY");
    const previous = currentConversation[messageIndex - 1];
    const previousDate = previous ? dayjs(previous.createdAt).format("MMM D, YYYY") : undefined;
    const newDate = !previousDate || previousDate !== thisDate;
    const newUserMessage = message.sender !== previous?.sender;
    const lastMessage = messageIndex === currentConversation.length - 1;

    return (
      <Box key={message.id} mb={lastMessage ? 7 : 0}>
        {newDate && (
          <Box display="flex" flexDirection="column" alignItems="center" my={2}>
            <Typography variant="caption" color={color.text.subtle}>
              {thisDate}
            </Typography>
            <Divider sx={{ borderColor: color.border.default, mt: 1 }} flexItem />
          </Box>
        )}
        {(newUserMessage || newDate) && (
          <Box mb={2} textAlign={message.sender === userId ? "right" : "left"} lineHeight={0}>
            <RenderMessageName message={message} userId={userId} />
          </Box>
        )}
        <ChatMessage message={message} userId={userId} />
      </Box>
    );
  });
};
