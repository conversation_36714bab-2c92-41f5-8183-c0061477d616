import { CareTeamUserType } from "@vivantehealth/vivante-core";
import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { FirebaseError } from "firebase/app";

import { LoadState } from "@Types";
import { buildSliceStateSelector, processError } from "@Utils/slice.util";

import { loadChat } from "./chatThunks";
import { MEMBER_TO_ROOM } from "../assets/constants";
import { FirestoreChatDocument, ChatData, ChatRoom } from "../types/chat.types";

/// //////////////////////////////////////////////////////
/// state

export type ChatState = Readonly<{
  loadState: LoadState;
  healthCoach: FirestoreChatDocument[];
  dietitian: FirestoreChatDocument[];
  currentRoomType: ChatRoom;
  allMessagesLoaded: boolean;
}>;

export const initialState: ChatState = {
  loadState: null,
  healthCoach: [],
  dietitian: [],
  currentRoomType: "healthCoach",
  allMessagesLoaded: false,
};

/// //////////////////////////////////////////////////////
/// slice

export const ChatStateSlice = createSlice({
  name: "chatState",
  initialState,
  reducers: {
    loadChatSuccess: (state, action: PayloadAction<{ room: ChatRoom; messages: FirestoreChatDocument[] }>) => {
      return {
        ...state,
        loadState: "loaded",
        [action.payload.room]: action.payload.messages,
      };
    },
    loadChatFailed: (
      state,
      action: PayloadAction<{ room: ChatRoom; error: Error | FirebaseError; displayError: boolean }>,
    ) => {
      processError({
        error: action.payload.error,
        errorDisplayType: action.payload.displayError ? "snackbar" : undefined,
      });
      return {
        ...state,
        loadState: "failure",
        [action.payload.room]: [],
      };
    },
    setCurrentRoomType: (state, action: PayloadAction<CareTeamUserType>) => {
      const currentRoomType =
        action.payload === CareTeamUserType.SHERPA || action.payload === CareTeamUserType.DIETITIAN
          ? MEMBER_TO_ROOM[action.payload]
          : "healthCoach";

      return {
        ...state,
        currentRoomType,
        allMessagesLoaded: false,
      };
    },
    submitReplyOptimistic: (state: ChatState, action: PayloadAction<ChatData>) => {
      // check if any conversation in the state has the same id as the updated conversation month
      // if there is a match, update existing conversation, otherwise add new conversation month
      const currentRoom = state.currentRoomType;
      const fullDocument = { id: action.payload.name, data: action.payload };
      const conversationIndex = state[currentRoom].findIndex((conversation) => conversation.id === fullDocument.id);

      const updatedConversations =
        conversationIndex !== -1
          ? state[currentRoom].map((conversation, currentIndex) =>
              currentIndex === conversationIndex ? { ...conversation, ...fullDocument } : conversation,
            )
          : [...state[currentRoom], fullDocument];

      return {
        ...state,
        [currentRoom]: updatedConversations,
      };
    },
    submitReplyFailed: (state, action: PayloadAction<Error>) => {
      const currentRoom = state.currentRoomType;
      const lastChatMonth = state[currentRoom][state[currentRoom].length - 1];

      // remove the last message that was added optimistically
      lastChatMonth.data.events.pop();
      processError({ error: action.payload, errorDisplayType: "snackbar", snackBarOptions: { variant: "info" } });
    },
    loadingMoreSuccess: (state, action: PayloadAction<FirestoreChatDocument[]>) => {
      const currentRoom = state.currentRoomType;

      if (!action.payload.length) {
        return {
          ...state,
          allMessagesLoaded: true,
        };
      }

      return {
        ...state,
        [currentRoom]: [...action.payload, ...state[currentRoom]],
        // if only one month is returned, it means there are no more messages to load because we ask for two by default
        allMessagesLoaded: action.payload.length === 1,
      };
    },
    loadingMoreFailed: (state, action: PayloadAction<Error>) => {
      processError({ error: action.payload });
      return {
        ...state,
      };
    },
  },
  extraReducers: (builder) => {
    builder.addCase(loadChat.pending, (state) => {
      state.loadState = "loading";
    });
  },
});

/// //////////////////////////////////////////////////////
/// actions

export const {
  setCurrentRoomType,
  loadingMoreSuccess,
  loadingMoreFailed,
  submitReplyOptimistic,
  submitReplyFailed,
  loadChatSuccess,
  loadChatFailed,
} = ChatStateSlice.actions;

/// //////////////////////////////////////////////////////
/// selectors

export const chatStateSelector = buildSliceStateSelector("chatState");

export const chatStateReducer = ChatStateSlice.reducer;
