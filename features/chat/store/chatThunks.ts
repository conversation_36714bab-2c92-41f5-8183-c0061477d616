import { createAsyncThunk } from "@reduxjs/toolkit";
import { FirebaseError } from "firebase/app";

import { loadChatSuccess, loadChatFailed } from "./chatStateSlice";
import { fetchMessages } from "../api/chatApi";
import { ROOMS } from "../assets/constants";

type LoadChatProps = { displayError: boolean; batchSize?: number };

/**
 * Fetches conversations for all rooms to know if
 * we need to display notifications of unread messages
 */
export const loadChat = createAsyncThunk(
  "chatState/loadChat",
  async ({ displayError, batchSize = 2 }: LoadChatProps, { dispatch }) => {
    for (const room of ROOMS) {
      try {
        const messages = await fetchMessages(room, undefined, batchSize);

        dispatch(loadChatSuccess({ room, messages }));
      } catch (error) {
        dispatch(
          loadChatFailed({
            room,
            error: error instanceof Error || error instanceof FirebaseError ? error : new Error("Unknown error"),
            displayError,
          }),
        );
      }
    }
  },
);
