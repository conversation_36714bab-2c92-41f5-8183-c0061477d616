import { vivanteCoreContainer } from "@Lib/vivanteCore";

import { CHAT_COLLECTION } from "../assets/constants";
import { FirestoreChatDocument, ChatData, ChatRoom } from "../types/chat.types";
import { getChatDocuments } from "../utils/chat.utils";

export const fetchMessages = async (
  currentRoomType: ChatRoom | undefined,
  offsetMonth?: string,
  batchSize = 2,
): Promise<FirestoreChatDocument[]> => {
  if (!currentRoomType) return [];

  const firebaseClient = vivanteCoreContainer.firebaseClient;
  const [firestore, memberId] = await Promise.all([firebaseClient.getFirestore(), firebaseClient.getMemberId()]);

  const documents = await firestore
    .collection(CHAT_COLLECTION)
    .document(memberId)
    .collection("roomTypes")
    .document(currentRoomType)
    .collection("dates")
    .get({
      orderBy: ["name", "desc"],
      limit: batchSize,
      startAfter: offsetMonth,
    });

  return getChatDocuments(documents).sort((a, b) => a.id.localeCompare(b.id));
};

type PostMessageParams = {
  currentRoomType: ChatRoom;
  draftDocument: ChatData;
};

export const postMessage = async ({ currentRoomType, draftDocument }: PostMessageParams) => {
  const firebaseClient = vivanteCoreContainer.firebaseClient;
  const [firestore, memberId] = await Promise.all([firebaseClient.getFirestore(), firebaseClient.getMemberId()]);

  return await firestore
    .collection(CHAT_COLLECTION)
    .document(memberId)
    .collection("roomTypes")
    .document(currentRoomType)
    .collection("dates")
    .document(draftDocument.name)
    .set(draftDocument);
};
