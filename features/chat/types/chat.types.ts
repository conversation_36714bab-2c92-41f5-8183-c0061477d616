import { CareTeamUserType } from "@vivantehealth/vivante-core";

export const CHAT_SENDER_TYPE_MEMBER = "MEMBER" as const;

type ExtendedCareTeamUserType = CareTeamUserType | typeof CHAT_SENDER_TYPE_MEMBER | "UNKNOWN";

export type ChatEvent = {
  text: string;
  delivered: boolean;
  senderAvatar?: string;
  sender: string;
  createdAt: string;
  memberRead: boolean;
  senderName?: string;
  senderType?: ExtendedCareTeamUserType;
  id: string;
  textHTML: string;
};

export type ChatData = {
  schemaVersion: string;
  name: string;
  events: ChatEvent[];
};

export type FirestoreChatDocument = {
  id: string;
  data: ChatData;
};

export type ChatRoom = "healthCoach" | "dietitian";
export type SherpaOrDietitian = Extract<keyof typeof CareTeamUserType, "SHERPA" | "DIETITIAN">;
