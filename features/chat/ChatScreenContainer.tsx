import { useEffect } from "react";
import { useSelector } from "react-redux";

import { APPOINTMENT_API_FEATURE_FLAG } from "@Features/appointmentsNew/assets/constants";
import { CareTeamSchedulingModalContainer } from "@Features/careTeam/components/CareTeamSchedulingModalContainer";
import { CareTeamSchedulingModalContainerNew } from "@Features/careTeam/components/CareTeamSchedulingModalContainerNew";
import { useCareTeamHook } from "@Features/careTeam/hooks/careTeamHook";
import { useCareTeam } from "@Features/careTeam/hooks/useCareTeam";
import { CareTeamStateSlice } from "@Features/careTeam/store/careTeamStateSlice";
import { careTeamStateSelector } from "@Features/careTeam/store/careTeamStateSlice";
import { chatStateSelector } from "@Features/chat/store/chatStateSlice";
import { NavOptions, NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { useFeatureFlag } from "@Hooks/useFeatureFlag";
import { useAppDispatch } from "@Store/hooks";

import { ROOM_TO_MEMBER } from "./assets/constants";
import { ChatScreen } from "./components/ChatScreen";
import { loadChat } from "./store/chatThunks";

export const ChatScreenContainer = () => {
  const dispatch = useAppDispatch();
  const { careTeamUsers, showSchedulingModal, setShowSchedulingModal, selectedMemberId, setSelectedMemberId } =
    useCareTeamHook();
  const currentRoomType = useSelector(chatStateSelector("currentRoomType"));
  const isCTNotLoaded = useSelector(careTeamStateSelector("loadState")) !== "loaded";
  const { isReady, treatment } = useFeatureFlag(APPOINTMENT_API_FEATURE_FLAG);
  const isUsingNewAppointmentAPI = isReady && treatment === "on";
  const careTeamProps = useCareTeam();

  useEffect(() => {
    if (isCTNotLoaded) {
      dispatch(CareTeamStateSlice.actions.loadCareTeam());
    }

    dispatch(loadChat({ displayError: true }));

    dispatch(NavigationStateSlice.actions.setActiveNavOption(NavOptions.CARE_TEAM));
  }, [dispatch, isCTNotLoaded]);

  return (
    <>
      {isUsingNewAppointmentAPI ? (
        <CareTeamSchedulingModalContainerNew
          isModalOpen={careTeamProps.showSchedulingModal}
          onToggleSchedulingModal={careTeamProps.setShowSchedulingModal}
          {...careTeamProps}
        />
      ) : (
        <CareTeamSchedulingModalContainer
          isModalOpen={showSchedulingModal}
          onToggleSchedulingModal={setShowSchedulingModal}
          selectedMemberId={selectedMemberId}
          setSelectedMemberId={setSelectedMemberId}
        />
      )}
      <ChatScreen
        onScheduleClick={() => {
          const currentUserType = ROOM_TO_MEMBER[currentRoomType];
          const currentConversationUserId = careTeamUsers?.find((user) => user.userType === currentUserType)?.id;

          if (isUsingNewAppointmentAPI) {
            careTeamProps.setSelectedCareTeamMemberId(currentConversationUserId);
            careTeamProps.setShowSchedulingModal(true);
          } else {
            setSelectedMemberId(currentConversationUserId);
            setShowSchedulingModal(true);
          }
        }}
      />
    </>
  );
};
