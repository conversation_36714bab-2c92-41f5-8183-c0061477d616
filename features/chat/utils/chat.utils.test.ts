import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";

import {
  hasChatEvents,
  getChatDocuments,
  getCurrentMonth,
  mergeDocumentsEvents,
  getDraftDocument,
  getLatestConversation,
  hasUnreadMessages,
} from "./chat.utils";
import { ALL_EVENTS, MISSING_SOME_EVENTS, MISSING_ALL_EVENTS, EMPTY, MEMBER, EXAMPLE_MESSAGE } from "./chat.utils.mock";
import { FirestoreChatDocument } from "../types/chat.types";

const mockUuid = "mock-uuid";

vi.mock("uuid", () => {
  return {
    v4: () => mockUuid,
  };
});

describe("hasChatEvents", () => {
  it("should return true if the document has chat events", () => {
    const result = hasChatEvents(ALL_EVENTS[0]);

    expect(result).toBeTruthy();
  });

  it("should return false if the document does not have chat events", () => {
    const result = hasChatEvents(MISSING_SOME_EVENTS[1]);

    expect(result).toBeFalsy();
  });
});

describe("getChatDocuments", () => {
  it("should return only documents with chat events", () => {
    const result = getChatDocuments(MISSING_SOME_EVENTS);

    expect(result).toHaveLength(1);
    expect(result[0].id).toBe("202409");
  });

  it("should return an empty array if no documents have chat events", () => {
    const result = getChatDocuments(MISSING_ALL_EVENTS);

    expect(result).toHaveLength(0);
  });
});
describe("mergeDocumentsEvents", () => {
  it("should merge events from all data items", () => {
    const result = mergeDocumentsEvents(ALL_EVENTS);

    expect(result).toHaveLength(2);
    expect(result).not.toContain(null);
  });

  it("should handle missing events in one data item", () => {
    const result = mergeDocumentsEvents(MISSING_SOME_EVENTS);

    expect(result).toHaveLength(1);
    expect(result).not.toContain(null);
  });

  it("should handle missing events in all data items", () => {
    const result = mergeDocumentsEvents(EMPTY);

    expect(result).toHaveLength(0);
    expect(result).not.toContain(null);
  });
});

describe("getCurrentMonth", () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it("should return the current month in YYYYMM format", () => {
    vi.setSystemTime(new Date(2023, 9, 30)); // October 30, 2023

    const result = getCurrentMonth();

    expect(result).toBe("202310");
  });

  it("should pad single-digit months with a leading zero", () => {
    vi.setSystemTime(new Date(2024, 0, 5)); // January 5, 2024

    const result = getCurrentMonth();

    expect(result).toBe("202401");
  });
});

describe("getDraftDocument", () => {
  const mockCurrentMonth = "202411";
  const mockDateISOString = "2024-11-04T00:00:00.000Z";

  beforeEach(() => {
    vi.useFakeTimers();
    vi.setSystemTime(new Date(2024, 10, 4)); // November 4, 2024
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it("should update the document if the last conversation record is from the current month", () => {
    const message = "Hello, world!";

    const result = getDraftDocument({ conversations: ALL_EVENTS as FirestoreChatDocument[], member: MEMBER, message });

    expect(result).toEqual({
      schemaVersion: "1.0.0",
      name: mockCurrentMonth,
      events: [
        EXAMPLE_MESSAGE,
        {
          text: message,
          textHTML: message,
          createdAt: mockDateISOString,
          delivered: false,
          sender: MEMBER.id,
          senderType: "MEMBER",
          memberRead: true,
          senderName: MEMBER.firstName,
          id: mockUuid,
        },
      ],
    });
  });

  it("should create a new document if the last conversation record is not from the current month", () => {
    const message = "Hello, world!";

    const result = getDraftDocument({
      conversations: MISSING_ALL_EVENTS as FirestoreChatDocument[],
      member: MEMBER,
      message,
    });

    expect(result).toEqual({
      schemaVersion: "1.0.0",
      name: mockCurrentMonth,
      events: [
        {
          text: message,
          textHTML: message,
          createdAt: expect.any(String),
          delivered: false,
          sender: MEMBER.id,
          senderType: "MEMBER",
          memberRead: true,
          senderName: MEMBER.firstName,
          id: mockUuid,
        },
      ],
    });
  });
});

describe("getLatestConversation", () => {
  it("should return null if chat is empty", () => {
    const result = getLatestConversation(EMPTY);

    expect(result).toBeNull();
  });

  it("should return the last event of the latest conversation", () => {
    const result = getLatestConversation(ALL_EVENTS);

    expect(result).toEqual(EXAMPLE_MESSAGE);
  });
});

describe("hasUnreadMessages", () => {
  it("should return true if there are some unread messages", () => {
    const result = hasUnreadMessages(ALL_EVENTS, ALL_EVENTS);

    expect(result).toBeTruthy();
  });

  it("should return false if there are no unread messages", () => {
    const result = hasUnreadMessages([ALL_EVENTS[0]], [ALL_EVENTS[0]]);

    expect(result).toBeFalsy();
  });

  it("should return false if there are no messages at all", () => {
    const result = hasUnreadMessages(EMPTY, EMPTY);

    expect(result).toBeFalsy();
  });
});
