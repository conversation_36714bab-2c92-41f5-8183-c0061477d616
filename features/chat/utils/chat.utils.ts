import { FirestoreDocument } from "@vivantehealth/vivante-core";
import { Member } from "@vivantehealth/vivante-core";
import { v4 as uuidV4 } from "uuid";

import { FirestoreChatDocument, ChatEvent, ChatData, CHAT_SENDER_TYPE_MEMBER } from "../types/chat.types";

export const hasChatEvents = (document: FirestoreDocument): document is FirestoreChatDocument => {
  return document.data?.events !== undefined;
};

export const getChatDocuments = (documents: FirestoreDocument[]): FirestoreChatDocument[] => {
  return documents.filter((item) => hasChatEvents(item));
};

export const mergeDocumentsEvents = (data: FirestoreDocument[]): ChatEvent[] => {
  return data.reduce<ChatEvent[]>((acc, item) => (hasChatEvents(item) ? acc.concat(item.data.events) : acc), []);
};

export const getCurrentMonth = (): string => {
  const date = new Date();
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0"); // getMonth() returns 0-11, so add 1 and pad with leading zero

  return `${year}${month}`;
};

type GetDraftDocumentProps = {
  conversations: FirestoreChatDocument[];
  member: Member;
  message: string;
};

export const getDraftDocument = ({ conversations, member, message }: GetDraftDocumentProps): ChatData => {
  // Update document if last conversation record is from the current month; otherwise, create a new one
  const latestMonthDoc = structuredClone(conversations[conversations.length - 1]);
  const currentMonth = getCurrentMonth();
  const isLatestMonth = latestMonthDoc?.id === currentMonth;
  const newMessage: ChatEvent = {
    text: message,
    textHTML: message,
    createdAt: new Date().toISOString(),
    delivered: false,
    sender: member.id,
    senderType: CHAT_SENDER_TYPE_MEMBER,
    memberRead: true,
    senderName: member.firstName,
    id: uuidV4(),
  };
  let updatedDoc: ChatData;

  if (isLatestMonth) {
    latestMonthDoc.data.events.push(newMessage);
    updatedDoc = latestMonthDoc.data;
  } else {
    updatedDoc = {
      schemaVersion: "1.0.0",
      name: currentMonth,
      events: [newMessage],
    };
  }

  return updatedDoc;
};

export const getLatestConversation = (chat: FirestoreDocument[]): ChatEvent | null => {
  if (!chat.length) return null;

  const latestConversation = chat[chat.length - 1];
  const events = latestConversation.data.events;

  return events.length ? events[events.length - 1] : null;
};

export const hasUnreadMessages = (
  healthCoachChat: FirestoreDocument[],
  dietitianChat: FirestoreDocument[],
): boolean => {
  const latestMessages = [getLatestConversation(healthCoachChat), getLatestConversation(dietitianChat)];

  return latestMessages.some((message) => message && !message.memberRead);
};
