import { CareTeamUserType } from "@vivantehealth/vivante-core";
import { FirestoreDocument } from "@vivantehealth/vivante-core";
import { Member } from "@vivantehealth/vivante-core";

export const EXAMPLE_MESSAGE = {
  text: "Hello, sending second message",
  delivered: true,
  senderAvatar: "https://picture.com/avatar/",
  sender: "68910f17-9c1d-4b6c-ae5d-753e201a8d92",
  createdAt: "2024-10-25T12:10:53",
  memberRead: false,
  senderName: "John",
  id: "1c503152-873d-11ef-ba8d-c302fb10edee",
  textHTML: "<b>Hello</b>, sending second message",
  senderType: CareTeamUserType.DIETITIAN,
};

export const ALL_EVENTS: FirestoreDocument[] = [
  {
    id: "202409",
    data: {
      schemaVersion: "1.0.0",
      name: "202409",
      events: [
        {
          text: "Hello, sending first message",
          delivered: true,
          senderAvatar: "https://picture.com/avatar/",
          sender: "68910f17-9c1d-4b6c-ae5d-753e201a8d92",
          createdAt: "2024-10-24T12:09:53",
          memberRead: true,
          senderName: "John",
          id: "1c503152-873d-11ef-ba8d-c302fb10eded",
          textHTML: "<b>Hello</b>, sending first message",
          senderType: CareTeamUserType.SHERPA,
        },
      ],
    },
  },
  {
    id: "202411",
    data: {
      schemaVersion: "1.0.0",
      name: "202411",
      events: [EXAMPLE_MESSAGE],
    },
  },
];

export const MISSING_SOME_EVENTS: FirestoreDocument[] = [
  {
    id: "202409",
    data: {
      schemaVersion: "1.0.0",
      name: "202409",
      events: [
        {
          text: "Hello, sending first message",
          delivered: true,
          sender: "68910f17-9c1d-4b6c-ae5d-753e201a8d92",
          createdAt: "2024-10-24T12:09:53",
          memberRead: true,
          senderName: "John",
          id: "1c503152-873d-11ef-ba8d-c302fb10edef",
          textHTML: "<b>Hello</b>, sending first message",
          senderType: "MEMBER",
        },
      ],
    },
  },
  {
    id: "202410",
    data: {
      schemaVersion: "1.0.0",
      name: "202410",
    },
  },
];

export const MISSING_ALL_EVENTS: FirestoreDocument[] = [
  {
    id: "202305",
    data: {
      schemaVersion: "1.0.0",
      name: "202305",
    },
  },
  {
    id: "202401",
    data: {
      schemaVersion: "1.0.0",
      name: "202401",
    },
  },
];

export const EMPTY: FirestoreDocument[] = [];

export const MEMBER: Member = {
  id: "member-id",
  firstName: "John",
  lastName: "Doe",
  avatarLink: "",
  email: "",
  birthDate: "",
  phoneMobile: "",
  setting: {
    email: false,
    timezone: "",
    memberNutritionReady: false,
    memberShowIntroductionScreen: false,
    sessionNotificationOffsetBeforeStart: "",
    featureNutritionEnabled: false,
    featureMicrobiomeEnabled: false,
    featureFoodLogEnabled: false,
    featureSymptomLogEnabled: false,
    onboardingPending: false,
  },
  hasGroup: false,
  condition: [],
};
