import * as Sentry from "@sentry/nextjs";

import { vivanteCoreContainer } from "@Lib/vivanteCore";

import { CONVERTED_TO_VIRTUAL_CLINIC_FIRESTORE_COLLECTION } from "../assets/constants";

export const completeConvertedToVirtualClinicStatus = async () => {
  const firebaseClient = vivanteCoreContainer.firebaseClient;
  const [firestore, memberId] = await Promise.all([firebaseClient.getFirestore(), firebaseClient.getMemberId()]);

  // First check if the document exists before trying to update it
  const document = await firestore
    .collection(CONVERTED_TO_VIRTUAL_CLINIC_FIRESTORE_COLLECTION)
    .document(memberId)
    .get();

  if (document) {
    await firestore
      .collection(CONVERTED_TO_VIRTUAL_CLINIC_FIRESTORE_COLLECTION)
      .document(memberId)
      .update({ workflow_completed: true, workflow_completed_on_date: new Date() });
  } else {
    /**
     * The document does not exist. This should not occur as only users who have converted to virtual clinic should see this modal
     * However, if it does, we should log it so we can investigate why
     */
    Sentry.withScope((scope) => {
      scope.setLevel("warning");
      Sentry.captureException(
        new Error(`MID: ${memberId} does not have a document in ${CONVERTED_TO_VIRTUAL_CLINIC_FIRESTORE_COLLECTION}`),
      );
    });
  }
};
