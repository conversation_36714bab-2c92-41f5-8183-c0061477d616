import { vivanteCoreContainer } from "@Lib/vivanteCore";

import { CONVERTED_TO_VIRTUAL_CLINIC_FIRESTORE_COLLECTION } from "../assets/constants";

export type ConvertedToVirtualClinicStatus = {
  /** Flag to determine if we show the HDHP warning */
  show_hdhp_warning: boolean;
  /** Unused property by Frontend. This is to keep track of the source registration code for BE purposes */
  source_registration_code?: string;
  /** The name of the required intervention for the member to complete as part of the conversion */
  transition_intervention_name: string;
  /** The terminal node name of the intervention for the member to complete as part of the conversion */
  transition_intervention_terminal_node_name?: string;
  /** Flag to determine if the member has completed the conversion workflow. If false, this will trigger the modal */
  workflow_completed: boolean;
  /** The date the member completed the conversion workflow */
  workflow_completed_on_date?: string;
};

const isConvertedToVirtualClinicStatus = (data: unknown): data is ConvertedToVirtualClinicStatus => {
  return (
    data != null &&
    typeof data === "object" &&
    "show_hdhp_warning" in data &&
    "transition_intervention_name" in data &&
    "workflow_completed" in data
  );
};

export const checkConvertedToVirtualClinicStatus = async (): Promise<ConvertedToVirtualClinicStatus | undefined> => {
  const firebaseClient = vivanteCoreContainer.firebaseClient;
  const [firestore, memberId] = await Promise.all([firebaseClient.getFirestore(), firebaseClient.getMemberId()]);

  const document = await firestore
    .collection(CONVERTED_TO_VIRTUAL_CLINIC_FIRESTORE_COLLECTION)
    .document(memberId)
    .get();

  return isConvertedToVirtualClinicStatus(document?.data) ? document.data : undefined;
};
