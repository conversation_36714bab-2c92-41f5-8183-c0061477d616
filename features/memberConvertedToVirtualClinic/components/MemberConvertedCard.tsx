import { Box, Divider, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { RADIUS_FULL_PX } from "@Assets/style_constants";
import { IconVariant, AppIcon } from "@Components/AppIcon/AppIcon";

export type MemberConvertedCardProps = Readonly<{
  iconName: Extract<IconVariant, "CarePlan" | "Phone" | "Medication" | "Doctor" | "Diet" | "Progress" | "Book">;
  iconBackgroundColor: string;
  iconColor?: string;
  title: string;
  description?: string;
}>;

export const MemberConvertedCard = ({
  iconBackgroundColor,
  iconName,
  iconColor = color.icon.strong,
  title,
  description,
}: MemberConvertedCardProps) => {
  const hasDescription = description && description.length > 0;

  return (
    <Box>
      <Box
        px={4}
        py={hasDescription ? 5 : 4}
        display="flex"
        alignItems={hasDescription ? "flex-start" : "center"}
        gap={4}
      >
        <Box display="flex" bgcolor={iconBackgroundColor} borderRadius={RADIUS_FULL_PX} p={2}>
          <AppIcon name={iconName} size="md" includeContainer={false} color={iconColor} />
        </Box>

        {hasDescription ? (
          <Box display="flex" flexDirection="column" gap={1}>
            <Typography variant="h4" color={color.text.default}>
              {title}
            </Typography>

            <Typography variant="bodyDense" color={color.text.subtle}>
              {description}
            </Typography>
          </Box>
        ) : (
          <Typography variant="h4" color={color.text.default}>
            {title}
          </Typography>
        )}
      </Box>

      <Divider />
    </Box>
  );
};
