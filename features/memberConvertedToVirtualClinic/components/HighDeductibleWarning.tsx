import { Box, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

const HIGH_DEDUCTIBLE_STRINGS = appStrings.features.memberConvertedToVirtualClinic.highDeductibleWarning;

export const HighDeductibleWarning = () => {
  return (
    <Box display="flex" flexDirection="column" gap={1}>
      <Typography variant="actionDense">{HIGH_DEDUCTIBLE_STRINGS.title}</Typography>

      <Typography variant="bodyDense">{HIGH_DEDUCTIBLE_STRINGS.description}</Typography>
    </Box>
  );
};
