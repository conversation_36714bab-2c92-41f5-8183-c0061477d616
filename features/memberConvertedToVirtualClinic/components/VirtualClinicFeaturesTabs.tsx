import React, { useState } from "react";
import { Box, Tabs, Tab } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { TabsPanel } from "@Components/TabsPanel/TabsPanel";
import { tabsA11yProps } from "@Components/TabsPanel/utils/tabsA11yProps";

import { MemberConvertedCard, MemberConvertedCardProps } from "./MemberConvertedCard";

const CONVERTED_TO_VC_STRINGS = appStrings.features.memberConvertedToVirtualClinic;
const NEW_FEATURES_STRINGS = CONVERTED_TO_VC_STRINGS.newFeatures;
const CURRENT_FEATURES_STRINGS = CONVERTED_TO_VC_STRINGS.currentFeatures;

const WHATS_NEW_SECTION = [
  {
    iconBackgroundColor: color.background.brand.fill3,
    iconName: "CarePlan",
    ...NEW_FEATURES_STRINGS.improvedCarePlan,
  },
  {
    iconBackgroundColor: color.background.brand.fill4,
    iconName: "Phone",
    ...NEW_FEATURES_STRINGS.consultationsWithDoctors,
  },
  {
    iconBackgroundColor: color.background.brand.fill5,
    iconName: "Medication",
    iconColor: color.icon.onColorFill,
    ...NEW_FEATURES_STRINGS.doctorOrderedTestsScreenings,
  },
] as const satisfies MemberConvertedCardProps[];

const CURRENT_FEATURES_SECTION = [
  {
    iconName: "Doctor",
    title: CURRENT_FEATURES_STRINGS.healthCoach,
    iconBackgroundColor: color.background.brand.fill4,
  },
  {
    iconName: "Diet",
    title: CURRENT_FEATURES_STRINGS.dietitian,
    iconBackgroundColor: color.background.brand.fill4,
  },
  {
    iconName: "Progress",
    title: CURRENT_FEATURES_STRINGS.trackingTools,
    iconBackgroundColor: color.background.brand.fill4,
  },
  {
    iconName: "Book",
    title: CURRENT_FEATURES_STRINGS.helpfulResources,
    iconBackgroundColor: color.background.brand.fill4,
  },
] as const satisfies MemberConvertedCardProps[];

type VirtualClinicTabs = (typeof CONVERTED_TO_VC_STRINGS.tabs)[keyof typeof CONVERTED_TO_VC_STRINGS.tabs];

export const VirtualClinicFeaturesTabs = () => {
  const [activeTab, setActiveTab] = useState<VirtualClinicTabs>(CONVERTED_TO_VC_STRINGS.tabs.whatsNew);

  return (
    <Box>
      <Tabs value={activeTab} onChange={(_, targetTab) => setActiveTab(targetTab)} variant="fullWidth">
        <Tab
          label={CONVERTED_TO_VC_STRINGS.tabs.whatsNew}
          value={CONVERTED_TO_VC_STRINGS.tabs.whatsNew}
          {...tabsA11yProps(CONVERTED_TO_VC_STRINGS.tabs.whatsNew)}
        />

        <Tab
          label={CONVERTED_TO_VC_STRINGS.tabs.currentFeatures}
          value={CONVERTED_TO_VC_STRINGS.tabs.currentFeatures}
          {...tabsA11yProps(CONVERTED_TO_VC_STRINGS.tabs.currentFeatures)}
        />
      </Tabs>

      <TabsPanel
        tabName={CONVERTED_TO_VC_STRINGS.tabs.whatsNew}
        currentSelectedTabName={activeTab}
        sx={{ marginTop: 4 }}
      >
        {WHATS_NEW_SECTION.map((section) => (
          <MemberConvertedCard key={section.title} {...section} />
        ))}
      </TabsPanel>

      <TabsPanel
        tabName={CONVERTED_TO_VC_STRINGS.tabs.currentFeatures}
        currentSelectedTabName={activeTab}
        sx={{ marginTop: 4 }}
      >
        {CURRENT_FEATURES_SECTION.map((section) => (
          <MemberConvertedCard key={section.title} {...section} />
        ))}
      </TabsPanel>
    </Box>
  );
};
