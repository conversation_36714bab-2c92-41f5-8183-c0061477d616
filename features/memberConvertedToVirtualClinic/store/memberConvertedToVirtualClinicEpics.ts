import { Action, PayloadAction } from "@reduxjs/toolkit";
import { Epic, ofType } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { switchMap, map, catchError } from "rxjs/operators";

import { appStrings } from "@Assets/app_strings";
import { ErrorStateSlice } from "@Features/error/store/errorStateSlice";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";
import { store } from "@Store/store";
import { Routes } from "@Types";

import { MemberConvertedToVirtualClinicStateSlice } from "./memberConvertedToVirtualClinicStateSlice";
import { checkConvertedToVirtualClinicStatus } from "../api/checkConvertedToVirtualClinicStatus";
import { completeConvertedToVirtualClinicStatus } from "../api/completeConvertedToVirtualClinicStatus";

const CONVERTED_TO_VC_STRINGS = appStrings.features.memberConvertedToVirtualClinic;

const {
  checkConvertedStatusSuccess,
  completeConvertedToVirtualClinic,
  completeConvertedToVirtualClinicSuccess,
  completeConvertedToVirtualClinicFailure,
  resetConversionStatus,
  checkConvertedStatus: checkConvertedStatusAction,
  checkConvertedStatusFailure: checkConvertedStatusActionFailure,
} = MemberConvertedToVirtualClinicStateSlice.actions;

const checkConversionStatusEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(checkConvertedStatusAction.type),
    switchMap(() => {
      return from(checkConvertedToVirtualClinicStatus()).pipe(
        map((conversionStatus) => checkConvertedStatusSuccess(conversionStatus)),
        catchError((error) => of(checkConvertedStatusActionFailure(error))),
      );
    }),
  );
};

const completeConvertedToVirtualClinicStatusEpic: Epic = (
  actions$: Observable<PayloadAction<{ shouldDisplayErrorModal: boolean }>>,
) => {
  return actions$.pipe(
    ofType(completeConvertedToVirtualClinic.type),
    switchMap((action: PayloadAction<{ shouldDisplayErrorModal: boolean }>) => {
      return from(completeConvertedToVirtualClinicStatus()).pipe(
        switchMap(() => {
          return [
            NavigationStateSlice.actions.navigateTo({ path: Routes.HOME, screenName: "Home" }),
            SnackbarStateSlice.actions.toggleSnackbar({
              isOpen: true,
              message: CONVERTED_TO_VC_STRINGS.successOnCompletion,
            }),
            completeConvertedToVirtualClinicSuccess(),
          ];
        }),
        catchError((error) => {
          const { shouldDisplayErrorModal } = action.payload;

          return of(
            NavigationStateSlice.actions.navigateTo({ path: Routes.HOME, screenName: "Home" }),
            completeConvertedToVirtualClinicFailure(error),
            shouldDisplayErrorModal
              ? ErrorStateSlice.actions.setError({
                  message: CONVERTED_TO_VC_STRINGS.failedToUpdateCompletion,
                  buttonText: appStrings.buttonText.gotIt,
                  onButtonClick: () => {
                    store.dispatch(checkConvertedStatusAction());
                  },
                })
              : () => {},
            shouldDisplayErrorModal ? resetConversionStatus() : () => {},
          );
        }),
      );
    }),
  );
};

export const memberConvertedToVirtualClinicEpics = [
  checkConversionStatusEpic,
  completeConvertedToVirtualClinicStatusEpic,
];
