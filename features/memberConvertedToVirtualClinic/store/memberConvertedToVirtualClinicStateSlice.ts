import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import * as Sentry from "@sentry/nextjs";

import { LoadState } from "@Types";
import { buildSliceStateSelector } from "@Utils/slice.util";

import { ConvertedToVirtualClinicStatus } from "../api/checkConvertedToVirtualClinicStatus";

export type MemberConvertedToVirtualClinicState = Readonly<{
  conversionStatus?: ConvertedToVirtualClinicStatus;
  loadState: LoadState;
}>;

const initialState: MemberConvertedToVirtualClinicState = {
  conversionStatus: undefined,
  loadState: "loaded",
};

export const MemberConvertedToVirtualClinicStateSlice = createSlice({
  name: "memberConvertedToVirtualClinicState",
  initialState,
  reducers: {
    resetConversionStatus: (state) => {
      return { ...state, conversionStatus: undefined };
    },
    checkConvertedStatus: (state) => {
      return { ...state, loadState: "loading" };
    },
    checkConvertedStatusSuccess: (state, action: PayloadAction<ConvertedToVirtualClinicStatus | undefined>) => {
      return { ...state, conversionStatus: action.payload, loadState: "loaded" };
    },
    checkConvertedStatusFailure: (state, action: PayloadAction<Error>) => {
      Sentry.withScope((scope) => {
        scope.setLevel("error");
        scope.captureException(action.payload);
      });

      return { ...state, loadState: "failure" };
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    completeConvertedToVirtualClinic: (state, _: PayloadAction<{ shouldDisplayErrorModal: boolean }>) => {
      return { ...state, loadState: "loading" };
    },
    completeConvertedToVirtualClinicSuccess: (state) => {
      return { ...state, conversionStatus: undefined, loadState: "loaded" };
    },
    completeConvertedToVirtualClinicFailure: (state, action: PayloadAction<Error>) => {
      Sentry.withScope((scope) => {
        scope.setLevel("error");
        scope.captureException(action.payload);
      });

      return { ...state, loadState: "failure" };
    },
  },
});

export const memberConvertedToVirtualClinicSelector = buildSliceStateSelector("memberConvertedToVirtualClinicState");

export const memberConvertedToVirtualClinicStateReducer = MemberConvertedToVirtualClinicStateSlice.reducer;
