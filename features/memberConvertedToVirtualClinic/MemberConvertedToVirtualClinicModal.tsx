import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Box, Button, CircularProgress, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { BaseModal } from "@Components/BaseModal/BaseModal";
import { actionPlansStateSelector } from "@Features/carePlan/store/actionPlansStateSlice";
import { handleUriNavigation } from "@Features/navigation/utils/navigation.util";
import { LoadState } from "@Types";

import { HighDeductibleWarning } from "./components/HighDeductibleWarning";
import { MemberConvertedCard } from "./components/MemberConvertedCard";
import { VirtualClinicFeaturesTabs } from "./components/VirtualClinicFeaturesTabs";
import {
  memberConvertedToVirtualClinicSelector,
  MemberConvertedToVirtualClinicStateSlice,
} from "./store/memberConvertedToVirtualClinicStateSlice";

const CONVERTED_TO_VC_STRINGS = appStrings.features.memberConvertedToVirtualClinic;
const BUTTON_STRINGS = appStrings.buttonText;
const { completeConvertedToVirtualClinic: completeConvertedToVirtualClinicStatus } =
  MemberConvertedToVirtualClinicStateSlice.actions;

type MemberConvertedToVirtualClinicModalScreens = keyof typeof CONVERTED_TO_VC_STRINGS.screens;

const getButtonText = (loadState: LoadState, isSurveyAvailable: boolean) => {
  if (loadState === "loading") {
    return <CircularProgress size={24} color="inherit" />;
  }

  if (isSurveyAvailable) {
    return BUTTON_STRINGS.next;
  }

  return BUTTON_STRINGS.continueToCylinder;
};

export const MemberConvertedToVirtualClinicModal = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [screen, setScreen] = useState<MemberConvertedToVirtualClinicModalScreens>("greatNews");
  const conversionStatus = useSelector(memberConvertedToVirtualClinicSelector("conversionStatus"));
  const loadingState = useSelector(memberConvertedToVirtualClinicSelector("loadState"));
  const interventions = useSelector(actionPlansStateSelector("interventionEntities"));
  const interventionToComplete =
    interventions && conversionStatus?.transition_intervention_name
      ? interventions[conversionStatus.transition_intervention_name]
      : undefined;
  const { header } = CONVERTED_TO_VC_STRINGS.screens[screen];
  const isSurveyAvailable = interventionToComplete !== undefined && interventionToComplete?.state !== "COMPLETED";
  const surveyRoute = handleUriNavigation(interventionToComplete?.action?.uri);
  const shouldDisplayModal = conversionStatus !== undefined && !conversionStatus.workflow_completed;

  const handleOnClick = async () => {
    if (screen === "greatNews") {
      if (!isSurveyAvailable) {
        return dispatch(
          completeConvertedToVirtualClinicStatus({
            shouldDisplayErrorModal: false,
          }),
        );
      }

      return setScreen("getStarted");
    }

    return surveyRoute ? router.push(surveyRoute) : undefined;
  };

  return (
    <BaseModal
      isModalOpen={shouldDisplayModal}
      onClose={() => {}}
      displayCloseButton={false}
      titleIcon={<AppIcon name="CompanyLogo" size="headerLogo" includeInTabIndex={false} />}
      bodyContent={
        <Box display="flex" flexDirection="column" gap={5}>
          <Typography variant="h3Serif">{header}</Typography>

          {screen === "greatNews" ? (
            <VirtualClinicFeaturesTabs />
          ) : (
            <MemberConvertedCard
              iconName="CarePlan"
              title={CONVERTED_TO_VC_STRINGS.updateHealthInformation}
              iconBackgroundColor={color.background.surface.secondary}
            />
          )}

          {conversionStatus?.show_hdhp_warning ? <HighDeductibleWarning /> : null}
        </Box>
      }
      actions={
        <Box display="flex" flexDirection="column" width="100%">
          {loadingState === "failure" ? (
            <Typography variant="caption" color={color.text.error} mb={2} align="center">
              {CONVERTED_TO_VC_STRINGS.failedToUpdateCompletion}
            </Typography>
          ) : null}

          <Button variant="primary" onClick={handleOnClick} fullWidth disabled={loadingState === "loading"}>
            {getButtonText(loadingState, isSurveyAvailable)}
          </Button>
        </Box>
      }
    />
  );
};
