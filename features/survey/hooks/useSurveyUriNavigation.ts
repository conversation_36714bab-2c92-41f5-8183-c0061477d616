import { useDispatch, useSelector } from "react-redux";
import { ActionPlanTargetState, ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/router";

import {
  actionPlansStateSelector,
  ActionPlansStateSlice,
  setActionPlanTargetState,
  setSelectedActionId,
} from "@Features/carePlan/store/actionPlansStateSlice";
import { shouldMarkAsDone } from "@Features/carePlan/utils/markAsDone.util";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { useCheckAndUpdateParentActionState } from "@Hooks/useCheckAndUpdateParentActionState";
import { useOpenSymptomLoggingDrawer } from "@Hooks/useOpenSymptopLoggingDrawer";

const { linkingInterventionTriggered } = ActionPlansStateSlice.actions;
/** Array of button titles which would generate the SCHEDULING_SESSION_LATER clickstream event */
const SCHEDULE_LATER_TITLES = ["schedule later", "explore my plan first"];
/** Array of button titles which would generate the SCHEDULING_SESSION_NOWclickstream event */
const SCHEDULE_NOW_TITLES = ["schedule an appointment", "schedule with my health coach"];

const clickstreamEventToSend = (title: string) => {
  const lowerCasedTitle = title.toLowerCase();

  if (SCHEDULE_LATER_TITLES.includes(lowerCasedTitle)) {
    return ClickStreamActivityEventType.ONBOARDING_SCHEDULE_SESSION_LATER;
  }

  if (SCHEDULE_NOW_TITLES.includes(lowerCasedTitle)) {
    return ClickStreamActivityEventType.ONBOARDING_SCHEDULE_SESSION_NOW;
  }
};

export const useSurveyUriNavigation = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const triggerSymptomLoggingDrawer = useOpenSymptomLoggingDrawer(true);
  const { sendEventAnalytics } = useAnalyticsHook();

  const originatingCarePlanActionId = useSelector(actionPlansStateSelector("selectedActionId"));
  const driverEntitiesState = useSelector(actionPlansStateSelector("driverEntities"));
  const checkAndUpdateParentActionState = useCheckAndUpdateParentActionState();

  const handleSurveyUriNavigation = (uri: string, title?: string, surveyId?: string) => {
    const clickstreamEvent = title ? clickstreamEventToSend(title) : undefined;
    const selectedAction = originatingCarePlanActionId ? driverEntitiesState?.[originatingCarePlanActionId] : undefined;

    // reset state for currently selected actionId
    if (originatingCarePlanActionId) {
      dispatch(setSelectedActionId(null));
    }

    if (
      originatingCarePlanActionId &&
      selectedAction &&
      selectedAction?.targetId &&
      shouldMarkAsDone(originatingCarePlanActionId, selectedAction.state)
    ) {
      // mark parent/target item as completed if this is the last uncompleted driver
      checkAndUpdateParentActionState(
        selectedAction.targetId,
        originatingCarePlanActionId,
        ActionPlanTargetState.COMPLETED,
      );

      // mark this action as completed
      dispatch(
        setActionPlanTargetState({
          targetId: originatingCarePlanActionId,
          newTargetState: ActionPlanTargetState.COMPLETED,
        }),
      );
    }

    if (clickstreamEvent) {
      sendEventAnalytics(clickstreamEvent, { surveyId: surveyId ?? "", surveyCode: searchParams.get("code") ?? "" });
    }

    if (uri.includes("/progress")) {
      triggerSymptomLoggingDrawer();
      return router.push("/progress");
    }

    // when the survey has linked intervention
    if (uri.includes("/survey-loading")) {
      // Extract query parameters of given url which indicates all linkable intervention ids
      const interventionIds = uri.split("/")[2].split(","); // shape is the the following: "/survey-loading/schedule_gi,referral_summary_ibd"

      return dispatch(linkingInterventionTriggered(interventionIds));
    }

    return router.push(uri);
  };

  return { handleSurveyUriNavigation } as const;
};
