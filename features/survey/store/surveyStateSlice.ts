import { Survey, SurveyAnswer, SurveyCustomValues } from "@vivantehealth/vivante-core";
import { PayloadAction, createAction, createSlice } from "@reduxjs/toolkit";

import { RootState } from "@Store/store";
import { LoadState } from "@Types";
import { processError } from "@Utils/slice.util";

import { LoadPreviousSurveyNodeAction, SubmitSurveyAction, SurveyAction } from "../types/actionTypes";

export type SurveyState = Readonly<{
  survey: Survey | null;
  loadState: LoadState;
  errorMessage: string | null;
}>;

export const initialState: SurveyState = {
  survey: null,
  loadState: null,
  errorMessage: null,
};

export type AnswerSurveyProps = Readonly<{
  survey: Survey;
  answer: SurveyAnswer;
  customValues: SurveyCustomValues;
}>;

// allows for optional action arg and payload
const loadSurvey = createAction<SurveyAction>("loadSurveyByLink");
const submitSurveyResponse = createAction<SubmitSurveyAction>("answerSurvey");
const loadPreviousQuestion = createAction<LoadPreviousSurveyNodeAction>("loadSurveyWithPreviousNode");

export const surveyStateSlice = createSlice({
  name: "surveyState",
  initialState,
  reducers: {
    [loadSurvey.type]: (state) => ({ ...state, loadState: "loading" }),
    loadSurveyByLinkSuccess: (state, action: PayloadAction<Survey>) => ({
      ...state,
      loadState: "loaded",
      survey: action.payload,
    }),
    loadSurveyByLinkFail: (state, action: PayloadAction<Error>) => {
      processError({ error: action.payload });

      return { ...state, loadState: "failure" };
    },
    resetState: (state) => ({ ...state, ...initialState }),
    [submitSurveyResponse.type]: (state) => ({ ...state, loadState: "loading" }),
    answerSurveySuccess: (state, action: PayloadAction<Survey>) => ({
      ...state,
      survey: action.payload,
      loadState: "loaded",
      errorMessage: null,
    }),
    answerSurveyFail: (state, action) => {
      return {
        ...state,
        loadState: "failure",
        errorMessage: action.payload,
      };
    },
    [loadPreviousQuestion.type]: (state) => ({ ...state, loadState: "loading" }),
    loadSurveyWithPreviousNodeSuccess: (state, action: PayloadAction<Survey>) => ({
      ...state,
      survey: action.payload,
      loadState: "loaded",
      errorMessage: initialState.errorMessage,
    }),
    loadSurveyWithPreviousNodeFail: (state, action: PayloadAction<string>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure", errorMessage: action.payload };
    },
  },
});

export const SurveyStateSliceSelector =
  <T extends keyof SurveyState>(attribute: T) =>
  (state: RootState): SurveyState[T] =>
    state.surveyState[attribute];

export const surveyStateReducer = surveyStateSlice.reducer;
