import {
  ClickStreamActivityEventType,
  GetSurveyWithPreviousNodeProps,
  Survey,
  ValidationException,
  VivanteApiError,
  VivanteException,
  VivanteLinkResource,
} from "@vivantehealth/vivante-core";
import { PayloadAction } from "@reduxjs/toolkit";
import * as Sentry from "@sentry/nextjs";
import { Epic, ofType } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { catchError, delay, map, switchMap } from "rxjs/operators";

import { ActionPlansStateSlice } from "@Features/carePlan/store/actionPlansStateSlice";
import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { formatVivanteException } from "@Utils/formatVivanteException";

import { AnswerSurveyProps, surveyStateSlice } from "./surveyStateSlice";
import { createSimpleAnalyticsEpic } from "../../analytics/store/analyticsEpics";

const SURVEY_RETRY_DELAY_TIME = 2500;
const MAX_RETRY_COUNT = 10;
const FIRST_RETRY_ATTEMPT = 0;
let retryCount = 0;

const isVivanteApiError = (error: unknown): error is VivanteApiError => {
  return VivanteApiError.isVivanteApiError(error);
};

const {
  loadSurveyByLink,
  loadSurveyByLinkSuccess,
  loadSurveyByLinkFail,
  answerSurvey,
  answerSurveySuccess,
  answerSurveyFail,
  loadSurveyWithPreviousNode,
  loadSurveyWithPreviousNodeSuccess,
  loadSurveyWithPreviousNodeFail,
} = surveyStateSlice.actions;
const { linkingInterventionSuccess } = ActionPlansStateSlice.actions;
/**
 * Set error message
 * Add sentry logging to error messages
 * Make comments to explain the code
 * Fix loading state
 */
const loadSurveyByCodeEpic: Epic = (actions$: Observable<PayloadAction<VivanteLinkResource>>) => {
  return actions$.pipe(
    ofType(loadSurveyByLink.type),
    switchMap((action: PayloadAction<VivanteLinkResource>) => {
      return from(vivanteCoreContainer.getSurveyUseCaseFactory().createGetSurveyUseCase().execute(action.payload)).pipe(
        switchMap((survey: Survey) => {
          return [loadSurveyByLinkSuccess(survey), linkingInterventionSuccess()];
        }),
        catchError((error) => {
          const errorMessage = isVivanteApiError(error?.error)
            ? `StatusCode - ${error.error.status}, ErrorDetail - ${error.error.detail}`
            : JSON.stringify(error);

          /**
           * We can experience an issue where survey's are not ready when attempting to load them.
           * This can be due to the survey not yet being generated but a care plan has been generated for example
           * To handle this, we will retry loading the survey up to 10 times with a delay of 2.5 seconds between each attempt
           * https://github.com/vivantehealth/zi/issues/6534
           */
          if (retryCount < MAX_RETRY_COUNT) {
            // Include Sentry logging on initial retry attempt
            if (retryCount === FIRST_RETRY_ATTEMPT) {
              Sentry.withScope((scope) => {
                scope.setLevel("error");
                Sentry.captureException(
                  new Error(
                    `RETRY ATTEMPT: Failed to load survey: ${action.payload.queryParams?.code}. Error: ${errorMessage}`,
                  ),
                );
              });
            }

            retryCount += 1;
            return of(loadSurveyByLink(action.payload)).pipe(delay(SURVEY_RETRY_DELAY_TIME));
          }

          // Reset retry count
          retryCount = 0;

          return of(
            loadSurveyByLinkFail(
              new Error(`Failed to load survey: ${action.payload.queryParams?.code}. Error: ${errorMessage}`),
            ),
          );
        }),
      );
    }),
  );
};

const answerSurveyEpic: Epic = (actions$: Observable<PayloadAction<AnswerSurveyProps>>) => {
  return actions$.pipe(
    ofType(answerSurvey.type),
    switchMap((action: PayloadAction<AnswerSurveyProps>) => {
      const submissionData = {
        surveyId: action.payload.survey.id,
        node: action.payload.survey.currentNode,
        ...(action.payload.answer !== undefined || action.payload.answer !== null
          ? { answer: action.payload.answer }
          : {}),
        ...(action.payload.customValues ? { customValues: action.payload.customValues } : {}),
      };

      return from(
        vivanteCoreContainer.getSurveyUseCaseFactory().createSubmitNodeReplyUseCase().execute(submissionData),
      ).pipe(
        map((survey: Survey) => answerSurveySuccess(survey)),
        catchError((error: VivanteException | ValidationException) => {
          if (!(error instanceof ValidationException)) {
            // handle system error in component with mui alert
          }

          return of(answerSurveyFail(error));
        }),
      );
    }),
  );
};

const loadSurveyWithPreviousNodeEpic: Epic = (actions$: Observable<PayloadAction<GetSurveyWithPreviousNodeProps>>) => {
  return actions$.pipe(
    ofType(loadSurveyWithPreviousNode.type),
    switchMap((action: PayloadAction<GetSurveyWithPreviousNodeProps>) =>
      from(
        vivanteCoreContainer.getSurveyUseCaseFactory().createGetSurveyWithPreviousNode().execute(action.payload),
      ).pipe(
        map((survey: Survey) => loadSurveyWithPreviousNodeSuccess(survey)),
        catchError((error) => {
          return of(loadSurveyWithPreviousNodeFail(formatVivanteException(error)));
        }),
      ),
    ),
  );
};

const analyticsEpics: Epic[] = [
  createSimpleAnalyticsEpic<Survey>(loadSurveyByLinkSuccess.type, (payload) => ({
    eventType: ClickStreamActivityEventType.ENTER_SCREEN,
    currentScreen: "SURVEY",
    activityContextExtra: {
      surveyId: payload?.id,
    },
  })),
];

export const surveyEpics = [answerSurveyEpic, loadSurveyByCodeEpic, loadSurveyWithPreviousNodeEpic, ...analyticsEpics];
