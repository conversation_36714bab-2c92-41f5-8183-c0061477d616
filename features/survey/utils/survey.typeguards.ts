import {
  SurveyQuestionNode,
  SurveyQuestionImperialWeightExtra,
  SurveyQuestionImperialHeightExtra,
  SurveyQuestionNumberExtra,
  SurveyAnswer,
  ImperialHeight,
  ImperialWeight,
  SurveyQuestionExtra,
  SurveyQuestionSingleMultipleExtra,
} from "@vivantehealth/vivante-core";

export const isSurveyNodeWithImperialWeight = (
  node: SurveyQuestionNode,
): node is Omit<SurveyQuestionNode, "extra"> & { extra: SurveyQuestionImperialWeightExtra } => {
  return "extra" in node && node.extra != null && ("minimum" in node.extra || "maximum" in node.extra);
};

export const isSurveyNodeWithImperialHeight = (
  node: SurveyQuestionNode,
): node is Omit<SurveyQuestionNode, "extra"> & { extra: SurveyQuestionImperialHeightExtra } => {
  return "extra" in node && node.extra != null && ("minimum" in node.extra || "maximum" in node.extra);
};

export const isSurveyNodeWithNumberExtra = (
  node: SurveyQuestionNode,
): node is Omit<SurveyQuestionNode, "extra"> & { extra: SurveyQuestionNumberExtra } => {
  return "extra" in node && node.extra != null && ("minimum" in node.extra || "maximum" in node.extra);
};

export const isSurveyAnswerWithImperialHeight = (value: SurveyAnswer | undefined): value is ImperialHeight => {
  return value != null && typeof value === "object" && "feet" in value && "inches" in value;
};

export const isSurveyQuestionOption = (extra?: SurveyQuestionExtra): extra is SurveyQuestionSingleMultipleExtra => {
  return extra !== undefined && "options" in extra;
};

export const isStringArray = (value: unknown): value is string[] => {
  return Array.isArray(value) && value.every((v) => typeof v === "string");
};

export const isImperialHeight = (value: unknown): value is ImperialHeight => {
  return value != null && typeof value === "object" && "feet" in value && "inches" in value;
};

export const isImperialWeight = (value: unknown): value is ImperialWeight => {
  return value != null && typeof value === "object" && "pounds" in value;
};
