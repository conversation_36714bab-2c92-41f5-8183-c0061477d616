import { SurveyQuestionNode } from "@vivantehealth/vivante-core";
import { describe, test, expect } from "vitest";

import {
  isInvalidImperialWeight,
  isInvalidNumber,
  isInvalidImperialHeight,
  getNumberValue,
  getStringArrayValue,
} from "./survey.utils";

describe("survey.utils", () => {
  describe("isInvalidImperialWeight", () => {
    test("Should return true if value are undefined", () => {
      const result = isInvalidImperialWeight(undefined, { id: "", title: "", type: "imperial-weight" });

      expect(result).toBe(true);
    });

    test("Should return true if value results in NaN", () => {
      const currentNode: SurveyQuestionNode = {
        id: "",
        title: "",
        type: "imperial-weight",
        extra: { minimum: { pounds: 100 } },
      };
      const values = { pounds: Number("-") };
      const result = isInvalidImperialWeight(values, currentNode);

      expect(result).toBe(true);
    });

    test("Should return true if pounds are less than the minimum", () => {
      const currentNode: SurveyQuestionNode = {
        id: "",
        title: "",
        type: "imperial-weight",
        extra: { minimum: { pounds: 100 } },
      };
      const values = { pounds: 90 };
      const result = isInvalidImperialWeight(values, currentNode);

      expect(result).toBe(true);
    });

    test("Should return true if pounds are greater than the maximum", () => {
      const currentNode: SurveyQuestionNode = {
        id: "",
        title: "",
        type: "imperial-weight",
        extra: { maximum: { pounds: 200 } },
      };
      const values = { pounds: 210 };
      const result = isInvalidImperialWeight(values, currentNode);

      expect(result).toBe(true);
    });

    test("Should return false if pounds are within the range", () => {
      const currentNode: SurveyQuestionNode = {
        id: "",
        title: "",
        type: "imperial-weight",
        extra: { minimum: { pounds: 100 }, maximum: { pounds: 200 } },
      };

      const values = { pounds: 150 };
      const result = isInvalidImperialWeight(values, currentNode);

      expect(result).toBe(false);
    });
  });

  describe("isInvalidNumber", () => {
    test("Should return true if values are undefined", () => {
      const result = isInvalidNumber(undefined, {} as SurveyQuestionNode);

      expect(result).toBe(true);
    });

    test("Should return true if values are NaN", () => {
      const currentNode: SurveyQuestionNode = {
        id: "",
        title: "",
        type: "number",
        extra: { minimum: 10, maximum: 20 },
      };
      const value = NaN;
      const result = isInvalidNumber(value, currentNode);

      expect(result).toBe(true);
    });

    test("Should return true if number is less than the minimum", () => {
      const currentNode: SurveyQuestionNode = {
        id: "",
        title: "",
        type: "number",
        extra: { minimum: 10, maximum: 20 },
      };
      const value = 5;
      const result = isInvalidNumber(value, currentNode);

      expect(result).toBe(true);
    });

    test("Should return true if number is greater than the maximum", () => {
      const currentNode: SurveyQuestionNode = {
        id: "",
        title: "",
        type: "number",
        extra: { minimum: 10, maximum: 20 },
      };
      const value = 25;
      const result = isInvalidNumber(value, currentNode);

      expect(result).toBe(true);
    });

    test("Should return false if number is within the range", () => {
      const currentNode: SurveyQuestionNode = {
        id: "",
        title: "",
        type: "number",
        extra: { minimum: 10, maximum: 20 },
      };
      const value = 15;
      const result = isInvalidNumber(value, currentNode);

      expect(result).toBe(false);
    });
  });

  describe("isInvalidImperialHeight", () => {
    test("Should return true if height exceeds maximum feet and inches", () => {
      const currentNode: SurveyQuestionNode = {
        id: "",
        title: "",
        type: "imperial-height",
        extra: { maximum: { feet: 6, inches: 2 } },
      };
      const values = { feet: 6, inches: 3 };
      const result = isInvalidImperialHeight(values, currentNode);

      expect(result).toBe(true);
    });

    test("Should return false if height is within the maximum feet and inches", () => {
      const currentNode: SurveyQuestionNode = {
        id: "",
        title: "",
        type: "imperial-height",
        extra: { maximum: { feet: 6, inches: 2 } },
      };
      const values = { feet: 6, inches: 1 };
      const result = isInvalidImperialHeight(values, currentNode);

      expect(result).toBe(false);
    });
  });

  describe("getStringArrayValue", () => {
    test("Should return an array with the string if a single string is provided", () => {
      const result = getStringArrayValue("test");

      expect(result).toEqual({ answerValues: ["test"] });
    });

    test("Should return the same array if a string array is provided", () => {
      const result = getStringArrayValue(["test1", "test2"]);

      expect(result).toEqual({ answerValues: ["test1", "test2"] });
    });

    test("Should return an empty array if the value is undefined", () => {
      const result = getStringArrayValue(undefined);

      expect(result).toEqual({ answerValues: [] });
    });

    test("Should return an empty array if the value is not a string or string array", () => {
      const result = getStringArrayValue(123);

      expect(result).toEqual({ answerValues: [] });
    });
  });

  describe("getNumberValue", () => {
    test("Should return the number if a valid number is provided", () => {
      const result = getNumberValue(42);

      expect(result).toBe(42);
    });

    test("Should return the number if a valid number string is provided", () => {
      const result = getNumberValue("42");

      expect(result).toBe(42);
    });

    test("Should return null if the value is undefined", () => {
      const result = getNumberValue(undefined);

      expect(result).toBeNull();
    });

    test("Should return null if the value is not a number or a valid number string", () => {
      const result = getNumberValue("invalid");

      expect(result).toBeNull();
    });
  });
});
