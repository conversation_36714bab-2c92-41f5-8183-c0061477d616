import { SurveyAnswer, SurveyQuestionNode } from "@vivantehealth/vivante-core";

import {
  isStringArray,
  isSurveyAnswerWithImperialHeight,
  isSurveyNodeWithImperialHeight,
  isSurveyNodeWithImperialWeight,
  isSurveyNodeWithNumberExtra,
} from "./survey.typeguards";

export const isInvalidImperialWeight = (value: SurveyAnswer | undefined, currentNode: SurveyQuestionNode) => {
  const isObjectContainingPounds = typeof value === "object" && "pounds" in value;

  if (
    value === undefined ||
    (isObjectContainingPounds && (value.pounds === undefined || Number.isNaN(value?.pounds)))
  ) {
    return true;
  }

  return (
    isObjectContainingPounds &&
    isSurveyNodeWithImperialWeight(currentNode) &&
    ((currentNode.extra.minimum?.pounds !== undefined && value.pounds < currentNode?.extra?.minimum?.pounds) ||
      (currentNode.extra.maximum?.pounds !== undefined && value.pounds > currentNode.extra.maximum?.pounds))
  );
};

export const isInvalidNumber = (value: SurveyAnswer | undefined, currentNode: SurveyQuestionNode) => {
  if (value === undefined || typeof value !== "number" || Number.isNaN(value)) {
    return true;
  }

  return (
    isSurveyNodeWithNumberExtra(currentNode) &&
    ((currentNode?.extra?.minimum !== undefined && value < currentNode?.extra?.minimum) ||
      (currentNode?.extra?.maximum !== undefined && value > currentNode?.extra?.maximum))
  );
};

export const isInvalidImperialHeight = (value: SurveyAnswer | undefined, currentNode: SurveyQuestionNode) => {
  return (
    isSurveyAnswerWithImperialHeight(value) &&
    isSurveyNodeWithImperialHeight(currentNode) &&
    !!currentNode?.extra?.maximum &&
    value?.feet >= currentNode?.extra?.maximum.feet &&
    value?.inches > currentNode?.extra?.maximum.inches
  );
};

export const getStringArrayValue = (answerValues: SurveyAnswer | undefined) => {
  if (isStringArray(answerValues)) {
    return { answerValues };
  }

  if (typeof answerValues === "string") {
    return { answerValues: [answerValues] };
  }

  return { answerValues: [] };
};

export const getNumberValue = (answerValues: SurveyAnswer | undefined) => {
  if (typeof answerValues === "number") {
    return answerValues;
  }

  if (typeof answerValues === "string" && !Number.isNaN(Number(answerValues))) {
    return Number(answerValues);
  }

  return null;
};
