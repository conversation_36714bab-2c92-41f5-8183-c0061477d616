import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  SurveyAnswer,
  SurveyCustomValues,
  SurveyNode,
  SurveyQuestionNode,
  SurveyRedirectNode,
} from "@vivantehealth/vivante-core";
import { Button, Paper, Typography } from "@mui/material";
import Router, { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { actionPlansStateSelector } from "@Features/carePlan/store/actionPlansStateSlice";
import { memberStateSelector } from "@Features/member/store/memberStateSlice";
import {
  memberConvertedToVirtualClinicSelector,
  MemberConvertedToVirtualClinicStateSlice,
} from "@Features/memberConvertedToVirtualClinic/store/memberConvertedToVirtualClinicStateSlice";
import { surveyStateSlice } from "@Features/survey/store/surveyStateSlice";
import { useSurveyHook } from "@Hooks/surveyHook";
import { formatVivanteException } from "@Utils/formatVivanteException";
import { linkifyText } from "@Utils/linkify";

import { SurveyForm } from "./components/SurveyForm";
import { SurveyNavigationWrapper } from "./components/SurveyNavigationWrapper";

const { answerSurvey, loadSurveyWithPreviousNode, resetState } = surveyStateSlice.actions;

const isSurveyQuestionNode = (currentNode: SurveyNode | undefined): currentNode is SurveyQuestionNode => {
  return currentNode !== undefined && currentNode instanceof SurveyQuestionNode;
};

export type SurveyAnswerWithCustomValues = Readonly<{
  answerValues?: SurveyAnswer;
  customValues?: SurveyCustomValues;
}>;

export const SurveyContainer = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const {
    survey,
    isSurveyLoading,
    isContextNode,
    isRedirectNode,
    error,
    getActionUris,
    answerSurveyAsync,
    fetchSurvey,
  } = useSurveyHook();
  const actionUris = getActionUris();
  /**
   * Since linking intervention loading logic is handled by action plan slice
   * This screen should slow loading spinner when action plan is loading too.
   */
  const isActionPlanLoading = useSelector(actionPlansStateSelector("loadState")) === "loading";
  const [answer, setAnswer] = useState<SurveyAnswerWithCustomValues>({
    answerValues: undefined,
    customValues: undefined,
  });
  const [showLoadingSpinner, setShowLoadingSpinner] = useState(false);
  const member = useSelector(memberStateSelector("member"));
  const conversionStatus = useSelector(memberConvertedToVirtualClinicSelector("conversionStatus"));

  const [isIntakeSurvey, setIsIntakeSurvey] = useState<boolean>();
  const isIntakeCompletedNode = survey?.currentNode?.id === "t.survey_intake_completed";
  /**
   * If the user is completing a survey as part of the conversion status workflow, we check if the current node is the
   * terminal node specified in the conversion status. We fallback to migration survey terminal node if we do not have the property
   * on the conversion status object.
   */
  const isConversionStatusSurveyCompletedNode =
    survey?.currentNode?.id ===
    (conversionStatus?.transition_intervention_terminal_node_name ?? "t.survey_migration_completed");
  const isLoading = isSurveyLoading || isActionPlanLoading || showLoadingSpinner;

  useEffect(() => {
    if (isIntakeSurvey === undefined) {
      setIsIntakeSurvey(member?.setting?.onboardingPending);
    }
  }, [isIntakeSurvey, member]);

  useEffect(() => {
    const currentNode = survey?.currentNode;

    if (isSurveyQuestionNode(currentNode) && currentNode.type === "imperial-height") {
      if (!currentNode?.answer) {
        setAnswer({ answerValues: { feet: 5, inches: 6 } });
      }
    }
  }, [survey]);

  useEffect(() => {
    return () => {
      dispatch(resetState());
    };
  }, [dispatch]);

  if (isIntakeSurvey === undefined) {
    return <LoadingSpinner open />;
  }

  if (isConversionStatusSurveyCompletedNode && conversionStatus !== undefined && !conversionStatus.workflow_completed) {
    /**
     * We set the loading spinner local state to true to keep the loading spinner showing while the conversion status is
     * updated. This is to prevent the survey terminal node from briefly showing between navigating to the home screen. We don't
     * use the Redux loading state as it updates before the navigation occurs, resulting in the brief showing of the terminal node.
     */
    if (!showLoadingSpinner) {
      setShowLoadingSpinner(true);
    }

    dispatch(
      MemberConvertedToVirtualClinicStateSlice.actions.completeConvertedToVirtualClinic({
        shouldDisplayErrorModal: true,
      }),
    );
  }

  const answerRedirectNode = async () => {
    // answer redirect survey without updating state
    // avoids react warning about updating components while redirecting
    answerSurveyAsync()
      .then(() => {
        if (survey?.currentNode instanceof SurveyRedirectNode) {
          Router.push(survey?.currentNode?.uri);
        }
      })
      .catch((err) => {
        dispatch(surveyStateSlice.actions.answerSurveyFail(formatVivanteException(err)));
      });
  };

  if (isRedirectNode) {
    answerRedirectNode();
  }

  const submitSurveyResponse = () => {
    const currentNode = survey?.currentNode;

    const answerData = answer.answerValues || (isSurveyQuestionNode(currentNode) && currentNode?.answer) || undefined;
    const customData =
      answer.customValues || (isSurveyQuestionNode(currentNode) && currentNode?.customValues) || undefined;

    dispatch(
      answerSurvey({
        answer: answerData,
        customValues: customData,
        survey,
      }),
    );
    setAnswer({ answerValues: undefined, customValues: undefined });
  };

  const handleLoadPreviousNode = () => {
    /** If we do not have a previousNodeId but are showing a back button, than we should route back */
    if (survey?.previousNodeId === undefined) {
      return router.back();
    }

    dispatch(
      loadSurveyWithPreviousNode({
        surveyId: survey.id,
        previousNodeId: survey.previousNodeId,
      }),
    );
    setAnswer({ answerValues: undefined, customValues: undefined });
  };

  if (error) {
    /** To ensure we keep the top bar and potentially the side navigation, we wrap the error in the SurveyNavigationWrapper */
    return (
      <SurveyNavigationWrapper
        isContextNode={isContextNode}
        isIntakeSurvey={!!isIntakeSurvey}
        showLoadingSpinner={false}
      >
        <Paper sx={{ px: 5 }}>
          <Typography variant="h3" mb={3}>
            {appStrings.errorPages.alerts.errorHeader}
          </Typography>

          <Typography variant="body">{error}</Typography>
        </Paper>
      </SurveyNavigationWrapper>
    );
  }

  if (isRedirectNode) {
    return <LoadingSpinner open overlayDrawer overlayHeader />;
  }

  return (
    <SurveyNavigationWrapper
      isContextNode={isContextNode}
      isIntakeSurvey={isIntakeSurvey || isIntakeCompletedNode}
      showLoadingSpinner={isLoading}
      surveyId={survey?.currentNode && survey.currentNode.id}
    >
      {survey != null ? (
        <SurveyForm
          survey={survey}
          answer={answer}
          submitSurvey={submitSurveyResponse}
          setAnswer={(data: SurveyAnswerWithCustomValues) => setAnswer(data)}
          loadPrevious={handleLoadPreviousNode}
          isContextual={isContextNode}
          isIntakeSurvey={isIntakeSurvey || isIntakeCompletedNode}
          actions={actionUris}
        />
      ) : (
        <>
          <Paper sx={{ mb: 4 }}>
            <Typography variant="h3">
              {linkifyText(appStrings.features.survey.surveyFetchFailed(!!isIntakeSurvey))}
            </Typography>
          </Paper>

          <Button variant="primary" onClick={fetchSurvey} fullWidth>
            {appStrings.buttonText.retry}
          </Button>
        </>
      )}
    </SurveyNavigationWrapper>
  );
};
