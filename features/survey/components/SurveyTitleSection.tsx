import { Survey, SurveyContextNode, SurveyTerminalNode } from "@vivantehealth/vivante-core";
import { Box, Button, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";

import { SurveyActions } from "./SurveyActions";
import {
  INTAKE_SURVEY_YELLOW_MOTIF_IDS,
  CLASSIC_SURVEY_TITLES,
  INTAKE_SURVEY_PINK_MOTIF_IDS,
} from "../assets/constants";
import { useSurveyUriNavigation } from "../hooks/useSurveyUriNavigation";
import { SurveyAnswerWithCustomValues } from "../SurveyContainer";

const BUTTON_STRINGS = appStrings.buttonText;

type SurveyTitleProps = Readonly<{
  currentNode: SurveyContextNode;
  isContextual: boolean;
  isIntakeSurvey: boolean;
  actions: { uri?: string; title: string }[] | undefined;
  answer: SurveyAnswerWithCustomValues;
  survey: Survey;
  loadPrevious: () => void;
  submitSurvey?: () => void;
}>;
/**
 * TODO: This function is currently rather fragile. We are dependent on the backend either not changing existing survey IDs/titles
 * or introducing new survey IDs/titles without letting us know. We should seek to consult with the backend team(s) to determine
 * a better approach moving forward post Rebrand.
 */
const getTypographyColor = (isIntakeSurvey: boolean, surveyId = "") => {
  // We use the default text color if it's not an intake survey or there is no surveyId supplied
  if (!isIntakeSurvey || surveyId.length === 0) {
    return color.text.default;
  }

  if (
    [...INTAKE_SURVEY_YELLOW_MOTIF_IDS, ...CLASSIC_SURVEY_TITLES, ...INTAKE_SURVEY_PINK_MOTIF_IDS].includes(surveyId)
  ) {
    return color.text.strong;
  }

  if (["c.health_history", "c.symptoms"].includes(surveyId)) {
    return color.text.action.onFill;
  }

  return color.text.default;
};

export const SurveyTitleSection = ({
  currentNode,
  isContextual,
  isIntakeSurvey,
  actions,
  answer,
  survey,
  loadPrevious,
  submitSurvey,
}: SurveyTitleProps) => {
  const { handleSurveyUriNavigation } = useSurveyUriNavigation();
  const renderActions = !!currentNode.extraInfo;
  const isInterventionContextScreen = isContextual && !isIntakeSurvey;
  const typographyColor = getTypographyColor(isIntakeSurvey, currentNode?.id);

  /** This is used to render actions that is also an interventionContextScreen */
  if (renderActions && isInterventionContextScreen) {
    return (
      <Box mt={6}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={5}>
          <Box>
            <Typography variant="h1Serif" color={color.text.default}>
              {currentNode?.title}
            </Typography>
            {currentNode?.body && (
              <Typography variant="body" whiteSpace="pre-wrap">
                {currentNode?.body}
              </Typography>
            )}
          </Box>
          <Box display="flex" gap={5} minWidth={"340px"}>
            <Button variant="secondary" fullWidth onClick={loadPrevious}>
              {BUTTON_STRINGS.back}
            </Button>

            <Button variant="primary" fullWidth onClick={submitSurvey}>
              {BUTTON_STRINGS.continue}
            </Button>
          </Box>
        </Box>
      </Box>
    );
  }

  if (renderActions) {
    return (
      <Box display="flex" textAlign="left" mb={0}>
        <Box display="flex" flexDirection="column">
          <Typography variant="h1Serif" color={typographyColor}>
            {currentNode?.title}
          </Typography>

          {currentNode?.body ? (
            <Typography variant="body" color={typographyColor}>
              {currentNode?.body}
            </Typography>
          ) : null}
        </Box>
        {renderActions && (
          <Box minWidth="calc(50% - 16px)" ml="16px">
            <SurveyActions
              actions={actions}
              answer={answer}
              survey={survey}
              loadPrevious={loadPrevious}
              submitSurvey={submitSurvey}
              isContextNode={isContextual}
              isIntakeSurvey={isIntakeSurvey}
            />
          </Box>
        )}
      </Box>
    );
  }

  /**
   * When the screen is an intervention context screen, we need to display the buttons differently than
   * the regular survey screens. We align them to the right and display them in a row with the content below the title/buttons
   */
  if (isInterventionContextScreen) {
    const isTerminalNode = currentNode?.constructor === SurveyTerminalNode;
    const isSingleTerminalNodeButton = isTerminalNode && actions && actions.length === 1;

    return (
      <Box>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={5}>
          <Typography variant="h1Serif" color={color.text.default}>
            {currentNode?.title}
          </Typography>

          <Box display="flex" gap={5} minWidth={isSingleTerminalNodeButton ? "170px" : "340px"}>
            {isTerminalNode ? (
              actions?.map((action, index) => {
                return (
                  <Button
                    key={action.title}
                    variant={index === 0 ? "primary" : "secondary"}
                    onClick={() => {
                      if (action.uri) {
                        handleSurveyUriNavigation(action.uri);
                      }
                    }}
                    fullWidth
                  >
                    {action.title}
                  </Button>
                );
              })
            ) : (
              <>
                <Button variant="secondary" fullWidth onClick={loadPrevious}>
                  {BUTTON_STRINGS.back}
                </Button>

                <Button variant="primary" fullWidth onClick={submitSurvey}>
                  {BUTTON_STRINGS.continue}
                </Button>
              </>
            )}
          </Box>
        </Box>
        {currentNode?.body && (
          <Paper sx={{ p: 5 }}>
            <Typography variant="body" whiteSpace="pre-wrap">
              {currentNode?.body}
            </Typography>
          </Paper>
        )}
      </Box>
    );
  }

  return (
    <Box textAlign={isContextual ? "center" : "initial"} mb={currentNode?.body ? 3 : 6} position="relative" zIndex={2}>
      <Typography variant="h1Serif" color={typographyColor}>
        {currentNode?.title}
      </Typography>

      {currentNode?.body && (
        <Typography variant="body" mt={6} color={typographyColor}>
          {currentNode?.body}
        </Typography>
      )}
    </Box>
  );
};
