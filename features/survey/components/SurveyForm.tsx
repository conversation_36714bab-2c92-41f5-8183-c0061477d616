import {
  Survey,
  SurveyAnswer,
  SurveyContextNode,
  SurveyCustomValues,
  SurveyNode,
  SurveyQuestionNode,
} from "@vivantehealth/vivante-core";
import { Box, Paper, TextField, Typography } from "@mui/material";
import Markdown from "react-markdown";

import { appStrings } from "@Assets/app_strings";
import { Form } from "@Components/form/Form";

import { DateField } from "./DateField";
import { HeightInputForm } from "./HeightInputForm";
import { NumberInputForm } from "./NumberInputForm";
import { SurveyActions } from "./SurveyActions";
import { SurveyFields } from "./SurveyFields";
import { SurveyTitleSection } from "./SurveyTitleSection";
import { SurveyAnswerWithCustomValues } from "../SurveyContainer";
import {
  isImperialHeight,
  isImperialWeight,
  isSurveyNodeWithImperialHeight,
  isSurveyNodeWithImperialWeight,
  isSurveyNodeWithNumberExtra,
  isSurveyQuestionOption,
} from "../utils/survey.typeguards";
import { getStringArrayValue, getNumberValue } from "../utils/survey.utils";

type SurveyFormProps = Readonly<{
  actions: { uri?: string; title: string }[] | undefined;
  answer: SurveyAnswerWithCustomValues;
  isContextual: boolean;
  isIntakeSurvey: boolean;
  survey: Survey;
  loadPrevious: () => void;
  setAnswer: (data: SurveyAnswerWithCustomValues) => void;
  submitSurvey: () => void;
}>;

export const SurveyForm = ({
  actions,
  answer,
  isContextual,
  survey,
  loadPrevious,
  setAnswer,
  submitSurvey,
  isIntakeSurvey,
}: SurveyFormProps) => {
  return (
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      height={isContextual && isIntakeSurvey ? "100%" : undefined}
    >
      <Box width="100%">
        <SurveyTitleSection
          currentNode={survey.currentNode}
          isContextual={isContextual}
          isIntakeSurvey={isIntakeSurvey}
          actions={actions}
          answer={answer}
          survey={survey}
          loadPrevious={loadPrevious}
          submitSurvey={submitSurvey}
        />

        <Form onSubmit={submitSurvey}>
          <RenderSurveyFormFields
            survey={survey}
            answer={answer?.answerValues}
            customAnswer={answer?.customValues}
            setAnswer={setAnswer}
          />

          {survey.currentNode && (
            <RenderExtraInfoOrActions
              currentNode={survey.currentNode}
              actions={actions}
              answer={answer}
              survey={survey}
              loadPrevious={loadPrevious}
              isContextual={isContextual}
              isIntakeSurvey={isIntakeSurvey}
            />
          )}
        </Form>
      </Box>
    </Box>
  );
};

type RenderExtraInfoOrActionsProps = Pick<
  SurveyFormProps,
  "actions" | "answer" | "survey" | "isIntakeSurvey" | "loadPrevious" | "isContextual"
> & {
  currentNode: SurveyNode;
};

const RenderExtraInfoOrActions = ({
  currentNode,
  actions,
  answer,
  survey,
  isContextual,
  isIntakeSurvey,
  loadPrevious,
}: RenderExtraInfoOrActionsProps) => {
  if (currentNode instanceof SurveyContextNode && currentNode.extraInfo) {
    return (
      <Paper sx={{ display: "flex", flexDirection: "column", gap: 2, p: 5 }}>
        <Markdown
          components={{
            h1: ({ children }) => (
              <Typography variant="h1Serif" mb={4}>
                {children}
              </Typography>
            ),
            h2: ({ children }) => (
              <Typography variant="h2Serif" mb={4}>
                {children}
              </Typography>
            ),
            h3: ({ children }) => <Typography variant="h3Serif">{children}</Typography>,
            h4: ({ children }) => <Typography variant="h4">{children}</Typography>,
            p: ({ children }) => (
              <Typography variant="body" mb={4}>
                {children}
              </Typography>
            ),
            li: ({ children }) => (
              <li>
                <Typography variant="body">{children}</Typography>
              </li>
            ),
          }}
        >
          {currentNode.extraInfo}
        </Markdown>
      </Paper>
    );
  }

  return (
    <SurveyActions
      actions={actions}
      answer={answer}
      survey={survey}
      isContextNode={isContextual}
      isIntakeSurvey={isIntakeSurvey}
      loadPrevious={loadPrevious}
    />
  );
};

type RenderSurveyFormFieldsProps = Readonly<{
  survey: Survey;
  answer: SurveyAnswer | undefined;
  customAnswer: SurveyCustomValues | undefined;
  setAnswer: (data: SurveyAnswerWithCustomValues) => void;
}>;

const RenderSurveyFormFields = ({ survey, answer, setAnswer, customAnswer }: RenderSurveyFormFieldsProps) => {
  const { currentNode } = survey;

  if (!currentNode || !(currentNode instanceof SurveyQuestionNode)) {
    return null;
  }

  const answerValues = answer || currentNode.answer;
  const customValues = customAnswer || currentNode.customValues;

  if (currentNode.type === "multiple") {
    return (
      <SurveyFields
        variant="checkbox"
        answers={getStringArrayValue(answerValues)}
        customAnswers={customValues ? { customValues } : undefined}
        setAnswer={setAnswer}
        options={isSurveyQuestionOption(currentNode.extra) ? currentNode.extra.options : []}
      />
    );
  }

  if (currentNode.type === "date") {
    return (
      <DateField
        answers={{ answerValues: answerValues instanceof Date ? answerValues : new Date() }}
        setAnswer={setAnswer}
      />
    );
  }

  if (currentNode.type === "string") {
    return (
      <TextField
        value={answerValues}
        minRows={3}
        onChange={(e) =>
          setAnswer({
            answerValues: e.target.value ? e.target.value : " ",
          })
        }
        autoFocus
        fullWidth
        multiline
      />
    );
  }

  if (currentNode.type === "number") {
    const minMaxProps = isSurveyNodeWithNumberExtra(currentNode)
      ? { minimum: currentNode.extra.minimum, maximum: currentNode.extra.maximum }
      : {};

    return (
      <NumberInputForm
        {...minMaxProps}
        setAnswer={setAnswer}
        aria-label={currentNode?.title}
        value={getNumberValue(answerValues)}
      />
    );
  }

  if (currentNode.type === "imperial-height") {
    const minMaxProps = isSurveyNodeWithImperialHeight(currentNode)
      ? { minimum: currentNode.extra.minimum, maximum: currentNode.extra.maximum }
      : {};

    return (
      <HeightInputForm
        {...minMaxProps}
        setAnswer={setAnswer}
        answers={{ answerValues: isImperialHeight(answerValues) ? answerValues : { feet: 5, inches: 6 } }}
      />
    );
  }

  if (currentNode.type === "imperial-weight") {
    const minMaxProps = isSurveyNodeWithImperialWeight(currentNode)
      ? { minimum: currentNode.extra.minimum?.pounds, maximum: currentNode.extra.maximum?.pounds }
      : {};

    return (
      <NumberInputForm
        variant="weight"
        aria-label={appStrings.features.survey.ariaLabels.weightInput}
        value={isImperialWeight(answerValues) ? answerValues?.pounds : null}
        {...minMaxProps}
        setAnswer={setAnswer}
      />
    );
  }

  return (
    <SurveyFields
      variant="radio"
      answers={getStringArrayValue(answerValues)}
      customAnswers={customValues ? { customValues } : undefined}
      setAnswer={setAnswer}
      options={isSurveyQuestionOption(currentNode.extra) ? currentNode.extra.options : []}
    />
  );
};
