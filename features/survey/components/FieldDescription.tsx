import { useState } from "react";
import { Button, IconButton, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { BaseModal } from "@Components/BaseModal/BaseModal";

type FieldDescriptionProps = Readonly<{
  text: string;
  title?: string;
}>;

export const FieldDescription = ({ text, title }: FieldDescriptionProps) => {
  const [modalOpen, setModalOpen] = useState(false);

  return (
    <>
      <IconButton
        onClick={() => setModalOpen(true)}
        size="small"
        aria-label={appStrings.features.survey.ariaLabels.informationDescription(title)}
      >
        <AppIcon name="Info" color={color.icon.subtle} />
      </IconButton>

      <BaseModal
        isModalOpen={modalOpen}
        displayCloseButton={false}
        onClose={() => setModalOpen(false)}
        bodyContent={<Typography variant="body">{text}</Typography>}
        actions={
          <Button variant="primary" fullWidth onClick={() => setModalOpen(false)}>
            {appStrings.buttonText.dismiss}
          </Button>
        }
      />
    </>
  );
};
