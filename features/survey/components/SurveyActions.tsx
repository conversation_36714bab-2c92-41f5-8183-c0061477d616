import { useSelector } from "react-redux";
import { Survey, SurveyQuestionNode, SurveyTerminalNode } from "@vivantehealth/vivante-core";
import { Box, Button, CircularProgress } from "@mui/material";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { memberStateSelector } from "@Features/member/store/memberStateSlice";

import { useSurveyUriNavigation } from "../hooks/useSurveyUriNavigation";
import { SurveyAnswerWithCustomValues } from "../SurveyContainer";
import { isInvalidImperialHeight, isInvalidImperialWeight, isInvalidNumber } from "../utils/survey.utils";

const BUTTON_STRINGS = appStrings.buttonText;
const MAX_BUTTON_WIDTH = "256px";
const INTAKE_SURVEY_TERMINAL_NODE_VARIANTS = ["t.survey_intake_completed", "t.survey_intake_completed_hc_cta"];

type SurveyActionsProps = Readonly<{
  actions: { uri?: string; title: string }[] | undefined;
  answer: SurveyAnswerWithCustomValues;
  survey: Survey;
  isContextNode: boolean;
  isIntakeSurvey: boolean;
  loadPrevious: () => void;
  submitSurvey?: () => void;
}>;

export const SurveyActions = ({
  actions,
  answer,
  survey,
  isContextNode,
  isIntakeSurvey,
  loadPrevious,
  submitSurvey,
}: SurveyActionsProps) => {
  const { handleSurveyUriNavigation } = useSurveyUriNavigation();
  const { currentNode } = survey;
  const isTerminalNode = currentNode?.constructor === SurveyTerminalNode;
  const isSurveyNode = !isContextNode && !isIntakeSurvey && !isTerminalNode;
  const member = useSelector(memberStateSelector("member"));
  const showCircularProgress = member?.setting?.onboardingPending;
  const isIntakeSurveyCompletedNode = INTAKE_SURVEY_TERMINAL_NODE_VARIANTS.includes(currentNode?.id);

  if (isTerminalNode && !isContextNode) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" gap={3} mt={6}>
        {actions?.map((action, index) => {
          return (
            <Button
              key={action.title}
              variant={index === 0 ? "primary" : "secondary"}
              onClick={() => {
                if (action.uri) {
                  handleSurveyUriNavigation(action.uri);
                }
              }}
              disabled={showCircularProgress}
              fullWidth
              sx={{ maxWidth: MAX_BUTTON_WIDTH }}
            >
              {showCircularProgress ? <CircularProgress size={24} color="inherit" /> : action.title}
            </Button>
          );
        })}
      </Box>
    );
  }

  return isIntakeSurvey || isSurveyNode ? (
    <Box display="flex" justifyContent="center" alignItems="center" gap={3} mt={6} width={"100%"}>
      {actions ? (
        (isIntakeSurveyCompletedNode ? [...actions].reverse() : actions).map((action, index) => {
          return (
            <Button
              key={action.title}
              variant={index === 0 ? "intakeSecondary" : "intakePrimary"}
              onClick={() => {
                if (action.uri) {
                  handleSurveyUriNavigation(action.uri, action.title, survey.id);
                }
              }}
              disabled={showCircularProgress}
              fullWidth
              sx={{ ...(isIntakeSurveyCompletedNode ? {} : { maxWidth: MAX_BUTTON_WIDTH }) }}
            >
              {showCircularProgress ? <CircularProgress size={24} color="inherit" /> : action.title}
            </Button>
          );
        })
      ) : (
        <>
          {survey.previousNodeId && (
            <Button
              variant={isContextNode ? "intakeSecondary" : "secondary"}
              onClick={loadPrevious}
              fullWidth
              sx={{ maxWidth: MAX_BUTTON_WIDTH }}
            >
              {BUTTON_STRINGS.back}
            </Button>
          )}

          <Button
            variant={isContextNode ? "intakePrimary" : "primary"}
            type={submitSurvey ? "button" : "submit"}
            onClick={submitSurvey || undefined}
            disabled={currentNode?.constructor !== SurveyQuestionNode ? false : calculateDisabled(answer, currentNode)}
            fullWidth
            sx={{ maxWidth: MAX_BUTTON_WIDTH }}
          >
            {BUTTON_STRINGS.next}
          </Button>
        </>
      )}
    </Box>
  ) : null;
};

const calculateDisabled = (answer: SurveyAnswerWithCustomValues, currentNode: SurveyQuestionNode) => {
  const values = answer?.answerValues || currentNode?.answer;

  switch (currentNode.type) {
    case "multiple":
      return !values || (Array.isArray(values) && values.length === 0);
    case "single":
    case "string":
      return !values || (typeof values === "string" && values.trim() === "");
    case "imperial-weight":
      return isInvalidImperialWeight(values, currentNode);
    case "number":
      return isInvalidNumber(values, currentNode);
    case "imperial-height":
      return isInvalidImperialHeight(values, currentNode);
    case "date":
      return !values || ((typeof values === "string" || values instanceof Date) && !dayjs(values)?.isValid());
    default:
      return false;
  }
};
