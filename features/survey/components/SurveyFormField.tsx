import { ChangeEventHandler } from "react";
import { Box, Checkbox, FormControlLabel, OutlinedInput, Radio, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { RADIUS_16_PX } from "@Assets/style_constants";
import { EnumFieldVariant } from "@Features/witch/components/WitchEnumField";

import { FieldDescription } from "./FieldDescription";

type SurveyFormFieldProps = Readonly<{
  variant: EnumFieldVariant;
  selected: boolean;
  title: string;
  allowsCustomResponse?: boolean;
  textInputValue: string;
  description?: string;
  handleTextInputPress?: () => void;
  onClick: () => void;
  setTextInputValue: ChangeEventHandler<HTMLInputElement | HTMLTextAreaElement>;
}>;

export const SurveyFormField = ({
  variant,
  selected,
  title,
  allowsCustomResponse,
  textInputValue,
  description,
  onClick,
  setTextInputValue,
}: SurveyFormFieldProps) => {
  return (
    <>
      <FormControlLabel
        value={title}
        checked={selected || false}
        onChange={onClick}
        control={
          variant === "radio" ? (
            <Radio size="small" sx={{ mr: 2 }} inputProps={{ "aria-label": title }} disableRipple />
          ) : (
            <Checkbox size="small" sx={{ p: 2 }} inputProps={{ "aria-label": title }} disableRipple />
          )
        }
        label={
          <Box display="flex" width="100%" justifyContent="space-between" alignItems="center" tabIndex={-1}>
            <Typography variant="body" tabIndex={-1} textAlign="left">
              {title}
            </Typography>

            {description ? <FieldDescription text={description} title={title} /> : null}
          </Box>
        }
        slotProps={{ typography: { tabIndex: -1 } }}
        sx={{
          px: 4,
          py: 3,
          m: 0,
          border: `1px solid ${selected ? color.border.action.brand : color.border.default}`,
          borderRadius: RADIUS_16_PX,
          backgroundColor: color.background.surface.primary,
          ":hover": { borderColor: color.border.action.brand },
          ".MuiFormControlLabel-label": { width: "100%" },
        }}
      />

      {allowsCustomResponse && selected ? (
        <OutlinedInput
          onChange={setTextInputValue}
          defaultValue={textInputValue}
          autoFocus
          aria-label={appStrings.a11y.surveyOtherField(title)}
        />
      ) : null}
    </>
  );
};
