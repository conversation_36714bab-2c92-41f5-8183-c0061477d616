import { DatePicker } from "@mui/x-date-pickers";
import { color } from "@vivantehealth/design-tokens";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";

import { SurveyAnswerWithCustomValues } from "../SurveyContainer";

type DateFieldProps = Readonly<{
  answers: { answerValues: Date };
  setAnswer: (answer: SurveyAnswerWithCustomValues) => void;
}>;

export const DateField = ({ answers, setAnswer }: DateFieldProps) => {
  return (
    <DatePicker
      defaultValue={answers?.answerValues ? dayjs(answers?.answerValues) : undefined}
      onChange={(selectedDate) => {
        if (selectedDate) {
          setAnswer({ answerValues: selectedDate.toDate() });
        }
      }}
      slots={{
        openPickerIcon: () => <AppIcon name="Calendar" color={color.icon.strong} />,
      }}
      slotProps={{
        textField: {
          fullWidth: true,
          sx: { ".MuiOutlinedInput-input": { paddingLeft: 0 } },
          placeholder: appStrings.features.survey.datePlaceholder,
        },
        inputAdornment: {
          position: "start",
        },
        day: {
          sx: {
            root: {
              backgroundColor: "transparent",
            },
            "&.Mui-selected": {
              backgroundColor: "transparent",
              border: `1px solid ${color.border.action.hover}`,
              ":hover": {
                background: "transparent",
              },
              ":focus": {
                background: "transparent",
              },
            },
            ":hover": {
              backgroundColor: "transparent",
            },
            "&.MuiPickersDay-today": { backgroundColor: color.background.surface.secondary, border: "none" },
          },
        },
      }}
    />
  );
};
