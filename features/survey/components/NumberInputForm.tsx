import * as React from "react";
import { Unstable_NumberInput as BaseNumberInput, NumberInputProps } from "@mui/base/Unstable_NumberInput";
import { Box, InputLabel, styled } from "@mui/material";
import { color, typography } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { RADIUS_12_PX, SPACING_12_PX } from "@Assets/style_constants";

import { SurveyAnswerWithCustomValues } from "../SurveyContainer";

const NumberInput = React.forwardRef(function CustomNumberInput(
  props: NumberInputProps,
  ref: React.ForwardedRef<HTMLDivElement>,
) {
  return (
    <BaseNumberInput
      slots={{
        input: StyledInput,
      }}
      slotProps={{
        incrementButton: {
          style: {
            display: "none",
          },
        },
        decrementButton: {
          style: {
            display: "none",
          },
        },
        input: {
          style: {
            ...typography.body,
            borderRadius: RADIUS_12_PX,
          },
        },
      }}
      {...props}
      ref={ref}
    />
  );
});

type NumberInputFormProps = Readonly<{
  value: number | null;
  minimum?: number;
  maximum?: number;
  variant?: "weight";
  "aria-label": React.ComponentProps<"button">["aria-label"];
  setAnswer: (answer: SurveyAnswerWithCustomValues) => void;
}>;

export const NumberInputForm = ({
  value,
  minimum,
  maximum,
  variant,
  "aria-label": ariaLabel,
  setAnswer,
}: NumberInputFormProps) => {
  const handleChange = (val: number) => {
    return setAnswer({
      answerValues: variant === "weight" ? { pounds: val } : val,
    });
  };

  const input: HTMLCollectionOf<Element> = document.getElementsByClassName("MuiNumberInput-input");

  React.useEffect(() => {
    if (input[0] instanceof HTMLInputElement) {
      input[0].focus();
    }
  }, [input]);

  return (
    <Box>
      {variant === "weight" ? <InputLabel>{appStrings.features.survey.weight}</InputLabel> : null}
      <NumberInput
        aria-label={ariaLabel}
        defaultValue={value}
        min={minimum}
        max={maximum}
        onChange={(event, val) => {
          if (val != null) {
            handleChange(val);
          }
        }}
        onInputChange={(event) => handleChange(Number(event.target.value))}
      />
    </Box>
  );
};

const StyledInput = styled("input")(
  `
  border: 1px solid ${color.border.input.default};
  backgroundColor: color.background.input.default;
  padding: ${SPACING_12_PX};
  width: 100%;

    &:hover {
      border-color: ${color.border.input.focused};
    }

    &:focus {
      border-color: ${color.border.input.focused};
    }

    &:focus-visible {
      outline: 0;
    }
`,
);
