import { useDispatch, useSelector } from "react-redux";
import { Grid } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { SPACING_40_PX, styleConstants } from "@Assets/style_constants";
import { HeaderContainer } from "@Components/Header/HeaderContainer";
import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { NavigationDrawer } from "@Components/Navigation/NavigationDrawer";
import { navigationStateSelector, setNavDrawerOpen } from "@Features/navigation/store/navigationStateSlice";
import { updatePageTitle } from "@Features/navigation/utils/updatePageTitle";
import {
  CLASSIC_SURVEY_TITLES,
  INTAKE_SURVEY_IDS,
  INTAKE_SURVEY_PINK_MOTIF_IDS,
  INTAKE_SURVEY_YELLOW_MOTIF_IDS,
} from "@Features/survey/assets/constants";
import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";
import { useDelayedLoader } from "@Hooks/useDelayedLoader";
import { useHeaderHook } from "@Hooks/useHeaderHook";

type DotMotifColor = "blue" | "maroon" | "purple" | "yellow" | "white";

type DotMotifImageUrl = `${DotMotifColor}_dot_motif`;

const CONTEXT_SCREEN_DETAILS: Record<string, { backgroundColor: string; imageUrl: DotMotifImageUrl }> = {
  "c.health_history": { backgroundColor: color.palette.maroon[200], imageUrl: "maroon_dot_motif" },
  "c.symptoms": { backgroundColor: color.palette.blue[200], imageUrl: "blue_dot_motif" },
};

INTAKE_SURVEY_PINK_MOTIF_IDS.forEach((title) => {
  CONTEXT_SCREEN_DETAILS[title] = { backgroundColor: color.palette.pink[200], imageUrl: "purple_dot_motif" };
});

[...CLASSIC_SURVEY_TITLES, ...INTAKE_SURVEY_YELLOW_MOTIF_IDS].forEach((title) => {
  CONTEXT_SCREEN_DETAILS[title] = { backgroundColor: color.palette.yellow[200], imageUrl: "yellow_dot_motif" };
});

/**
 * TODO: This function is currently rather fragile. We are dependent on the backend either not changing existing survey IDs/titles
 * or introducing new survey IDs/titles without letting us know. We should seek to consult with the backend team(s) to determine
 * a better approach moving forward post Rebrand.
 */
const getBackgroundDetails = (
  isIntakeSurvey: boolean,
  surveyId = "",
): { backgroundColor: string; imageUrl?: DotMotifImageUrl } => {
  // We use the default background color if it's not an intake survey or there is no surveyId supplied
  if (!isIntakeSurvey || surveyId.length === 0) {
    return { backgroundColor: color.background.page };
  }

  // If the surveyId is not found in the lookup table, we return the default background color
  return CONTEXT_SCREEN_DETAILS[surveyId] ?? { backgroundColor: color.background.page };
};

type SurveyNavigationWrapperProps = Readonly<{
  children: React.ReactNode;
  isContextNode: boolean;
  isIntakeSurvey: boolean;
  showLoadingSpinner: boolean;
  surveyId?: string;
}>;

export const SurveyNavigationWrapper = ({
  children,
  isContextNode,
  showLoadingSpinner,
  isIntakeSurvey,
  surveyId,
}: SurveyNavigationWrapperProps) => {
  const dispatch = useDispatch();
  const loaderStatus = useDelayedLoader(showLoadingSpinner);
  const { contextWidth, formWidth } = useResponsiveStylesHook(isIntakeSurvey);
  const activeNavOption = useSelector(navigationStateSelector("activeNavOption"));
  const navDrawerOpen = useSelector(navigationStateSelector("navDrawerOpen"));
  const navDrawerOpenCallback = (open: boolean) => {
    dispatch(setNavDrawerOpen(open));
  };
  const { contentHeight } = useHeaderHook();

  /**
   * For new intake surveys, a context screen id beings with "c." while a terminal node begins with "t."
   * In this case, we want to consider t.survey_intake_completed as still being an intakeContextScreen
   * Classic intake survey uses titles (CLASSIC_SURVEY_TITLES) instead of IDs and we check those as well
   * */
  const isIntakeContextScreen = surveyId ? [...INTAKE_SURVEY_IDS, ...CLASSIC_SURVEY_TITLES].includes(surveyId) : false;
  const { backgroundColor, imageUrl } = getBackgroundDetails(isIntakeSurvey || isIntakeContextScreen, surveyId);
  const surveyImageUrl = `/images/surveyBackgrounds/${imageUrl}`;

  return (
    <>
      <HeaderContainer isIntakeSurvey={isIntakeSurvey} />
      <NavigationDrawer
        activeNavOption={activeNavOption}
        navDrawerOpenCallback={navDrawerOpenCallback}
        navDrawerOpen={navDrawerOpen}
        hide={isIntakeSurvey || isIntakeContextScreen}
        contentHeight={contentHeight}
        updatePageTitle={updatePageTitle}
      >
        {loaderStatus === "DELAYED" && null}

        {["SHOW_LOADER", "MINIMUM_TIME_SHOWN"].includes(loaderStatus) && (
          <LoadingSpinner open overlay={isIntakeSurvey} overlayDrawer={isIntakeSurvey} overlayHeader={false} />
        )}

        {loaderStatus === "SHOW_CONTENT" && (
          <Grid
            container
            justifyContent="center"
            sx={{
              overflow: isIntakeContextScreen ? "hidden" : "auto",
              backgroundColor,
              // Remove top padding from NavigationDrawer parent when we display a color other than the default for the background
              marginTop: isIntakeContextScreen ? `-${SPACING_40_PX}` : undefined,
              height: isIntakeContextScreen ? styleConstants.contentHeightIntakeSurvey : "auto",
              position: "relative",
            }}
          >
            <Grid
              item
              mb={7}
              maxWidth={!isContextNode ? styleConstants.contentMaxWidth : contextWidth}
              width={!isContextNode ? formWidth : contextWidth}
              px={{
                xs: 2,
                sm: isContextNode ? 4 : 0,
                lg: isContextNode ? 2 : 0,
              }}
            >
              {isIntakeContextScreen && imageUrl ? (
                <>
                  <img
                    src={`${surveyImageUrl}_550.webp`}
                    srcSet={`${surveyImageUrl}_300.webp 300w, ${surveyImageUrl}_550.webp 550w`}
                    sizes="(max-width: 900px) 53vw, 550px"
                    style={{ position: "absolute", left: 0, top: 0 }}
                    alt={appStrings.features.survey.dotMotifAltText("top")}
                  />
                  <img
                    src={`${surveyImageUrl}_550.webp`}
                    srcSet={`${surveyImageUrl}_300.webp 300w, ${surveyImageUrl}_550.webp 550w`}
                    sizes="(max-width: 900px) 53vw, 550px"
                    style={{
                      position: "absolute",
                      right: 0,
                      bottom: 0,
                      transform: "rotate(180deg)",
                    }}
                    alt={appStrings.features.survey.dotMotifAltText("bottom")}
                  />
                </>
              ) : null}
              {children}
            </Grid>
          </Grid>
        )}
      </NavigationDrawer>
    </>
  );
};
