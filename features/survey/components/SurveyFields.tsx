import { SurveyCustomValues, SurveyQuestionOption } from "@vivantehealth/vivante-core";
import { Box } from "@mui/material";

import { SurveyFormField } from "./SurveyFormField";
import { SurveyAnswerWithCustomValues } from "../SurveyContainer";

type SurveyFieldsProps = Readonly<{
  options: SurveyQuestionOption[];
  customAnswers?: { customValues: SurveyCustomValues };
  answers: { answerValues: string[] };
  variant: "checkbox" | "radio";
  setAnswer: (data: SurveyAnswerWithCustomValues) => void;
}>;

export const SurveyFields = ({ options, answers, customAnswers, variant, setAnswer }: SurveyFieldsProps) => {
  const { answerValues } = answers;
  const { customValues } = customAnswers ?? { customValues: {} };
  const isMultiple = variant === "checkbox";

  const isExclusiveOption = (val: string) => {
    return options.find((o) => o.id === val)?.isExclusive;
  };

  const handleCheckboxChange = (option: SurveyQuestionOption, isChecked: boolean) => {
    const updatedCustomAnswers = updateCustomAnswers(option, isChecked);
    const existingValues = { ...answers, ...updatedCustomAnswers };

    if (!answerValues) {
      return setAnswer({ answerValues: [option.id], ...updatedCustomAnswers });
    }

    return !answerValues.includes(option.id)
      ? setAnswer(
          option.isExclusive
            ? { answerValues: [option.id], customValues: {} }
            : {
                ...existingValues,
                answerValues: [...answerValues, option.id].filter((val) => !isExclusiveOption(val)),
              },
        )
      : setAnswer({
          ...existingValues,
          answerValues: answerValues.filter((o: string) => o !== option.id),
        });
  };

  const handleClick = (option: SurveyQuestionOption) => {
    setAnswer({
      answerValues: option.id,
      customValues: !option.allowsCustomValue ? {} : { ...customValues },
    });
  };

  const handleInputChange = (val: string, optionId: string) => {
    setAnswer({
      answerValues: isMultiple ? [...answerValues] : answerValues,
      customValues: { ...customValues, [optionId]: val },
    });
  };

  const updateCustomAnswers = (option: SurveyQuestionOption, isChecked: boolean) => {
    const updatedCustomAnswers = { ...customAnswers };

    if (option.allowsCustomValue && isChecked) {
      if (updatedCustomAnswers?.customValues) {
        delete updatedCustomAnswers?.customValues[option.id];
      }
    }

    return updatedCustomAnswers;
  };

  return (
    <Box display="flex" flexDirection="column" gap={3}>
      {options?.map((option) => {
        const isChecked = isMultiple
          ? answerValues?.includes(option.id)
          : answerValues && option.id === String(answerValues);

        return (
          <SurveyFormField
            key={option.title}
            variant={variant}
            selected={isChecked}
            title={option.title}
            allowsCustomResponse={option.allowsCustomValue}
            description={option.description}
            textInputValue={customValues ? customValues[option.id] : ""}
            setTextInputValue={(event: React.ChangeEvent<HTMLInputElement>) =>
              handleInputChange(event.target.value, option.id)
            }
            onClick={() => (isMultiple ? handleCheckboxChange(option, isChecked) : handleClick(option))}
          />
        );
      })}
    </Box>
  );
};
