import { ImperialHeight } from "@vivantehealth/vivante-core";
import { Box, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { NumberStepper } from "@Components/form/NumberStepper";

import { SurveyAnswerWithCustomValues } from "../SurveyContainer";

const SURVEY_STRINGS = appStrings.features.survey;

type HeightInputFormProps = Readonly<{
  answers: { answerValues: ImperialHeight };
  minimum?: ImperialHeight;
  maximum?: ImperialHeight;
  setAnswer: (data: SurveyAnswerWithCustomValues) => void;
}>;

export const HeightInputForm = ({ answers, minimum, maximum, setAnswer }: HeightInputFormProps) => {
  const { answerValues } = answers;

  return (
    <>
      <Box mb={6}>
        <Typography variant="body" textAlign="left" ml={8} mb={1}>
          {SURVEY_STRINGS.feet}
        </Typography>

        <NumberStepper
          ariaLabel={SURVEY_STRINGS.ariaLabels.feetInput}
          value={answerValues?.feet ?? 5}
          minimum={minimum?.feet ?? 2}
          maximum={maximum?.feet ?? 8}
          onChange={(_event, val) => {
            if (val != null) {
              setAnswer({
                answerValues: { feet: val, inches: answerValues?.inches },
              });
            }
          }}
          onInputChange={(event) =>
            setAnswer({
              answerValues: {
                feet: Number(event.target.value),
                inches: answerValues?.inches,
              },
            })
          }
          numberFor="Feet"
        />
      </Box>

      <Box>
        <Typography variant="body" textAlign="left" ml={8} mb={1}>
          {SURVEY_STRINGS.inches}
        </Typography>

        <NumberStepper
          ariaLabel={SURVEY_STRINGS.ariaLabels.inchesInput}
          value={answerValues?.inches ?? 6}
          minimum={minimum?.inches ?? 0}
          maximum={answerValues?.feet === maximum?.feet ? maximum?.inches : 11}
          onChange={(_event, val) => {
            if (val != null) {
              setAnswer({
                answerValues: { feet: answerValues?.feet, inches: val },
              });
            }
          }}
          onInputChange={(event) =>
            setAnswer({
              answerValues: {
                feet: answerValues?.feet,
                inches: Number(event.target.value),
              },
            })
          }
          numberFor="Inches"
        />
      </Box>
    </>
  );
};
