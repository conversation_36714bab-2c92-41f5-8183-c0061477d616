import { Survey, SurveyAnswer, SurveyCustomValues, VivanteLinkResource } from "@vivantehealth/vivante-core";

export type SurveyAction = {
  type: "loadSurveyByLink";
  payload: VivanteLinkResource;
};

export type SubmitSurveyAction = {
  type: "answerSurvey";
  payload: {
    survey: Survey;
    answer: SurveyAnswer;
    customValues: SurveyCustomValues;
  };
};

export type LoadPreviousSurveyNodeAction = {
  type: "loadSurveyWithPreviousNode";
};
