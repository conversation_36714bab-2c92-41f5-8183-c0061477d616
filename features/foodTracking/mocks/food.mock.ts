import { FoodSource } from "@vivantehealth/vivante-core";

import { ServingSize } from "../utils/generateServingSizeOptions";

export type MOCKED_FOOD_TYPE = {
  title: string;
  subtitle: string;
  source: FoodSource;
  numberOfServings?: number;
  servingSizes?: ServingSize[];
};

export const MOCKED_FOOD: MOCKED_FOOD_TYPE[] = [
  {
    title: "Salmon",
    subtitle: "Morey's Seafood, 1 cup",
    source: FoodSource.PASSIO_FOOD,
    numberOfServings: 1,
    servingSizes: [
      { unitName: "cup", quantity: 1 },
      { unitName: "oz", quantity: 4 },
    ],
  },
  {
    title: "Cheddar cheese cubes",
    subtitle: "Kraft, .5 cup",
    source: FoodSource.PASSIO_FOOD,
    numberOfServings: 2,
    servingSizes: [
      { unitName: "cup", quantity: 0.5 },
      { unitName: "oz", quantity: 4 },
    ],
  },
  {
    title: "Iced caramel macchiato",
    subtitle: "Starbucks, 12 oz",
    source: FoodSource.CUSTOM_USER_FOOD,
  },
  {
    title: "Bacon gouda sandwhich",
    subtitle: "Starbucks 1 sandwich",
    source: FoodSource.CUSTOM_USER_FOOD,
  },
  {
    title: "Catfish",
    subtitle: "Morey's Seafood, 1 cup",
    source: FoodSource.CUSTOM_USER_FOOD,
  },
  {
    title: "Carrots",
    subtitle: "Frash Prodooz, 1 cuph",
    source: FoodSource.CUSTOM_USER_FOOD,
  },
  {
    title: "Apple Fritter",
    subtitle: "Voodoo Doughnut, 1 cup",
    source: FoodSource.CUSTOM_USER_RECIPE,
  },
  {
    title: "Chia cheese stick",
    subtitle: "Kraft, .5 cup",
    source: FoodSource.CUSTOM_USER_FOOD,
  },
];
