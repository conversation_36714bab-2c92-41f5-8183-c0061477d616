import { Food, FoodSource } from "@vivantehealth/vivante-core";
import { describe, expect, test } from "vitest";

import { normalizePassioFoodItems } from "./normalizePassioFoodItems";
import { PassioFoodResult } from "../api/foodLogApi";

describe("normalizePassioFoodItems", () => {
  test("Should return an empty array if passioFoodItems is empty", () => {
    const result = normalizePassioFoodItems([]);

    expect(result).toEqual([]);
  });

  test("Should normalize passioFoodItems into Food objects", () => {
    const passioFoodItems: PassioFoodResult[] = [
      {
        type: "reference",
        displayName: "Quest, Mint Chocolate Chunk Protein Bar",
        shortName: "",
        longName: "Quest, Mint Chocolate Chunk Protein Bar",
        scoredName: "Quest, Mint Chocolate Chunk Protein Bar",
        score: 0.8699999999999999,
        displayNameScore: 0.8699999999999999,
        brandName: "QUEST",
        iconId: "",
        labelId: "",
        synonymId: "",
        recipeId: "",
        referenceId: "1603211499946",
        resultId: "1603211499946",
        nutritionPreview: {
          portion: {
            weight: {
              unit: "g",
              value: 60,
            },
            name: "bar",
            quantity: 1,
          },
          calories: 200,
        },
      },
      {
        type: "synonym",
        displayName: "The Bakery, Banana Bread",
        shortName: "banana bread",
        longName: "The Bakery, Banana Bread",
        scoredName: "Banana Bread",
        score: 1,
        displayNameScore: 0.9120075680162567,
        brandName: "Wal-Mart Stores, Inc.",
        iconId: "BAK0100",
        labelId: "4c80265c-9ea3-11ea-a1d5-933a6a81717b",
        synonymId: "731eb964-7dd6-11eb-8ccb-7b75e74387f8",
        recipeId: "",
        referenceId: "1603211573534",
        resultId: "1603211573534",
        nutritionPreview: {
          portion: {
            weight: {
              unit: "g",
              value: 28,
            },
            name: "slice",
            quantity: 2,
          },
          calories: 190,
        },
      },
    ];
    const expected: Food[] = [
      {
        id: "1603211499946",
        name: "Quest, Mint Chocolate Chunk Protein Bar",
        brand: "QUEST",
        source: FoodSource.PASSIO_FOOD,
        meta: {
          passioId: "1603211499946",
          type: "reference",
          labelId: "",
          servingSizes: [
            {
              unitName: "bar",
              quantity: 1,
            },
          ],
          suggestedQuantity: [1],
          weight: {
            unit: "g",
            value: 60,
          },
          calories: 200,
        },
        servingSize: "1 Bar",
      },
      {
        id: "1603211573534",
        name: "The Bakery, Banana Bread",
        brand: "Wal-Mart Stores, Inc.",
        source: FoodSource.PASSIO_FOOD,
        meta: {
          passioId: "1603211573534",
          type: "synonym",
          labelId: "4c80265c-9ea3-11ea-a1d5-933a6a81717b",
          servingSizes: [
            {
              unitName: "slice",
              quantity: 2,
            },
          ],
          suggestedQuantity: [1],
          weight: {
            unit: "g",
            value: 28,
          },
          calories: 190,
        },
        servingSize: "2 Slice",
      },
    ];
    const result = normalizePassioFoodItems(passioFoodItems);

    expect(result).toEqual(expected);
  });
});
