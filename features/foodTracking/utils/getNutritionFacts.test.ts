import { PassioFoodMeta } from "@vivantehealth/vivante-core";
import { beforeEach, describe, expect, test, vi } from "vitest";

import { getNutritionFacts } from "./getNutritionFacts";
import { PassioDetailedFoodIngredient } from "../api/foodLogApi";

describe("getNutritionFacts", () => {
  beforeEach(() => {
    vi.mock("@Utils/logger", () => ({
      logger: vi.fn(),
    }));
  });

  const foodItemMeta: PassioFoodMeta = {
    passioId: "1603211199522",
    type: "synonym",
    labelId: "4329d822-2a08-11ed-9ce5-f63a81699d62",
    servingSizes: [
      {
        unitName: "slice",
        quantity: 1,
      },
    ],
    suggestedQuantity: [1],
    weight: {
      unit: "g",
      value: 100,
    },
    calories: 280,
  };

  const passioFoodData: PassioDetailedFoodIngredient = {
    id: "1603211199522",
    name: 'PIZZA HUT 12" Cheese Pizza, Pan Crust',
    nutrients: [
      {
        id: 1603211196545,
        nutrient: {
          name: "Total lipid (fat)",
          unit: "G",
          shortName: "fat",
        },
        amount: 12.56,
      },
      {
        id: 1603211196546,
        nutrient: {
          name: "Carbohydrate, by difference",
          unit: "G",
          shortName: "carbs",
        },
        amount: 29.93,
      },
      {
        id: 1603211196548,
        nutrient: {
          name: "Energy",
          unit: "KCAL",
          shortName: "calories",
        },
        amount: 280,
      },
      {
        id: 1603211196751,
        nutrient: {
          name: "Sugars, total including NLEA",
          unit: "G",
          shortName: "sugarTotal",
        },
        amount: 3.21,
      },
      {
        id: 1603211196688,
        nutrient: {
          name: "20:0",
          unit: "G",
        },
        amount: 0.025,
      },
      {
        id: 1603211196689,
        nutrient: {
          name: "18:1",
          unit: "G",
        },
        amount: 2.997,
      },
      {
        id: 1603211196544,
        nutrient: {
          name: "Protein",
          unit: "G",
          shortName: "protein",
        },
        amount: 11.73,
      },
      {
        id: 1603211196566,
        nutrient: {
          name: "Energy",
          unit: "kJ",
        },
        amount: 1170,
      },
      {
        id: 1603211196571,
        nutrient: {
          name: "Fiber, total dietary",
          unit: "G",
          shortName: "fiber",
        },
        amount: 1.7,
      },
      {
        id: 1603211196583,
        nutrient: {
          name: "Sodium, Na",
          unit: "MG",
          shortName: "sodium",
        },
        amount: 624,
      },
      {
        id: 1603211196677,
        nutrient: {
          name: "Cholesterol",
          unit: "MG",
          shortName: "cholesterol",
        },
        amount: 21,
      },
    ],
    portions: [
      {
        weight: {
          unit: "g",
          value: 100,
        },
        name: "slice",
        quantity: 1,
      },
      {
        weight: {
          unit: "g",
          value: 728,
        },
        name: "pizza",
        quantity: 1,
      },
      {
        weight: {
          unit: "kg",
          value: 0.728,
        },
        name: "pizzaKG",
        quantity: 1,
      },
    ],
    origin: [
      {
        source: "fdc",
        id: "172041",
        dataType: "sr_legacy_food",
        timestamp: "2020-10-20T16:26:58.492154Z",
      },
    ],
    qualityScore: "Medium",
    timestamp: "0001-01-01T00:00:00Z",
    tags: ["NOVA4", "meals"],
  };
  const entirePizzaNutrients = {
    calories: 2038.4,
    totalFat: 91.4368,
    totalCarbohydrates: 217.8904,
    sugars: 23.3688,
    protein: 85.3944,
    dietaryFiber: 12.376,
    sodium: 4542.72,
    cholesterol: 152.88,
  };

  test("should return the correct nutrition facts for 1 slice", () => {
    const nutritionFacts = getNutritionFacts(foodItemMeta, passioFoodData, "1 Slice");

    expect(nutritionFacts).toEqual({
      calories: 280,
      totalFat: 12.56,
      cholesterol: 21,
      sodium: 624,
      totalCarbohydrates: 29.93,
      sugars: 3.21,
      dietaryFiber: 1.7,
      protein: 11.73,
    });
  });

  test("should return the correct nutrition facts for 1 pizza", () => {
    const nutritionFacts = getNutritionFacts(foodItemMeta, passioFoodData, "1 Pizza");

    expect(nutritionFacts).toEqual(entirePizzaNutrients);
  });

  test("Should return the correct nutrition facts for 1 pizza in KG weight", () => {
    const nutritionFacts = getNutritionFacts(foodItemMeta, passioFoodData, "1 PizzaKG");

    expect(nutritionFacts).toEqual(entirePizzaNutrients);
  });
});
