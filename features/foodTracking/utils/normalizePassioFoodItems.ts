import { Food, FoodSource } from "@vivantehealth/vivante-core";

import { PassioFoodResult } from "../api/foodLogApi";
import { generateServingSizeString } from "../components/TrackAFoodDrawer/utils/foodTracking.utils";

export const normalizePassioFoodItems = (passioFoodItems: PassioFoodResult[]): Food[] => {
  return passioFoodItems.map((food) => {
    const servingSizes = [
      {
        unitName: food.nutritionPreview?.portion?.name.length > 0 ? food.nutritionPreview.portion.name : "serving",
        quantity: food.nutritionPreview?.portion?.quantity ?? 1,
      },
    ];

    return {
      id: food.resultId,
      name: food.displayName,
      brand: food.brandName,
      source: FoodSource.PASSIO_FOOD,
      meta: {
        passioId: food.resultId,
        type: food.type,
        labelId: food.labelId,
        servingSizes,
        suggestedQuantity: food.nutritionPreview.portion.suggestedQuantity ?? [1],
        weight: food.nutritionPreview.portion.weight,
        calories: food.nutritionPreview.calories,
      },
      servingSize: generateServingSizeString(servingSizes[0].quantity, servingSizes[0].unitName),
    };
  });
};
