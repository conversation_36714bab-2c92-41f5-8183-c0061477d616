import { NutrientFacts, PassioFoodMeta } from "@vivantehealth/vivante-core";

import { logger } from "@Utils/logger";

import { PassioNutrients, PassioSearchByFoodId } from "../api/foodLogApi";
import { generateServingSizeString } from "../components/TrackAFoodDrawer/utils/foodTracking.utils";
/** Default values provided by Passio: https://passio.gitbook.io/nutrition-api/response-usage-guide/models-and-usage#calculating-nutrient-amounts */
const DEFAULT_PASSIO_WEIGHT = 100;
const DEFAULT_PASSIO_UNIT = "g";
const DEFAULT_PASSIO_SUGGESTED_QAUNTITY = 1;
/**
 * We use this map to map the nutrient names from Passio to the nutrient names we track
 */
const PASSIO_NUTRIENTS_TO_TRACK_MAP = {
  Energy: "calories",
  "Total lipid (fat)": "totalFat",
  Cholesterol: "cholesterol",
  "Sodium, Na": "sodium",
  "Carbohydrate, by difference": "totalCarbohydrates",
  "Sugars, total including NLEA": "sugars",
  "Fiber, total dietary": "dietaryFiber",
  Protein: "protein",
} as const satisfies Record<string, keyof NutrientFacts>;

const isPassioNutrientName = (nutrientName: string): nutrientName is keyof typeof PASSIO_NUTRIENTS_TO_TRACK_MAP =>
  nutrientName in PASSIO_NUTRIENTS_TO_TRACK_MAP;

/**
 * We use this map as we can encounter multiple differing units for the same nutrient
 * in the nutrition facts from Passio. This will help to map to the correct unit
 */
const PASSIO_NUTRIENTS_UNIT_TO_TRACK_MAP = {
  calories: "KCAL",
  totalFat: "G",
  cholesterol: "MG",
  sodium: "MG",
  totalCarbohydrates: "G",
  sugars: "G",
  dietaryFiber: "G",
  protein: "G",
  addedSugars: "G",
  sugarAlcohols: "G",
} as const satisfies Record<keyof NutrientFacts, string>;

const GRAM_TO_OTHER_VALUE_CONVERSION = {
  g: 1,
  ml: 1,
  kg: 1000,
  mg: 0.001,
  oz: 0.035274,
  /** This is the weight of water in grams, we'll have to use a rough calculation here */
  cup: 236.588,
} as const;

const isConversionValue = (unit: string): unit is keyof typeof GRAM_TO_OTHER_VALUE_CONVERSION =>
  unit in GRAM_TO_OTHER_VALUE_CONVERSION;

const calculateWeightValue = (value: number, unit: string, passioFoodId?: string) => {
  if (isConversionValue(unit)) {
    const conversionValue = GRAM_TO_OTHER_VALUE_CONVERSION[unit];

    /** If the conversionValue is undefined, we will just use the value as is albeit may result in inaccurate conversion */
    return value * conversionValue;
  }

  /**
   * Log the conversion error if the value is not present in the conversion map
   * This will also log it to Sentry so we can keep track of any missing conversion values
   * and add them to the map. Passio does not provide a comprehensive list of conversion values
   * so we need to build it out over time.
   */
  logger.error(`Conversion value for ${unit} is not defined for Passio Food: ${passioFoodId}`);
  return value;
};

const calculateNutritionInformation = (
  weightValue: number,
  suggestedQuantity: number,
  nutrition?: PassioNutrients[],
) => {
  /**
   * Calculation provided by Passio (https://passio.gitbook.io/nutrition-api/response-usage-guide/models-and-usage#calculating-nutrient-amounts)
   */
  const nutritionValue = (nutrientAmount: number) => nutrientAmount * (weightValue / 100) * suggestedQuantity;

  const nutrientFacts = nutrition?.reduce<NutrientFacts>(
    (nutrientFact, { nutrient, amount }) => {
      const mappedNutrientName = isPassioNutrientName(nutrient.name)
        ? PASSIO_NUTRIENTS_TO_TRACK_MAP[nutrient.name]
        : undefined;

      if (mappedNutrientName && nutrient.unit === PASSIO_NUTRIENTS_UNIT_TO_TRACK_MAP[mappedNutrientName]) {
        return { ...nutrientFact, [mappedNutrientName]: nutritionValue(amount) };
      }

      return nutrientFact;
    },
    { calories: 0 },
  );

  return nutrition === undefined ? { calories: 0 } : nutrientFacts;
};

export const getNutritionFacts = (
  foodItemMeta: PassioFoodMeta,
  passioFoodData: PassioSearchByFoodId,
  servingSize: string,
) => {
  const defaultServingSizeLabel = generateServingSizeString(
    foodItemMeta?.servingSizes?.[0].quantity ?? 1,
    foodItemMeta?.servingSizes?.[0].unitName ?? "serving",
  );
  const selectedPortion =
    servingSize === defaultServingSizeLabel
      ? foodItemMeta
      : passioFoodData?.portions.find(
          ({ quantity, name }) => servingSize === generateServingSizeString(quantity, name),
        );

  const weightValue = calculateWeightValue(
    selectedPortion?.weight?.value ?? DEFAULT_PASSIO_WEIGHT,
    selectedPortion?.weight?.unit ?? DEFAULT_PASSIO_UNIT,
    foodItemMeta?.passioId,
  );

  const nutrientFacts = calculateNutritionInformation(
    weightValue,
    selectedPortion?.suggestedQuantity?.[0] ?? DEFAULT_PASSIO_SUGGESTED_QAUNTITY,
    passioFoodData?.nutrients,
  );

  return nutrientFacts ?? { calories: foodItemMeta.calories ?? 0 };
};
