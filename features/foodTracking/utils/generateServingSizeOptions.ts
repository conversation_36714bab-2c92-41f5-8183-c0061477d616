import { generateServingSizeString } from "../components/TrackAFoodDrawer/utils/foodTracking.utils";

export type ServingSize = Readonly<{
  unitName: string;
  quantity: number;
}>;
type ServingSizeOptions = Readonly<{
  label: string;
  value: string;
}>;

export const generateServingSizeOptions = (servingSizes: ServingSize[]): ServingSizeOptions[] => {
  const servingSizeOptionsMap = new Map();

  (servingSizes.length > 0 ? servingSizes : [{ quantity: 1, unitName: "serving" }]).forEach(
    ({ unitName, quantity }) => {
      const servingSizeLabel = generateServingSizeString(quantity, unitName);

      servingSizeOptionsMap.set(servingSizeLabel, { value: servingSizeLabel, label: servingSizeLabel });
    },
  );

  return Array.from(servingSizeOptionsMap.values());
};
