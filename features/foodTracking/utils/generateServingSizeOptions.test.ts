import { describe, expect, test } from "vitest";

import { generateServingSizeOptions } from "./generateServingSizeOptions";

describe("generateServingSizeOptions", () => {
  test("should generate the correct serving size options", () => {
    const servingSizes = [
      { quantity: 1, unitName: "serving" },
      { quantity: 2, unitName: "cups" },
      { quantity: 0.5, unitName: "tablespoons" },
    ];
    const expectedResults = [
      { value: "1 Serving", label: "1 Serving" },
      { value: "2 Cups", label: "2 Cups" },
      { value: "1/2 Tablespoons", label: "1/2 Tablespoons" },
    ];

    expect(generateServingSizeOptions(servingSizes)).toEqual(expectedResults);
  });
});
