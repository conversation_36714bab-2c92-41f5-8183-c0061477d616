import { Food, FoodSource } from "@vivantehealth/vivante-core";
import { describe, expect, test } from "vitest";

import { normalizeIngredientList } from "./normalizeIngredientList";

const INGREDIENTS: Food[] = [
  {
    id: "1603211585378",
    nutrientFacts: {
      protein: 7.266,
      sodium: 19.03,
      sugars: 94.285,
      cholesterol: 0,
      calories: 830.4,
      totalCarbohydrates: 110.547,
      totalFat: 51.9,
      dietaryFiber: 10.207,
    },
    numberOfServings: 2,
    source: FoodSource.PASSIO_FOOD,
    name: "Chocolate, Semi-Sweet Morsel",
    servingSize: "1 Cup, mini chips",
    meta: {
      passioId: "1603211585378",
      servingSizes: [
        {
          quantity: 1,
          unitName: "cup, mini chips",
        },
      ],
      weight: {
        unit: "g",
        value: 173,
      },
      calories: 80.63999938964844,
      suggestedQuantity: [1],
      labelId: "77b552a0-8951-11ea-a893-cf0b895c1cd2",
    },
  },
  {
    name: "Devils Food Cake Or Cupcake, Fudge, With Icing Or Filling",
    nutrientFacts: {
      cholesterol: 144.32,
      protein: 22.828799999999998,
      totalFat: 131.528,
      totalCarbohydrates: 346.6304,
      sodium: 2282.8799999999997,
      calories: 2551.8399999999997,
      sugars: 262.13759999999996,
      dietaryFiber: 14.432,
    },
    id: "1603211585067",
    meta: {
      passioId: "1603211585067",
      labelId: "90a579d8-a027-11ea-aaec-57f79a95ecf6",
      weight: {
        value: 656,
        unit: "g",
      },
      suggestedQuantity: [1],
      calories: 2551.840087890625,
      servingSizes: [
        {
          quantity: 1,
          unitName: '1-layer cake (8" or 9" dia, 2" high)',
        },
      ],
    },
    source: FoodSource.PASSIO_FOOD,
    numberOfServings: 4,
    servingSize: '1 1-layer cake (8" or 9" dia, 2" high)',
  },
  {
    id: "1603211585378",
    nutrientFacts: {
      protein: 7.266,
      sodium: 19.03,
      sugars: 94.285,
      cholesterol: 0,
      calories: 830.4,
      totalCarbohydrates: 110.547,
      totalFat: 51.9,
      dietaryFiber: 10.207,
    },
    numberOfServings: 2,
    source: FoodSource.PASSIO_FOOD,
    name: "Chocolate, Semi-Sweet Morsel",
    servingSize: "1 Cup, mini chips",
    meta: {
      passioId: "1603211585378",
      servingSizes: [
        {
          quantity: 1,
          unitName: "cup, mini chips",
        },
      ],
      weight: {
        unit: "g",
        value: 173,
      },
      calories: 80.63999938964844,
      suggestedQuantity: [1],
      labelId: "77b552a0-8951-11ea-a893-cf0b895c1cd2",
    },
  },
];

describe("normalizeIngredientList", () => {
  test("Should remove duplicate ingredients and maintain order", () => {
    const normalizedIngredients = normalizeIngredientList(INGREDIENTS);

    expect(normalizedIngredients.size).toBe(2);
    expect(normalizedIngredients.has("1603211585067")).toBeTruthy();
    expect(normalizedIngredients.values().next().value).toEqual(INGREDIENTS[0]);
  });

  test("Should return an empty Map if no ingredients are provided", () => {
    const normalizedIngredients = normalizeIngredientList([]);

    expect(normalizedIngredients.size).toBe(0);
  });
});
