import { Food } from "@vivantehealth/vivante-core";

/** Use a Map so that we maintain order and can easily delete ingredients without having to search an array */
export const normalizeIngredientList = (ingredients?: Food[]) => {
  const ingredientsMap = new Map<string, Food>();

  ingredients?.forEach((ingredient) => {
    if (ingredient.id) {
      ingredientsMap.set(ingredient.id, ingredient);
    }
  });

  return ingredientsMap;
};
