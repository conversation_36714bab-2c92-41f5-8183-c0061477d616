import { Food, FoodSource } from "@vivantehealth/vivante-core";
import { describe, expect, test } from "vitest";

import { calculateRecipeNutrientFacts } from "./calculateRecipeNutrientFacts";

const ingredients: Food[] = [
  {
    id: "1603211588716",
    name: "Cheese, Cheddar",
    source: FoodSource.PASSIO_FOOD,
    servingSize: "1 Cup",
    numberOfServings: 0.5,
    nutrientFacts: {
      calories: 400,
      totalFat: 35,
      cholesterol: 110,
      sodium: 740,
      totalCarbohydrates: 3.5,
      dietaryFiber: 0,
      sugars: 0.5,
      protein: 25,
    },
  },
  {
    id: "1603211583236",
    name: "Pasta, Cooked",
    source: FoodSource.PASSIO_FOOD,
    servingSize: "1 Cup, cooked",
    numberOfServings: 4,
    nutrientFacts: {
      calories: 220,
      protein: 8,
      totalFat: 1,
      totalCarbohydrates: 40,
      sugars: 0.75,
      dietaryFiber: 2.5,
      sodium: 325,
      cholesterol: 0,
    },
  },
  {
    id: "1603211203255",
    name: "Cream, Heavy",
    source: FoodSource.PASSIO_FOOD,
    servingSize: "1 Fl oz",
    numberOfServings: 8,
    nutrientFacts: {
      calories: 100,
      protein: 1,
      totalFat: 10,
      totalCarbohydrates: 1,
      sugars: 1,
      dietaryFiber: 0,
      sodium: 8,
      cholesterol: 35,
    },
  },
];

describe("calculateRecipeNutrientFacts", () => {
  test("Shoudl return 0 calories object if no ingredients are provided", () => {
    const result = calculateRecipeNutrientFacts([], 2);

    expect(result.calories).toBe(0);
  });

  test("should calculate the nutrient facts correctlyb for 4 total servings", () => {
    const result = calculateRecipeNutrientFacts(ingredients, 4);

    expect(result).toEqual({
      calories: 470,
      totalFat: 25.375,
      cholesterol: 83.75,
      sodium: 433.5,
      totalCarbohydrates: 42.4375,
      dietaryFiber: 2.5,
      sugars: 2.8125,
      protein: 13.125,
    });
  });

  test("should calculate the nutrient facts correctlyb for 2 total servings", () => {
    const result = calculateRecipeNutrientFacts(ingredients, 2);

    expect(result).toEqual({
      calories: 940,
      totalFat: 50.75,
      cholesterol: 167.5,
      sodium: 867,
      totalCarbohydrates: 84.875,
      dietaryFiber: 5,
      sugars: 5.625,
      protein: 26.25,
    });
  });
});
