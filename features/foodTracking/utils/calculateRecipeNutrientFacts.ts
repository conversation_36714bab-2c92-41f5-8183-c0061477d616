import { Food, NutrientFacts } from "@vivantehealth/vivante-core";
const nutrientKeys = [
  "calories",
  "totalFat",
  "cholesterol",
  "sodium",
  "totalCarbohydrates",
  "sugars",
  "dietaryFiber",
  "protein",
  "addedSugars",
  "sugarAlcohols",
];

const isNutrientKey = (key: string): key is keyof NutrientFacts => nutrientKeys.includes(key);

export const calculateRecipeNutrientFacts = (ingredients: Food[], totalNumberOfServings: number) => {
  return ingredients.reduce<NutrientFacts>(
    (calculatedNutrientFacts, ingredient) => {
      const numberOfServings = ingredient?.numberOfServings ?? 1;
      const nutrientFacts = ingredient?.nutrientFacts ?? { calories: 0 };

      Object.entries(nutrientFacts).forEach(([nutrient, value]) => {
        if (isNutrientKey(nutrient)) {
          if (!(nutrient in calculatedNutrientFacts)) {
            calculatedNutrientFacts[nutrient] = 0;
          }

          calculatedNutrientFacts[nutrient] += (value * numberOfServings) / totalNumberOfServings;
        }
      });

      return calculatedNutrientFacts;
    },
    { calories: 0 },
  );
};
