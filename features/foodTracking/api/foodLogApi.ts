import { PassioFoodType } from "@vivantehealth/vivante-core";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

import { appStrings } from "@Assets/app_strings";
import { ErrorStateSlice } from "@Features/error/store/errorStateSlice";
import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { getBaseApiUrl } from "@Utils/getBaseApiUrl";
import { isFetchBaseQueryError } from "@Utils/isFetchBaseQueryError";
import { logger } from "@Utils/logger";

/**
 * This is the API for the food tracking feature. It is used to search for food items and get detailed information about them.
 * https://passio.gitbook.io/nutrition-api/
 */
export const foodLogApi = createApi({
  reducerPath: "foodLogApi",
  baseQuery: fetchBaseQuery({
    baseUrl: getBaseApiUrl("PASSIO_PROXY_URL"),
    prepareHeaders: async (headers) => {
      const firebaseToken = await vivanteCoreContainer.authClient.getValidAccessToken();

      headers.set("Authorization", `Bearer ${firebaseToken}`);
      headers.set("X-PASSIO-USE-NAPI", "true");

      return headers;
    },
  }),
  endpoints: (builder) => ({
    /** Gets the food details by the provided UPC code */
    searchByProductCode: builder.query({
      query: (productCode: string) => `productCode/${productCode}`,
    }),
    /** Gets the food details by the provided foodId (passio ID) */
    searchByFoodId: builder.query<PassioSearchByFoodId, string>({
      query: (foodId) => `${foodId}`,
      onQueryStarted: async (_, { dispatch, queryFulfilled }) => {
        try {
          await queryFulfilled;
        } catch (error) {
          /** If status is 404, this just means we have a passioId but no match in their API and we don't need to show the Error modal */
          if (isFetchBaseQueryError(error) && error.error.status !== 404) {
            logger.error(error);
            dispatch(ErrorStateSlice.actions.setError(appStrings.features.foodTracking.errorModal));
          }
        }
      },
    }),
    /** This query will get us a list of search results with partial information
     * You'll need to query the searchForResultDetails endpoint to get more detailed information about a specific result
     */
    searchByFoodName: builder.query<PassioSearchFoodResult, string>({
      query: (foodName) => `search/advanced?term=${encodeURI(foodName)}`,
      onQueryStarted: async (_, { dispatch, queryFulfilled }) => {
        try {
          await queryFulfilled;
        } catch (error) {
          logger.error(error);
          dispatch(ErrorStateSlice.actions.setError(appStrings.features.foodTracking.errorModal));
        }
      },
    }),
    /** This query will get us a more detailed result for a specific food including nutrient information and portions */
    searchForResultDetails: builder.query<PassioDetailedFoodResult, PassioDetailedFoodQuery>({
      query: ({ type, resultId, labelId }) =>
        `search/result/${labelId ?? "00000000-0000-0000-0000-000000000000"}/${type}/${resultId}`,
    }),
  }),
});

export const {
  useSearchByProductCodeQuery,
  useSearchByFoodIdQuery,
  useLazySearchByFoodIdQuery,
  useSearchByFoodNameQuery,
  useLazySearchByFoodNameQuery,
  useSearchForResultDetailsQuery,
} = foodLogApi;

export type PassioNutrients = Readonly<{ amount: number; id: number; nutrient: { name: string; unit: string } }>;

export type PassioSearchByFoodId = Readonly<{
  id: string;
  name: string;
  qualityScore: string;
  timestamp: string;
  tags: string[];
  portions: { name: string; quantity: number; suggestedQuantity?: number[]; weight: { unit: string; value: number } }[];
  origin: { dataType: string; id: string; source: string; timestamp: string }[];
  nutrients: PassioNutrients[];
}>;

type PassioDetailedFoodQuery = Readonly<{ type: PassioFoodType; resultId: string; labelId?: string }>;

type NutritionPreview = Readonly<{
  calories: number;
  portion: {
    name: string;
    quantity: number;
    weight: { unit: string; value: number };
    suggestedQuantity?: number[];
  };
}>;

export type PassioFoodResult = Readonly<{
  brandName: string;
  displayName: string;
  shortName: string;
  longName: string;
  iconId: string;
  labelId?: string;
  nutritionPreview: NutritionPreview;
  recipeId?: string;
  referenceId: string;
  resultId: string;
  displayNameScore: number;
  score: number;
  scoredName: string;
  synonymId?: string;
  type: PassioFoodType;
}>;

export type PassioSearchFoodResult = Readonly<{
  alternateNames: string[];
  results: PassioFoodResult[];
}>;

export const isPassioSearchFoodResult = (searchResult: unknown): searchResult is PassioSearchFoodResult => {
  return (
    searchResult != null &&
    typeof searchResult === "object" &&
    "results" in searchResult &&
    "alternateNames" in searchResult
  );
};

export type PassioDetailedFoodIngredient = Readonly<{
  brandName?: string;
  branded?: {
    certified: true;
    country: string;
    images: {
      bucket: string;
      path: string;
    }[];
    ingredients: string;
    ocrText: string;
    owner: string;
    productCode: string;
    reviewed: true;
    tested: true;
    upc: string;
  };
  description?: string;
  iconId?: string;
  id: string;
  licenseCopy?: string;
  name: string;
  name_i18n?: {
    property1: string;
    property2: string;
  };
  nutrients: {
    amount: number;
    id: number;
    nutrient: {
      group?: string;
      id?: number;
      name: string;
      origin?: { dataType: null; id: null; source: null; timestamp: null }[];
      shortName?: string;
      unit: string;
    };
  }[];
  origin: {
    dataType: string;
    id: string;
    source: string;
    timestamp: string;
  }[];
  popularity?: number;
  portions: {
    name: string;
    quantity: number;
    suggestedQuantity?: number[];
    weight: {
      unit: string;
      value: number;
    };
  }[];
  qualityScore: string;
  tags: string[];
  timestamp: string;
  webLinks?: string[];
}>;

type PassioDetailedFood = Readonly<{
  displayName: string;
  iconId: string;
  ingredients: PassioDetailedFoodIngredient[];
  internalId: string;
  internalName: string;
  portions: {
    name: string;
    quantity: number;
    suggestedQuantity: number[];
    weight: {
      unit: string;
      value: number;
    };
  }[];
  type: string;
}>;

type PassioDetailedFoodAlternatives = Readonly<{
  displayName: string;
  iconId: string;
  labelId: string;
  resultId: string;
  type: string;
}>;

type PassioDetailedFoodResult = Readonly<{
  alternatives: PassioDetailedFoodAlternatives[];
  results: PassioDetailedFood[];
}>;
