import { PostFoodLogProps, UpdateFoodLogProps, UpdateRecipeLogProps } from "@vivantehealth/vivante-core";
import { Action, PayloadAction } from "@reduxjs/toolkit";
import { Epic, StateObservable, ofType } from "redux-observable";
import { Observable, from, of, zip } from "rxjs";
import { catchError, map, mergeMap, switchMap, withLatestFrom } from "rxjs/operators";

import { appStrings } from "@Assets/app_strings";
import { ProgressStateSlice } from "@Features/progress/store/progressStateSlice";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";
import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { RootState } from "@Store/store";

import { FoodLogStateSlice } from "./foodLogStateSlice";
import { SORT_ORDER_MAPPER } from "../hooks/useSortOrder";

const FOOD_TRACKING_TABS = appStrings.features.foodTracking.tabs;

const {
  loadTrackedFoodLogs,
  loadTrackedFoodLogsSuccess,
  loadCustomFoodItems,
  loadCustomFoodItemsSuccess,
  loadHistoryAllFoods,
  loadHistoryAllFoodsSuccess,
  loadRecipes,
  loadRecipesSuccess,
  loadFoodLogFailure,
  updateRecipe,
  deleteRecipe,
  saveFoodLog,
  deleteFoodLog,
  deleteCustomFood,
  updateFoodLog,
  updateSubmissionStatus,
} = FoodLogStateSlice.actions;

const { toggleSnackbar } = SnackbarStateSlice.actions;

const { loadHistory } = ProgressStateSlice.actions;
/**
 * Pulls down the food logs (All), custom food items (Added foods), and recipes (My recipes) from Firestore in their currently
 * set sort order. Default in state is by lastTracked DESC order
 */
const loadTrackedFoodlogsEpic: Epic = (actions$: Observable<Action>, state$: StateObservable<RootState>) => {
  return actions$.pipe(
    ofType(loadTrackedFoodLogs.type),
    withLatestFrom(state$),
    mergeMap(([, state]) => {
      const { lastSortStateByTab } = state.foodLogState;
      const allTabSortOptions = SORT_ORDER_MAPPER[lastSortStateByTab[FOOD_TRACKING_TABS.all]];
      const addedFoodsTabSortOptions = SORT_ORDER_MAPPER[lastSortStateByTab[FOOD_TRACKING_TABS.addedFoods]];
      const myRecipesTabSortOptions = SORT_ORDER_MAPPER[lastSortStateByTab[FOOD_TRACKING_TABS.myRecipes]];

      return zip(
        vivanteCoreContainer
          .getNutritionUseCaseFactory()
          .createGetAllUniqueFoodsHistoryUseCase()
          .execute(allTabSortOptions),
        vivanteCoreContainer.getNutritionUseCaseFactory().createGetFoodsUseCase().execute(addedFoodsTabSortOptions),
        vivanteCoreContainer.getNutritionUseCaseFactory().createGetRecipeLogsUseCase().execute(myRecipesTabSortOptions),
      ).pipe(
        map(([historyAllFoods, customFoodItems, recipes]) =>
          loadTrackedFoodLogsSuccess({ historyAllFoods, customFoodItems, recipes }),
        ),
        catchError((error) => of(loadFoodLogFailure(error))),
      );
    }),
  );
};
/**
 * Pulls down custom food items (Added foods) from Firestore in the provided sort order
 * If no order is provided, than by lastTracked DESC order
 */
const loadFoodLibraryItemsEpic: Epic = (actions$: Observable<Action>, state$: StateObservable<RootState>) => {
  return actions$.pipe(
    ofType(loadCustomFoodItems.type),
    withLatestFrom(state$),
    switchMap(([, state]) => {
      const lastSortOption = state.foodLogState.lastSortStateByTab[FOOD_TRACKING_TABS.addedFoods];
      const sortOptions = SORT_ORDER_MAPPER[lastSortOption];

      return from(vivanteCoreContainer.getNutritionUseCaseFactory().createGetFoodsUseCase().execute(sortOptions)).pipe(
        map((customFoodItems) => loadCustomFoodItemsSuccess(customFoodItems)),
        catchError((error) => of(loadFoodLogFailure(error))),
      );
    }),
  );
};
/**
 * Pulls down the history of all unique food items the user has tracked in DESC order
 */
const loadHistoryAllFoodsEpic: Epic = (actions$: Observable<Action>, state$: StateObservable<RootState>) => {
  return actions$.pipe(
    ofType(loadHistoryAllFoods.type),
    withLatestFrom(state$),
    switchMap(([, state]) => {
      const lastSortOption = state.foodLogState.lastSortStateByTab[FOOD_TRACKING_TABS.all];
      const sortOptions = SORT_ORDER_MAPPER[lastSortOption];

      return from(
        vivanteCoreContainer.getNutritionUseCaseFactory().createGetAllUniqueFoodsHistoryUseCase().execute(sortOptions),
      ).pipe(
        map((historyAllFoods) => loadHistoryAllFoodsSuccess(historyAllFoods)),
        catchError((error) => of(loadFoodLogFailure(error))),
      );
    }),
  );
};
/**
 * Pulls down all recipes created by the user in the provided sort order
 * If no order is provided, than by lastTracked DESC order
 */
const loadRecipesEpic: Epic = (actions$: Observable<Action>, state$: StateObservable<RootState>) => {
  return actions$.pipe(
    ofType(loadRecipes.type),
    withLatestFrom(state$),
    switchMap(([, state]) => {
      const lastSortOption = state.foodLogState.lastSortStateByTab[FOOD_TRACKING_TABS.myRecipes];
      const sortOptions = SORT_ORDER_MAPPER[lastSortOption];

      return from(
        vivanteCoreContainer.getNutritionUseCaseFactory().createGetRecipeLogsUseCase().execute(sortOptions),
      ).pipe(
        map((recipeLogs) => loadRecipesSuccess(recipeLogs)),
        catchError((error) => of(loadFoodLogFailure(error))),
      );
    }),
  );
};
/**
 * Updates an existing recipe from the user's recipe log. On successful update, it will reload the recipes list
 */
const updateRecipeEpic: Epic = (actions$: Observable<PayloadAction<UpdateRecipeLogProps>>) => {
  return actions$.pipe(
    ofType(updateRecipe.type),
    switchMap((action: PayloadAction<UpdateRecipeLogProps>) => {
      return from(
        vivanteCoreContainer.getNutritionUseCaseFactory().createUpdateRecipeLogUseCase().execute(action.payload),
      ).pipe(
        switchMap(() => {
          return [updateSubmissionStatus("success"), loadRecipes()];
        }),
        catchError((error) => of(loadFoodLogFailure(error))),
      );
    }),
  );
};

/**
 * Deletes an existing recipe from the user's recipe log. On successful deletion, it will reload the recipes list
 */
const deleteRecipeEpic: Epic = (actions$: Observable<PayloadAction<{ recipeId: string; snackbarMessage: string }>>) => {
  return actions$.pipe(
    ofType(deleteRecipe.type),
    switchMap((action: PayloadAction<{ recipeId: string; snackbarMessage: string }>) => {
      const { recipeId, snackbarMessage } = action.payload;

      return from(
        vivanteCoreContainer.getNutritionUseCaseFactory().createDeleteRecipeLogUseCase().execute(recipeId),
      ).pipe(
        switchMap(() => {
          return [
            updateSubmissionStatus("success"),
            loadRecipes(),
            toggleSnackbar({ message: snackbarMessage, isOpen: true }),
          ];
        }),
        catchError((error) => of(loadFoodLogFailure(error))),
      );
    }),
  );
};
/**
 * Saves a food log entry to the user's food log. On successful save, it will reload the tracked food logs
 */
const saveFoodLogEpic: Epic = (actions$: Observable<PayloadAction<PostFoodLogProps & { snackbarMessage: string }>>) => {
  return actions$.pipe(
    ofType(saveFoodLog.type),
    switchMap((action: PayloadAction<PostFoodLogProps & { snackbarMessage: string }>) => {
      const { snackbarMessage, ...postFoodLogProps } = action.payload;

      return from(
        vivanteCoreContainer.getNutritionUseCaseFactory().createPostFoodLogUseCase().execute(postFoodLogProps),
      ).pipe(
        switchMap(() => {
          return [
            updateSubmissionStatus("success"),
            loadTrackedFoodLogs(),
            toggleSnackbar({ message: snackbarMessage, isOpen: true }),
          ];
        }),
        catchError((error) => of(loadFoodLogFailure(error), updateSubmissionStatus("failed"))),
      );
    }),
  );
};
/**
 * Deletes the foodlog entry that matches the provided ID. On successful deletion, it will reload the history (food logs, symptoms, etc) for the selected date
 */
const deleteFoodLogEpic: Epic = (
  actions$: Observable<PayloadAction<{ foodLogId: string; snackbarMessage: string }>>,
  state$: StateObservable<RootState>,
) => {
  return actions$.pipe(
    ofType(deleteFoodLog.type),
    withLatestFrom(state$),
    switchMap(([action, state]: [PayloadAction<{ foodLogId: string; snackbarMessage: string }>, RootState]) => {
      const { foodLogId, snackbarMessage } = action.payload;
      const { currentDate } = state.progressState;

      return from(
        vivanteCoreContainer.getNutritionUseCaseFactory().createDeleteFoodLogUseCase().execute(foodLogId),
      ).pipe(
        switchMap(() => {
          return [
            updateSubmissionStatus("success"),
            loadHistory(currentDate ?? new Date()),
            toggleSnackbar({ isOpen: true, message: snackbarMessage }),
          ];
        }),
        catchError((error) => of(loadFoodLogFailure(error), updateSubmissionStatus("failed"))),
      );
    }),
  );
};
/**
 * Updates the foodlog entry that matches the provided ID. On successful update, it will reload the history (food logs, symptoms, etc) for the selected date
 */
const updateFoodLogEpic: Epic = (
  actions$: Observable<PayloadAction<UpdateFoodLogProps & { snackbarMessage: string }>>,
  state$: StateObservable<RootState>,
) => {
  return actions$.pipe(
    ofType(updateFoodLog.type),
    withLatestFrom(state$),
    switchMap(([action, state]: [PayloadAction<UpdateFoodLogProps & { snackbarMessage: string }>, RootState]) => {
      const { snackbarMessage, ...updateFoodLogProps } = action.payload;
      const { currentDate } = state.progressState;

      return from(
        vivanteCoreContainer.getNutritionUseCaseFactory().createUpdateFoodLogUseCase().execute(updateFoodLogProps),
      ).pipe(
        switchMap(() => {
          return [
            updateSubmissionStatus("success"),
            loadHistory(currentDate ?? new Date()),
            toggleSnackbar({ message: snackbarMessage, isOpen: true }),
          ];
        }),
        catchError((error) => of(loadFoodLogFailure(error), updateSubmissionStatus("failed"))),
      );
    }),
  );
};
/**
 * Deletes a custom food item that matches the provided ID. On successful deletion, it will reload the custom food items
 */
const deleteCustomFoodEpic: Epic = (
  actions$: Observable<PayloadAction<{ foodLogId: string; snackbarMessage: string }>>,
) => {
  return actions$.pipe(
    ofType(deleteCustomFood.type),
    switchMap((action: PayloadAction<{ foodLogId: string; snackbarMessage: string }>) => {
      const { foodLogId, snackbarMessage } = action.payload;

      return from(
        vivanteCoreContainer.getNutritionUseCaseFactory().createDeleteCustomFoodUseCase().execute(foodLogId),
      ).pipe(
        switchMap(() => {
          return [
            updateSubmissionStatus("success"),
            loadCustomFoodItems(),
            toggleSnackbar({ isOpen: true, message: snackbarMessage }),
          ];
        }),
        catchError((error) => of(loadFoodLogFailure(error), updateSubmissionStatus("failed"))),
      );
    }),
  );
};

export const foodLogEpics = [
  loadFoodLibraryItemsEpic,
  loadHistoryAllFoodsEpic,
  loadRecipesEpic,
  updateRecipeEpic,
  deleteRecipeEpic,
  loadTrackedFoodlogsEpic,
  saveFoodLogEpic,
  deleteFoodLogEpic,
  deleteCustomFoodEpic,
  updateFoodLogEpic,
];
