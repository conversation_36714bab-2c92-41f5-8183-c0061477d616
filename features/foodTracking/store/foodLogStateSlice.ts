// Expect this error as the action is actually used in epic and not in the reducer
/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  Food,
  UpdateFoodLogProps,
  UpdateRecipeLogProps,
  VivanteApiError,
  PostFoodLogProps,
  CustomFood,
  RecipeLog,
} from "@vivantehealth/vivante-core";
import { PayloadAction, createSlice } from "@reduxjs/toolkit";

import { errorDispatcher } from "@Features/error/utils/errorDispatcher";
import { LoadState } from "@Types";
import { buildSliceStateSelector } from "@Utils/slice.util";

import { FoodTrackingTabs } from "../FoodTrackingScreenContainer";
import { FoodTrackingSort } from "../hooks/useSortOrder";

type TrackAFoodTabSortState = Record<FoodTrackingTabs, FoodTrackingSort>;

export type SubmissionStatus = "idle" | "pending" | "success" | "failed";

export type FoodLogState = Readonly<{
  loadState: LoadState;
  /** This is the list of all food items the user has tracked (All tab) */
  historyAllFoods: Food[];
  /** These are foods the user has added and are within the customFood collection (Added Foods tab) */
  customFoodItems: CustomFood[];
  /** This is the list of all recipes the user has created (My Recipes tab) */
  recipes: RecipeLog[];
  /** This maintains the last sort order selected per tab which keeps unnessary refetching of data
   * while also reseting the sort order when the user navigates back to the tab (and thus refetching data)
   * The expected behavior when navigating back to a tab is to reset to Recently tracked
   */
  lastSortStateByTab: TrackAFoodTabSortState;
  /** Keeps track of the submission status of the current process */
  submissionStatus: SubmissionStatus;
}>;

const initialState: FoodLogState = {
  loadState: null,
  customFoodItems: [],
  historyAllFoods: [],
  recipes: [],
  lastSortStateByTab: {
    All: "Recently tracked",
    "Added foods": "Recently tracked",
    "My recipes": "Recently tracked",
  },
  submissionStatus: "idle",
};

export const FoodLogStateSlice = createSlice({
  name: "foodLogState",
  initialState,
  reducers: {
    loadTrackedFoodLogs: (state) => {
      return { ...state, loadState: "loading" };
    },
    loadTrackedFoodLogsSuccess: (
      state,
      action: PayloadAction<{ historyAllFoods: Food[]; customFoodItems: CustomFood[]; recipes: RecipeLog[] }>,
    ) => {
      const { historyAllFoods, customFoodItems, recipes } = action.payload;

      return {
        ...state,
        loadState: "loaded",
        historyAllFoods,
        customFoodItems,
        recipes,
      };
    },
    loadCustomFoodItems: (state) => {
      return { ...state, loadState: "loading" };
    },
    loadCustomFoodItemsSuccess: (state, action: PayloadAction<CustomFood[]>) => {
      return { ...state, loadState: "loaded", customFoodItems: action.payload };
    },
    loadHistoryAllFoods: (state) => {
      return { ...state, loadState: "loading" };
    },
    loadHistoryAllFoodsSuccess: (state, action: PayloadAction<Food[]>) => {
      return { ...state, loadState: "loaded", historyAllFoods: action.payload };
    },
    loadRecipes: (state) => {
      return { ...state, loadState: "loading" };
    },
    loadRecipesSuccess: (state, action: PayloadAction<RecipeLog[]>) => {
      return { ...state, loadState: "loaded", recipes: action.payload };
    },
    loadFoodLogFailure: (state, action: PayloadAction<Error | VivanteApiError>) => {
      const { payload } = action;
      const message =
        payload instanceof VivanteApiError
          ? payload.detail
          : payload instanceof Error
            ? payload.message
            : "An unexpected error has occurred";

      errorDispatcher({
        message,
      });

      return { ...state, loadState: "failure" };
    },
    updateRecipe: (state, _: PayloadAction<UpdateRecipeLogProps>) => {
      return { ...state, submissionStatus: "pending" };
    },
    deleteRecipe: (state, _: PayloadAction<{ recipeId: string; snackbarMessage: string }>) => {
      return { ...state, submissionStatus: "pending" };
    },
    saveFoodLog: (state, _: PayloadAction<PostFoodLogProps & { snackbarMessage: string }>) => {
      return { ...state, submissionStatus: "pending" };
    },
    deleteFoodLog: (state, _: PayloadAction<{ foodLogId: string; snackbarMessage: string }>) => {
      return { ...state, submissionStatus: "pending" };
    },
    deleteCustomFood: (state, _: PayloadAction<{ foodLogId: string; snackbarMessage: string }>) => {
      return { ...state, submissionStatus: "pending" };
    },
    updateFoodLog: (state, _: PayloadAction<UpdateFoodLogProps & { snackbarMessage: string }>) => {
      return { ...state, submissionStatus: "pending" };
    },
    updateLastTabSortState: (state, action: PayloadAction<{ tab: FoodTrackingTabs; sort: FoodTrackingSort }>) => {
      return {
        ...state,
        lastSortStateByTab: {
          ...state.lastSortStateByTab,
          [action.payload.tab]: action.payload.sort,
        },
      };
    },
    updateSubmissionStatus: (state, action: PayloadAction<SubmissionStatus>) => {
      return {
        ...state,
        submissionStatus: action.payload,
      };
    },
  },
});

export const foodLogSelector = buildSliceStateSelector("foodLogState");

export const foodLogStateReducer = FoodLogStateSlice.reducer;
