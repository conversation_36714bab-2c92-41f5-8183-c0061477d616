import { useDebounceValue } from "usehooks-ts";

import { useSearchByFoodNameQuery } from "../api/foodLogApi";
import { FOOD_TRACKING_DEBOUNCE_TIME } from "../assets/constants";

export const useSearchByFoodNameDebounced = (searchTerm: string) => {
  const [debouncedSearchTerm] = useDebounceValue(searchTerm, FOOD_TRACKING_DEBOUNCE_TIME);

  const {
    isLoading,
    isFetching,
    isUninitialized,
    data: passioSearchResults,
  } = useSearchByFoodNameQuery(debouncedSearchTerm, { skip: debouncedSearchTerm === "" });

  /**
   * We consider the query to be loading whenever we are loading, fetching, or the current search is uninitialized.
   * This ensures we always show our loading state properly without showing stale results (eg prior searched terms)
   */
  return { isLoading: isLoading || isFetching || isUninitialized, passioSearchResults };
};
