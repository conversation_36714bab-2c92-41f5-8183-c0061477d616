import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { SortAndPaginateProps } from "@vivantehealth/vivante-core";

import { appStrings } from "@Assets/app_strings";

import { FoodTrackingTabs } from "../FoodTrackingScreenContainer";
import { FoodLogStateSlice, foodLogSelector } from "../store/foodLogStateSlice";

export type FoodTrackingSort = (typeof FOOD_TRACKING.sortOrder)[keyof typeof FOOD_TRACKING.sortOrder];

const FOOD_TRACKING = appStrings.features.foodTracking;

export const SORT_ORDER_MAPPER: Record<FoodTrackingSort, SortAndPaginateProps> = {
  [FOOD_TRACKING.sortOrder.recentlyTracked]: { sortKey: "lastTracked" },
  [FOOD_TRACKING.sortOrder.alphabeticalAsc]: { sortKey: "name", sortDirection: "asc" },
  [FOOD_TRACKING.sortOrder.alphabeticalDesc]: { sortKey: "name", sortDirection: "desc" },
};

type DispatchActions = "loadRecipes" | "loadCustomFoodItems" | "loadHistoryAllFoods";

const DISPATCH_ACTION_MAPPER: Record<FoodTrackingTabs, DispatchActions> = {
  All: "loadHistoryAllFoods",
  "Added foods": "loadCustomFoodItems",
  "My recipes": "loadRecipes",
};

export function useSortOrder(currentTab: FoodTrackingTabs) {
  const dispatch = useDispatch();
  const [sortOrder, setSortOrder] = useState<FoodTrackingSort>(FOOD_TRACKING.sortOrder.recentlyTracked);
  /**
   * We get the lastSortOrder from the redux store for the selected tab which allows us to
   * only refetch data when absolutely necessary and avoids the need for a useEffect
   */
  const lastSortOrder = useSelector(foodLogSelector("lastSortStateByTab"))[currentTab];

  if (sortOrder !== lastSortOrder) {
    const dispatchAction = DISPATCH_ACTION_MAPPER[currentTab];

    dispatch(FoodLogStateSlice.actions.updateLastTabSortState({ tab: currentTab, sort: sortOrder }));
    dispatch(FoodLogStateSlice.actions[dispatchAction]());
  }

  return [sortOrder, setSortOrder] as const;
}
