import { CSSProperties } from "react";
import { CustomFood, Food, FoodSource, RecipeLog, isCustomFoodV2, isRecipeLog } from "@vivantehealth/vivante-core";
import { Box, Paper, Skeleton, Typography } from "@mui/material";
import { color, typography } from "@vivantehealth/design-tokens";

import { RADIUS_FULL_PX, SPACING_8_PX } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";

type FoodCardContainerProps = Readonly<{
  isLoading: boolean;
  foodCardData: (Food | RecipeLog | CustomFood)[];
  /**
   * The icon to display on the right side of the card
   * Defaults to "addCircle"
   */
  foodCardRightIcon?: keyof typeof ICON_MAP;
  includeBrandName?: boolean;
  onFoodCardClick?: (food: Food | RecipeLog | CustomFood) => void;
  containerStyles?: CSSProperties;
  displayIcon?: boolean;
}>;

const SKELETON_LOADING_COUNT = 5;

export const FoodCardContainer = ({
  isLoading,
  foodCardData,
  foodCardRightIcon = "addCircle",
  includeBrandName = true,
  onFoodCardClick,
  containerStyles,
  displayIcon = true,
}: FoodCardContainerProps) => {
  const hasOnClickHandler = !!onFoodCardClick;

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        gap: 2,
        height: "calc(100vh - 480px)",
        overflow: "auto",
        ...containerStyles,
      }}
    >
      {isLoading
        ? [...Array(SKELETON_LOADING_COUNT)].map((_, index) => (
            <FoodCard
              food={{ name: "Loading...", source: FoodSource.CUSTOM_USER_FOOD }}
              onClick={() => {}}
              key={index}
              icon={foodCardRightIcon}
              isSkeletonLoading
              includeBrandName={includeBrandName}
              displayIcon={displayIcon}
            />
          ))
        : foodCardData.map((food, index) => (
            <FoodCard
              key={`${food.name}-${index}`}
              food={food}
              onClick={() => {
                if (hasOnClickHandler) {
                  onFoodCardClick(food);
                }
              }}
              icon={foodCardRightIcon}
              includeBrandName={includeBrandName}
              displayIcon={displayIcon}
            />
          ))}
    </Box>
  );
};

const generateSubtitle = (food: Food) => {
  if (food.source === FoodSource.PASSIO_FOOD || isCustomFoodV2(food)) {
    const { brand, servingSize } = food;

    return `${brand ?? ""}${brand && servingSize ? ", " : ""}${servingSize ?? ""}`;
  }

  if (isRecipeLog(food)) {
    return food.servingSize;
  }

  return "";
};

const ICON_MAP = {
  addCircle: <AppIcon name="Plus" size="sm" />,
  chevronRight: <AppIcon name="RightChevron" size="sm" />,
  edit: <AppIcon name="Edit" size="sm" />,
} as const;

type FoodCardIconProps = Readonly<{
  icon: keyof typeof ICON_MAP;
  isLoading: boolean;
  displayIcon: boolean;
}>;

const FoodCardIcon = ({ icon, isLoading, displayIcon }: FoodCardIconProps) => {
  if (!displayIcon) {
    return null;
  }

  return isLoading ? (
    <Skeleton variant="circular" width={24} height={24} />
  ) : (
    <Box
      sx={{
        padding: SPACING_8_PX,
        border: `1px solid ${color.border.action.default}`,
        borderRadius: RADIUS_FULL_PX,
      }}
    >
      {ICON_MAP[icon]}
    </Box>
  );
};

type FoodCardProps = {
  food: Food | CustomFood | RecipeLog;
  onClick: () => void;
  icon: keyof typeof ICON_MAP;
  includeBrandName: boolean;
  isSkeletonLoading?: boolean;
  displayIcon: boolean;
};

const FoodCard = ({ food, onClick, icon, includeBrandName, isSkeletonLoading, displayIcon }: FoodCardProps) => {
  const title = food.name;
  const subtitle = includeBrandName ? generateSubtitle(food) : "";

  return (
    <Paper
      component="button"
      onClick={onClick}
      aria-label={`${title}, ${subtitle}`}
      sx={{
        display: "flex",
        width: "100%",
        p: 4,
        cursor: displayIcon ? "pointer" : "default",
        ":hover": { ...(displayIcon ? { border: `1px solid ${color.border.action.hover}` } : {}) },
      }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center" width="100%">
        <Box display="flex" flexDirection="column" alignItems="start" textAlign="left">
          {isSkeletonLoading ? (
            <Skeleton variant="text" sx={{ width: "150px", fontSize: typography.heading4.fontSize }} />
          ) : (
            <Typography variant="h4" tabIndex={-1}>
              {title}
            </Typography>
          )}
          {subtitle || isSkeletonLoading ? (
            <FoodCardSubtitle subtitle={subtitle} isSkeletonLoading={!!isSkeletonLoading} />
          ) : null}
        </Box>

        <FoodCardIcon icon={icon} isLoading={!!isSkeletonLoading} displayIcon={displayIcon} />
      </Box>
    </Paper>
  );
};

const FoodCardSubtitle = ({ subtitle, isSkeletonLoading }: { subtitle: string; isSkeletonLoading: boolean }) => {
  if (isSkeletonLoading) {
    return <Skeleton variant="text" sx={{ width: "200px", fontSize: typography.bodyDense.fontSize }} />;
  }

  return (
    <Typography variant="bodyDense" tabIndex={-1}>
      {subtitle}
    </Typography>
  );
};
