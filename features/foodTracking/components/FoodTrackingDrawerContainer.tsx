import { ReactNode } from "react";
import { Box, Drawer } from "@mui/material";

import { FoodTrackingDrawerHeader } from "./FoodTrackingDrawerHeader";

type FoodTrackingDrawerProps = Readonly<{
  isDrawerOpen: boolean;
  ariaLabel: string;
  children: ReactNode;
  title: string;
  closeBtnAriaLabel: string;
  showBackBtn?: boolean;
  backBtnAriaLabel?: string;
  onBack?: () => void;
  onClose: () => void;
}>;

export const FoodTrackingDrawerContainer = ({
  isDrawerOpen,
  children,
  title,
  closeBtnAriaLabel,
  showBackBtn = false,
  backBtnAriaLabel,
  onBack,
  onClose,
}: FoodTrackingDrawerProps) => {
  return (
    <Drawer
      open={isDrawerOpen}
      onClose={onClose}
      anchor="right"
      variant="temporary"
      PaperProps={{
        sx: { px: 5 },
      }}
    >
      <FoodTrackingDrawerHeader
        title={title}
        closeBtnAriaLabel={closeBtnAriaLabel}
        handleOnClose={onClose}
        showBackBtn={showBackBtn}
        backBtnAriaLabel={backBtnAriaLabel}
        handleOnBack={onBack}
      />

      <Box height="100%" overflow="hidden">
        {children}
      </Box>
    </Drawer>
  );
};
