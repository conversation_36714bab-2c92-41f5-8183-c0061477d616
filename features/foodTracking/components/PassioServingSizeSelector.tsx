import { appStrings } from "@Assets/app_strings";
import { FormDropdown, SelectOptions } from "@Components/form/Fields";

const SHARED_FORM_TEXT = appStrings.sharedFormText;
const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking;

type PassioServingSizeSelectorProps = Readonly<{
  servingSizeOptions: SelectOptions[];
}>;

export const PassioServingSizeSelector = ({ servingSizeOptions }: PassioServingSizeSelectorProps) => {
  return (
    <FormDropdown
      label={FOOD_TRACKING_STRINGS.servingSize}
      name="servingSize"
      options={servingSizeOptions}
      rules={{
        required: {
          value: true,
          message: SHARED_FORM_TEXT.requiredMessage,
        },
      }}
    />
  );
};
