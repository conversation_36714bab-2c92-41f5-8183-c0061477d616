import { Typo<PERSON>, Box, Button, CircularProgress } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { BaseModal } from "@Components/BaseModal/BaseModal";

const BUTTON_TEXT = appStrings.buttonText;

type ConfirmDeleteAddedFoodDialogProps = Readonly<{
  title: string;
  subTitle: string;
  closeBtnAriaLabel: string;
  isPendingSubmit?: boolean;
  handleOnConfirm: () => void;
  handleOnClose: () => void;
}>;

export const ConfirmDeletionModal = ({
  title,
  subTitle,
  closeBtnAriaLabel,
  isPendingSubmit,
  handleOnConfirm,
  handleOnClose,
}: ConfirmDeleteAddedFoodDialogProps) => {
  return (
    <BaseModal
      isModalOpen
      title={title}
      bodyContent={<Typography variant="body">{subTitle}</Typography>}
      onClose={handleOnClose}
      closeBtnAriaLabel={closeBtnAriaLabel}
      actions={
        <Box display="flex" gap={4} width="100%">
          <Button variant="secondary" onClick={handleOnClose} disabled={isPendingSubmit} fullWidth>
            {isPendingSubmit ? <CircularProgress size={24} color="inherit" /> : BUTTON_TEXT.no}
          </Button>
          <Button variant="primary" onClick={handleOnConfirm} disabled={isPendingSubmit} fullWidth>
            {isPendingSubmit ? <CircularProgress size={24} color="inherit" /> : BUTTON_TEXT.yes}
          </Button>
        </Box>
      }
    />
  );
};
