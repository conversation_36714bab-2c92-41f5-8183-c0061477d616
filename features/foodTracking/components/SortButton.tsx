import { Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { BasicMenu } from "@Components/BasicMenu/BasicMenu";

import { FoodTrackingSort } from "../hooks/useSortOrder";

const FOOD_TRACKING = appStrings.features.foodTracking;

type SortButtonProps = Readonly<{
  sortOrder: string;
  setSortOrder: React.Dispatch<React.SetStateAction<FoodTrackingSort>>;
}>;

export function SortButton({ sortOrder, setSortOrder }: SortButtonProps) {
  return (
    <BasicMenu
      buttonVariant="secondary"
      buttonSize="small"
      id="food-tracking-sort-button"
      menuItems={[
        {
          text: FOOD_TRACKING.sortOrder.recentlyTracked,
          onClick: () => setSortOrder(FOOD_TRACKING.sortOrder.recentlyTracked),
        },
        {
          text: FOOD_TRACKING.sortOrder.alphabeticalAsc,
          onClick: () => setSortOrder(FOOD_TRACKING.sortOrder.alphabeticalAsc),
        },
        {
          text: FOOD_TRACKING.sortOrder.alphabeticalDesc,
          onClick: () => setSortOrder(FOOD_TRACKING.sortOrder.alphabeticalDesc),
        },
      ]}
      ariaLabel={FOOD_TRACKING.sortButtonAriaLabel}
    >
      <AppIcon name="Sorting" />
      <Typography variant="actionDense" pl={2} color={color.text.action.hover} tabIndex={-1}>
        {sortOrder}
      </Typography>
    </BasicMenu>
  );
}
