import { Button } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";

const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking;

type SearchAllFoodsButtonProps = Readonly<{
  searchTerm: string;
  onClick: () => void;
}>;

export const SearchAllFoodsButton = ({ searchTerm, onClick }: SearchAllFoodsButtonProps) => {
  return (
    <Button
      variant="secondary"
      size="small"
      onClick={onClick}
      startIcon={<AppIcon name="Search" size="sm" />}
      sx={{ mt: 4 }}
    >
      {FOOD_TRACKING_STRINGS.searchAllFoodsFor(searchTerm)}
    </Button>
  );
};
