import { Button } from "@mui/material";

import { AppIcon, IconVariant } from "@Components/AppIcon/AppIcon";

export type FoodTrackingButtonIcon = Extract<IconVariant, "Diet" | "Edit" | "MenuBook" | "Plus">;

type FoodTrackingButtonProps = Readonly<{
  text: string;
  icon: FoodTrackingButtonIcon;
  onClick: () => void;
  buttonSize?: "small" | "medium";
  disabled?: boolean;
}>;

export const FoodTrackingButton = ({
  text,
  icon,
  onClick,
  buttonSize = "medium",
  disabled,
}: FoodTrackingButtonProps) => {
  return (
    <Button
      startIcon={<AppIcon name={icon} />}
      variant="secondary"
      onClick={onClick}
      disabled={disabled}
      size={buttonSize}
      fullWidth
    >
      {text}
    </Button>
  );
};
