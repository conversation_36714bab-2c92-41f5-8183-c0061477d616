/* eslint-disable no-alert */
import React from "react";
import { COLOR, Food, FoodSource } from "@vivantehealth/vivante-core";
import type { Meta, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import { FoodCardContainer } from "./FoodCardContainer";

const WideView = ({ children }: { children: JSX.Element }) => {
  return (
    <div style={{ width: 684, height: 500, padding: 24, backgroundColor: COLOR.warmColors.warm1 }}>{children}</div>
  );
};

const meta: Meta<typeof FoodCardContainer> = {
  title: "@Features/foodTracking/FoodCardContainer",
  component: FoodCardContainer,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    ...cylinderThemeDecorator,
    (Story) => (
      <WideView>
        <Story />
      </WideView>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof FoodCardContainer>;

export const Primary: Story = {
  args: {
    foodCardData: [
      { name: "Salmon", brand: "Morey's Seafood", servingSize: "1 cup", source: FoodSource.PASSIO_FOOD },
      { name: "Coffee", brand: "Starbucks", servingSize: "1/2 cup", source: FoodSource.PASSIO_FOOD },
    ],
    onFoodCardClick: (food: Food) => alert(`Clicked on ${food.name}`),
    isLoading: false,
  },
};

export const Loading: Story = {
  args: {
    foodCardData: [],
    onFoodCardClick: () => {},
    isLoading: true,
  },
};
