import { useEffect, useRef } from "react";
import { Box, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { SPACING_0_PX, SPACING_16_PX } from "@Assets/style_constants";
import { OutlinedIconButton } from "@Components/OutlinedIconButton/OutlinedIconButton";

type FoodTrackingDrawerHeader = Readonly<{
  title: string;
  showBackBtn?: boolean;
  closeBtnAriaLabel: string;
  backBtnAriaLabel?: string;
  handleOnBack?: () => void;
  handleOnClose: () => void;
}>;

export const FoodTrackingDrawerHeader = ({
  title,
  showBackBtn,
  closeBtnAriaLabel,
  backBtnAriaLabel,
  handleOnBack,
  handleOnClose,
}: FoodTrackingDrawerHeader) => {
  const textRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (textRef?.current) {
      textRef.current?.focus();
    }
  }, [title]);

  return (
    <Box display="flex" justifyContent="space-between" alignItems="center" mb={5}>
      <Box display="flex" alignItems="center">
        {showBackBtn ? (
          <OutlinedIconButton icon="LeftChevron" onClick={() => handleOnBack?.()} ariaLabel={backBtnAriaLabel} />
        ) : null}

        <Typography
          variant="h3"
          color={color.text.strong}
          sx={showBackBtn ? { padding: `${SPACING_0_PX} ${SPACING_16_PX}` } : {}}
          ref={textRef}
        >
          {title}
        </Typography>
      </Box>

      <OutlinedIconButton icon="Close" onClick={() => handleOnClose()} ariaLabel={closeBtnAriaLabel} />
    </Box>
  );
};
