import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { CustomFood, Food, FoodSource, RecipeLog, isCustomFood, isRecipeLog } from "@vivantehealth/vivante-core";

import { appStrings } from "@Assets/app_strings";

import { CustomFoodScreen } from "./components/CustomFoodScreen";
import { PassioFoodScreen } from "./components/PassioFoodScreen";
import { RecipeFoodScreen } from "./components/RecipeFoodScreen";
import { FoodLogStateSlice, foodLogSelector } from "../../store/foodLogStateSlice";
import { ConfirmDeletionModal } from "../ConfirmDeletionModal";
import { FoodTrackingDrawerContainer } from "../FoodTrackingDrawerContainer";

const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking.trackAFood;
const FOOD_TRACKING_A11Y_STRINGS = appStrings.a11y.foodTracking;

type TrackAFoodDrawerProps = Readonly<{
  foodItem?: Food | CustomFood | RecipeLog;
  /** Depending on where the drawer originates from, we may want to perform an action on save */
  onDrawerClose: (shouldPerformSaveAction?: boolean) => void;
  /** The following properties are optional, but will be used if we are editting a food log item */
  numberOfServings?: number;
  servingSize?: string;
  foodLogTime?: Date;
  foodLogId?: string;
  onDeleteFoodLog?: (snackbarMessage: string) => void;
  isDrawerOpen: boolean;
}>;
/**
 * This drawer is utilized to track, edit or delete a food item to the food log. Depending on the food source
 * (Passio, Custom Food or Recipe) it will render the appropriate screen.
 */
export const TrackAFoodDrawer = ({
  isDrawerOpen,
  foodItem,
  onDrawerClose,
  onDeleteFoodLog,
  ...props
}: TrackAFoodDrawerProps) => {
  const dispatch = useDispatch();
  const [isConfirmDeleteModalOpen, setIsConfirmDeleteDialogOpen] = useState(false);
  const submissionStatus = useSelector(foodLogSelector("submissionStatus"));
  const isPendingSubmit = submissionStatus === "pending";

  useEffect(() => {
    if (submissionStatus === "success") {
      onDrawerClose(true);
      dispatch(FoodLogStateSlice.actions.updateSubmissionStatus("idle"));
    }
  }, [dispatch, onDrawerClose, submissionStatus]);

  const handleDeleteFoodLog = () => {
    if (onDeleteFoodLog) {
      onDeleteFoodLog(
        foodItem?.source === FoodSource.CUSTOM_USER_RECIPE
          ? FOOD_TRACKING_STRINGS.removedATrackedRecipeSuccessfully
          : FOOD_TRACKING_STRINGS.removedATrackedFoodSuccessfully,
      );
      setIsConfirmDeleteDialogOpen(false);
    }
  };

  return (
    <>
      {isConfirmDeleteModalOpen && (
        <ConfirmDeletionModal
          title={FOOD_TRACKING_STRINGS.confirmDialog.title}
          subTitle={FOOD_TRACKING_STRINGS.confirmDialog.subTitle}
          closeBtnAriaLabel={FOOD_TRACKING_STRINGS.confirmDialog.ariaLabel}
          isPendingSubmit={isPendingSubmit}
          handleOnConfirm={handleDeleteFoodLog}
          handleOnClose={() => setIsConfirmDeleteDialogOpen(false)}
        />
      )}

      <FoodTrackingDrawerContainer
        isDrawerOpen={isDrawerOpen}
        ariaLabel={FOOD_TRACKING_A11Y_STRINGS.foodTrackingDrawer}
        title={props.foodLogId ? FOOD_TRACKING_STRINGS.editTitle : FOOD_TRACKING_STRINGS.title}
        closeBtnAriaLabel={FOOD_TRACKING_A11Y_STRINGS.closeFoodTrackingDrawer}
        onClose={() => onDrawerClose(false)}
      >
        {foodItem?.source === FoodSource.CUSTOM_USER_FOOD && isCustomFood(foodItem) && (
          <CustomFoodScreen
            foodItem={foodItem}
            onRemoveClicked={() => setIsConfirmDeleteDialogOpen(true)}
            isPendingSubmit={isPendingSubmit}
            {...props}
          />
        )}

        {foodItem?.source === FoodSource.PASSIO_FOOD && (
          <PassioFoodScreen
            foodItem={foodItem}
            onRemoveClicked={() => setIsConfirmDeleteDialogOpen(true)}
            isPendingSubmit={isPendingSubmit}
            {...props}
          />
        )}

        {foodItem?.source === FoodSource.CUSTOM_USER_RECIPE && isRecipeLog(foodItem) && (
          <RecipeFoodScreen
            recipe={foodItem}
            onRemoveClicked={() => setIsConfirmDeleteDialogOpen(true)}
            isPendingSubmit={isPendingSubmit}
            {...props}
          />
        )}
      </FoodTrackingDrawerContainer>
    </>
  );
};
