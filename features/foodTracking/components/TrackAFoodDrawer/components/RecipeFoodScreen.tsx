import { useDispatch } from "react-redux";
import { ClickStreamActivityEventType, RecipeLog } from "@vivantehealth/vivante-core";
import { Box, Divider, Typography } from "@mui/material";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { Form } from "@Components/form/Form";
import { FoodLogStateSlice } from "@Features/foodTracking/store/foodLogStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { FoodLogTimeInput } from "./FoodLogTimeInput";
import { TrackAFoodButtons } from "./TrackAFoodButtons";
import { NumberOfServingsInput } from "../../NumberOfServingsInput";
import { isNumberOfServingsAndTimeFormValues } from "../utils/isNumberOfServingsAndTimeFormValues";

const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking.trackAFood;

type RecipeFoodScreenProps = Readonly<{
  recipe: RecipeLog;
  isPendingSubmit: boolean;
  numberOfServings?: number;
  foodLogTime?: Date;
  foodLogId?: string;
  onRemoveClicked: () => void;
}>;

const getServingSize = (servings: number) => `${servings} ${servings === 1 ? "Serving" : "Servings"}`;

export const RecipeFoodScreen = ({
  recipe,
  isPendingSubmit,
  numberOfServings,
  foodLogTime,
  foodLogId,
  onRemoveClicked,
}: RecipeFoodScreenProps) => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const isEdit = foodLogId !== undefined;
  const { name } = recipe;
  const servingSizeQuantity = Number(recipe.servingSize.split(" ")[0]);
  const defaultValues = {
    numberOfServings: numberOfServings || servingSizeQuantity || 1,
    foodLogTime: isEdit ? dayjs(foodLogTime) : dayjs(),
  };

  const handleSubmit = (data: typeof defaultValues) => {
    const servingSize = getServingSize(data.numberOfServings);

    const submissionData = {
      ...recipe,
      dateTime: data.foodLogTime.toDate(),
      foods: [{ ...recipe, servingSize }],
      numberOfServings: Number(data.numberOfServings),
      servingSize,
    };

    if (isEdit) {
      sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_LOG_EDIT_PERFORMED);
      dispatch(
        FoodLogStateSlice.actions.updateFoodLog({
          ...submissionData,
          foodLogId,
          timepoint: submissionData.dateTime,
          snackbarMessage: FOOD_TRACKING_STRINGS.edittedATrackedRecipeSuccessfully,
        }),
      );
    } else {
      sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_LOG_SAVE_PERFORMED);
      dispatch(
        FoodLogStateSlice.actions.saveFoodLog({
          ...submissionData,
          snackbarMessage: FOOD_TRACKING_STRINGS.trackedARecipeSuccessfully,
        }),
      );
    }
  };

  return (
    <Form
      onSubmit={(data) => {
        if (isNumberOfServingsAndTimeFormValues(data)) {
          handleSubmit(data);
        }
      }}
      defaultValues={defaultValues}
      sx={{ height: "100%" }}
    >
      <Box height="calc(100% - 50px)" overflow="auto">
        <Box mb={5}>
          <Typography variant="h3" pb={2}>
            {name}
          </Typography>
          <Typography variant="body">{getServingSize(recipe.servings)}</Typography>

          <Divider sx={{ mt: 4 }} />
        </Box>

        <Box display="flex" flexDirection="column" gap={5}>
          <NumberOfServingsInput />
          <FoodLogTimeInput />
        </Box>
      </Box>

      <TrackAFoodButtons isEdit={isEdit} isPending={isPendingSubmit} onRemoveClicked={onRemoveClicked} />
    </Form>
  );
};
