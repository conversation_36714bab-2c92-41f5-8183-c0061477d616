import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import dayjs, { Dayjs } from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { DateTimeInput } from "@Components/form/Fields";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

const SHARED_FORM_TEXT = appStrings.sharedFormText;
const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking.trackAFood;

export const FoodLogTimeInput = () => {
  const { sendEventAnalytics } = useAnalyticsHook();
  /**
   * We may end up with multiple analytic events sent here. This is a result of the fact that DateTimePicker
   * which is used for DateTimeInput component will only fire the onBlur event when the user clicks outside
   * of the input area, while the onAccept event will only fire when the user uses the DateTime picker itself
   */
  const handleAcceptedDateTimeAnalytics = (value: string | Dayjs | null) => {
    if (!value) {
      return;
    }

    const [date, time] = dayjs(value).format("YYYY-MM-DD HH:mm:ss").split(" ");

    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_DATE_SELECTED, { date });
    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_TIME_SELECTED, { time });
  };

  return (
    <DateTimeInput
      label={FOOD_TRACKING_STRINGS.foodLogTime}
      name="foodLogTime"
      rules={{
        required: { value: true, message: SHARED_FORM_TEXT.requiredMessage },
        validate: (value) => {
          return (dayjs.isDayjs(value) && value.isValid()) || SHARED_FORM_TEXT.invalidDateTime;
        },
      }}
      onOpen={() => {
        sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_DATE_PICKER_OPENED);
      }}
      onAccept={handleAcceptedDateTimeAnalytics}
      onBlur={handleAcceptedDateTimeAnalytics}
    />
  );
};
