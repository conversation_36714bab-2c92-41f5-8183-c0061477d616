import { useDispatch } from "react-redux";
import { ClickStreamActivityEventType, Food } from "@vivantehealth/vivante-core";
import { Box, Divider, Typography } from "@mui/material";
import dayjs, { Dayjs } from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { Form } from "@Components/form/Form";
import { InputWithLabelSkeleton } from "@Components/InputWithLabel/InputWithLabelSkeleton";
import { useSearchByFoodIdQuery } from "@Features/foodTracking/api/foodLogApi";
import { FoodLogStateSlice } from "@Features/foodTracking/store/foodLogStateSlice";
import { generateServingSizeOptions } from "@Features/foodTracking/utils/generateServingSizeOptions";
import { getNutritionFacts } from "@Features/foodTracking/utils/getNutritionFacts";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { FoodLogTimeInput } from "./FoodLogTimeInput";
import { TrackAFoodButtons } from "./TrackAFoodButtons";
import { NumberOfServingsInput } from "../../NumberOfServingsInput";
import { PassioServingSizeSelector } from "../../PassioServingSizeSelector";
import { isNumberOfServingsAndTimeFormValues } from "../utils/isNumberOfServingsAndTimeFormValues";

const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking.trackAFood;
const SKELETON_LOADING_COUNT = 3;

type PassioFoodScreenProps = Readonly<{
  foodItem: Food;
  isPendingSubmit: boolean;
  numberOfServings?: number;
  servingSize?: string;
  foodLogTime?: Date;
  foodLogId?: string;
  onRemoveClicked: () => void;
}>;

const isPassioFoodFormValues = (
  data: unknown,
): data is { numberOfServings: number; servingSize: string; foodLogTime: Dayjs } => {
  return isNumberOfServingsAndTimeFormValues(data) && "servingSize" in data;
};

export const PassioFoodScreen = ({
  foodItem,
  isPendingSubmit,
  numberOfServings,
  servingSize,
  foodLogTime,
  foodLogId,
  onRemoveClicked,
}: PassioFoodScreenProps) => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  /** We use the skip option when passioId is undefined so as to not run the query */
  const { data: passioFoodData, isLoading } = useSearchByFoodIdQuery(foodItem?.meta?.passioId ?? "", {
    skip: foodItem?.meta?.passioId === undefined,
  });
  const isEdit = foodLogId !== undefined;
  const { name, brand, meta } = foodItem;
  const servingSizeOptions = generateServingSizeOptions([
    ...(meta?.servingSizes ?? []),
    ...(passioFoodData?.portions?.map(({ name, quantity }) => ({ quantity, unitName: name })) ?? []),
  ]);

  const defaultValues = {
    /** The first option should be our default serving size from Passio */
    servingSize:
      servingSizeOptions.find(({ label }) => {
        if (servingSize) return label === servingSize;

        return label === foodItem.servingSize;
      })?.value ?? servingSizeOptions[0]?.value,
    /** Total number of servings the user is tracking. Defaults to 1 */
    numberOfServings: numberOfServings ?? foodItem?.numberOfServings ?? 1,
    /** The time the user is tracking. Defaults to current day/time */
    foodLogTime: isEdit ? dayjs(foodLogTime) : dayjs(),
  };

  const handleSubmit = (data: typeof defaultValues) => {
    const nutrientFacts =
      foodItem.meta && passioFoodData
        ? getNutritionFacts(foodItem.meta, passioFoodData, data.servingSize)
        : { calories: 0 };

    const submissionData = {
      dateTime: data.foodLogTime.toDate(),
      foods: [{ ...foodItem, nutrientFacts }],
      numberOfServings: data.numberOfServings,
      servingSize: data.servingSize,
      ...(foodItem.brand ? { brand: foodItem.brand } : {}),
      nutrientFacts,
    };

    if (isEdit) {
      sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_LOG_EDIT_PERFORMED);
      dispatch(
        FoodLogStateSlice.actions.updateFoodLog({
          ...submissionData,
          foodLogId,
          timepoint: submissionData.dateTime,
          snackbarMessage: FOOD_TRACKING_STRINGS.edittedATrackedFoodSuccessfully,
        }),
      );
    } else {
      sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_LOG_SAVE_PERFORMED);

      dispatch(
        FoodLogStateSlice.actions.saveFoodLog({
          ...submissionData,
          snackbarMessage: FOOD_TRACKING_STRINGS.trackedAFoodSuccessfully,
        }),
      );
    }
  };

  return (
    <Form
      onSubmit={(data) => {
        if (isPassioFoodFormValues(data)) {
          handleSubmit(data);
        }
      }}
      defaultValues={defaultValues}
      sx={{ height: "100%" }}
    >
      <Box height="calc(100% - 50px)" overflow="auto">
        <Box mb={5}>
          <Typography variant="h3" pb={2}>
            {name}
          </Typography>
          <Typography variant="body">{brand ?? ""}</Typography>

          <Divider sx={{ mt: 4 }} />
        </Box>
        <Box display="flex" flexDirection="column" gap={5}>
          {isLoading ? (
            [...Array(SKELETON_LOADING_COUNT)].map((_, skeletonIndex) => (
              <InputWithLabelSkeleton key={`skeleton-${skeletonIndex}`} />
            ))
          ) : (
            <>
              <PassioServingSizeSelector servingSizeOptions={servingSizeOptions} />
              <NumberOfServingsInput />
              <FoodLogTimeInput />
            </>
          )}
        </Box>
      </Box>

      <TrackAFoodButtons isEdit={isEdit} isPending={isPendingSubmit} onRemoveClicked={onRemoveClicked} />
    </Form>
  );
};
