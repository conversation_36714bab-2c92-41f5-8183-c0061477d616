import { useDispatch } from "react-redux";
import { ClickStreamActivityEventType, CustomFood, CustomFoodV2, isCustomFoodV2 } from "@vivantehealth/vivante-core";
import { Box, Divider, Typography } from "@mui/material";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { Form } from "@Components/form/Form";
import { FoodLogStateSlice } from "@Features/foodTracking/store/foodLogStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { FoodLogTimeInput } from "./FoodLogTimeInput";
import { TrackAFoodButtons } from "./TrackAFoodButtons";
import { NumberOfServingsInput } from "../../NumberOfServingsInput";
import { isNumberOfServingsAndTimeFormValues } from "../utils/isNumberOfServingsAndTimeFormValues";

const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking.trackAFood;

type CustomFoodScreenProps = Readonly<{
  foodItem: CustomFood;
  isPendingSubmit: boolean;
  numberOfServings?: number;
  foodLogTime?: Date;
  foodLogId?: string;
  onRemoveClicked: () => void;
}>;

const getCustomFoodV2Values = (foodItem: CustomFoodV2) => {
  return {
    servingSize: foodItem.servingSize,
    nutrientFacts: foodItem.nutrientFacts,
    ...(foodItem.brand ? { brand: foodItem.brand } : {}),
  };
};

export const CustomFoodScreen = ({
  foodItem,
  isPendingSubmit,
  numberOfServings,
  foodLogTime,
  foodLogId,
  onRemoveClicked,
}: CustomFoodScreenProps) => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const isEdit = foodLogId !== undefined;
  const { name } = foodItem;
  const foodIsCustomFoodV2 = isCustomFoodV2(foodItem);
  const defaultValues = {
    numberOfServings: (foodIsCustomFoodV2 && foodItem.numberOfServings) || numberOfServings || 1,
    foodLogTime: isEdit ? dayjs(foodLogTime) : dayjs(),
  };

  const handleSubmit = (data: typeof defaultValues) => {
    const submissionData = {
      dateTime: data.foodLogTime.toDate(),
      foods: [foodItem],
      numberOfServings: Number(data.numberOfServings),
      ...(foodIsCustomFoodV2 ? getCustomFoodV2Values(foodItem) : {}),
    };

    if (isEdit) {
      sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_LOG_EDIT_PERFORMED);
      dispatch(
        FoodLogStateSlice.actions.updateFoodLog({
          ...submissionData,
          foodLogId,
          timepoint: submissionData.dateTime,
          snackbarMessage: FOOD_TRACKING_STRINGS.edittedATrackedFoodSuccessfully,
        }),
      );
    } else {
      sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_LOG_SAVE_PERFORMED);
      dispatch(
        FoodLogStateSlice.actions.saveFoodLog({
          ...submissionData,
          snackbarMessage: FOOD_TRACKING_STRINGS.trackedAFoodSuccessfully,
        }),
      );
    }
  };

  return (
    <Form
      onSubmit={(data) => {
        if (isNumberOfServingsAndTimeFormValues(data)) {
          handleSubmit(data);
        }
      }}
      defaultValues={defaultValues}
      sx={{ height: "100%" }}
    >
      <Box height="calc(100% - 50px)" overflow="auto">
        <Box mb={5}>
          <Typography variant="h3" pb={2}>
            {name}
          </Typography>
          {foodIsCustomFoodV2 && foodItem.brand && (
            <Typography variant="body" pb={2}>
              {foodItem.brand}
            </Typography>
          )}
          {foodIsCustomFoodV2 && <Typography variant="body">{`1 serving - ${foodItem.servingSize}`}</Typography>}

          <Divider sx={{ mt: 4 }} />
        </Box>

        <Box display="flex" flexDirection="column" gap={5}>
          <NumberOfServingsInput />
          <FoodLogTimeInput />
        </Box>
      </Box>

      <TrackAFoodButtons isEdit={isEdit} isPending={isPendingSubmit} onRemoveClicked={onRemoveClicked} />
    </Form>
  );
};
