import { Box, Button, CircularProgress } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

const FOOD_TRACKING_BUTTON_STRINGS = appStrings.features.foodTracking.trackAFood.buttonText;

type TrackAFoodButtonsProps = Readonly<{ isEdit: boolean; isPending: boolean; onRemoveClicked: () => void }>;

export const TrackAFoodButtons = ({ isEdit, isPending, onRemoveClicked }: TrackAFoodButtonsProps) => {
  return (
    <Box display="flex" gap={2}>
      {isEdit ? (
        <>
          <Button variant="secondary" onClick={onRemoveClicked} fullWidth disabled={isPending}>
            {isPending ? <CircularProgress size={24} color="inherit" /> : FOOD_TRACKING_BUTTON_STRINGS.remove}
          </Button>
          <Button type="submit" variant="primary" fullWidth disabled={isPending}>
            {isPending ? <CircularProgress size={24} color="inherit" /> : FOOD_TRACKING_BUTTON_STRINGS.save}
          </Button>
        </>
      ) : (
        <Button type="submit" variant="primary" fullWidth disabled={isPending}>
          {isPending ? <CircularProgress size={24} color="inherit" /> : FOOD_TRACKING_BUTTON_STRINGS.trackFood}
        </Button>
      )}
    </Box>
  );
};
