import dayjs from "dayjs";
import { describe, test, expect } from "vitest";

import { isNumberOfServingsAndTimeFormValues } from "./isNumberOfServingsAndTimeFormValues";

describe("isNumberOfServingsAndTimeFormValues", () => {
  test("Should return true for valid form object", () => {
    const validFormObject = { numberOfServings: 2, foodLogTime: dayjs() };

    expect(isNumberOfServingsAndTimeFormValues(validFormObject)).toBe(true);
  });

  test("Should return false for null or undefined data", () => {
    expect(isNumberOfServingsAndTimeFormValues(null)).toBe(false);
    expect(isNumberOfServingsAndTimeFormValues(undefined)).toBe(false);
  });

  test("Should return false for non-object data", () => {
    const nonObjectData = "string";

    expect(isNumberOfServingsAndTimeFormValues(nonObjectData)).toBe(false);
  });

  test("Should return false for object without numberOfServings", () => {
    const invalidFormObject = { foodLogTime: dayjs() };

    expect(isNumberOfServingsAndTimeFormValues(invalidFormObject)).toBe(false);
  });

  test("Should return false for object without foodLogTime", () => {
    const invalidFormObject = { numberOfServings: 2 };

    expect(isNumberOfServingsAndTimeFormValues(invalidFormObject)).toBe(false);
  });
});
