/**
 * Converts a decimal to a fraction. To do so, we start with a fraction of 1/1 and increment
 * the numerator or denominator until the error is within a certain tolerance.
 * We can adjust the tolerance to get a more accurate fraction, but the value we use should be sufficient.
 */
export const decimalToFraction = (decimal: number) => {
  const tolerance = 1.0e-2;
  let numerator = 1;
  let denominator = 1;
  let currentError = Math.abs(decimal - numerator / denominator);

  while (currentError > tolerance) {
    if (decimal > numerator / denominator) {
      numerator++;
    } else {
      denominator++;
    }

    currentError = Math.abs(decimal - numerator / denominator);
  }

  return `${numerator}/${denominator}`;
};

export const generateServingSizeString = (quantity: number, unitName: string) => {
  const [wholeNumber, decimalNumber] = quantity.toString().split(".").map(Number);
  const fraction = decimalNumber ? decimalToFraction(parseFloat(`.${decimalNumber}`)) : "";

  return `${wholeNumber || ""}${wholeNumber && fraction.length ? ` ${fraction}` : fraction} ${unitName
    .slice(0, 1)
    .toUpperCase()}${unitName.slice(1)}`;
};
