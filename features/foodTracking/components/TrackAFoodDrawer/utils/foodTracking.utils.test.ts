import { describe, test, expect } from "vitest";

import { generateServingSizeString, decimalToFraction } from "./foodTracking.utils";

describe("generateServingSizeString", () => {
  test("Should return the correct serving size string", () => {
    expect(generateServingSizeString(1, "cup")).toBe("1 Cup");
    expect(generateServingSizeString(0.5, "teaspoon")).toBe("1/2 Teaspoon");
    expect(generateServingSizeString(2.25, "ounce")).toBe("2 1/4 Ounce");
  });
});

describe("decimalToFraction", () => {
  test("Should convert a decimal to the expected fraction", () => {
    expect(decimalToFraction(0.5)).toBe("1/2");
    expect(decimalToFraction(0.33)).toBe("1/3");
    expect(decimalToFraction(0.25)).toBe("1/4");
    expect(decimalToFraction(0.2)).toBe("1/5");
    expect(decimalToFraction(0.75)).toBe("3/4");
  });
});
