import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { ClickStreamActivityEventType, CustomFood, Food, RecipeLog } from "@vivantehealth/vivante-core";
import { Box } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { AddedFoodsDrawer, AddedFoodsDrawerState } from "./AddedFoodsDrawer/AddedFoodsDrawer";
import { FoodCardContainer } from "./FoodCardContainer";
import { FoodTrackingButton } from "./FoodTrackingButton";
import { FoodTrackingTitleSection } from "./FoodTrackingTitleSection";
import { SearchAllFoodsButton } from "./SearchAllFoodsButton";
import { useSortOrder } from "../hooks/useSortOrder";
import { foodLogSelector } from "../store/foodLogStateSlice";

const FOOD_TRACKING = appStrings.features.foodTracking;

type AddedFoodsScreenProps = Readonly<{
  searchTerm: string;
  isAddedFoodsDrawerOpen: boolean;
  toggleAddedFoodsDrawer: (isOpen: boolean) => void;
  handleTrackAFood: (food: Food | CustomFood | RecipeLog) => void;
  handleSearchPassio: () => void;
}>;

const getEmptyStateText = (shouldDisplayEmptyStateText: boolean, isFilteredResultsEmpty: boolean) => {
  if (isFilteredResultsEmpty) {
    return FOOD_TRACKING.emptyState.addedFoods.noSearchResults;
  }
  if (shouldDisplayEmptyStateText) {
    return FOOD_TRACKING.emptyState.addedFoods.subHeader;
  }

  return "";
};

export function AddedFoodsScreen({
  searchTerm,
  isAddedFoodsDrawerOpen,
  toggleAddedFoodsDrawer,
  handleTrackAFood,
  handleSearchPassio,
}: AddedFoodsScreenProps) {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const [selectedFood, setSelectedFood] = useState<Partial<AddedFoodsDrawerState>>({});
  const isLoading = useSelector(foodLogSelector("loadState")) === "loading";
  const customFoodItems = useSelector(foodLogSelector("customFoodItems"));
  const filteredCustomFoodItem = searchTerm.length
    ? customFoodItems.filter((food) => food.name.toLowerCase().includes(searchTerm.toLowerCase()))
    : customFoodItems;
  const [sortOrder, setSortOrder] = useSortOrder("Added foods");
  const isFilteredResultsEmpty = filteredCustomFoodItem.length === 0 && searchTerm.length > 0;
  const shouldDisplayEmptyStateText = !isLoading && !customFoodItems.length;

  const handleViewAddedFoods = () => {
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: "/progress/food-tracking/added-foods",
        screenName: "FoodTrackingViewAddedFoods",
      }),
    );
  };

  const handleOnAddedFoodsDrawerClose = (customFoodSaved: boolean) => {
    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_CUSTOM_FOOD_CREATION_CLOSED, {
      customFoodSaved: customFoodSaved ? "Yes" : "No",
    });
    toggleAddedFoodsDrawer(false);
    setSelectedFood({});
  };

  const handleAddANewFoodClick = () => {
    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_CUSTOM_FOOD_CREATION_STARTED);
    toggleAddedFoodsDrawer(true);
  };

  return (
    <>
      <AddedFoodsDrawer
        {...selectedFood}
        isDrawerOpen={isAddedFoodsDrawerOpen}
        onDrawerClose={(customFoodSaved = false) => handleOnAddedFoodsDrawerClose(customFoodSaved)}
        key={isAddedFoodsDrawerOpen.toString()}
      />

      <Box display="flex" gap={6} pb={6}>
        <FoodTrackingButton text={FOOD_TRACKING.addedFood.addANewFood} icon="Plus" onClick={handleAddANewFoodClick} />

        <FoodTrackingButton
          text={FOOD_TRACKING.addedFood.viewAddedFoods}
          icon="Diet"
          onClick={handleViewAddedFoods}
          disabled={isLoading || !customFoodItems.length}
        />
      </Box>

      <FoodTrackingTitleSection
        title={shouldDisplayEmptyStateText ? FOOD_TRACKING.emptyState.addedFoods.header : FOOD_TRACKING.history}
        sortOrder={sortOrder}
        setSortOrder={setSortOrder}
        emptyStateText={getEmptyStateText(shouldDisplayEmptyStateText, isFilteredResultsEmpty)}
      />

      {isFilteredResultsEmpty ? (
        <SearchAllFoodsButton searchTerm={searchTerm} onClick={handleSearchPassio} />
      ) : (
        <FoodCardContainer
          isLoading={isLoading}
          foodCardData={filteredCustomFoodItem}
          onFoodCardClick={handleTrackAFood}
        />
      )}
    </>
  );
}
