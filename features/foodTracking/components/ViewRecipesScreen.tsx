import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ClickStreamActivityEventType, RecipeLog, isRecipeLog } from "@vivantehealth/vivante-core";
import { Food } from "@vivantehealth/vivante-core";
import { IconButton, InputAdornment, OutlinedInput, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { useDebounceValue } from "usehooks-ts";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { BackButton } from "@Components/BackButton/BackButton";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { FoodCardContainer } from "./FoodCardContainer";
import { NoSearchResults } from "./NoSearchResults";
import { RecipeDetailsDrawer } from "./RecipeDetailsDrawer/RecipeDetailsDrawer";
import { RecipeDrawer } from "./RecipeDrawer/RecipeDrawer";
import { FOOD_TRACKING_DEBOUNCE_TIME } from "../assets/constants";
import { FoodLogStateSlice, foodLogSelector } from "../store/foodLogStateSlice";

const BUTTON_TEXT = appStrings.buttonText;
const FOOD_TRACKING = appStrings.features.foodTracking;

export const ViewRecipesScreen = () => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const [searchTerm, setSearchTerm] = useState("");
  const [isRecipeDetailsDrawerOpen, setIsRecipeDetailsDrawerOpen] = useState(false);
  const [isRecipeDrawerOpen, setIsRecipeDrawerOpen] = useState(false);
  const [selectedRecipe, setSelectedRecipe] = useState<RecipeLog>();
  const isLoading = useSelector(foodLogSelector("loadState")) === "loading";
  const recipes = useSelector(foodLogSelector("recipes"));

  const [debouncedSearchTerm] = useDebounceValue(searchTerm, FOOD_TRACKING_DEBOUNCE_TIME);
  /** If searchTerm is cleared, we shouldn't make the user wait the debounce time */
  const searchTermToUse = searchTerm.length ? debouncedSearchTerm : searchTerm;
  const filteredRecipes = searchTermToUse.length
    ? recipes.filter((food) => food.name.toLowerCase().includes(searchTermToUse.toLowerCase()))
    : recipes;

  useEffect(() => {
    dispatch(FoodLogStateSlice.actions.loadRecipes());
  }, [dispatch]);

  useEffect(() => {
    /** If recipes updates, get the latest version of the selected recipe so it refreshes in the details drawer */
    if (selectedRecipe) {
      const updatedRecipe = recipes.find((recipe) => recipe.id === selectedRecipe.id);

      if (updatedRecipe) {
        setSelectedRecipe(updatedRecipe);
      }
    }
  }, [recipes, selectedRecipe]);

  const handleGoBackToFoodTracking = () => {
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: `/progress/food-tracking?tabToRedirectTo=${FOOD_TRACKING.tabs.myRecipes}`,
        screenName: "FoodTracking",
      }),
    );
  };

  const handleFoodCardClick = (recipe: Food) => {
    if (isRecipeLog(recipe)) {
      sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_RECIPE_EDIT_STARTED);
      setSelectedRecipe(recipe);
      setIsRecipeDetailsDrawerOpen(true);
    }
  };

  const handleRecipeDrawerClose = (recipeSaved: boolean) => {
    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_RECIPE_CREATION_CLOSED, {
      recipeSaved: recipeSaved ? "Yes" : "No",
    });
    setIsRecipeDrawerOpen(false);
    setSearchTerm("");
  };

  const handleRecipeDetailsDrawerClosed = () => {
    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_RECIPE_EDIT_CLOSED);
    setIsRecipeDetailsDrawerOpen(false);
  };

  const handleAddNewRecipeClick = () => {
    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_RECIPE_CREATION_STARTED);
    setIsRecipeDrawerOpen(true);
  };

  /**
   * If the user has no recipe items, redirect them back to Track a food page
   * which by using the query param tabToRedirectTo=myRecipes will redirect them to the My recipes tab
   */
  if (!isLoading && recipes.length === 0) {
    handleGoBackToFoodTracking();
  }

  return (
    <>
      <RecipeDetailsDrawer
        isDrawerOpen={isRecipeDetailsDrawerOpen && !!selectedRecipe}
        recipe={selectedRecipe}
        onDrawerClose={handleRecipeDetailsDrawerClosed}
        key={selectedRecipe?.id}
      />

      <RecipeDrawer
        isDrawerOpen={isRecipeDrawerOpen}
        onDrawerClose={(recipeSaved = false) => handleRecipeDrawerClose(recipeSaved)}
      />
      <BackButton onClick={handleGoBackToFoodTracking}>{BUTTON_TEXT.back}</BackButton>

      <Typography variant="h1Serif" aria-label={`${FOOD_TRACKING.viewRecipes.title} screen`} my={5}>
        {FOOD_TRACKING.viewRecipes.title}
      </Typography>

      <OutlinedInput
        value={searchTerm}
        onChange={(event) => setSearchTerm(event.target.value)}
        placeholder={FOOD_TRACKING.viewRecipes.inputPlaceholder}
        startAdornment={
          <InputAdornment position="start">
            <AppIcon name="Search" color={color.icon.strong} />
          </InputAdornment>
        }
        endAdornment={
          searchTerm ? (
            <InputAdornment position="end">
              <IconButton
                aria-label={FOOD_TRACKING.clearTextButtonAriaLabel}
                sx={{ p: 0 }}
                onClick={() => setSearchTerm("")}
                disableRipple
              >
                <AppIcon name="Close" color={color.icon.strong} />
              </IconButton>
            </InputAdornment>
          ) : null
        }
        fullWidth
        sx={{ mb: 6 }}
      />

      {filteredRecipes.length === 0 ? (
        <NoSearchResults
          emptyStateText={FOOD_TRACKING.recipes.noSearchResults}
          buttonText={FOOD_TRACKING.recipes.addANewRecipe}
          buttonIcon="MenuBook"
          onAddNewButtonClicked={handleAddNewRecipeClick}
        />
      ) : (
        <FoodCardContainer
          isLoading={isLoading}
          foodCardData={filteredRecipes}
          foodCardRightIcon="chevronRight"
          onFoodCardClick={handleFoodCardClick}
        />
      )}
    </>
  );
};
