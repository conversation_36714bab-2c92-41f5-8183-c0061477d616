import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ClickStreamActivityEventType, CustomFood, isCustomFood } from "@vivantehealth/vivante-core";
import { IconButton, InputAdornment, OutlinedInput, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { useDebounceValue } from "usehooks-ts";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { BackButton } from "@Components/BackButton/BackButton";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { AddedFoodsDrawer, AddedFoodsDrawerState } from "./AddedFoodsDrawer/AddedFoodsDrawer";
import { FoodCardContainer } from "./FoodCardContainer";
import { FoodDetailsDrawer } from "./FoodDetailsDrawer/FoodDetailsDrawer";
import { NoSearchResults } from "./NoSearchResults";
import { FOOD_TRACKING_DEBOUNCE_TIME } from "../assets/constants";
import { FoodLogStateSlice, foodLogSelector } from "../store/foodLogStateSlice";

const BUTTON_TEXT = appStrings.buttonText;
const FOOD_TRACKING = appStrings.features.foodTracking;

export const ViewAllAddedFoodsScreen = () => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const [searchTerm, setSearchTerm] = useState("");
  const [isFoodDetailsDrawerOpen, setIsFoodDetailsDrawerOpen] = useState(false);
  const [selectedFoodItem, setSelectedFoodItem] = useState<CustomFood>();
  const [isAddedFoodsDrawerOpen, setIsAddedFoodsDrawerOpen] = useState(false);
  const [selectedFood, setSelectedFood] = useState<Partial<AddedFoodsDrawerState>>({});
  const isLoading = useSelector(foodLogSelector("loadState")) === "loading";
  const customFoodItems = useSelector(foodLogSelector("customFoodItems"));

  const [debouncedSearchTerm] = useDebounceValue(searchTerm, FOOD_TRACKING_DEBOUNCE_TIME);
  /** If searchTerm is cleared, we shouldn't make the user wait the debounce time */
  const searchTermToUse = searchTerm.length ? debouncedSearchTerm : searchTerm;
  const filteredCustomFoodItem = searchTermToUse.length
    ? customFoodItems.filter((food) => food.name.toLowerCase().includes(searchTermToUse.toLowerCase()))
    : customFoodItems;

  useEffect(() => {
    dispatch(FoodLogStateSlice.actions.loadCustomFoodItems());
  }, [dispatch]);

  const handleGoBackToFoodTracking = () => {
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: `/progress/food-tracking?tabToRedirectTo=${FOOD_TRACKING.tabs.addedFoods}`,
        screenName: "FoodTracking",
      }),
    );
  };

  const handleFoodSelected = (food: CustomFood) => {
    setSelectedFoodItem(food);
    setIsFoodDetailsDrawerOpen(true);
  };

  const handleOnFoodDetailsDrawerClose = () => {
    setIsFoodDetailsDrawerOpen(false);
    setSelectedFoodItem(undefined);
  };

  const handleOnAddedFoodsDrawerClose = (customFoodSaved: boolean) => {
    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_CUSTOM_FOOD_CREATION_CLOSED, {
      customFoodSaved: customFoodSaved ? "Yes" : "No",
    });
    setIsAddedFoodsDrawerOpen(false);
    setSelectedFood({});
    setSearchTerm("");
  };

  const handleAddANewFoodClick = () => {
    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_CUSTOM_FOOD_CREATION_STARTED);
    setIsAddedFoodsDrawerOpen(true);
  };

  /**
   * If the user has no custom food items, redirect them back to Track a food page
   * which by using the query param tabToRedirectTo=addedFoods will redirect them to the Added foods tab
   */
  if (!isLoading && customFoodItems.length === 0) {
    handleGoBackToFoodTracking();
  }

  return (
    <>
      <FoodDetailsDrawer
        isDrawerOpen={isFoodDetailsDrawerOpen && !!selectedFoodItem}
        foodItem={selectedFoodItem}
        onDrawerClose={handleOnFoodDetailsDrawerClose}
      />
      <AddedFoodsDrawer
        {...selectedFood}
        isDrawerOpen={isAddedFoodsDrawerOpen}
        onDrawerClose={(customFoodSaved = false) => handleOnAddedFoodsDrawerClose(customFoodSaved)}
      />
      <BackButton onClick={handleGoBackToFoodTracking}>{BUTTON_TEXT.back}</BackButton>

      <Typography variant="h1Serif" aria-label={`${FOOD_TRACKING.viewAllAddedFoods.title} screen`} my={5}>
        {FOOD_TRACKING.viewAllAddedFoods.title}
      </Typography>

      <OutlinedInput
        value={searchTerm}
        onChange={(event) => setSearchTerm(event.target.value)}
        placeholder={FOOD_TRACKING.viewAllAddedFoods.inputPlaceholder}
        startAdornment={
          <InputAdornment position="start">
            <AppIcon name="Search" color={color.icon.strong} />
          </InputAdornment>
        }
        endAdornment={
          searchTerm ? (
            <InputAdornment position="end">
              <IconButton
                aria-label={FOOD_TRACKING.clearTextButtonAriaLabel}
                sx={{ p: 0 }}
                onClick={() => setSearchTerm("")}
                disableRipple
              >
                <AppIcon name="Close" color={color.icon.strong} />
              </IconButton>
            </InputAdornment>
          ) : null
        }
        fullWidth
        sx={{ mb: 6 }}
      />

      {filteredCustomFoodItem.length === 0 ? (
        <NoSearchResults
          emptyStateText={FOOD_TRACKING.emptyState.shared.noSearchResults}
          buttonText={FOOD_TRACKING.addedFood.addANewFood}
          buttonIcon="Diet"
          onAddNewButtonClicked={handleAddANewFoodClick}
        />
      ) : (
        <FoodCardContainer
          isLoading={isLoading}
          foodCardData={filteredCustomFoodItem}
          foodCardRightIcon="chevronRight"
          onFoodCardClick={(food) => {
            if (isCustomFood(food)) {
              handleFoodSelected(food);
            }
          }}
        />
      )}
    </>
  );
};
