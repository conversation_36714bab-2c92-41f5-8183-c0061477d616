import React, { useEffect, useRef } from "react";
import { Box, Skeleton, Typography } from "@mui/material";
import { typography } from "@vivantehealth/design-tokens";

import { SortButton } from "./SortButton";
import { FoodTrackingSort } from "../hooks/useSortOrder";

type FoodTrackingTitleSectionProps = {
  title: string;
  sortOrder?: FoodTrackingSort;
  setSortOrder?: React.Dispatch<React.SetStateAction<FoodTrackingSort>>;
  emptyStateText: string;
  hideSortButton?: boolean;
  isLoading?: boolean;
  shouldAutoFocus?: boolean;
};

export const FoodTrackingTitleSection = ({
  title,
  sortOrder,
  setSortOrder,
  emptyStateText,
  hideSortButton,
  isLoading,
  shouldAutoFocus,
}: FoodTrackingTitleSectionProps) => {
  const titleRef = useRef<HTMLDivElement | null>(null);
  const shouldDisplaySortbutton = emptyStateText.length === 0 && !hideSortButton && sortOrder && setSortOrder;

  useEffect(() => {
    if (!isLoading && shouldAutoFocus && titleRef?.current) {
      titleRef.current?.focus();
    }
  }, [isLoading, shouldAutoFocus, titleRef]);

  return (
    <>
      <Box display="flex" justifyContent="space-between" alignItems="center" pb={4}>
        {isLoading ? (
          <Skeleton variant="text" sx={{ width: "125px", fontSize: typography.heading2.fontSize }} />
        ) : (
          <Typography variant="h2Serif" ref={titleRef} sx={{ "&:focus-visible": { outline: "none" } }}>
            {title}
          </Typography>
        )}
        {shouldDisplaySortbutton && <SortButton sortOrder={sortOrder} setSortOrder={setSortOrder} />}
      </Box>
      {emptyStateText.length > 0 && <Typography variant="body">{emptyStateText}</Typography>}
    </>
  );
};
