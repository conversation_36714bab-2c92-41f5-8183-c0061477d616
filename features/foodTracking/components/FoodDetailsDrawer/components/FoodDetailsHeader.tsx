import { CustomFood, isCustomFoodV2 } from "@vivantehealth/vivante-core";
import { Box, Divider, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

const FOOD_DETAILS_DRAWER_STRINGS = appStrings.features.foodTracking.viewAllAddedFoods.drawer;

type FoodDetailsHeaderProps = Readonly<{
  foodItem?: CustomFood;
}>;

export const FoodDetailsHeader = ({ foodItem }: FoodDetailsHeaderProps) => {
  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <Typography variant="h3">{foodItem?.name}</Typography>
      {foodItem && isCustomFoodV2(foodItem) ? (
        <>
          {foodItem?.brand && <Typography variant="body">{foodItem?.brand}</Typography>}
          <Typography variant="body">
            {FOOD_DETAILS_DRAWER_STRINGS.servingsPerContainer(
              foodItem?.servingsPerContainer,
              foodItem?.nutrientFacts.calories,
            )}
          </Typography>
          <Typography variant="body">{FOOD_DETAILS_DRAWER_STRINGS.servingSize(foodItem?.servingSize)}</Typography>
        </>
      ) : null}
      <Divider sx={{ mt: 2, mb: 5 }} />
    </Box>
  );
};
