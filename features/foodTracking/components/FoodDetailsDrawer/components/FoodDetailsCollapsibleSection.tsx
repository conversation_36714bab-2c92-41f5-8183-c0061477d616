import { useState } from "react";
import { Box, Divider, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

import { SeeMoreButton } from "../../SeeMoreButton";

export type FoodDetailListItem = Readonly<{
  title: string;
  value?: number | string;
}>;
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const FOOD_DETAILS_DRAWER_STRINGS = appStrings.features.foodTracking.viewAllAddedFoods.drawer;

type FoodDetailsCollapsibleSectionProps = Readonly<{
  title: typeof FOOD_DETAILS_DRAWER_STRINGS.nutritionFacts | typeof FOOD_DETAILS_DRAWER_STRINGS.ingredientList;
  foodDetailsListItems: FoodDetailListItem[];
  noListItemsText?: string;
}>;

export const FoodDetailsCollapsibleSection = ({
  title,
  foodDetailsListItems,
  noListItemsText,
}: FoodDetailsCollapsibleSectionProps) => {
  const [shouldShowMore, setShouldShowMore] = useState(false);
  const itemsToShow = shouldShowMore ? foodDetailsListItems : foodDetailsListItems.slice(0, 3);

  return (
    <>
      <Typography variant="h3" mb={4}>
        {title}
      </Typography>
      {foodDetailsListItems.length === 0 && noListItemsText ? (
        <Typography variant="body">{noListItemsText}</Typography>
      ) : (
        <Box display="flex" flexDirection="column" gap={2}>
          {itemsToShow.map((item, itemIndex) => {
            const isLastItemToShow = itemIndex === itemsToShow.length - 1;

            return (
              <Box key={item.title}>
                <Box
                  display="flex"
                  justifyContent="space-between"
                  alignItems="center"
                  tabIndex={0}
                  aria-label={`${item.title}${item.value ? `: ${item.value?.toString()}` : ""}`}
                  py={2}
                >
                  <Typography variant="body" tabIndex={-1}>
                    {item.title}
                  </Typography>
                  {item.value && (
                    <Typography variant="body" tabIndex={-1}>
                      {item.value.toString()}
                    </Typography>
                  )}
                </Box>
                {!isLastItemToShow && <Divider />}
              </Box>
            );
          })}
        </Box>
      )}

      {foodDetailsListItems.length > 3 && (
        <Box pt={1}>
          <SeeMoreButton
            showMore={shouldShowMore}
            onShowMoreToggle={() => setShouldShowMore((prevShouldShowMore) => !prevShouldShowMore)}
          />
        </Box>
      )}
    </>
  );
};
