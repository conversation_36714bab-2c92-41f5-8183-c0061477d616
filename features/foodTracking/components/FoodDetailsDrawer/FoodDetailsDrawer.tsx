import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ClickStreamActivityEventType, CustomFood, isCustomFoodV2, NutrientFacts } from "@vivantehealth/vivante-core";
import { Box, Button, CircularProgress } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { FoodDetailsCollapsibleSection, FoodDetailListItem } from "./components/FoodDetailsCollapsibleSection";
import { FoodDetailsHeader } from "./components/FoodDetailsHeader";
import { FoodLogStateSlice, foodLogSelector } from "../../store/foodLogStateSlice";
import { ConfirmDeletionModal } from "../ConfirmDeletionModal";
import { FoodTrackingDrawerContainer } from "../FoodTrackingDrawerContainer";

const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking.viewAllAddedFoods;
const FOOD_TRACKING_A11Y_STRINGS = appStrings.a11y.foodTracking;

type FoodDetailDrawerProps = Readonly<{
  foodItem?: CustomFood;
  onDrawerClose: () => void;
  isDrawerOpen: boolean;
}>;

const NUTRIENT_KEY_MAP = {
  calories: "Calories",
  totalFat: "Total fat (g)",
  cholesterol: "Cholesterol (mg)",
  sodium: "Sodium (mg)",
  totalCarbohydrates: "Total carbohydrates (g)",
  sugars: "Sugars (g)",
  dietaryFiber: "Dietary fiber (g)",
  addedSugars: "Added sugars (g)",
  sugarAlcohols: "Sugar alcohols (g)",
  protein: "Protein (g)",
} as const satisfies Record<keyof NutrientFacts, string>;

const isNutrientKey = (nutrientKey: string): nutrientKey is keyof NutrientFacts => nutrientKey in NUTRIENT_KEY_MAP;

const getNutrientValue = (nutrientKey: string, nutrientFacts: NutrientFacts) => {
  if (isNutrientKey(nutrientKey)) {
    return nutrientFacts[nutrientKey] ?? FOOD_TRACKING_STRINGS.drawer.notAvailable;
  }

  return FOOD_TRACKING_STRINGS.drawer.notAvailable;
};

export const FoodDetailsDrawer = ({ foodItem, onDrawerClose, isDrawerOpen }: FoodDetailDrawerProps) => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const [isConfirmDeleteDialogOpen, setIsConfirmDeleteDialogOpen] = useState(false);
  const submissionStatus = useSelector(foodLogSelector("submissionStatus"));
  const isPending = submissionStatus === "pending";

  useEffect(() => {
    if (submissionStatus === "success") {
      setIsConfirmDeleteDialogOpen(false);
      onDrawerClose();
      dispatch(FoodLogStateSlice.actions.updateSubmissionStatus("idle"));
    }
  }, [dispatch, onDrawerClose, submissionStatus]);

  const handleDeleteFood = () => {
    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_CUSTOM_FOOD_DELETED);
    dispatch(
      FoodLogStateSlice.actions.deleteCustomFood({
        foodLogId: foodItem?.id ?? "",
        snackbarMessage: FOOD_TRACKING_STRINGS.drawer.successfullyRemovedFood,
      }),
    );
  };

  return (
    <>
      {isConfirmDeleteDialogOpen && (
        <ConfirmDeletionModal
          title={FOOD_TRACKING_STRINGS.drawer.confirmDialog.title}
          subTitle={FOOD_TRACKING_STRINGS.drawer.confirmDialog.subtitle}
          closeBtnAriaLabel={FOOD_TRACKING_STRINGS.drawer.confirmDialog.ariaLabel}
          isPendingSubmit={isPending}
          handleOnConfirm={handleDeleteFood}
          handleOnClose={() => setIsConfirmDeleteDialogOpen(false)}
        />
      )}
      <FoodTrackingDrawerContainer
        isDrawerOpen={isDrawerOpen}
        ariaLabel={FOOD_TRACKING_A11Y_STRINGS.foodDetailsDrawer}
        onClose={onDrawerClose}
        title={FOOD_TRACKING_STRINGS.drawer.title}
        closeBtnAriaLabel={FOOD_TRACKING_A11Y_STRINGS.foodDetailsDrawerClose}
      >
        <Box height="calc(100% - 50px)" overflow="auto">
          <FoodDetailsHeader foodItem={foodItem} />

          <FoodDetailsCollapsibleSection
            title={FOOD_TRACKING_STRINGS.drawer.nutritionFacts}
            foodDetailsListItems={Object.entries(NUTRIENT_KEY_MAP).map<FoodDetailListItem>(
              ([nutrientKey, nutrientName]) => ({
                title: nutrientName,
                value:
                  foodItem && isCustomFoodV2(foodItem)
                    ? getNutrientValue(nutrientKey, foodItem.nutrientFacts)
                    : FOOD_TRACKING_STRINGS.drawer.notAvailable,
              }),
            )}
          />

          <Box mt={6}>
            <FoodDetailsCollapsibleSection
              title={FOOD_TRACKING_STRINGS.drawer.ingredientList}
              foodDetailsListItems={
                foodItem && isCustomFoodV2(foodItem)
                  ? foodItem.ingredients?.map<FoodDetailListItem>((food) => ({ title: food.name })) ?? []
                  : []
              }
              noListItemsText={FOOD_TRACKING_STRINGS.drawer.ingredientListUnavailable}
            />
          </Box>
        </Box>

        <Button variant="primary" onClick={() => setIsConfirmDeleteDialogOpen(true)} fullWidth disabled={isPending}>
          {isPending ? <CircularProgress size={24} color="inherit" /> : FOOD_TRACKING_STRINGS.drawer.buttonText}
        </Button>
      </FoodTrackingDrawerContainer>
    </>
  );
};
