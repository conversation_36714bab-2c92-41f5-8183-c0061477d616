import { appStrings } from "@Assets/app_strings";
import { FormInput } from "@Components/form/Fields";

const SHARED_FORM_TEXT = appStrings.sharedFormText;
const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking.trackAFood;

export const NumberOfServingsInput = () => {
  return (
    <FormInput
      label={FOOD_TRACKING_STRINGS.numberOfServings}
      type="text"
      name="numberOfServings"
      onBlur={(value) => {
        if (value === "") return value;

        /** Remove any non-numeric characters from the end of the number */
        const removeTrailingCharacters = value.split(/[^0-9.]/)[0];
        /** This will round the decimal to the hundredth value or clear the value if its not a valid number */
        const roundedValue = Math.round(Number(removeTrailingCharacters) * 100) / 100;

        return Number.isNaN(roundedValue) ? "" : roundedValue.toString();
      }}
      rules={{
        required: {
          value: true,
          message: SHARED_FORM_TEXT.requiredMessage,
        },
        validate: (value) => {
          const isStringOrNumber = typeof value === "string" || typeof value === "number";

          if (!isStringOrNumber) return SHARED_FORM_TEXT.numberOnly;

          /** This regex will match only a valid decimal number */
          const matches = value.toString().match(/^(?:0\.(?:0[0-9]|[0-9]\d?)|[0-9]\d*(?:\.\d{1,2})?)(?:e[+-]?\d+)?$/);

          return (matches && matches.length > 0) || SHARED_FORM_TEXT.numberOnly;
        },
      }}
    />
  );
};
