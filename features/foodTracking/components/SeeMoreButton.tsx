import { Button } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking;

type SeeMoreButtonProps = Readonly<{
  showMore: boolean;
  onShowMoreToggle: () => void;
}>;

export const SeeMoreButton = ({ showMore, onShowMoreToggle }: SeeMoreButtonProps) => {
  return (
    <Button variant="tertiary" onClick={onShowMoreToggle} disableRipple>
      {showMore ? FOOD_TRACKING_STRINGS.seeLess : FOOD_TRACKING_STRINGS.seeMore}
    </Button>
  );
};
