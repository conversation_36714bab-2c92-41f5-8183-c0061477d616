import type { Meta, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import { FoodTrackingButton } from "./FoodTrackingButton";

const meta: Meta<typeof FoodTrackingButton> = {
  decorators: cylinderThemeDecorator,
  title: "@Features/foodTracking/FoodTrackingButton",
  component: FoodTrackingButton,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof FoodTrackingButton>;

export const Primary: Story = {
  args: {
    text: "Add a new food",
    icon: "Plus",
  },
};
