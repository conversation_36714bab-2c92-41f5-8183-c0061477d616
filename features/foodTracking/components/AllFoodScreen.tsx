import React from "react";
import { useSelector } from "react-redux";
import { CustomFood, Food, RecipeLog } from "@vivantehealth/vivante-core";

import { appStrings } from "@Assets/app_strings";

import { FoodCardContainer } from "./FoodCardContainer";
import { FoodTrackingTitleSection } from "./FoodTrackingTitleSection";
import { NoSearchResults } from "./NoSearchResults";
import { SearchAllFoodsButton } from "./SearchAllFoodsButton";
import { PassioFoodResult } from "../api/foodLogApi";
import { useSortOrder } from "../hooks/useSortOrder";
import { foodLogSelector } from "../store/foodLogStateSlice";
import { normalizePassioFoodItems } from "../utils/normalizePassioFoodItems";

const FOOD_TRACKING = appStrings.features.foodTracking;

type AllFoodScreenProps = Readonly<{
  searchTerm: string;
  shouldDisplayPassioResults: boolean;
  isLoadingPassioResults: boolean;
  passioSearchResults?: PassioFoodResult[];
  handleTrackAFood: (food: Food | CustomFood | RecipeLog) => void;
  handleAddACustomFood: () => void;
  handleSearchPassio: () => void;
}>;

const getHeaderTitle = (shouldDisplayEmptyStateText: boolean, shouldDisplaySearchResults: boolean) => {
  if (shouldDisplaySearchResults) {
    return FOOD_TRACKING.searchResults;
  }

  if (shouldDisplayEmptyStateText) {
    return FOOD_TRACKING.emptyState.all.header;
  }

  return FOOD_TRACKING.history;
};

const getEmptyStateText = (
  shouldDisplayEmptyStateText: boolean,
  isFilteredResultsEmpty: boolean,
  shouldDisplayPassioResults: boolean,
) => {
  if (shouldDisplayPassioResults) {
    return "";
  }

  if (isFilteredResultsEmpty) {
    return FOOD_TRACKING.emptyState.all.noSearchResults;
  }
  if (shouldDisplayEmptyStateText) {
    return FOOD_TRACKING.emptyState.all.subHeader;
  }

  return "";
};

export function AllFoodScreen({
  searchTerm,
  shouldDisplayPassioResults,
  isLoadingPassioResults,
  passioSearchResults,
  handleTrackAFood,
  handleAddACustomFood,
  handleSearchPassio,
}: AllFoodScreenProps) {
  const isLoading = useSelector(foodLogSelector("loadState")) === "loading" || isLoadingPassioResults;
  const historyAllFoods = useSelector(foodLogSelector("historyAllFoods"));
  const [sortOrder, setSortOrder] = useSortOrder("All");
  const shouldDisplayEmptyStateText = !isLoading && !historyAllFoods.length;
  const normalizedPassioFoodItems =
    shouldDisplayPassioResults && passioSearchResults ? normalizePassioFoodItems(passioSearchResults) : undefined;
  const filteredHistoryAllFoods =
    searchTerm.length && !shouldDisplayPassioResults
      ? historyAllFoods.filter((food) => food.name.toLowerCase().includes(searchTerm.toLowerCase()))
      : historyAllFoods;
  const shouldDisplayNoPassioResults =
    shouldDisplayPassioResults &&
    !isLoading &&
    (normalizedPassioFoodItems?.length === 0 || normalizedPassioFoodItems === undefined);
  const isFilteredResultsEmpty =
    filteredHistoryAllFoods.length === 0 && searchTerm.length > 0 && !shouldDisplayPassioResults;
  const shouldHideSortButtons = shouldDisplayPassioResults || isLoading || isFilteredResultsEmpty;

  return (
    <>
      {shouldDisplayNoPassioResults ? (
        <NoSearchResults
          emptyStateText={FOOD_TRACKING.emptyState.shared.noSearchResults}
          buttonText={FOOD_TRACKING.addedFood.addANewFood}
          buttonIcon="Plus"
          onAddNewButtonClicked={handleAddACustomFood}
          shouldAutoFocus={shouldDisplayPassioResults}
        />
      ) : (
        <>
          <FoodTrackingTitleSection
            title={getHeaderTitle(shouldDisplayEmptyStateText, !!normalizedPassioFoodItems)}
            sortOrder={sortOrder}
            setSortOrder={setSortOrder}
            emptyStateText={getEmptyStateText(
              shouldDisplayEmptyStateText,
              isFilteredResultsEmpty,
              shouldDisplayPassioResults,
            )}
            hideSortButton={shouldHideSortButtons}
            isLoading={isLoading}
            shouldAutoFocus={shouldDisplayPassioResults}
          />

          {isFilteredResultsEmpty ? (
            <SearchAllFoodsButton searchTerm={searchTerm} onClick={handleSearchPassio} />
          ) : (
            <FoodCardContainer
              isLoading={isLoading}
              foodCardData={normalizedPassioFoodItems ?? filteredHistoryAllFoods}
              onFoodCardClick={handleTrackAFood}
            />
          )}
        </>
      )}
    </>
  );
}
