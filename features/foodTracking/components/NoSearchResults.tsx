import { Box } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

import { FoodTrackingButton, FoodTrackingButtonIcon } from "./FoodTrackingButton";
import { FoodTrackingTitleSection } from "./FoodTrackingTitleSection";

const FOOD_TRACKING = appStrings.features.foodTracking;

type NoSearchResultsProps = Readonly<{
  emptyStateText: string;
  buttonText: string;
  buttonIcon: FoodTrackingButtonIcon;
  onAddNewButtonClicked: () => void;
  shouldAutoFocus?: boolean;
}>;

export const NoSearchResults = ({
  emptyStateText,
  buttonText,
  buttonIcon,
  onAddNewButtonClicked,
  shouldAutoFocus,
}: NoSearchResultsProps) => {
  return (
    <>
      <FoodTrackingTitleSection
        title={FOOD_TRACKING.searchResults}
        emptyStateText={emptyStateText}
        shouldAutoFocus={shouldAutoFocus}
      />
      <Box width="28%" mt={4}>
        <FoodTrackingButton text={buttonText} icon={buttonIcon} onClick={onAddNewButtonClicked} buttonSize="small" />
      </Box>
    </>
  );
};
