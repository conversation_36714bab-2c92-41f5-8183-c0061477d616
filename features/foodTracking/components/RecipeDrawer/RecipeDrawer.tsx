import { useState } from "react";
import { useDispatch } from "react-redux";
import { ClickStreamActivityEventType, Food } from "@vivantehealth/vivante-core";

import { appStrings } from "@Assets/app_strings";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { ConfirmDeletionModal } from "../ConfirmDeletionModal";
import { FoodTrackingDrawerContainer } from "../FoodTrackingDrawerContainer";
import { ConfirmIngredientScreen } from "./components/ConfirmIngredientScreen";
import { IngredientEditScreen } from "./components/IngredientEditScreen";
import { RecipeIngredientsScreen } from "./components/RecipeIngredientsScreen";
import { RecipeOverviewScreen } from "./components/RecipeOverviewScreen";

const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking.recipes;
const FOOD_TRACKING_A11Y_STRINGS = appStrings.a11y.foodTracking;

type FormScreens = keyof typeof FOOD_TRACKING_STRINGS.screens;

export type RecipeOverviewState = Readonly<{
  recipeName: string;
  servings: string;
}>;

export type RecipeDrawerState = Readonly<{
  recipeOverview: RecipeOverviewState;
  ingredients: Map<string, Food>;
  foodLogTime: Date;
}>;

type RecipeDrawerProps = Readonly<{
  onDrawerClose: (recipeSaved?: boolean) => void;
  isDrawerOpen: boolean;
}>;

const BACK_BUTTON_TO_SCREEN_MAP = {
  recipeIngredients: "recipeOverview",
  editIngredient: "recipeIngredients",
  confirmIngredient: "recipeIngredients",
} as const;

const isScreenName = (screenName: string): screenName is keyof typeof BACK_BUTTON_TO_SCREEN_MAP =>
  screenName in BACK_BUTTON_TO_SCREEN_MAP;

export const RecipeDrawer = ({ onDrawerClose, isDrawerOpen }: RecipeDrawerProps) => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const [recipeProps, setRecipeProps] = useState<RecipeDrawerState>({
    recipeOverview: {
      recipeName: "",
      servings: "",
    },
    foodLogTime: new Date(),
    ingredients: new Map(),
  });
  const [currentFormScreen, setCurrentFormScreen] = useState<FormScreens>("recipeOverview");
  const [selectedIngredientDetails, setSelectedIngredientDetails] = useState<Food | null>(null);
  const [isConfirmDeleteDialogOpen, setIsConfirmDeleteDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const { title } = FOOD_TRACKING_STRINGS.screens[currentFormScreen];

  const handleDeleteIngredient = () => {
    const newIngredients = new Map(recipeProps.ingredients);

    if (selectedIngredientDetails?.id && newIngredients.has(selectedIngredientDetails.id)) {
      newIngredients.delete(selectedIngredientDetails.id);
    }

    setRecipeProps((prevRecipeProps) => ({ ...prevRecipeProps, ingredients: newIngredients }));
    setIsConfirmDeleteDialogOpen(false);
    setSelectedIngredientDetails(null);
    setCurrentFormScreen("recipeIngredients");
    dispatch(
      SnackbarStateSlice.actions.toggleSnackbar({ message: FOOD_TRACKING_STRINGS.ingredientRemoved, isOpen: true }),
    );
  };

  const handleUpdateIngredientList = (ingredient: Food) => {
    const newIngredients = new Map(recipeProps.ingredients);

    if (ingredient.id) {
      newIngredients.set(ingredient.id, ingredient);
    }

    setRecipeProps((prevRecipeProps) => ({ ...prevRecipeProps, ingredients: newIngredients }));
    setCurrentFormScreen("recipeIngredients");
    setSearchTerm("");
    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_INGREDIENT_ADDED, { ingredient: ingredient.name });
  };

  const handleIngredientSelected = (ingredient: Food, isNewIngredient: boolean) => {
    setSelectedIngredientDetails(ingredient);
    setCurrentFormScreen(isNewIngredient ? "confirmIngredient" : "editIngredient");
  };

  return (
    <>
      {isConfirmDeleteDialogOpen && (
        <ConfirmDeletionModal
          title={FOOD_TRACKING_STRINGS.confirmationDialog.title}
          subTitle={FOOD_TRACKING_STRINGS.confirmationDialog.subtitle}
          closeBtnAriaLabel={FOOD_TRACKING_A11Y_STRINGS.closeRecipeIngredientModal}
          handleOnConfirm={handleDeleteIngredient}
          handleOnClose={() => setIsConfirmDeleteDialogOpen(false)}
        />
      )}

      <FoodTrackingDrawerContainer
        isDrawerOpen={isDrawerOpen}
        ariaLabel={FOOD_TRACKING_A11Y_STRINGS.recipeDrawer}
        title={title}
        closeBtnAriaLabel={FOOD_TRACKING_A11Y_STRINGS.closeRecipeDrawer}
        showBackBtn={currentFormScreen !== "recipeOverview"}
        onBack={() => {
          if (isScreenName(currentFormScreen)) {
            setCurrentFormScreen(BACK_BUTTON_TO_SCREEN_MAP[currentFormScreen]);
          }
        }}
        /** We use an annoymous function here to avoid passing in the event to the onDrawerClose handler */
        onClose={() => onDrawerClose()}
      >
        {currentFormScreen === "recipeOverview" && (
          <RecipeOverviewScreen
            recipeOverviewProps={recipeProps.recipeOverview}
            handleOnSubmit={(submittedRecipeOverview) => {
              setRecipeProps((prevRecipeProps) => ({ ...prevRecipeProps, recipeOverview: submittedRecipeOverview }));
              setCurrentFormScreen("recipeIngredients");
            }}
          />
        )}

        {currentFormScreen === "recipeIngredients" && (
          <RecipeIngredientsScreen
            recipeProps={recipeProps}
            searchTerm={searchTerm}
            onSearchTermChange={setSearchTerm}
            onDrawerClose={onDrawerClose}
            onIngredientAdd={(ingredient) => {
              handleIngredientSelected(ingredient, true);
            }}
            onIngredientEdit={(ingredient) => {
              handleIngredientSelected(ingredient, false);
            }}
          />
        )}

        {currentFormScreen === "confirmIngredient" && selectedIngredientDetails && (
          <ConfirmIngredientScreen
            ingredient={selectedIngredientDetails}
            onConfirmIngredient={handleUpdateIngredientList}
          />
        )}

        {currentFormScreen === "editIngredient" && selectedIngredientDetails && (
          <IngredientEditScreen
            ingredient={selectedIngredientDetails}
            onDeleteIngredient={() => setIsConfirmDeleteDialogOpen(true)}
            onUpdateIngredient={handleUpdateIngredientList}
          />
        )}
      </FoodTrackingDrawerContainer>
    </>
  );
};
