import { Food } from "@vivantehealth/vivante-core";
import { Box, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

import { RecipeFormButtons } from "../../sharedRecipeComponents/RecipeFormButtons";
import { RecipeIngredientEdit } from "../../sharedRecipeComponents/RecipeIngredientEdit";

const INGREDIENT_EDIT_STRINGS = appStrings.features.foodTracking.recipes.screens.editIngredient;

type IngredientEditScreenProps = Readonly<{
  ingredient: Food;
  onDeleteIngredient: () => void;
  onUpdateIngredient: (ingredient: Food) => void;
}>;

export const IngredientEditScreen = ({
  ingredient,
  onDeleteIngredient,
  onUpdateIngredient,
}: IngredientEditScreenProps) => {
  return (
    <RecipeIngredientEdit
      ingredient={ingredient}
      header={
        <Box>
          <Typography variant="h3">{ingredient.name} </Typography>
          {ingredient?.brand && ingredient.brand.length > 0 ? (
            <Typography variant="body" mt={2}>
              {ingredient.brand}
            </Typography>
          ) : null}
        </Box>
      }
      buttons={
        <RecipeFormButtons
          secondaryButtonText={INGREDIENT_EDIT_STRINGS.remove}
          primaryButtonText={INGREDIENT_EDIT_STRINGS.save}
          primaryButtonType="submit"
          onSecondaryButtonClick={onDeleteIngredient}
          showCircularProgress={false}
        />
      }
      onUpdateIngredient={onUpdateIngredient}
    />
  );
};
