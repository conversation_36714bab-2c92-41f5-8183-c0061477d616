import { useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ClickStreamActivityEventType, Food, FoodSource } from "@vivantehealth/vivante-core";
import { Box, Button, CircularProgress, IconButton, InputAdornment, OutlinedInput, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { useSearchByFoodNameDebounced } from "@Features/foodTracking/hooks/useSearchByFoodNameDebounced";
import { FoodLogStateSlice, foodLogSelector } from "@Features/foodTracking/store/foodLogStateSlice";
import { calculateRecipeNutrientFacts } from "@Features/foodTracking/utils/calculateRecipeNutrientFacts";
import { normalizePassioFoodItems } from "@Features/foodTracking/utils/normalizePassioFoodItems";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { FoodCardContainer } from "../../FoodCardContainer";
import { RecipeDrawerState } from "../RecipeDrawer";

const INGREDIENT_LIST_STRINGS = appStrings.features.foodTracking.recipes.screens.recipeIngredients;
const FOOD_CARD_CONTAINER_HEIGHT = "calc(100vh - 340px)";
/** We need to account for the "Search results" string and any padding/margin */
const SEARCH_RESULTS_CONTAINER_HEIGHT = "calc(100vh - 380px)";

type RecipeIngredientsScreenProps = Readonly<{
  recipeProps: RecipeDrawerState;
  searchTerm: string;
  onSearchTermChange: (searchTerm: string) => void;
  onDrawerClose: (recipeSaved?: boolean) => void;
  onIngredientAdd: (ingredient: Food) => void;
  onIngredientEdit: (ingredient: Food) => void;
}>;

export const RecipeIngredientsScreen = ({
  recipeProps,
  searchTerm,
  onSearchTermChange,
  onDrawerClose,
  onIngredientAdd,
  onIngredientEdit,
}: RecipeIngredientsScreenProps) => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const searchInputRef = useRef<HTMLInputElement>(null);
  const { isLoading, passioSearchResults } = useSearchByFoodNameDebounced(searchTerm);
  const submissionStatus = useSelector(foodLogSelector("submissionStatus"));
  const isPendingSubmission = submissionStatus === "pending";

  const { ingredients } = recipeProps;
  const foodCardData = normalizePassioFoodItems(passioSearchResults?.results ?? []);

  useEffect(() => {
    if (submissionStatus === "success") {
      onDrawerClose(true);
      dispatch(FoodLogStateSlice.actions.updateSubmissionStatus("idle"));
    }
  }, [dispatch, onDrawerClose, submissionStatus]);

  const handleClearSearch = () => {
    onSearchTermChange("");
    searchInputRef?.current?.focus();
  };

  const handleSubmit = () => {
    /** While this value should always be defined, we can default to 1 as at minimum a recipe should make 1 serving */
    const recipeServings = Number(recipeProps.recipeOverview?.servings) || 1;
    /** Default to 1 serving for food log purposes */
    const recipeServingSize = "1 Serving";
    const nutrientFacts = calculateRecipeNutrientFacts(Array.from(ingredients.values()), recipeServings);

    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_LOG_SAVE_PERFORMED);
    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_RECIPE_ADDED);

    dispatch(
      FoodLogStateSlice.actions.saveFoodLog({
        numberOfServings: 1,
        servingSize: recipeServingSize,
        dateTime: recipeProps.foodLogTime,
        nutrientFacts,
        foods: [
          {
            name: recipeProps?.recipeOverview?.recipeName ?? "",
            servings: recipeServings,
            nutrientFacts,
            ingredients: [...ingredients.values()].reverse(),
            source: FoodSource.CUSTOM_USER_RECIPE,
            servingSize: recipeServingSize,
          },
        ],
        snackbarMessage: INGREDIENT_LIST_STRINGS.successMessage,
      }),
    );
  };

  return (
    <>
      <Box height="calc(100% - 50px)" overflow="auto">
        <Box display="flex" flexDirection="column" gap={5}>
          <Box>
            <Typography variant="h3" mb={2}>
              {INGREDIENT_LIST_STRINGS.header}
            </Typography>
            <Typography variant="body">{INGREDIENT_LIST_STRINGS.subHeader}</Typography>
          </Box>

          <OutlinedInput
            fullWidth
            value={searchTerm}
            onChange={(event) => onSearchTermChange(event.target.value)}
            placeholder={INGREDIENT_LIST_STRINGS.searchPlaceholder}
            startAdornment={
              <InputAdornment position="start">
                <AppIcon name="Search" color={color.icon.strong} />
              </InputAdornment>
            }
            endAdornment={
              searchTerm.length > 0 ? (
                <InputAdornment position="end">
                  <IconButton
                    aria-label={INGREDIENT_LIST_STRINGS.clearIngredientSearch}
                    sx={{ p: 0 }}
                    onClick={handleClearSearch}
                    disableRipple
                  >
                    <AppIcon name="Close" color={color.icon.strong} />
                  </IconButton>
                </InputAdornment>
              ) : null
            }
          />

          {ingredients.size > 0 && searchTerm.length === 0 ? (
            <FoodCardContainer
              isLoading={false}
              foodCardData={Array.from(ingredients.values()).reverse()}
              onFoodCardClick={onIngredientEdit}
              foodCardRightIcon="edit"
              containerStyles={{ height: FOOD_CARD_CONTAINER_HEIGHT }}
            />
          ) : null}

          {searchTerm ? (
            <Box>
              <Typography variant="h4" mb={4}>
                {INGREDIENT_LIST_STRINGS.searchResults}
              </Typography>
              {isLoading || foodCardData.length > 0 ? (
                <FoodCardContainer
                  isLoading={isLoading}
                  foodCardData={foodCardData}
                  onFoodCardClick={onIngredientAdd}
                  containerStyles={{ height: SEARCH_RESULTS_CONTAINER_HEIGHT }}
                />
              ) : (
                <Typography variant="body">{INGREDIENT_LIST_STRINGS.noResultsFound}</Typography>
              )}
            </Box>
          ) : null}
        </Box>
      </Box>

      <Button
        variant="primary"
        onClick={handleSubmit}
        disabled={ingredients.size === 0 || isPendingSubmission}
        fullWidth
      >
        {isPendingSubmission ? <CircularProgress size={24} color="inherit" /> : INGREDIENT_LIST_STRINGS.buttonText}
      </Button>
    </>
  );
};
