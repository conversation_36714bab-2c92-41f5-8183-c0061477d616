import { Box, Button, Typography } from "@mui/material";
import { useForm, FormProvider } from "react-hook-form";

import { appStrings } from "@Assets/app_strings";

import { RecipeOverviewInputs } from "../../sharedRecipeComponents/RecipeOverviewInputs";
import { RecipeOverviewState } from "../RecipeDrawer";

const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking.recipes;

type RecipeOverviewScreenProps = Readonly<{
  recipeOverviewProps?: RecipeOverviewState;
  handleOnSubmit: (recipeOverview: RecipeOverviewState) => void;
}>;

export const RecipeOverviewScreen = ({ recipeOverviewProps, handleOnSubmit }: RecipeOverviewScreenProps) => {
  const defaultValues = {
    recipeName: recipeOverviewProps?.recipeName ?? "",
    servings: recipeOverviewProps?.servings ?? "",
  };

  const methods = useForm({ defaultValues });
  const { handleSubmit, watch } = methods;
  /** The next button should be disabled until all of the required inputs have a value */
  const isNextButtonDisabled = watch(["recipeName", "servings"]).some((value) => value.toString().length === 0);

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={handleSubmit((data: typeof defaultValues) => {
          handleOnSubmit({ ...data, servings: data.servings });
        })}
        style={{ height: "100%" }}
      >
        <Box height="calc(100% - 50px)" overflow="auto">
          <Box display="flex" flexDirection="column" gap={5}>
            <Typography variant="h3">{FOOD_TRACKING_STRINGS.screens.recipeOverview.header}</Typography>

            <RecipeOverviewInputs />
          </Box>
        </Box>

        <Button type="submit" variant="primary" disabled={isNextButtonDisabled} fullWidth>
          {FOOD_TRACKING_STRINGS.screens.recipeOverview.buttonText}
        </Button>
      </form>
    </FormProvider>
  );
};
