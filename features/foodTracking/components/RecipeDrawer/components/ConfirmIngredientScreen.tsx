import { Food } from "@vivantehealth/vivante-core";
import { Box, Button, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

import { RecipeIngredientEdit } from "../../sharedRecipeComponents/RecipeIngredientEdit";

const CONFIRM_INGREDIENT_STRINGS = appStrings.features.foodTracking.recipes.screens.confirmIngredient;

type ConfirmIngredientScreenProps = Readonly<{
  ingredient: Food;
  onConfirmIngredient: (ingredient: Food) => void;
}>;

export const ConfirmIngredientScreen = ({ ingredient, onConfirmIngredient }: ConfirmIngredientScreenProps) => {
  return (
    <RecipeIngredientEdit
      ingredient={ingredient}
      header={
        <Box>
          <Typography variant="h3">{ingredient.name}</Typography>
          {ingredient?.brand && ingredient?.brand.length > 0 ? (
            <Typography variant="body" mt={2}>
              {ingredient.brand}
            </Typography>
          ) : null}
        </Box>
      }
      buttons={
        <Button type="submit" variant="primary" fullWidth>
          {CONFIRM_INGREDIENT_STRINGS.buttonText}
        </Button>
      }
      onUpdateIngredient={onConfirmIngredient}
    />
  );
};
