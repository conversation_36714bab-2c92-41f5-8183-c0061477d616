import { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ClickStreamActivityEventType, Food, FoodSource } from "@vivantehealth/vivante-core";
import {
  Box,
  Button,
  Chip,
  CircularProgress,
  IconButton,
  InputAdornment,
  OutlinedInput,
  Typography,
} from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import CloseIcon from "@Assets/images/close.svg";
import { iconSize } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { useSearchByFoodNameDebounced } from "@Features/foodTracking/hooks/useSearchByFoodNameDebounced";
import { FoodLogStateSlice, foodLogSelector } from "@Features/foodTracking/store/foodLogStateSlice";
import { normalizeIngredientList } from "@Features/foodTracking/utils/normalizeIngredientList";
import { normalizePassioFoodItems } from "@Features/foodTracking/utils/normalizePassioFoodItems";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { FoodCardContainer } from "../../FoodCardContainer";
import { SeeMoreButton } from "../../SeeMoreButton";
import { AddedFoodsDrawerState } from "../AddedFoodsDrawer";

const SHARED_FORM_TEXT = appStrings.sharedFormText;
const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking.addedFood;
const INGREDIENT_LIST_STRINGS = FOOD_TRACKING_STRINGS.screens.ingredientList;
/** This is the height in px for two rows of chips including padding/margin */
const MAX_HEIGHT_FOR_TWO_ROWS = 74;

type IngredientListScreenProps = Readonly<{
  addedFoodsProps: AddedFoodsDrawerState;
  onDrawerClose: (customFoodSaved?: boolean) => void;
  onIngredientsUpdate: (ingredients: Food[]) => void;
}>;

export const IngredientListScreen = ({
  addedFoodsProps,
  onDrawerClose,
  onIngredientsUpdate,
}: IngredientListScreenProps) => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const [searchTerm, setSearchTerm] = useState("");

  const [showMoreIngredients, setShowMoreIngredients] = useState(false);
  const [shouldShowSeeMoreButton, setShouldShowSeeMoreButton] = useState(false);
  const { isLoading, passioSearchResults } = useSearchByFoodNameDebounced(searchTerm);

  const chipContainerRef = useRef<HTMLDivElement>(null);
  const submissionStatus = useSelector(foodLogSelector("submissionStatus"));
  const isPendingSubmission = submissionStatus === "pending";
  const { foodOverview, nutrientFacts, foodLogTime } = addedFoodsProps;
  const ingredients = normalizeIngredientList(addedFoodsProps.ingredients ?? []);
  const foodCardData = normalizePassioFoodItems(passioSearchResults?.results ?? []);
  const boundingClientRect = chipContainerRef?.current?.getBoundingClientRect();

  useEffect(() => {
    const scrollHeight = chipContainerRef?.current?.scrollHeight;

    if (scrollHeight && scrollHeight > MAX_HEIGHT_FOR_TWO_ROWS) {
      return setShouldShowSeeMoreButton(true);
    }
    // Resets the state of showMoreIngredients so that after its removal, the button will say
    // "See more" instead of "See less" when the user adds more ingredients again
    if (showMoreIngredients) {
      setShowMoreIngredients(false);
    }

    setShouldShowSeeMoreButton(false);
    // We use boundingClientRect as a dependency to ensure that we get the most up to date value for scrollHeight
    // Otherwise, scrollHeight is not reactive on removing items from the list
  }, [boundingClientRect, showMoreIngredients]);

  useEffect(() => {
    if (submissionStatus === "success") {
      onDrawerClose(true);
      dispatch(FoodLogStateSlice.actions.updateSubmissionStatus("idle"));
    }
  }, [dispatch, onDrawerClose, submissionStatus]);

  const handleAddFood = () => {
    if (!foodOverview || !nutrientFacts) return;

    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_LOG_SAVE_PERFORMED);
    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_CUSTOM_FOOD_SAVE_PERFORMED);

    dispatch(
      FoodLogStateSlice.actions.saveFoodLog({
        ...foodOverview,
        nutrientFacts,
        dateTime: foodLogTime,
        numberOfServings: 1,
        foods: [
          {
            ...foodOverview,
            nutrientFacts,
            // To maintain the order of ingredients listed by latest added first, we reverse the results
            ingredients: [...ingredients.values()].reverse(),
            source: FoodSource.CUSTOM_USER_FOOD,
          },
        ],
        snackbarMessage: FOOD_TRACKING_STRINGS.addedSuccessfully,
      }),
    );
  };

  const handleAddIngredient = (ingredient: Food) => {
    const newIngredients = new Map(ingredients);

    if (ingredient.id) {
      newIngredients.set(ingredient.id, ingredient);
    }

    onIngredientsUpdate([...newIngredients.values()]);

    setSearchTerm("");
    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_INGREDIENT_ADDED, { ingredient: ingredient.name });
  };

  const handleDeleteIngredient = (ingredient: Food) => {
    const newIngredients = new Map(ingredients);

    if (ingredient.id) {
      newIngredients.delete(ingredient.id);
    }

    onIngredientsUpdate([...newIngredients.values()]);
  };

  return (
    <>
      <Box height="calc(100% - 50px)" overflow="auto" display="flex" flexDirection="column" gap={5}>
        <Box>
          <Typography variant="h3" pb={2}>
            {INGREDIENT_LIST_STRINGS.header} - {SHARED_FORM_TEXT.optional}
          </Typography>
          <Typography variant="body">{INGREDIENT_LIST_STRINGS.subHeader}</Typography>
        </Box>

        <OutlinedInput
          fullWidth
          value={searchTerm}
          onChange={(event) => setSearchTerm(event.target.value)}
          placeholder={INGREDIENT_LIST_STRINGS.searchPlaceholder}
          startAdornment={
            <InputAdornment position="start">
              <AppIcon name="Search" color={color.icon.strong} />
            </InputAdornment>
          }
          endAdornment={
            searchTerm.length > 0 ? (
              <InputAdornment position="end">
                <IconButton
                  aria-label={INGREDIENT_LIST_STRINGS.clearIngredientSearch}
                  sx={{ p: 0 }}
                  onClick={() => setSearchTerm("")}
                  disableRipple
                >
                  <AppIcon name="Close" color={color.icon.strong} />
                </IconButton>
              </InputAdornment>
            ) : null
          }
        />

        {ingredients.size > 0 && (
          <Box>
            <Box
              display="flex"
              flexWrap="wrap"
              gap={2}
              overflow="hidden"
              sx={{
                ...(!showMoreIngredients ? { maxHeight: MAX_HEIGHT_FOR_TWO_ROWS } : {}),
              }}
              ref={chipContainerRef}
            >
              {[...ingredients.values()].reverse().map((ingredient) => (
                <Chip
                  key={ingredient.name}
                  label={ingredient.name}
                  variant="active"
                  onDelete={() => handleDeleteIngredient(ingredient)}
                  deleteIcon={<CloseIcon style={{ ...iconSize.sm, color: color.icon.onColorFill }} />}
                />
              ))}
            </Box>

            {shouldShowSeeMoreButton && (
              <Box pt={5}>
                <SeeMoreButton
                  showMore={showMoreIngredients}
                  onShowMoreToggle={() =>
                    setShowMoreIngredients((prev) => {
                      return !prev;
                    })
                  }
                />
              </Box>
            )}
          </Box>
        )}

        {searchTerm && (
          <Box>
            <Typography variant="h4" pb={4}>
              {INGREDIENT_LIST_STRINGS.searchResults}
            </Typography>
            {isLoading || foodCardData.length > 0 ? (
              <FoodCardContainer
                isLoading={isLoading}
                foodCardData={foodCardData}
                onFoodCardClick={handleAddIngredient}
                includeBrandName={false}
              />
            ) : (
              <Typography variant="body">{INGREDIENT_LIST_STRINGS.noResultsFound}</Typography>
            )}
          </Box>
        )}
      </Box>

      <Button onClick={handleAddFood} variant="primary" fullWidth disabled={isPendingSubmission}>
        {isPendingSubmission ? <CircularProgress size={24} color="inherit" /> : INGREDIENT_LIST_STRINGS.buttonText}
      </Button>
    </>
  );
};
