import { NutrientFacts } from "@vivantehealth/vivante-core";
import { Box, Button, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { useForm, FormProvider } from "react-hook-form";

import { appStrings } from "@Assets/app_strings";
import { FormInput } from "@Components/form/Fields";

const SHARED_FORM_TEXT = appStrings.sharedFormText;
const NUTRITION_FACTS_STRINGS = appStrings.features.foodTracking.addedFood.screens.nutritionFacts;

type NutritionFactsScreenProps = Readonly<{
  nutrientFactsProps?: NutrientFacts;
  handleOnSubmit: (nutrientFacts: NutrientFacts) => void;
}>;

const NUTRIENT_FORM_MAP = {
  Calories: { name: "calories", required: true },
  "Total fat(g)": { name: "totalFat", required: false },
  "Cholesterol (mg)": { name: "cholesterol", required: false },
  "Sodium (mg)": { name: "sodium", required: false },
  "Total carbohydrates (g)": { name: "totalCarbohydrates", required: false },
  "Sugars (g)": { name: "sugars", required: false },
  "Dietary fiber (g)": { name: "dietaryFiber", required: false },
  "Added sugars (g)": { name: "addedSugars", required: false },
  "Sugar alcohols (g)": { name: "sugarAlcohols", required: false },
  "Protein (g)": { name: "protein", required: false },
};

export const NutritionFactsScreen = ({ nutrientFactsProps, handleOnSubmit }: NutritionFactsScreenProps) => {
  const defaultValues = {
    calories: nutrientFactsProps?.calories ?? "",
    totalFat: nutrientFactsProps?.totalFat ?? "",
    cholesterol: nutrientFactsProps?.cholesterol ?? "",
    sodium: nutrientFactsProps?.sodium ?? "",
    totalCarbohydrates: nutrientFactsProps?.totalCarbohydrates ?? "",
    sugars: nutrientFactsProps?.sugars ?? "",
    dietaryFiber: nutrientFactsProps?.dietaryFiber ?? "",
    addedSugars: nutrientFactsProps?.addedSugars ?? "",
    sugarAlcohols: nutrientFactsProps?.sugarAlcohols ?? "",
    protein: nutrientFactsProps?.protein ?? "",
  };
  const methods = useForm({ defaultValues });
  const { handleSubmit, watch } = methods;
  /** Next button should be disabled until all required fields have a value */
  const isNextButtonDisabled = watch("calories").toString().length === 0;

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={handleSubmit((data: typeof defaultValues) => {
          const definedNutrientFacts = Object.entries(data).reduce<NutrientFacts>(
            (nutrientFacts, [key, value]) => {
              if (value === "") return nutrientFacts;

              return { ...nutrientFacts, [key]: Number(value) };
            },
            { calories: 0 },
          );

          handleOnSubmit(definedNutrientFacts);
        })}
        style={{ height: "100%" }}
      >
        <Box height="calc(100% - 50px)" overflow="auto" display="flex" flexDirection="column" gap={5}>
          <Typography variant="h3">{NUTRITION_FACTS_STRINGS.header}</Typography>

          {Object.entries(NUTRIENT_FORM_MAP).map(([header, { name, required }]) => {
            return (
              <Box key={header} display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="body" color={color.text.strong}>
                  {header}
                </Typography>
                <FormInput
                  name={name}
                  type="text"
                  rules={{
                    required: { value: required, message: SHARED_FORM_TEXT.requiredMessage },
                  }}
                  placeholder={required ? "" : SHARED_FORM_TEXT.optional}
                  styles={{ width: "169px" }}
                  onBlur={(value) => {
                    if (value === "") return value;

                    /** Remove any non-numeric characters from the end of the number */
                    const removeTrailingCharacters = value.split(/[^0-9.]/)[0];
                    /** This will round the decimal to the tenth value or clear the value if its not a valid number */
                    const roundedValue = Math.round(Number(removeTrailingCharacters) * 10) / 10;

                    return Number.isNaN(roundedValue) ? "" : roundedValue.toString();
                  }}
                />
              </Box>
            );
          })}
        </Box>

        <Button type="submit" variant="primary" disabled={isNextButtonDisabled} fullWidth>
          {NUTRITION_FACTS_STRINGS.buttonText}
        </Button>
      </form>
    </FormProvider>
  );
};
