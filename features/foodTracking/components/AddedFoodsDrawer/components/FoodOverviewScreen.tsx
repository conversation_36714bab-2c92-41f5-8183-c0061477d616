import { <PERSON>, Button, Typography } from "@mui/material";
import { useForm, FormProvider } from "react-hook-form";

import { appStrings } from "@Assets/app_strings";
import { FormInput, Rules } from "@Components/form/Fields";
import { InputType } from "@Components/InputWithLabel/InputWithLabel";

import { FoodOverviewState } from "../AddedFoodsDrawer";

const SHARED_FORM_TEXT = appStrings.sharedFormText;
const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking.addedFood;
const FOODOVERVIEW_INPUTS: {
  label: string;
  type: InputType;
  name: string;
  isOptional?: boolean;
  rules?: Rules;
}[] = [
  { label: FOOD_TRACKING_STRINGS.brandName, type: "text", name: "brand", isOptional: true },
  {
    label: FOOD_TRACKING_STRINGS.foodName,
    type: "text",
    name: "name",
    rules: { required: { value: true, message: SHARED_FORM_TEXT.requiredMessage } },
  },
  {
    label: FOOD_TRACKING_STRINGS.servingSize,
    type: "text",
    name: "servingSize",
    rules: { required: { value: true, message: SHARED_FORM_TEXT.requiredMessage } },
  },
  {
    label: FOOD_TRACKING_STRINGS.servingsPerContainer,
    name: "servingsPerContainer",
    type: "number",
    rules: { required: { value: true, message: SHARED_FORM_TEXT.requiredMessage } },
  },
];

type FoodOverviewScreenProps = Readonly<{
  foodOverviewProps?: FoodOverviewState;
  handleOnSubmit: (foodOverview: FoodOverviewState) => void;
}>;

export const FoodOverviewScreen = ({ foodOverviewProps, handleOnSubmit }: FoodOverviewScreenProps) => {
  const defaultValues = {
    brand: foodOverviewProps?.brand ?? "",
    name: foodOverviewProps?.name ?? "",
    servingSize: foodOverviewProps?.servingSize ?? "",
    servingsPerContainer: foodOverviewProps?.servingsPerContainer ?? "",
  };

  const methods = useForm({ defaultValues });
  const { handleSubmit, watch } = methods;
  /** The next button should be disabled until all of the required inputs have a value */
  const isNextButtonDisabled = watch(["name", "servingSize", "servingsPerContainer"]).some(
    (value) => value.toString().length === 0,
  );

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={handleSubmit((data: typeof defaultValues) => {
          handleOnSubmit({ ...data, servingsPerContainer: Number(data.servingsPerContainer) });
        })}
        style={{ height: "100%" }}
      >
        <Box height="calc(100% - 50px)" overflow="auto">
          <Box display="flex" flexDirection="column" gap={5}>
            <Box>
              <Typography variant="h3" mb={2}>
                {FOOD_TRACKING_STRINGS.screens.foodOverview.header}
              </Typography>

              <Typography variant="body">{FOOD_TRACKING_STRINGS.screens.foodOverview.subHeader}</Typography>
            </Box>
            {FOODOVERVIEW_INPUTS.map(({ label, type, name, rules, isOptional }) => (
              <FormInput label={label} key={name} type={type} name={name} rules={rules} isOptional={isOptional} />
            ))}
          </Box>
        </Box>

        <Button type="submit" variant="primary" disabled={isNextButtonDisabled} fullWidth>
          {FOOD_TRACKING_STRINGS.screens.foodOverview.buttonText}
        </Button>
      </form>
    </FormProvider>
  );
};
