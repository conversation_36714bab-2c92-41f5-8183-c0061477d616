import { useState } from "react";
import { Food, NutrientFacts } from "@vivantehealth/vivante-core";

import { appStrings } from "@Assets/app_strings";

import { FoodTrackingDrawerContainer } from "../FoodTrackingDrawerContainer";
import { FoodOverviewScreen } from "./components/FoodOverviewScreen";
import { IngredientListScreen } from "./components/IngredientListScreen";
import { NutritionFactsScreen } from "./components/NutrientFactsScreen";

const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking.addedFood;
const FOOD_TRACKING_A11Y_STRINGS = appStrings.a11y.foodTracking;

type FormScreens = "foodOverview" | "nutritionFacts" | "ingredientList";

export type FoodOverviewState = Readonly<{
  brand?: string;
  name: string;
  servingSize: string;
  servingsPerContainer: number;
}>;

export type AddedFoodsDrawerState = Readonly<{
  foodOverview?: FoodOverviewState;
  nutrientFacts?: NutrientFacts;
  ingredients?: Food[];
  foodLogTime: Date;
}>;

type AddedFoodsDrawerProps = Readonly<{
  foodOverview?: FoodOverviewState;
  nutrientFacts?: NutrientFacts;
  ingredients?: Food[];
  onDrawerClose: (customFoodSaved?: boolean) => void;
  isDrawerOpen: boolean;
}>;

const BACK_BUTTON_TO_SCREEN_MAP = {
  nutritionFacts: "foodOverview",
  ingredientList: "nutritionFacts",
} as const;

const isScreenName = (screenName: string): screenName is keyof typeof BACK_BUTTON_TO_SCREEN_MAP =>
  screenName in BACK_BUTTON_TO_SCREEN_MAP;

export const AddedFoodsDrawer = ({
  foodOverview,
  nutrientFacts,
  ingredients,
  onDrawerClose,
  isDrawerOpen,
}: AddedFoodsDrawerProps) => {
  const [addedFoodsProps, setAddedFoodsProps] = useState<AddedFoodsDrawerState>({
    foodOverview,
    nutrientFacts,
    ingredients,
    foodLogTime: new Date(),
  });
  const [currentFormScreen, setCurrentFormScreen] = useState<FormScreens>("foodOverview");
  const { title } = FOOD_TRACKING_STRINGS.screens[currentFormScreen];

  return (
    <FoodTrackingDrawerContainer
      isDrawerOpen={isDrawerOpen}
      ariaLabel={FOOD_TRACKING_A11Y_STRINGS.addedFoodDrawer}
      /** We use an annoymous function here to avoid passing in the event to the onDrawerClose handler */
      onClose={() => onDrawerClose()}
      title={title}
      showBackBtn={currentFormScreen !== "foodOverview"}
      closeBtnAriaLabel={FOOD_TRACKING_A11Y_STRINGS.closeAddedFoodDrawer}
      backBtnAriaLabel={FOOD_TRACKING_A11Y_STRINGS.addedFoodBackBtn(
        currentFormScreen === "foodOverview" ? "food overview" : "nutrition facts",
      )}
      onBack={() => {
        if (isScreenName(currentFormScreen)) {
          setCurrentFormScreen(BACK_BUTTON_TO_SCREEN_MAP[currentFormScreen]);
        }
      }}
    >
      {currentFormScreen === "foodOverview" && (
        <FoodOverviewScreen
          foodOverviewProps={addedFoodsProps.foodOverview}
          handleOnSubmit={(submittedFoodOverview) => {
            setAddedFoodsProps((prevAddedFoodsProps) => ({
              ...prevAddedFoodsProps,
              foodOverview: submittedFoodOverview,
            }));
            setCurrentFormScreen("nutritionFacts");
          }}
        />
      )}

      {currentFormScreen === "nutritionFacts" && (
        <NutritionFactsScreen
          nutrientFactsProps={addedFoodsProps.nutrientFacts}
          handleOnSubmit={(submittedNutrientFacts) => {
            setAddedFoodsProps((prevAddedFoodsProps) => ({
              ...prevAddedFoodsProps,
              nutrientFacts: submittedNutrientFacts,
            }));
            setCurrentFormScreen("ingredientList");
          }}
        />
      )}

      {currentFormScreen === "ingredientList" && (
        <IngredientListScreen
          addedFoodsProps={addedFoodsProps}
          onIngredientsUpdate={(ingredients: Food[]) => {
            setAddedFoodsProps((prevAddedFoodsProps) => ({ ...prevAddedFoodsProps, ingredients }));
          }}
          onDrawerClose={onDrawerClose}
        />
      )}
    </FoodTrackingDrawerContainer>
  );
};
