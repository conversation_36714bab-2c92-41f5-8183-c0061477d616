import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ClickStreamActivityEventType, Food } from "@vivantehealth/vivante-core";
import { Box } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { FoodCardContainer } from "./FoodCardContainer";
import { FoodTrackingButton } from "./FoodTrackingButton";
import { FoodTrackingTitleSection } from "./FoodTrackingTitleSection";
import { RecipeDrawer } from "./RecipeDrawer/RecipeDrawer";
import { useSortOrder } from "../hooks/useSortOrder";
import { foodLogSelector } from "../store/foodLogStateSlice";

const FOOD_TRACKING = appStrings.features.foodTracking;

type MyRecipesScreenProps = Readonly<{ searchTerm: string; handleTrackAFood: (food: Food) => void }>;

const getEmptyStateText = (shouldDisplayEmptyStateText: boolean, isFilteredResultsEmpty: boolean) => {
  if (shouldDisplayEmptyStateText) {
    return FOOD_TRACKING.emptyState.myRecipes.subHeader;
  }

  if (isFilteredResultsEmpty) {
    return FOOD_TRACKING.recipes.noSearchResults;
  }

  return "";
};

export function MyRecipesScreen({ searchTerm, handleTrackAFood }: MyRecipesScreenProps) {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const [isRecipeDrawerOpen, setIsRecipeDrawerOpen] = useState(false);
  const isLoading = useSelector(foodLogSelector("loadState")) === "loading";
  const recipes = useSelector(foodLogSelector("recipes"));
  const filteredRecipes = searchTerm.length
    ? recipes.filter((food) => food.name.toLowerCase().includes(searchTerm.toLowerCase()))
    : recipes;
  const [sortOrder, setSortOrder] = useSortOrder("My recipes");
  const shouldDisplayEmptyStateText = !isLoading && !recipes.length;

  const handleViewRecipes = () => {
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: "/progress/food-tracking/recipes",
        screenName: "FoodTrackingViewRecipes",
      }),
    );
  };

  const handleRecipeDrawerClose = (recipeSaved: boolean) => {
    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_RECIPE_CREATION_CLOSED, {
      recipeSaved: recipeSaved ? "Yes" : "No",
    });
    setIsRecipeDrawerOpen(false);
  };

  const handleAddNewRecipeClick = () => {
    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_RECIPE_CREATION_STARTED);
    setIsRecipeDrawerOpen(true);
  };

  return (
    <>
      <RecipeDrawer
        isDrawerOpen={isRecipeDrawerOpen}
        onDrawerClose={(recipeSaved = false) => handleRecipeDrawerClose(recipeSaved)}
        key={isRecipeDrawerOpen.toString()}
      />

      <Box display="flex" gap={6} pb={6}>
        <FoodTrackingButton
          text={FOOD_TRACKING.recipes.addANewRecipe}
          icon="MenuBook"
          onClick={handleAddNewRecipeClick}
        />

        <FoodTrackingButton
          text={FOOD_TRACKING.recipes.editRecipes}
          icon="Edit"
          onClick={handleViewRecipes}
          disabled={!recipes.length}
        />
      </Box>

      <FoodTrackingTitleSection
        title={shouldDisplayEmptyStateText ? FOOD_TRACKING.emptyState.myRecipes.header : FOOD_TRACKING.history}
        sortOrder={sortOrder}
        setSortOrder={setSortOrder}
        emptyStateText={getEmptyStateText(shouldDisplayEmptyStateText, filteredRecipes.length === 0)}
      />

      <FoodCardContainer isLoading={isLoading} foodCardData={filteredRecipes} onFoodCardClick={handleTrackAFood} />
    </>
  );
}
