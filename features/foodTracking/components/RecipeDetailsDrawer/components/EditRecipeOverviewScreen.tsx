import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ClickStreamActivityEventType, Food, RecipeLog } from "@vivantehealth/vivante-core";
import { Box, Typography } from "@mui/material";
import { FormProvider, useForm } from "react-hook-form";

import { appStrings } from "@Assets/app_strings";
import { foodLogSelector, FoodLogStateSlice } from "@Features/foodTracking/store/foodLogStateSlice";
import { calculateRecipeNutrientFacts } from "@Features/foodTracking/utils/calculateRecipeNutrientFacts";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { FoodCardContainer } from "../../FoodCardContainer";
import { RecipeFormButtons } from "../../sharedRecipeComponents/RecipeFormButtons";
import { RecipeOverviewInputs } from "../../sharedRecipeComponents/RecipeOverviewInputs";

const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking.viewRecipes.drawer.screens.editRecipe;
const FOOD_CARD_CONTAINER_HEIGHT = "calc(100vh - 440px)";

type EditRecipeOverviewScreenProps = Readonly<{
  recipe: RecipeLog;
  ingredients: Food[];
  onNavigateToPriorScreen: () => void;
  onFoodCardClick: (ingredient: Food) => void;
  onAddIngredientClick: () => void;
}>;

export const EditRecipeOverviewScreen = ({
  recipe,
  ingredients,
  onNavigateToPriorScreen,
  onFoodCardClick,
  onAddIngredientClick,
}: EditRecipeOverviewScreenProps) => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const submissionStatus = useSelector(foodLogSelector("submissionStatus"));
  const loadState = useSelector(foodLogSelector("loadState"));

  const defaultValues = {
    recipeName: recipe.name,
    servings: recipe.servings,
  };

  const methods = useForm({ defaultValues });
  const { handleSubmit, watch } = methods;

  useEffect(() => {
    /** On successful submission, we should navigate back to the prior screen
     *  we wait on the loadState to be loaded as well to ensure we have the latest recipe
     * data and we don't have a flash between the old and new recipe data
     */
    if (submissionStatus === "success" && loadState === "loaded") {
      onNavigateToPriorScreen();
      dispatch(FoodLogStateSlice.actions.updateSubmissionStatus("idle"));
    }
  }, [dispatch, loadState, onNavigateToPriorScreen, submissionStatus]);

  /** If recipe name or servings are empty, we should disable the Add ingredient/Save recipe buttons */
  const isSaveRecipeButtonDisabled =
    watch(["recipeName", "servings"]).some((value) => value.toString().length === 0) || ingredients.length === 0;

  const handleSubmitRecipe = (data: typeof defaultValues) => {
    const nutrientFacts = calculateRecipeNutrientFacts(ingredients, data.servings);

    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_RECIPE_EDITED);
    dispatch(
      FoodLogStateSlice.actions.updateRecipe({
        ...recipe,
        ingredients,
        recipeLogId: recipe.id,
        name: data.recipeName,
        servings: Number(data.servings),
        nutrientFacts,
      }),
    );
  };

  return (
    <>
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(handleSubmitRecipe)} style={{ height: "100%" }}>
          <Box height="calc(100% - 50px)" overflow="auto" display="flex" flexDirection="column" gap={5}>
            <Typography variant="h3">{recipe.name}</Typography>

            <RecipeOverviewInputs />

            <Box>
              <Typography variant="h4" mb={4}>
                {FOOD_TRACKING_STRINGS.ingredientList}
              </Typography>

              <FoodCardContainer
                foodCardData={ingredients}
                isLoading={false}
                foodCardRightIcon="edit"
                onFoodCardClick={onFoodCardClick}
                containerStyles={{ height: FOOD_CARD_CONTAINER_HEIGHT }}
              />
            </Box>
          </Box>

          <RecipeFormButtons
            secondaryButtonText={FOOD_TRACKING_STRINGS.addIngredientButtonText}
            primaryButtonText={FOOD_TRACKING_STRINGS.saveRecipeButtonText}
            primaryButtonType="submit"
            isPrimaryButtonDisabled={isSaveRecipeButtonDisabled}
            showCircularProgress={submissionStatus === "pending" || loadState === "loading"}
            onSecondaryButtonClick={onAddIngredientClick}
          />
        </form>
      </FormProvider>
    </>
  );
};
