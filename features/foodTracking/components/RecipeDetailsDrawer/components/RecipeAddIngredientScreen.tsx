import { useRef } from "react";
import { Food } from "@vivantehealth/vivante-core";
import { Box, IconButton, InputAdornment, OutlinedInput, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { useSearchByFoodNameDebounced } from "@Features/foodTracking/hooks/useSearchByFoodNameDebounced";
import { normalizePassioFoodItems } from "@Features/foodTracking/utils/normalizePassioFoodItems";

import { FoodCardContainer } from "../../FoodCardContainer";

const RECIPE_ADD_INGREDIENT_STRING = appStrings.features.foodTracking.viewRecipes.drawer.screens.addIngredients;
const FOOD_CARD_CONTAINER_HEIGHT = "calc(100vh - 260px)";

type RecipeAddIngredientsScreenProps = Readonly<{
  searchTerm: string;
  onSearchTermChange: (searchTerm: string) => void;
  onFoodCardClick: (ingredient: Food) => void;
}>;

export const RecipeAddIngredientsScreen = ({
  searchTerm,
  onSearchTermChange,
  onFoodCardClick,
}: RecipeAddIngredientsScreenProps) => {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const { isLoading, passioSearchResults } = useSearchByFoodNameDebounced(searchTerm);
  const foodCardData = normalizePassioFoodItems(passioSearchResults?.results ?? []);

  const handleClearSearch = () => {
    onSearchTermChange("");
    searchInputRef?.current?.focus();
  };

  return (
    <Box height="100%" overflow="auto" display="flex" flexDirection="column" gap={5}>
      <Box display="flex" flexDirection="column" gap={2}>
        <Typography variant="h3">{RECIPE_ADD_INGREDIENT_STRING.header}</Typography>
        <Typography variant="body">{RECIPE_ADD_INGREDIENT_STRING.subHeader}</Typography>
      </Box>
      <OutlinedInput
        fullWidth
        value={searchTerm}
        onChange={(event) => onSearchTermChange(event.target.value)}
        placeholder={RECIPE_ADD_INGREDIENT_STRING.searchPlaceholder}
        startAdornment={
          <InputAdornment position="start">
            <AppIcon name="Search" color={color.icon.strong} />
          </InputAdornment>
        }
        endAdornment={
          searchTerm.length > 0 ? (
            <InputAdornment position="end">
              <IconButton
                aria-label={RECIPE_ADD_INGREDIENT_STRING.clearIngredientSearch}
                sx={{ p: 0 }}
                onClick={handleClearSearch}
                disableRipple
              >
                <AppIcon name="Close" color={color.icon.strong} />
              </IconButton>
            </InputAdornment>
          ) : null
        }
      />

      {searchTerm && (
        <Box>
          <Typography variant="h4" mb={4}>
            {RECIPE_ADD_INGREDIENT_STRING.searchResults}
          </Typography>
          {isLoading || foodCardData.length > 0 ? (
            <FoodCardContainer
              isLoading={isLoading}
              foodCardData={foodCardData}
              onFoodCardClick={onFoodCardClick}
              containerStyles={{ height: FOOD_CARD_CONTAINER_HEIGHT }}
            />
          ) : (
            <Typography variant="body">{RECIPE_ADD_INGREDIENT_STRING.noResultsFound}</Typography>
          )}
        </Box>
      )}
    </Box>
  );
};
