import { RecipeLog } from "@vivantehealth/vivante-core";
import { Box, Divider, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking.viewRecipes.drawer.screens.recipeDetails.header;

type RecipeDetailsHeaderProps = Readonly<{
  recipe: RecipeLog;
}>;

export const RecipeDetailsHeader = ({ recipe }: RecipeDetailsHeaderProps) => {
  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <Typography variant="h3">{recipe.name}</Typography>
      <Typography variant="body">{FOOD_TRACKING_STRINGS.serves(recipe.servings)} </Typography>
      <Typography variant="body">{FOOD_TRACKING_STRINGS.caloriesPerServing(recipe.nutrientFacts.calories)}</Typography>
      <Divider sx={{ mt: 2 }} />
    </Box>
  );
};
