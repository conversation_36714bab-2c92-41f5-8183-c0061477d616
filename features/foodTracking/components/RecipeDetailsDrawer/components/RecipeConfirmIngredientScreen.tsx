import { Food } from "@vivantehealth/vivante-core";
import { Button, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

import { RecipeIngredientEdit } from "../../sharedRecipeComponents/RecipeIngredientEdit";

const RECIPE_CONFIRM_INGREDIENT_STRINGS = appStrings.features.foodTracking.viewRecipes.drawer.screens.confirmIngredient;

type RecipeConfirmIngredientScreenProps = Readonly<{
  ingredient: Food;
  onAddIngredient: (ingredient: Food) => void;
}>;

export const RecipeConfirmIngredientScreen = ({ ingredient, onAddIngredient }: RecipeConfirmIngredientScreenProps) => {
  return (
    <RecipeIngredientEdit
      ingredient={ingredient}
      header={<Typography variant="h3">{ingredient.name}</Typography>}
      buttons={
        <Button variant="primary" type="submit" fullWidth>
          {RECIPE_CONFIRM_INGREDIENT_STRINGS.buttonText}
        </Button>
      }
      onUpdateIngredient={onAddIngredient}
    />
  );
};
