import { Food } from "@vivantehealth/vivante-core";
import { Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

import { RecipeFormButtons } from "../../sharedRecipeComponents/RecipeFormButtons";
import { RecipeIngredientEdit } from "../../sharedRecipeComponents/RecipeIngredientEdit";

const INGREDIENT_EDIT_STRINGS = appStrings.features.foodTracking.viewRecipes.drawer.screens.editIngredient;

type RecipeIngredientEditScreenProps = Readonly<{
  ingredient: Food;
  onDeleteIngredient: () => void;
  onUpdateIngredient: (ingredient: Food) => void;
}>;

export const RecipeIngredientEditScreen = ({
  ingredient,
  onDeleteIngredient,
  onUpdateIngredient,
}: RecipeIngredientEditScreenProps) => {
  return (
    <RecipeIngredientEdit
      ingredient={ingredient}
      header={<Typography variant="h3">{ingredient.name}</Typography>}
      buttons={
        <RecipeFormButtons
          secondaryButtonText={INGREDIENT_EDIT_STRINGS.removeIngredientButtonText}
          primaryButtonText={INGREDIENT_EDIT_STRINGS.saveIngredientButtonText}
          primaryButtonType="submit"
          onSecondaryButtonClick={onDeleteIngredient}
          showCircularProgress={false}
        />
      }
      onUpdateIngredient={onUpdateIngredient}
    />
  );
};
