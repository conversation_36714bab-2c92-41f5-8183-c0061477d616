import { RecipeLog } from "@vivantehealth/vivante-core";
import { Box, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

import { RecipeDetailsHeader } from "./RecipeDetailsHeader";
import { FoodCardContainer } from "../../FoodCardContainer";
import { RecipeFormButtons } from "../../sharedRecipeComponents/RecipeFormButtons";

const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking.viewRecipes.drawer.screens.recipeDetails;
const FOOD_CARD_CONTAINER_HEIGHT = "calc(100vh - 325px)";

type RecipeDetailsScreenProps = Readonly<{
  recipe: RecipeLog;
  isPending: boolean;
  onDeleteRecipe: () => void;
  onEditRecipe: () => void;
}>;

export const RecipeDetailsScreen = ({ recipe, isPending, onDeleteRecipe, onEditRecipe }: RecipeDetailsScreenProps) => {
  return (
    <>
      <Box height="calc(100% - 50px)" overflow="auto">
        <RecipeDetailsHeader recipe={recipe} />

        <Typography variant="h4" mt={5} mb={4}>
          {FOOD_TRACKING_STRINGS.ingredientList}
        </Typography>

        <FoodCardContainer
          foodCardData={recipe.ingredients}
          isLoading={false}
          displayIcon={false}
          containerStyles={{ height: FOOD_CARD_CONTAINER_HEIGHT }}
        />
      </Box>

      <RecipeFormButtons
        secondaryButtonText={FOOD_TRACKING_STRINGS.removeButtonText}
        primaryButtonText={FOOD_TRACKING_STRINGS.editButtonText}
        showCircularProgress={isPending}
        onSecondaryButtonClick={onDeleteRecipe}
        onPrimaryButtonClick={onEditRecipe}
      />
    </>
  );
};
