import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ClickStreamActivityEventType, Food, RecipeLog } from "@vivantehealth/vivante-core";

import { appStrings } from "@Assets/app_strings";
import { SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

import { EditRecipeOverviewScreen } from "./components/EditRecipeOverviewScreen";
import { RecipeAddIngredientsScreen } from "./components/RecipeAddIngredientScreen";
import { RecipeConfirmIngredientScreen } from "./components/RecipeConfirmIngredientScreen";
import { RecipeDetailsScreen } from "./components/RecipeDetailsScreen";
import { RecipeIngredientEditScreen } from "./components/RecipeIngredientEditScreen";
import { FoodLogStateSlice, foodLogSelector } from "../../store/foodLogStateSlice";
import { normalizeIngredientList } from "../../utils/normalizeIngredientList";
import { ConfirmDeletionModal } from "../ConfirmDeletionModal";
import { FoodTrackingDrawerContainer } from "../FoodTrackingDrawerContainer";

const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking.viewRecipes;
const FOOD_TRACKING_A11Y_STRINGS = appStrings.a11y.foodTracking;

type DrawerScreens = keyof typeof FOOD_TRACKING_STRINGS.drawer.screens;

type RecipeDetailsDrawerProps = Readonly<{
  recipe?: RecipeLog;
  onDrawerClose: () => void;
  isDrawerOpen: boolean;
}>;

const BACK_BUTTON_TO_SCREEN_MAP = {
  recipeDetails: "recipeDetails", // 'recipeDetails' is the default screen, so we don't need to navigate back to it
  editRecipe: "recipeDetails",
  editIngredient: "editRecipe",
  addIngredients: "editRecipe",
  confirmIngredient: "addIngredients",
} as const;

export const RecipeDetailsDrawer = ({ recipe, onDrawerClose, isDrawerOpen }: RecipeDetailsDrawerProps) => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const [isConfirmDeleteRecipeDialogOpen, setIsConfirmDeleteRecipeDialogOpen] = useState(false);
  const [isConfirmDeleteIngredientDialogOpen, setIsConfirmDeleteIngredientDialogOpen] = useState(false);
  const [currentScreen, setCurrentScreen] = useState<DrawerScreens>("recipeDetails");
  const [selectedIngredient, setSelectedIngredient] = useState<Food | null>(null);
  const [ingredients, setIngredients] = useState(normalizeIngredientList(recipe?.ingredients ?? []));
  /** Maintain state for add ingredient search term here as to allow the search term to persist when navigating from the confirm screen back to add ingredient screen */
  const [addIngredientSearchTerm, setAddIngredientSearchTerm] = useState("");
  const submissionStatus = useSelector(foodLogSelector("submissionStatus"));
  const isPending = submissionStatus === "pending";
  const { title } = FOOD_TRACKING_STRINGS.drawer.screens[currentScreen];

  useEffect(() => {
    /**  Only run this if confirmation dialog is open which indicates the delete action was just taken and succeeded*/
    if (submissionStatus === "success" && isConfirmDeleteRecipeDialogOpen) {
      setIsConfirmDeleteRecipeDialogOpen(false);
      onDrawerClose();
      dispatch(FoodLogStateSlice.actions.updateSubmissionStatus("idle"));
    }
  }, [dispatch, isConfirmDeleteRecipeDialogOpen, onDrawerClose, submissionStatus]);

  const handleBackButtonClicked = () => {
    const screenToReturnTo = BACK_BUTTON_TO_SCREEN_MAP[currentScreen];

    /**
     * If we're returning to the recipe details screen and the user has made changes to the ingredients without saving,
     * we need to revert back to the original recipe ingredients list.
     */
    if (screenToReturnTo === "recipeDetails") {
      setIngredients(normalizeIngredientList(recipe?.ingredients));
    }
    /**
     * If we're navigating away from the Add ingredients screen, we need to clear the search term
     */
    if (currentScreen === "addIngredients") {
      setAddIngredientSearchTerm("");
    }

    setCurrentScreen(screenToReturnTo);
  };

  const handleDeleteRecipe = () => {
    if (recipe?.id) {
      sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_RECIPE_DELETED);

      dispatch(
        FoodLogStateSlice.actions.deleteRecipe({
          recipeId: recipe.id,
          snackbarMessage: FOOD_TRACKING_STRINGS.drawer.confirmRemoveRecipeDialog.successfullyRemovedRecipe,
        }),
      );
    }
  };

  const handleIngredientClicked = (ingredient: Food, screenToSwitchTo: "editIngredient" | "confirmIngredient") => {
    setSelectedIngredient(ingredient);
    setCurrentScreen(screenToSwitchTo);
  };

  const handleClearSelectedIngredient = () => {
    setSelectedIngredient(null);
    setCurrentScreen("editRecipe");
  };

  const handleAddIngredient = (ingredient: Food) => {
    const updatedIngredients = new Map(ingredients);

    if (ingredient?.id) {
      updatedIngredients.set(ingredient.id, ingredient);
    }

    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_INGREDIENT_ADDED, { ingedient: ingredient.name });
    setIngredients(updatedIngredients);
    handleClearSelectedIngredient();
    setAddIngredientSearchTerm("");
  };

  const handleUpdateIngredient = (ingredient: Food) => {
    const updatedIngredients = new Map(ingredients);

    if (ingredient?.id && updatedIngredients.has(ingredient.id)) {
      updatedIngredients.set(ingredient.id, ingredient);
    }

    setIngredients(updatedIngredients);
    handleClearSelectedIngredient();
  };

  const handleDeleteIngredient = () => {
    const updatedIngredients = new Map(ingredients);

    if (selectedIngredient?.id && updatedIngredients.has(selectedIngredient.id)) {
      updatedIngredients.delete(selectedIngredient.id);
    }

    setIngredients(updatedIngredients);
    setIsConfirmDeleteIngredientDialogOpen(false);
    handleClearSelectedIngredient();

    dispatch(
      SnackbarStateSlice.actions.toggleSnackbar({
        isOpen: true,
        message: FOOD_TRACKING_STRINGS.drawer.confirmRemoveIngredientDialog.successfullyRemovedIngredient,
      }),
    );
  };

  return (
    <>
      {isConfirmDeleteRecipeDialogOpen && (
        <ConfirmDeletionModal
          title={FOOD_TRACKING_STRINGS.drawer.confirmRemoveRecipeDialog.title}
          subTitle={FOOD_TRACKING_STRINGS.drawer.confirmRemoveRecipeDialog.subtitle}
          closeBtnAriaLabel={FOOD_TRACKING_A11Y_STRINGS.closeRemoveRecipeConfirmationModal}
          isPendingSubmit={isPending}
          handleOnConfirm={handleDeleteRecipe}
          handleOnClose={() => setIsConfirmDeleteRecipeDialogOpen(false)}
        />
      )}

      {isConfirmDeleteIngredientDialogOpen && (
        <ConfirmDeletionModal
          title={FOOD_TRACKING_STRINGS.drawer.confirmRemoveIngredientDialog.title}
          subTitle={FOOD_TRACKING_STRINGS.drawer.confirmRemoveIngredientDialog.subtitle}
          closeBtnAriaLabel={FOOD_TRACKING_A11Y_STRINGS.closeRemoveIngredientConfirmationModal}
          isPendingSubmit={isPending}
          handleOnConfirm={handleDeleteIngredient}
          handleOnClose={() => setIsConfirmDeleteIngredientDialogOpen(false)}
        />
      )}

      <FoodTrackingDrawerContainer
        isDrawerOpen={isDrawerOpen}
        ariaLabel={FOOD_TRACKING_A11Y_STRINGS.recipeDetailsDrawer}
        onClose={() => {
          onDrawerClose();
          setCurrentScreen("recipeDetails");
        }}
        title={title}
        closeBtnAriaLabel={FOOD_TRACKING_A11Y_STRINGS.recipeDetailsDrawerClose}
        showBackBtn={currentScreen !== "recipeDetails"}
        onBack={handleBackButtonClicked}
      >
        {currentScreen === "recipeDetails" && recipe && (
          <RecipeDetailsScreen
            recipe={recipe}
            isPending={isPending}
            onDeleteRecipe={() => setIsConfirmDeleteRecipeDialogOpen(true)}
            onEditRecipe={() => setCurrentScreen("editRecipe")}
          />
        )}

        {currentScreen === "editRecipe" && recipe && (
          <EditRecipeOverviewScreen
            recipe={recipe}
            ingredients={[...ingredients.values()]}
            onNavigateToPriorScreen={() => setCurrentScreen("recipeDetails")}
            onFoodCardClick={(ingredient) => handleIngredientClicked(ingredient, "editIngredient")}
            onAddIngredientClick={() => setCurrentScreen("addIngredients")}
          />
        )}

        {currentScreen === "editIngredient" && selectedIngredient && (
          <RecipeIngredientEditScreen
            ingredient={selectedIngredient}
            onUpdateIngredient={handleUpdateIngredient}
            onDeleteIngredient={() => setIsConfirmDeleteIngredientDialogOpen(true)}
          />
        )}

        {currentScreen === "addIngredients" && (
          <RecipeAddIngredientsScreen
            searchTerm={addIngredientSearchTerm}
            onSearchTermChange={setAddIngredientSearchTerm}
            onFoodCardClick={(ingredient) => handleIngredientClicked(ingredient, "confirmIngredient")}
          />
        )}

        {currentScreen === "confirmIngredient" && selectedIngredient && (
          <RecipeConfirmIngredientScreen ingredient={selectedIngredient} onAddIngredient={handleAddIngredient} />
        )}
      </FoodTrackingDrawerContainer>
    </>
  );
};
