import { ReactNode } from "react";
import { Food } from "@vivantehealth/vivante-core";
import { Box } from "@mui/material";

import { Form } from "@Components/form/Form";
import { useSearchByFoodIdQuery } from "@Features/foodTracking/api/foodLogApi";
import { generateServingSizeOptions } from "@Features/foodTracking/utils/generateServingSizeOptions";
import { getNutritionFacts } from "@Features/foodTracking/utils/getNutritionFacts";

import { NumberOfServingsInput } from "../NumberOfServingsInput";
import { PassioServingSizeSelector } from "../PassioServingSizeSelector";

type RecipeIngredientEditProps = Readonly<{
  ingredient: Food;
  header: ReactNode;
  buttons: ReactNode;
  onUpdateIngredient: (ingredient: Food) => void;
}>;

const isUpdateIngredientFormValues = (data: unknown): data is { servingSize: string; numberOfServings: number } => {
  return data != null && typeof data === "object" && "servingSize" in data && "numberOfServings" in data;
};

export const RecipeIngredientEdit = ({
  ingredient,
  header,
  buttons,
  onUpdateIngredient,
}: RecipeIngredientEditProps) => {
  /** We query for the food information so we always have the proper default nutrient facts to recalculate as needed */
  const { data: passioFoodData } = useSearchByFoodIdQuery(ingredient?.meta?.passioId ?? "", {
    skip: !ingredient?.meta?.passioId === undefined,
  });

  const servingSizeOptions = generateServingSizeOptions([
    ...(ingredient?.meta?.servingSizes ?? []),
    ...(passioFoodData?.portions?.map(({ name, quantity }) => ({ unitName: name, quantity })) ?? []),
  ]);
  const defaultValues = {
    servingSize: ingredient?.servingSize ?? servingSizeOptions[0]?.value,
    numberOfServings: ingredient?.numberOfServings ?? 1,
  };

  const handleSubmit = (data: typeof defaultValues) => {
    const nutrientFacts =
      ingredient?.meta && passioFoodData
        ? getNutritionFacts(ingredient.meta, passioFoodData, data.servingSize)
        : { calories: 0 };

    onUpdateIngredient({ ...ingredient, ...data, nutrientFacts });
  };

  return (
    <Form
      onSubmit={(data) => {
        if (isUpdateIngredientFormValues(data)) {
          handleSubmit(data);
        }
      }}
      defaultValues={defaultValues}
      sx={{ height: "100%" }}
    >
      <Box height="calc(100% - 50px)" overflow="auto">
        <Box display="flex" flexDirection="column" gap={5}>
          {header}

          <PassioServingSizeSelector servingSizeOptions={servingSizeOptions} />
          <NumberOfServingsInput />
        </Box>
      </Box>

      {buttons}
    </Form>
  );
};
