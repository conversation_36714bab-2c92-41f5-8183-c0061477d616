import { Box, Button, CircularProgress } from "@mui/material";

type RecipeFormButtonsProps = Readonly<{
  secondaryButtonText: string;
  primaryButtonText: string;
  primaryButtonType?: "submit" | "button";
  isSecondaryButtonDisabled?: boolean;
  isPrimaryButtonDisabled?: boolean;
  showCircularProgress: boolean;
  onSecondaryButtonClick: () => void;
  onPrimaryButtonClick?: () => void;
}>;

export const RecipeFormButtons = ({
  secondaryButtonText,
  primaryButtonText,
  primaryButtonType = "button",
  isSecondaryButtonDisabled,
  isPrimaryButtonDisabled,
  showCircularProgress,
  onSecondaryButtonClick,
  onPrimaryButtonClick,
}: RecipeFormButtonsProps) => {
  return (
    <Box display="flex" gap={2}>
      <Button
        variant="secondary"
        onClick={onSecondaryButtonClick}
        disabled={isSecondaryButtonDisabled || showCircularProgress}
        fullWidth
      >
        {showCircularProgress ? <CircularProgress size={24} color="inherit" /> : secondaryButtonText}
      </Button>
      <Button
        variant="primary"
        onClick={onPrimaryButtonClick}
        type={primaryButtonType}
        disabled={isPrimaryButtonDisabled || showCircularProgress}
        fullWidth
      >
        {showCircularProgress ? <CircularProgress size={24} color="inherit" /> : primaryButtonText}
      </Button>
    </Box>
  );
};
