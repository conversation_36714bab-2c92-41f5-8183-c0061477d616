import { appStrings } from "@Assets/app_strings";
import { FormInput, Rules } from "@Components/form/Fields";
import { InputType } from "@Components/InputWithLabel/InputWithLabel";

type RecipeOverviewInput = Readonly<{
  label: string;
  type: InputType;
  name: string;
  rules: Rules;
}>;

const SHARED_FORM_TEXT = appStrings.sharedFormText;
const FOOD_TRACKING_STRINGS = appStrings.features.foodTracking.recipes;
const RECIPE_OVERVIEW_INPUTS: RecipeOverviewInput[] = [
  {
    label: FOOD_TRACKING_STRINGS.recipeName,
    type: "text",
    name: "recipeName",
    rules: { required: { value: true, message: SHARED_FORM_TEXT.requiredMessage } },
  },
  {
    label: FOOD_TRACKING_STRINGS.servings,
    type: "number",
    name: "servings",
    rules: { required: { value: true, message: SHARED_FORM_TEXT.requiredMessage } },
  },
];

export const RecipeOverviewInputs = () => {
  return RECIPE_OVERVIEW_INPUTS.map(({ label, type, name, rules }) => (
    <FormInput label={label} type={type} name={name} rules={rules} key={name} />
  ));
};
