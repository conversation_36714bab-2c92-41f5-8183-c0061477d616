import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { ClickStreamActivityEventType, CustomFood, Food, FoodSource, RecipeLog } from "@vivantehealth/vivante-core";
import { Box, IconButton, InputAdornment, OutlinedInput, Tab, Tabs, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/router";
import { useDebounceValue } from "usehooks-ts";

import { appStrings } from "@Assets/app_strings";
import { SPACING_32_PX } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { BackButton } from "@Components/BackButton/BackButton";
import { TabsPanel } from "@Components/TabsPanel/TabsPanel";
import { tabsA11yProps } from "@Components/TabsPanel/utils/tabsA11yProps";
import { NavigationStateSlice, NavOptions } from "@Features/navigation/store/navigationStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { Routes } from "@Types";

import { useLazySearchByFoodNameQuery } from "./api/foodLogApi";
import { FOOD_TRACKING_DEBOUNCE_TIME } from "./assets/constants";
import { AddedFoodsScreen } from "./components/AddedFoodsScreen";
import { AllFoodScreen } from "./components/AllFoodScreen";
import { MyRecipesScreen } from "./components/MyRecipesScreen";
import { TrackAFoodDrawer } from "./components/TrackAFoodDrawer/TrackAFoodDrawer";
import { FoodLogStateSlice } from "./store/foodLogStateSlice";

const FOOD_TRACKING = appStrings.features.foodTracking;
const FOOD_TRACKING_A11Y_STRINGS = appStrings.a11y.foodTracking;

export type FoodTrackingTabs = (typeof FOOD_TRACKING.tabs)[keyof typeof FOOD_TRACKING.tabs];

const foodTypeToAnalyticsEventType = {
  [FoodSource.PASSIO_FOOD]: ClickStreamActivityEventType.FOOD_LOGGING_PASSIO_LIBRARY_FOOD_SELECTED_FROM_SEARCH,
  [FoodSource.CUSTOM_USER_FOOD]: ClickStreamActivityEventType.FOOD_LOGGING_CUSTOM_FOOD_SELECTED_FROM_SEARCH,
  [FoodSource.CUSTOM_USER_RECIPE]: ClickStreamActivityEventType.FOOD_LOGGING_RECIPE_SELECTED_FROM_SEARCH,
} as const;

export function FoodTrackingScreenContainer() {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const router = useRouter();
  const params = useSearchParams();
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState<FoodTrackingTabs>(FOOD_TRACKING.tabs.all);
  const [foodToTrack, setFoodToTrack] = useState<Food | CustomFood | RecipeLog>();
  const [isTrackAFoodDrawerOpen, setIsTrackAFoodDrawerOpen] = useState(false);
  const [isAddedFoodsDrawerOpen, setIsAddedFoodsDrawerOpen] = useState(false);
  const [shouldDisplayPassioResults, setShouldDisplayPassioResults] = useState(false);

  const [handleSearchPassioByFoodName, { isLoading, isFetching, isError, data }] = useLazySearchByFoodNameQuery();
  const [debouncedSearchTerm] = useDebounceValue(searchTerm, FOOD_TRACKING_DEBOUNCE_TIME);
  /** If searchTerm is cleared, we shouldn't make the user wait the debounce time */
  const searchTermToUse = searchTerm.length ? debouncedSearchTerm : searchTerm;
  const isAllTabActive = activeTab === FOOD_TRACKING.tabs.all;

  /** Pulls in all data for All, Added foods, and My recipes tabs and sets active navigation tab to Progress on initial render only*/
  useEffect(() => {
    dispatch(FoodLogStateSlice.actions.loadTrackedFoodLogs());
    dispatch(NavigationStateSlice.actions.setActiveNavOption(NavOptions.PROGRESS));
  }, [dispatch]);
  /**
   * This useEffect is used to redirect the user to a specific tab if they are coming from the Added foods/Recipe page
   * after deleting the last food/recipe item in the Added foods/recipe page (or if they end up on that page somehow).
   * This is done by checking the query params for the tabToRedirectTo key and the value.
   * We use router.replace to remove the parameter from the URL
   */
  useEffect(() => {
    const tabToRedirectTo = params.get("tabToRedirectTo");

    if (
      params.size > 0 &&
      (tabToRedirectTo === FOOD_TRACKING.tabs.addedFoods || tabToRedirectTo === FOOD_TRACKING.tabs.myRecipes)
    ) {
      router.replace("/progress/food-tracking");
      setActiveTab(tabToRedirectTo);
    }
  }, [params, router]);

  const handleBackButtonClick = () => {
    dispatch(NavigationStateSlice.actions.navigateTo({ path: Routes.PROGRESS, screenName: "HistoryDayView" }));
  };

  const handleClearSearch = () => {
    setSearchTerm("");
    setShouldDisplayPassioResults(false);
  };

  const handleTabChange = (selectedTab: FoodTrackingTabs) => {
    handleClearSearch();
    setActiveTab(selectedTab);
  };

  const handleTrackAFood = (food: Food | CustomFood | RecipeLog) => {
    setFoodToTrack(food);

    setIsTrackAFoodDrawerOpen(true);

    if (food.source !== FoodSource.VIVANTE_HEALTH_FOOD) {
      sendEventAnalytics(foodTypeToAnalyticsEventType[food.source]);
    }
  };

  const handleAddACustomFood = () => {
    handleTabChange(FOOD_TRACKING.tabs.addedFoods);
    setIsAddedFoodsDrawerOpen(true);
  };

  const handleOnTrackAFoodDrawerClose = (shouldClearSearch = false) => {
    setIsTrackAFoodDrawerOpen(false);
    setFoodToTrack(undefined);

    if (shouldClearSearch) {
      handleClearSearch();
    }
  };

  const handleOnSearchPassio = () => {
    setShouldDisplayPassioResults(true);
    handleSearchPassioByFoodName(searchTerm, true);
    sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_TEXT_SEARCH_PERFORMED);
  };

  return (
    <div>
      <TrackAFoodDrawer
        foodItem={foodToTrack}
        isDrawerOpen={isTrackAFoodDrawerOpen && !!foodToTrack}
        onDrawerClose={handleOnTrackAFoodDrawerClose}
      />

      <BackButton onClick={handleBackButtonClick}>{FOOD_TRACKING.backButtonText}</BackButton>

      <Box display="flex" flexDirection="column" gap={5} mt={5}>
        <Typography variant="h1Serif">{FOOD_TRACKING.title}</Typography>

        <OutlinedInput
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onKeyDown={(event) => {
            if (event.key === "Enter" && searchTerm.length > 0 && isAllTabActive) {
              handleOnSearchPassio();
            }
          }}
          onClick={() => {
            sendEventAnalytics(ClickStreamActivityEventType.FOOD_LOGGING_SEARCH_OPENED);
          }}
          placeholder={FOOD_TRACKING.inputPlaceHolder}
          aria-label={FOOD_TRACKING_A11Y_STRINGS.searchLabel(searchTerm.length > 0)}
          startAdornment={
            <InputAdornment position="start">
              <AppIcon name="Search" color={color.icon.strong} />
            </InputAdornment>
          }
          endAdornment={
            searchTerm && isAllTabActive ? (
              <InputAdornment position="end">
                <Box display="flex" alignItems="center" justifyContent="center">
                  <Typography variant="body" sx={{ color: color.text.disabled, whiteSpace: "nowrap", pr: 2 }}>
                    {FOOD_TRACKING.inputHintLabel}
                  </Typography>
                  <IconButton
                    aria-label={FOOD_TRACKING.clearTextButtonAriaLabel}
                    sx={{ p: 0 }}
                    onClick={handleClearSearch}
                    disableRipple
                  >
                    <AppIcon name="Close" color={color.icon.strong} />
                  </IconButton>
                </Box>
              </InputAdornment>
            ) : null
          }
        />

        <Box>
          <Tabs value={activeTab} onChange={(_, targetTab) => setActiveTab(targetTab)}>
            <Tab
              label={FOOD_TRACKING.tabs.all}
              value={FOOD_TRACKING.tabs.all}
              {...tabsA11yProps(FOOD_TRACKING.tabs.all)}
            />
            <Tab
              label={FOOD_TRACKING.tabs.addedFoods}
              value={FOOD_TRACKING.tabs.addedFoods}
              {...tabsA11yProps(FOOD_TRACKING.tabs.addedFoods)}
            />
            <Tab
              label={FOOD_TRACKING.tabs.myRecipes}
              value={FOOD_TRACKING.tabs.myRecipes}
              {...tabsA11yProps(FOOD_TRACKING.tabs.myRecipes)}
            />
          </Tabs>

          <TabsPanel
            tabName={FOOD_TRACKING.tabs.all}
            currentSelectedTabName={activeTab}
            sx={{ paddingTop: SPACING_32_PX }}
          >
            <AllFoodScreen
              searchTerm={searchTermToUse}
              /** If an error occurs, then we don't want to show stale passio results */
              shouldDisplayPassioResults={shouldDisplayPassioResults && !isError}
              isLoadingPassioResults={isLoading || isFetching}
              passioSearchResults={data?.results}
              handleTrackAFood={handleTrackAFood}
              handleAddACustomFood={handleAddACustomFood}
              handleSearchPassio={handleOnSearchPassio}
            />
          </TabsPanel>
          <TabsPanel
            tabName={FOOD_TRACKING.tabs.addedFoods}
            currentSelectedTabName={activeTab}
            sx={{ paddingTop: SPACING_32_PX }}
          >
            <AddedFoodsScreen
              searchTerm={searchTermToUse}
              isAddedFoodsDrawerOpen={isAddedFoodsDrawerOpen}
              toggleAddedFoodsDrawer={(isOpen) => setIsAddedFoodsDrawerOpen(isOpen)}
              handleTrackAFood={handleTrackAFood}
              handleSearchPassio={() => {
                setActiveTab(FOOD_TRACKING.tabs.all);
                handleOnSearchPassio();
              }}
            />
          </TabsPanel>
          <TabsPanel
            tabName={FOOD_TRACKING.tabs.myRecipes}
            currentSelectedTabName={activeTab}
            sx={{ paddingTop: SPACING_32_PX }}
          >
            <MyRecipesScreen searchTerm={searchTermToUse} handleTrackAFood={handleTrackAFood} />
          </TabsPanel>
        </Box>
      </Box>
    </div>
  );
}
