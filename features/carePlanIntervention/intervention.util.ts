/** Variants of referral intervention screen */
export type ScreenType = "referral" | "recommendation";

/**
 * Represents the set of referral code that can be passed to this screen
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const SERVICE_REFERRAL_CODE = {
  colonoscopy: "colonoscopy",
  gi: "gi",
} as const;

/** Referral codes for the recommendation letter interventions */
const REFERRAL_LETTER_CODE = {
  referral_summary_crc: "referral_summary_crc",
  referral_summary_high_risk: "referral_summary_high_risk",
  referral_summary_ibd: "referral_summary_ibd",
  referral_summary_ibs: "referral_summary_ibs",
  referral_summary_sxs: "referral_summary_sxs",
  referral_summary_gerd: "referral_summary_gerd",
} as const;

/**
 * Referral code to be passed by navigation param to change context of the screen
 * It's given by a query of intervention uri parameter.
 */
type ServiceReferralCode = (typeof SERVICE_REFERRAL_CODE)[keyof typeof SERVICE_REFERRAL_CODE];

/**
 * Type to be passed by navigation param to change context of the screen.
 * It's given by a query of intervention uri parameter.
 */
type ReferralLetterCode = (typeof REFERRAL_LETTER_CODE)[keyof typeof REFERRAL_LETTER_CODE];

export type ReferralCode = ServiceReferralCode | ReferralLetterCode;

export type ReferralTextContent = Readonly<{
  headerTitle: string;
  screenTitle: string;
  paragraph: string;
}>;

/** Represents each texts for referral screen. They are based on ReferralCode */
export const REFERRAL_TEXT: {
  // Each keys in this object is a referral code which is included intervention's uri code query
  readonly [key in ServiceReferralCode]: ReferralTextContent;
} = {
  gi: {
    headerTitle: "Referral",
    screenTitle: "Finding a specialist in your area",
    paragraph:
      "A GIThrive care coordinator will reach out to you via email to help you find a GI specialist in your area.",
  } as const,
  colonoscopy: {
    headerTitle: "Test request",
    screenTitle: "Finding a specialist in your area",
    paragraph:
      "A GIThrive care coordinator will reach out to you via email to help you schedule a colonoscopy with a GI specialist in your area.",
  } as const,
};

const RECOMMENDATION_TEXT = {
  headerTitle: "Talk to your doctor about this recommendation",
  screenTitle: "Download and print this recommendation letter to bring to your next visit with your doctor.",
  paragraph: "Recommendation letter",
} as const;

export function getScreenText(referralCode: ReferralCode): ReferralTextContent {
  if (referralCode in REFERRAL_LETTER_CODE) {
    return RECOMMENDATION_TEXT;
  }

  if (referralCode === "gi" || referralCode === "colonoscopy") {
    return REFERRAL_TEXT[referralCode];
  }

  return REFERRAL_TEXT.gi;
}

export function getScreenType(referralCode: ReferralCode): ScreenType {
  if (referralCode in REFERRAL_LETTER_CODE) {
    return "recommendation";
  }

  return "referral";
}

export function getPdfUrl(referralCode: ReferralCode) {
  const CYLINDER_PDF_BUCKET = "https://storage.googleapis.com/cylinderhealth-media-assets-ea9d/cylinder/pdfs";

  switch (referralCode) {
    case REFERRAL_LETTER_CODE.referral_summary_crc:
      return `${CYLINDER_PDF_BUCKET}/Referral-letter-high-risk-digestive-symptoms.pdf`;
    case "referral_summary_crc":
      return `${CYLINDER_PDF_BUCKET}/Referral-letter-colorectal-cancer-screening.pdf`;
    case "referral_summary_high_risk":
      return `${CYLINDER_PDF_BUCKET}/Referral-letter-high-risk-digestive-symptoms.pdf`;
    case "referral_summary_ibd":
      return `${CYLINDER_PDF_BUCKET}/Referral-letter-inflammatory-bowel-disease.pdf`;
    case "referral_summary_ibs":
      return `${CYLINDER_PDF_BUCKET}/Referral-letter-irritable-bowel-syndrome.pdf`;
    case "referral_summary_sxs":
      return `${CYLINDER_PDF_BUCKET}/Referral-letter-symptomatic-no-diagnosis.pdf`;
    case "referral_summary_gerd":
      return `${CYLINDER_PDF_BUCKET}/Referral-letter-gastroesophageal-reflux-disease.pdf`;
    default:
      return "";
  }
}
