import { RecommendationPage } from "./components/RecommendationPage";
import { ReferralPage } from "./components/ReferralPage";
import { ReferralTextContent, ScreenType } from "./intervention.util";

type CarePlanInterventionScreenProps = Readonly<{
  onPdfClick: () => void;
  onBackClick: () => void;
  onDoneClick: () => void;
  screenType: ScreenType;
  screenText: ReferralTextContent;
}>;

export const CarePlanInterventionScreen = ({
  onPdfClick,
  onBackClick,
  onDoneClick,
  screenText,
  screenType,
}: CarePlanInterventionScreenProps) => {
  return (
    <>
      {screenType === "recommendation" && (
        <RecommendationPage onPdfClick={onPdfClick} onDoneClick={onDoneClick} screenText={screenText} />
      )}
      {screenType === "referral" && (
        <ReferralPage onBackClick={onBackClick} onDoneClick={onDoneClick} screenText={screenText} />
      )}
    </>
  );
};
