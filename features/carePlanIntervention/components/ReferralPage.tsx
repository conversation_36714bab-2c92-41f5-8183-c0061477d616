import { Box, Typography, Button, Paper } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

import { ReferralTextContent } from "../intervention.util";

export const ReferralPage = ({
  onBackClick,
  onDoneClick,
  screenText,
}: {
  onBackClick: () => void;
  onDoneClick: () => void;
  screenText: ReferralTextContent;
}) => (
  <>
    <Paper>
      <Typography variant="h3" mb={3}>
        {screenText.headerTitle}
      </Typography>
      <Typography variant="body">{screenText.screenTitle}</Typography>
    </Paper>
    <Box display="flex" justifyContent="space-between" gap={4} mt={5}>
      <Button variant="secondary" onClick={onBackClick} sx={{ flex: "0 1 250px" }}>
        {appStrings.buttonText.back}
      </Button>
      <Button variant="primary" onClick={onDoneClick} sx={{ flex: "0 1 250px" }}>
        {appStrings.buttonText.done}
      </Button>
    </Box>
  </>
);
