import { Box, Typography, Button, Paper } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";

import { ReferralTextContent } from "../intervention.util";

export const RecommendationPage = ({
  onPdfClick,
  onDoneClick,
  screenText,
}: {
  onPdfClick: () => void;
  onDoneClick: () => void;
  screenText: ReferralTextContent;
}) => (
  <>
    <Paper>
      <Typography variant="h3" mb={3} color={color.text.strong}>
        {screenText.headerTitle}
      </Typography>
      <Typography variant="body">{screenText.screenTitle}</Typography>
    </Paper>
    <Box display="flex" justifyContent="space-between" gap={4} mt={5}>
      <Button variant="secondary" onClick={onPdfClick} sx={{ flex: "0 1 250px" }}>
        {screenText.paragraph}
      </Button>
      <Button variant="primary" onClick={onDoneClick} sx={{ flex: "0 1 250px" }}>
        {appStrings.buttonText.done}
      </Button>
    </Box>
  </>
);
