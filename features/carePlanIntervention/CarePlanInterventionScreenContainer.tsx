import { useDispatch, useSelector } from "react-redux";
import { CarePlanIntervention } from "@vivantehealth/vivante-core";
import { useRouter } from "next/router";

import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { ActionPlansStateSlice, selectInterventions } from "@Features/carePlan/store/actionPlansStateSlice";

import { CarePlanInterventionScreen } from "./CarePlanInterventionScreen";
import { ReferralCode, ScreenType, getPdfUrl, getScreenText, getScreenType } from "./intervention.util";

type CarePlanInterventionScreenContainerProps = Readonly<{
  interventionId: string;
}>;

export const CarePlanInterventionScreenContainer = ({ interventionId }: CarePlanInterventionScreenContainerProps) => {
  const router = useRouter();
  const referralCode: ReferralCode = interventionId as ReferralCode;

  const dispatch = useDispatch();

  const interventions: CarePlanIntervention[] = useSelector(selectInterventions);
  const targetIntervention = interventions?.find((intervention) => intervention.id === interventionId);
  const onPdfClick = () => {
    window.open(getPdfUrl(referralCode), "_blank");
  };

  const onDoneClick = () => {
    if (targetIntervention) {
      dispatch(
        ActionPlansStateSlice.actions.setInterventionState({
          intervention: targetIntervention,
          newState: "COMPLETED",
        }),
      );
    }
  };

  const onBackClick = () => {
    router.back();
  };

  const screenText = getScreenText(referralCode);
  const screenType: ScreenType = getScreenType(referralCode);

  return (
    <>
      <LoadingSpinner open={!targetIntervention} />
      {targetIntervention && (
        <CarePlanInterventionScreen
          onBackClick={onBackClick}
          onPdfClick={onPdfClick}
          onDoneClick={onDoneClick}
          screenText={screenText}
          screenType={screenType}
        />
      )}
    </>
  );
};
