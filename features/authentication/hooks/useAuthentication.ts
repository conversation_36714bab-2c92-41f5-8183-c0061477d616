import { useDispatch, useSelector } from "react-redux";
import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import * as Sentry from "@sentry/nextjs";
import { sendPasswordResetEmail, signInWithCustomToken } from "firebase/auth";

import { selectAccessCode } from "@Features/eligibility/store/eligibilityProcessStateSlice";
import { memberStateSelector } from "@Features/member/store/memberStateSlice";
import { useLazyAssociateTimezoneQuery, useLazyAssociateRegistrationCodeQuery } from "@Features/signUp/api/signUpApi";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { getFirebaseAuth } from "@Utils/getFirebaseAuth";
import { logger } from "@Utils/logger";

import { LOCAL_STORAGE_ACCESS_CODE } from "../assets/constants";
import { initializeFirebaseAuth, signInSuccess } from "../store/authenticationEpics";
import { authenticationStateSlice } from "../store/authenticationStateSlice";

const getAccessCodeFromLocalStorage = () => {
  if (typeof window !== "undefined") {
    return localStorage.getItem(LOCAL_STORAGE_ACCESS_CODE);
  }

  return null;
};

export const useAuthentication = () => {
  const dispatch = useDispatch();
  const { sendEventAnalytics } = useAnalyticsHook();
  const [associateTimeZoneThroughMainApi] = useLazyAssociateTimezoneQuery();
  const [associateRegistrationCodeThroughMainApi] = useLazyAssociateRegistrationCodeQuery();
  const member = useSelector(memberStateSelector("member"));
  const registrationCodeFromState = useSelector(selectAccessCode);
  const registrationCode = registrationCodeFromState || getAccessCodeFromLocalStorage() || "";

  const associateMemberWithRegCodeAndTimezone = async () => {
    try {
      await Promise.all([
        associateTimeZoneThroughMainApi(Intl.DateTimeFormat().resolvedOptions().timeZone).unwrap(),
        associateRegistrationCodeThroughMainApi(registrationCode).unwrap(),
      ]);
    } catch (error) {
      /** Matching existing behavior from Core where we just log the error */
      logger.error(error);
      Sentry.captureException(error);
    }
  };

  const authenticateNewlyCreatedUser = async (signInToken: string) => {
    await authenticateUserWithCustomToken(signInToken, false);
    await associateMemberWithRegCodeAndTimezone();

    sendEventAnalytics(ClickStreamActivityEventType.REGISTRATION_SUCCESS);
    dispatch(authenticationStateSlice.actions.setCurrentlyRegistering(false));
  };

  const authenticateUserWithCustomToken = async (signInToken: string, shouldInitializeLogin = true) => {
    const firebaseAuth = await getFirebaseAuth();

    await signInWithCustomToken(firebaseAuth, signInToken);

    if (shouldInitializeLogin) {
      dispatch(initializeFirebaseAuth());
      dispatch(signInSuccess());
    }
  };

  const sendPasswordReset = async () => {
    const firebaseAuth = await getFirebaseAuth();

    await sendPasswordResetEmail(firebaseAuth, member?.email ?? "");
  };

  return { authenticateUserWithCustomToken, authenticateNewlyCreatedUser, sendPasswordReset } as const;
};
