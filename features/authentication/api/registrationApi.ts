import { JsonSchemaForm } from "@vivantehealth/vivante-core";

import { bffApi } from "@Api/bffApi";
import { LowerCaseMfaType } from "@Features/multiFactorAuthentication/types/mfa.types";

import { authenticationStateSlice } from "../store/authenticationStateSlice";

export const registrationApi = bffApi.injectEndpoints({
  endpoints: (builder) => ({
    getUserStatus: builder.query<UserStatusResponse, void>({
      query: () => ({
        url: `v1/user/status`,
        method: "GET",
      }),
    }),
    /** Get configuration info set to the given accessCode. It contains info like whether the registration is v2 or not */
    getRegistrationCodeConfig: builder.query<RegistrationCodeConfigResponse, string>({
      query: (accessCode) => ({
        url: `v1/registration_code/${accessCode}`,
        method: "GET",
      }),
    }),
    registeredMemberMatch: builder.mutation<RegisteredMemberMatchResponse, void>({
      query: () => ({
        url: "/v1/user/registered_member_match",
        method: "POST",
      }),
    }),
    /** Creates a new user in Firebase */
    createUser: builder.query<CreateUserResponse, CreateUserRequest>({
      query: (body) => {
        const { registrationCode, mfaPhoneNumber, ...createUserBody } = body;

        return {
          url: "/v1/user",
          method: "POST",
          body: {
            ...createUserBody,
            registration_code: registrationCode,
            mfa_phone_number: mfaPhoneNumber?.replace(/-/g, ""),
          },
        };
      },
      onQueryStarted: async (_, { dispatch, queryFulfilled }) => {
        dispatch(authenticationStateSlice.actions.setCurrentlyRegistering(true));

        await queryFulfilled;
      },
    }),
  }),
});

export const {
  useLazyGetUserStatusQuery,
  useGetUserStatusQuery,
  useLazyGetRegistrationCodeConfigQuery,
  useRegisteredMemberMatchMutation,
  useLazyCreateUserQuery,
} = registrationApi;

type CreateUserRequest = {
  email: string;
  password: string;
  registrationCode: string;
  mfaPhoneNumber?: string;
};

type CreateUserResponse = {
  id: string;
  authentication_system_user_id: string;
};

export type UserAttributes = {
  first_name: string;
  last_name: string;
  phone_mobile: string;
  us_state: string;
};

type BaseRegistrationConfigNoMfaRequired = {
  mfa_required: false;
  mfa_supported_methods?: never;
};

type BaseRegistrationConfigMfaResponseMfaRequired = {
  mfa_required: true;
  mfa_supported_methods: LowerCaseMfaType[];
};

type BaseRegistrationConfigMfa = BaseRegistrationConfigNoMfaRequired | BaseRegistrationConfigMfaResponseMfaRequired;

type BaseRegistrationConfigResponse = BaseRegistrationConfigMfa & {
  launch_date: string;
  status: string;
  pre_launch_form: string;
  registration_code?: string;
};
/** Shape of configurations of the access code */
type RegistrationCodeConfigResponse = {
  data: {
    attributes: BaseRegistrationConfigResponse & {
      /** This flag determines whether the registration is in v2 or not */
      registration_v2: boolean;
      passive_v2_eligibility_check?: boolean;
    };
    id: string;
    type: string;
  };
};

type RedirectToPartnerPreverifiedRegistration =
  | {
      redirect_to_partner_preverified_registration: true;
      partner_preverified_registration_url: string;
    }
  | {
      redirect_to_partner_preverified_registration: false;
    };

type BaseRegistrationDetails = BaseRegistrationConfigResponse & {
  registration_code: string;
  check_if_sso_user: boolean;
  non_sso_user_type_options?: string[];
  sso_user_type_options?: string[];
  sso_redirect_link?: string;
  //   /** pre populated user info when the user is registering via SSO. An empty object otherwise */
  sso_user_attributes?: UserAttributes & {
    date_of_birth: string;
    sso_email: string;
    member_id: string;
  };
  passive_v2_eligibility_check: boolean;
} & RedirectToPartnerPreverifiedRegistration;

type RegistrationDetailsV2 = BaseRegistrationDetails & {
  pre_launch_form_link: string;
  /** form schema to be fed into eligibility screen to populate registration form */
  registration_form_schema: JsonSchemaForm;
};

type AccountStatus = "active" | "inactive" | "not_applicable";

type UserStatusResponse =
  | {
      data: {
        type: string;
        attributes: {
          registration_status: string;
          account_status: AccountStatus;
          registration_v2: false;
          registration_details: BaseRegistrationDetails;
        };
        id: string;
      };
    }
  | {
      data: {
        type: string;
        attributes: {
          registration_status: string;
          account_status: AccountStatus;
          registration_v2: true;
          registration_details: RegistrationDetailsV2;
        };
        id: string;
      };
    };

type RegisteredMemberMatchNonMatchWithUrl = {
  matched_to_registered_member: false;
  traditional_registration_code: string;
  redirect_to_partner_preverified_registration: true;
  partner_preverified_registration_url: string;
  matched_member_id?: never;
  matched_member_cylinder_person_id?: never;
};

type RegisteredMemberMatchNonMatchWithoutUrl = {
  matched_to_registered_member: false;
  traditional_registration_code: string;
  redirect_to_partner_preverified_registration: false;
  partner_preverified_registration_url?: never;
  matched_member_id?: never;
  matched_member_cylinder_person_id?: never;
};

type RegisteredMemberMatchNonMatch = RegisteredMemberMatchNonMatchWithUrl | RegisteredMemberMatchNonMatchWithoutUrl;

type RegisteredMemberMatchResponse =
  | RegisteredMemberMatchNonMatch
  | {
      matched_to_registered_member: true;
      matched_member_id: string;
      matched_member_cylinder_person_id: string;
      traditional_registration_code?: never;
    };
