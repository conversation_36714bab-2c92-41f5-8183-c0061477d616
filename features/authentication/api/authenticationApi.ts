import { bffApi } from "@Api/bffApi";

import { LOCAL_STORAGE_DEVICE_TOKEN } from "../assets/constants";

export const authenticationApi = bffApi.injectEndpoints({
  endpoints: (builder) => ({
    /** This endpoint is used to sign in a a non-mfa user by providing a sign in token OR sends the verification code for MFA and returns a 401 indicating as such */
    firstFactorSignIn: builder.query<FirstFactorSignInResponse, FirstFactorSignInRequest>({
      query: (body) => {
        /** Check device id stored in local storage for MFA */
        const trusted_device_token = localStorage.getItem(LOCAL_STORAGE_DEVICE_TOKEN) ?? undefined;
        const { phone, ...rest } = body;

        return {
          url: "v1/user/first_factor_signin",
          method: "POST",
          body: { ...rest, phone: phone?.replace(/-/g, ""), trusted_device_token },
        };
      },
    }),
    /** This endpoint is used to verify an MFA verification code and to establish a trusted device if the user elects to do so */
    secondFactorSignIn: builder.query<TwoFactorsSignInResponse, TwoFactorsSignInRequest>({
      query: (body) => {
        const { verificationCode, trustThisDevice, phone, ...secondFactorSignInBody } = body;

        return {
          url: "v1/user/two_factors_signin",
          method: "POST",
          body: {
            ...secondFactorSignInBody,
            phone: phone?.replace(/-/g, ""),
            trust_this_device: trustThisDevice,
            second_factor_verification_code: verificationCode,
          },
        };
      },
    }),
  }),
});

export const { useLazyFirstFactorSignInQuery, useLazySecondFactorSignInQuery } = authenticationApi;

type FirstFactorSignInRequest = {
  email: string;
  password: string;
  phone?: string;
};

type FirstFactorSignInResponse = {
  signin_token: string;
};

type TwoFactorsSignInResponse = FirstFactorSignInResponse & { trusted_device_token?: string };

type TwoFactorsSignInRequest = {
  email: string;
  password: string;
  verificationCode: string;
  phone?: string; // Required if SMS mfa
  trustThisDevice: boolean;
};
