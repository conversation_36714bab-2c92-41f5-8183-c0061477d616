{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"type": "text", "version": 459, "versionNonce": 2079609761, "isDeleted": false, "id": "iqGZ6Z0D1q2K1979yTUo0", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3011.0756483195155, "y": 716.6208254546498, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 167.1584930419922, "height": 31.28705544719843, "seed": 1425087858, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 25.029644357758745, "fontFamily": 1, "text": "Survey screen", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Survey screen", "lineHeight": 1.25}, {"type": "rectangle", "version": 906, "versionNonce": 719647471, "isDeleted": false, "id": "cgNvH9d6UyySzuDdLc5YF", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 2998.2201234632016, "y": 757.2869923997627, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 895.5650325186058, "height": 222.05671290029704, "seed": 1160196206, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"id": "a-5o0QtF1bY4THkgqKRhR", "type": "arrow"}], "updated": 1712619379640, "link": null, "locked": false}, {"type": "rectangle", "version": 315, "versionNonce": 407582593, "isDeleted": false, "id": "iQALjViX3D2I1ef1ew-2S", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3484.0101421412664, "y": 830.2233851447096, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 133.9283905193456, "height": 85, "seed": 243372206, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "vfpJvc_4fJ-N8RQFgNEBo"}, {"id": "ec12K7us-QqS0PyPkmuq9", "type": "arrow"}, {"id": "3Wl33pOW7JR-VKxN0Ub2n", "type": "arrow"}], "updated": 1712619379640, "link": null, "locked": false}, {"type": "text", "version": 268, "versionNonce": 1321647375, "isDeleted": false, "id": "vfpJvc_4fJ-N8RQFgNEBo", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3489.8444011826778, "y": 847.7233851447096, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 122.25987243652344, "height": 50, "seed": 1302809326, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Fetch \ninterventions", "textAlign": "center", "verticalAlign": "middle", "containerId": "iQALjViX3D2I1ef1ew-2S", "originalText": "Fetch interventions", "lineHeight": 1.25}, {"type": "rectangle", "version": 693, "versionNonce": 1943707489, "isDeleted": false, "id": "DPONekTet1lcrJiUtxmby", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3691.54442941931, "y": 776.7970965257265, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 182.12056571954005, "height": 185, "seed": 1726798766, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "2aBkMZCKzjMkdPQnnHd3I"}, {"id": "ec12K7us-QqS0PyPkmuq9", "type": "arrow"}], "updated": 1712619379640, "link": null, "locked": false}, {"type": "text", "version": 777, "versionNonce": 1143620399, "isDeleted": false, "id": "2aBkMZCKzjMkdPQnnHd3I", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3698.0347888782007, "y": 781.7970965257265, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 169.1398468017578, "height": 175, "seed": 812725742, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "<PERSON><PERSON> receives and\nfinds an \nintervention with \nan id in the \nquery param in \nthe \"terminal \nnode\"", "textAlign": "center", "verticalAlign": "middle", "containerId": "DPONekTet1lcrJiUtxmby", "originalText": "<PERSON><PERSON> receives and finds an intervention with an id in the query param in the \"terminal node\"", "lineHeight": 1.25}, {"type": "arrow", "version": 679, "versionNonce": 485624641, "isDeleted": false, "id": "ec12K7us-QqS0PyPkmuq9", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3625.9064493379506, "y": 872.0343518652089, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 55.62605492802413, "height": 0.06615364778281219, "seed": 319831410, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "startBinding": {"elementId": "iQALjViX3D2I1ef1ew-2S", "focus": -0.02115834964540242, "gap": 7.96791667733828}, "endBinding": {"elementId": "DPONekTet1lcrJiUtxmby", "focus": -0.03156963205965434, "gap": 10.011925153334914}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [55.62605492802413, 0.06615364778281219]]}, {"type": "text", "version": 292, "versionNonce": 1803027791, "isDeleted": false, "id": "H3taTiGxB2trDA3KxuRau", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3975.477786086407, "y": 815.7225690815009, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 158.2798614501953, "height": 25, "seed": 1663101422, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Follow up screen", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Follow up screen", "lineHeight": 1.25}, {"type": "rectangle", "version": 329, "versionNonce": 737213217, "isDeleted": false, "id": "G-nRJ9L8672qmws9-gWkD", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3985.1770579243735, "y": 844.862192271733, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 133.9283905193456, "height": 85, "seed": 529103918, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "aYe8GyyeMYWQIE2WrRXr6"}, {"id": "a-5o0QtF1bY4THkgqKRhR", "type": "arrow"}], "updated": 1712619379640, "link": null, "locked": false}, {"type": "text", "version": 297, "versionNonce": 67666799, "isDeleted": false, "id": "aYe8GyyeMYWQIE2WrRXr6", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3991.9213129984996, "y": 849.862192271733, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 120.43988037109375, "height": 75, "seed": 1571145326, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Whatever \nthe following\nup item", "textAlign": "center", "verticalAlign": "middle", "containerId": "G-nRJ9L8672qmws9-gWkD", "originalText": "Whatever the following up item", "lineHeight": 1.25}, {"type": "text", "version": 408, "versionNonce": 21425921, "isDeleted": false, "id": "5wIcHSw_09ZwVod24aydR", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3923.625971218882, "y": 935.4491483068584, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 279.07977294921875, "height": 25, "seed": **********, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "(survey, MD scheduling, etc.)", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "(survey, MD scheduling, etc.)", "lineHeight": 1.25}, {"type": "text", "version": 337, "versionNonce": **********, "isDeleted": false, "id": "bQl4YBNz5DVs-nv4FnHSf", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3009.9439609042884, "y": 839.7287462149685, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 129.57078552246094, "height": 47.984375000000014, "seed": **********, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"id": "BX3bnlj3nX1-9JYeF3fsL", "type": "arrow"}], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 19.193750000000005, "fontFamily": 1, "text": "User finishes \nSurvey", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "User finishes \nSurvey", "lineHeight": 1.25}, {"type": "arrow", "version": 541, "versionNonce": **********, "isDeleted": false, "id": "BX3bnlj3nX1-9JYeF3fsL", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3143.9634921542884, "y": 865.9710571148521, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 41.8203125, "height": 0.4192033657270713, "seed": 1885622427, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "startBinding": {"elementId": "w_iNle9NXkzBsbXeNI_La", "focus": 0.024807211946090865, "gap": 8.30078125}, "endBinding": {"elementId": "MJDlxp8OkaJMcCyImxSz2", "focus": 0.04880368866178852, "gap": 3.12890625}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [41.8203125, 0.4192033657270713]]}, {"type": "text", "version": 552, "versionNonce": 1828959151, "isDeleted": false, "id": "rWyOcNfAsvwigsfCFw5Dq", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3200.826536893058, "y": 806.6232774649685, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 211.01303100585938, "height": 119.96093750000003, "seed": 8911477, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"id": "BX3bnlj3nX1-9JYeF3fsL", "type": "arrow"}], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 19.193750000000005, "fontFamily": 1, "text": "App receives \na \"terminal node\" \nwith url that\ncontains all possibly\nlinked interventions ids", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "App receives \na \"terminal node\" \nwith url that\ncontains all possibly\nlinked interventions ids", "lineHeight": 1.25}, {"type": "rectangle", "version": 152, "versionNonce": 486924993, "isDeleted": false, "id": "MJDlxp8OkaJMcCyImxSz2", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3188.9127109042884, "y": 793.3185899649685, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 225.4375, "height": 156.19921875, "seed": 1942708987, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"id": "BX3bnlj3nX1-9JYeF3fsL", "type": "arrow"}, {"id": "3Wl33pOW7JR-VKxN0Ub2n", "type": "arrow"}], "updated": 1712619379640, "link": null, "locked": false}, {"type": "rectangle", "version": 139, "versionNonce": 147809743, "isDeleted": false, "id": "QFSzPoEOKUI0It-Xj1zdz", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3063.594717981199, "y": 634.888681692248, "strokeColor": "#f08c00", "backgroundColor": "#ffec99", "width": 53.02734375, "height": 48.3671875, "seed": 1814087445, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false}, {"type": "text", "version": 751, "versionNonce": 983425697, "isDeleted": false, "id": "LRtkDQgP27DAHJ3DAOJIh", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3130.5413274782695, "y": 646.826181692248, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 193.1088409423828, "height": 23.992187500000007, "seed": 255503099, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 19.193750000000005, "fontFamily": 1, "text": "= Background action", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "= Background action", "lineHeight": 1.25}, {"type": "rectangle", "version": 203, "versionNonce": 815780847, "isDeleted": false, "id": "P_WyJMDMoH-xGBf6ux5pZ", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3062.678702356199, "y": 570.021494192248, "strokeColor": "#1e1e1e", "backgroundColor": "#1e1e1e", "width": 53.02734375, "height": 48.3671875, "seed": 1120118811, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false}, {"type": "text", "version": 859, "versionNonce": 149612161, "isDeleted": false, "id": "7eFFuYqzWD8q5_UlGDpDr", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3133.880141260008, "y": 584.404306692248, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 183.32192993164062, "height": 23.992187500000007, "seed": 125961717, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 19.193750000000005, "fontFamily": 1, "text": "= User interaction ", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "= User interaction ", "lineHeight": 1.25}, {"type": "rectangle", "version": 144, "versionNonce": 2088599055, "isDeleted": false, "id": "w_iNle9NXkzBsbXeNI_La", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3005.2447421542884, "y": 820.4787462149685, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 130.41796875, "height": 87.3125, "seed": 1775305525, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"id": "BX3bnlj3nX1-9JYeF3fsL", "type": "arrow"}], "updated": 1712619379640, "link": null, "locked": false}, {"type": "arrow", "version": 519, "versionNonce": 850161249, "isDeleted": false, "id": "3Wl33pOW7JR-VKxN0Ub2n", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3427.369894957891, "y": 869.815307148346, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 45.45097033778211, "height": 0.1498848902321015, "seed": 1039097006, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "startBinding": {"elementId": "MJDlxp8OkaJMcCyImxSz2", "focus": -0.02571057917736513, "gap": 13.01968405360276}, "endBinding": {"elementId": "iQALjViX3D2I1ef1ew-2S", "focus": 0.05853033088785929, "gap": 11.18927684559344}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [45.45097033778211, 0.1498848902321015]]}, {"type": "arrow", "version": 760, "versionNonce": 1868620847, "isDeleted": false, "id": "a-5o0QtF1bY4THkgqKRhR", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3914.906372703569, "y": 885.2001493017472, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 62.616444364138715, "height": 0.8687337508500832, "seed": 522953518, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "startBinding": {"elementId": "cgNvH9d6UyySzuDdLc5YF", "focus": 0.088529566583069, "gap": 21.121216721761584}, "endBinding": {"elementId": "G-nRJ9L8672qmws9-gWkD", "focus": 0.00594213122671671, "gap": 7.65424085666541}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [62.616444364138715, 0.8687337508500832]]}, {"type": "rectangle", "version": 720, "versionNonce": 1588722241, "isDeleted": false, "id": "mSKHsWWXxTnTMxYSxJ4Lt", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8603.727833593392, "y": -2764.672635777186, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 804.7398016753369, "height": 743.3167600596698, "seed": 1142702662, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false}, {"type": "text", "version": 249, "versionNonce": 422741583, "isDeleted": false, "id": "PbO3kLrAK3M9OwwbqWv4E", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8619.542685949573, "y": -2806.254982587161, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 144.14419555664062, "height": 30.2968750000002, "seed": 2049797530, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 24.23750000000016, "fontFamily": 1, "text": "Vivante-core", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Vivante-core", "lineHeight": 1.25}, {"type": "rectangle", "version": 307, "versionNonce": 769329697, "isDeleted": false, "id": "F_UwcrL67ANkMsnauBOS0", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8648.976279699573, "y": -2547.133888837161, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 254.02734375, "height": 150.56640625, "seed": 547860358, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"id": "uvEdMV5nKhDnBceUA5sLJ", "type": "arrow"}, {"id": "pm6zCklfKMPQGYx1_R3GX", "type": "arrow"}, {"id": "-uSyGxyWULyjaC6xW0UZC", "type": "arrow"}, {"id": "LxcNVswbjijmK-xI9iX7H", "type": "arrow"}], "updated": 1712619379640, "link": null, "locked": false}, {"type": "text", "version": 428, "versionNonce": 2026562671, "isDeleted": false, "id": "RE3Wt-EglN00Ha7w22X3d", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8667.632529699573, "y": -2527.165138837161, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 215.92604064941406, "height": 31.78906250000001, "seed": 1553391686, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 25.43125000000001, "fontFamily": 1, "text": "Passio repository", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Passio repository", "lineHeight": 1.25}, {"type": "arrow", "version": 257, "versionNonce": 2031558145, "isDeleted": false, "id": "uvEdMV5nKhDnBceUA5sLJ", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 9038.355185949573, "y": -2468.5600388708076, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 129.71875, "height": 0.7430934232406798, "seed": 1049720902, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"type": "text", "id": "gkENeW0wll9pHRBFeUwuN"}], "updated": 1712619379640, "link": null, "locked": false, "startBinding": {"elementId": "6Ipa2TN_5wd4I5oLFDRKg", "focus": -0.03677449554839633, "gap": 10.755859375}, "endBinding": {"elementId": "F_UwcrL67ANkMsnauBOS0", "focus": 0.06306479042103237, "gap": 5.6328125}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-129.71875, 0.7430934232406798]]}, {"type": "text", "version": 29, "versionNonce": 502431375, "isDeleted": false, "id": "gkENeW0wll9pHRBFeUwuN", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8892.48708292223, "y": -2456.645607587161, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 59.0799560546875, "height": 25, "seed": 2083810758, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Inject", "textAlign": "center", "verticalAlign": "middle", "containerId": "uvEdMV5nKhDnBceUA5sLJ", "originalText": "Inject", "lineHeight": 1.25}, {"type": "rectangle", "version": 196, "versionNonce": 1332065761, "isDeleted": false, "id": "V7AgAg77Ax4mkwB9SKT5d", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8030.218467199573, "y": -2709.106545087161, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 406.42578125, "height": 698.765625, "seed": 1261670554, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false}, {"type": "text", "version": 118, "versionNonce": 190960815, "isDeleted": false, "id": "5exMHk2BgWhryhphsaJLB", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8038.675498449573, "y": -2749.751076337161, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 121.61041259765625, "height": 34.94140624999987, "seed": 1852274138, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 27.953124999999897, "fontFamily": 1, "text": "GIThrive", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "GIThrive", "lineHeight": 1.25}, {"type": "text", "version": 262, "versionNonce": 869683649, "isDeleted": false, "id": "_g1yeG5dPMb6JgkGzk5qH", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8100.773558043662, "y": -2687.373718149276, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 202.17697143554688, "height": 27.43473159284248, "seed": 1776626502, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 21.947785274273983, "fontFamily": 1, "text": "RNPassioSDKClient", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "RNPassioSDKClient", "lineHeight": 1.25}, {"type": "rectangle", "version": 358, "versionNonce": 1489069775, "isDeleted": false, "id": "k1Fbez8rP8ZQO7gn-08Bp", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8083.745810949573, "y": -2697.837013837161, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 276.2634604631124, "height": 156.1992187500001, "seed": 1451812422, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"id": "pm6zCklfKMPQGYx1_R3GX", "type": "arrow"}, {"id": "gOR5jx-gbfmvG9baD0DMl", "type": "arrow"}, {"id": "4_evCKaEZUmWPMWJO1IoE", "type": "arrow"}], "updated": 1712619379640, "link": null, "locked": false}, {"type": "rectangle", "version": 192, "versionNonce": 461926817, "isDeleted": false, "id": "zpdysa9-WNn1M838t1rt0", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8106.788779699573, "y": -2649.329201337161, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 210.1875, "height": 85.84765625, "seed": 1934638810, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "uemXoZsYwaZbvNFnCGf85"}], "updated": 1712619379640, "link": null, "locked": false}, {"type": "text", "version": 95, "versionNonce": 346666223, "isDeleted": false, "id": "uemXoZsYwaZbvNFnCGf85", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8171.5025705931275, "y": -2618.905373212161, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 80.75991821289062, "height": 25, "seed": 312385114, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Methods", "textAlign": "center", "verticalAlign": "middle", "containerId": "zpdysa9-WNn1M838t1rt0", "originalText": "Methods", "lineHeight": 1.25}, {"type": "rectangle", "version": 516, "versionNonce": 732665217, "isDeleted": false, "id": "XZ926yK-N5HLW8llrt3SQ", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8839.954795324573, "y": -2738.713966962161, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 254.02734375, "height": 87.76171875, "seed": 1837503686, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"id": "gOR5jx-gbfmvG9baD0DMl", "type": "arrow"}], "updated": 1712619379640, "link": null, "locked": false}, {"type": "text", "version": 540, "versionNonce": 1721974543, "isDeleted": false, "id": "iYnyOwX9GtRz-M6pxz3-X", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8865.279014074573, "y": -2709.299904462161, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 200.71888732910156, "height": 31.78906250000001, "seed": 1723042822, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 25.43125000000001, "fontFamily": 1, "text": "PassioSDKClient", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "PassioSDKClient", "lineHeight": 1.25}, {"type": "arrow", "version": 159, "versionNonce": 931085665, "isDeleted": false, "id": "pm6zCklfKMPQGYx1_R3GX", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8375.234092199573, "y": -2594.921708439955, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 262.72265625, "height": 107.66728742725354, "seed": 1436738374, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"type": "text", "id": "Sod14RPiJPetcXeHvlQwk"}], "updated": 1712619379640, "link": null, "locked": false, "startBinding": {"elementId": "k1Fbez8rP8ZQO7gn-08Bp", "focus": -0.2823286481453768, "gap": 15.224820786886994}, "endBinding": {"elementId": "F_UwcrL67ANkMsnauBOS0", "focus": -0.32327399689716624, "gap": 11.01953125}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [262.72265625, 107.66728742725354]]}, {"type": "text", "version": 30, "versionNonce": 1898349871, "isDeleted": false, "id": "Sod14RPiJPetcXeHvlQwk", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8477.14137979723, "y": -2553.6427522263284, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 59.0799560546875, "height": 25, "seed": 728335130, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Inject", "textAlign": "center", "verticalAlign": "middle", "containerId": "pm6zCklfKMPQGYx1_R3GX", "originalText": "Inject", "lineHeight": 1.25}, {"type": "arrow", "version": 178, "versionNonce": 2119362881, "isDeleted": false, "id": "gOR5jx-gbfmvG9baD0DMl", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8827.823935949573, "y": -2702.96607860952, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 455.75, "height": 78.08695395962013, "seed": 946692358, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"type": "text", "id": "zb6z4ijr3bPCb-fGwLULi"}], "updated": 1712619379640, "link": null, "locked": false, "startBinding": {"elementId": "XZ926yK-N5HLW8llrt3SQ", "focus": 0.4871196801366879, "gap": 12.130859375}, "endBinding": {"elementId": "k1Fbez8rP8ZQO7gn-08Bp", "focus": 0.20235021455663357, "gap": 12.064664536886994}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-455.75, 78.08695395962013]]}, {"type": "text", "version": 31, "versionNonce": 1939605327, "isDeleted": false, "id": "zb6z4ijr3bPCb-fGwLULi", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8533.846007787952, "y": -2677.102638837161, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 95.11991882324219, "height": 25, "seed": 879925510, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Interface", "textAlign": "center", "verticalAlign": "middle", "containerId": "gOR5jx-gbfmvG9baD0DMl", "originalText": "Interface", "lineHeight": 1.25}, {"type": "rectangle", "version": 256, "versionNonce": 696654113, "isDeleted": false, "id": "GH5TJ2rJrZhgZAaaUSYAm", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8094.277060949573, "y": -2421.852638837161, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 263.2890625, "height": 100.80078125, "seed": 913706822, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"id": "4_evCKaEZUmWPMWJO1IoE", "type": "arrow"}, {"id": "4BpXnpQFDrzMBNu_tjUFt", "type": "arrow"}], "updated": 1712619379640, "link": null, "locked": false}, {"type": "text", "version": 380, "versionNonce": 744244591, "isDeleted": false, "id": "AtMsa6STtFJypsPGT_cnt", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8108.710654699573, "y": -2411.504982587161, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 246.77980041503906, "height": 75, "seed": 1815496134, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"id": "i_k_D480PNnIj6unZ8Tuw", "type": "arrow"}, {"id": "LxcNVswbjijmK-xI9iX7H", "type": "arrow"}], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "VivanteCoreContainer\n(Instantiates SDK client\nand repos)", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "VivanteCoreContainer\n(Instantiates SDK client\nand repos)", "lineHeight": 1.25}, {"type": "arrow", "version": 294, "versionNonce": 176704769, "isDeleted": false, "id": "4_evCKaEZUmWPMWJO1IoE", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8229.727702932236, "y": -2430.606545087161, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "width": 2.4860391713300487, "height": 106.68359375, "seed": 892758938, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"type": "text", "id": "0y0HKA_gZ0mbN5I0xfkHI"}], "updated": 1712619379640, "link": null, "locked": false, "startBinding": {"elementId": "GH5TJ2rJrZhgZAaaUSYAm", "focus": 0.03903491401385105, "gap": 8.75390625}, "endBinding": {"elementId": "k1Fbez8rP8ZQO7gn-08Bp", "focus": -0.024600352260400547, "gap": 4.34765625}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-2.4860391713300487, -106.68359375]]}, {"type": "text", "version": 44, "versionNonce": 2058338191, "isDeleted": false, "id": "0y0HKA_gZ0mbN5I0xfkHI", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8167.612967931995, "y": -2512.624123212161, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "width": 116.33990478515625, "height": 25, "seed": 993978374, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Instantiate", "textAlign": "center", "verticalAlign": "middle", "containerId": "4_evCKaEZUmWPMWJO1IoE", "originalText": "Instantiate", "lineHeight": 1.25}, {"type": "rectangle", "version": 494, "versionNonce": 416875745, "isDeleted": false, "id": "BI6ih7JNuXVyEDYt_30NK", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8088.551580718016, "y": -2204.835060712161, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 276.2634604631124, "height": 156.1992187500001, "seed": 1104913050, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "vdpnwnyq-qYMLhKlbmgGm"}, {"id": "O8wgx3Yqjx9HRic1k-9-j", "type": "arrow"}, {"id": "4BpXnpQFDrzMBNu_tjUFt", "type": "arrow"}], "updated": 1712619379640, "link": null, "locked": false}, {"type": "text", "version": 131, "versionNonce": 1087305135, "isDeleted": false, "id": "vdpnwnyq-qYMLhKlbmgGm", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8123.99341531969, "y": -2151.735451337161, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 205.37979125976562, "height": 50, "seed": 373651526, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Redux layer that\ninvokes repo methods", "textAlign": "center", "verticalAlign": "middle", "containerId": "BI6ih7JNuXVyEDYt_30NK", "originalText": "Redux layer that\ninvokes repo methods", "lineHeight": 1.25}, {"type": "text", "version": 510, "versionNonce": 805814465, "isDeleted": false, "id": "OoaHZooL2TC_pa0CWSoy4", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8834.784873449573, "y": -2177.036232587161, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 223.05979919433594, "height": 25, "seed": 1000423750, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "PassioUseCaseFactory", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "PassioUseCaseFactory", "lineHeight": 1.25}, {"type": "rectangle", "version": 311, "versionNonce": 98723791, "isDeleted": false, "id": "yDcGdIP_v7VKjhINbmz5X", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8813.136435949573, "y": -2193.876076337161, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 265.5625, "height": 128.5195312500001, "seed": 1192476122, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"id": "-uSyGxyWULyjaC6xW0UZC", "type": "arrow"}, {"id": "i_k_D480PNnIj6unZ8Tuw", "type": "arrow"}, {"id": "O8wgx3Yqjx9HRic1k-9-j", "type": "arrow"}], "updated": 1712619379640, "link": null, "locked": false}, {"type": "rectangle", "version": 425, "versionNonce": 229900449, "isDeleted": false, "id": "6Ipa2TN_5wd4I5oLFDRKg", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 9049.111045324573, "y": -2550.700295087161, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 286.30859375, "height": 156.6953125, "seed": 394992006, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"id": "uvEdMV5nKhDnBceUA5sLJ", "type": "arrow"}], "updated": 1712619379640, "link": null, "locked": false}, {"type": "text", "version": 438, "versionNonce": 1476564463, "isDeleted": false, "id": "NUAZ6LqAs3LC3HuuaECYy", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 9067.419639074573, "y": -2475.258888837161, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 248.85975646972656, "height": 25, "seed": 993654982, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "PassioFoodToFoodMapper", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "PassioFoodToFoodMapper", "lineHeight": 1.25}, {"type": "arrow", "version": 202, "versionNonce": 260916353, "isDeleted": false, "id": "-uSyGxyWULyjaC6xW0UZC", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8789.361112484075, "y": -2379.016701337161, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 138.8127732132234, "height": 173.94140625, "seed": 1324171930, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"type": "text", "id": "SYqfz-80f5O-BR7nK6HgY"}], "updated": 1712619379640, "link": null, "locked": false, "startBinding": {"elementId": "F_UwcrL67ANkMsnauBOS0", "gap": 17.55078125, "focus": 0.3245038247729079}, "endBinding": {"elementId": "yDcGdIP_v7VKjhINbmz5X", "gap": 11.19921875, "focus": 0.23076697291417425}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [138.8127732132234, 173.94140625]]}, {"type": "text", "version": 22, "versionNonce": 1849163791, "isDeleted": false, "id": "SYqfz-80f5O-BR7nK6HgY", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8833.44020792223, "y": -2289.333107587161, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 59.0799560546875, "height": 25, "seed": 1462012570, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Inject", "textAlign": "center", "verticalAlign": "middle", "containerId": "-uSyGxyWULyjaC6xW0UZC", "originalText": "Inject", "lineHeight": 1.25}, {"type": "arrow", "version": 210, "versionNonce": 827526241, "isDeleted": false, "id": "i_k_D480PNnIj6unZ8Tuw", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8366.163779699573, "y": -2362.7068404455626, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "width": 435.453125, "height": 242.94542071251635, "seed": 503378458, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"type": "text", "id": "OAmdsuawHhdXKFlBWZl8s"}], "updated": 1712619379640, "link": null, "locked": false, "startBinding": {"elementId": "AtMsa6STtFJypsPGT_cnt", "focus": -0.5971134336174593, "gap": 10.673324584960938}, "endBinding": {"elementId": "yDcGdIP_v7VKjhINbmz5X", "focus": -0.6531887741283162, "gap": 11.51953125}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [435.453125, 242.94542071251635]]}, {"type": "text", "version": 28, "versionNonce": 175842863, "isDeleted": false, "id": "OAmdsuawHhdXKFlBWZl8s", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8526.657889806995, "y": -2251.270607587161, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "width": 116.33990478515625, "height": 25, "seed": 214737670, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Instantiate", "textAlign": "center", "verticalAlign": "middle", "containerId": "i_k_D480PNnIj6unZ8Tuw", "originalText": "Instantiate", "lineHeight": 1.25}, {"type": "arrow", "version": 99, "versionNonce": 1255462977, "isDeleted": false, "id": "LxcNVswbjijmK-xI9iX7H", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8366.019248449573, "y": -2377.864357587161, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "width": 272.7265625, "height": 80.4609375, "seed": 481751302, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"type": "text", "id": "K3Yy7ZVr1kepjWU614LOq"}], "updated": 1712619379640, "link": null, "locked": false, "startBinding": {"elementId": "AtMsa6STtFJypsPGT_cnt", "focus": 0.4823874223796433, "gap": 10.528793334960938}, "endBinding": {"elementId": "F_UwcrL67ANkMsnauBOS0", "focus": 0.23914574641745254, "gap": 10.23046875}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [272.7265625, -80.4609375]]}, {"type": "text", "version": 28, "versionNonce": 464816207, "isDeleted": false, "id": "K3Yy7ZVr1kepjWU614LOq", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8444.380546056995, "y": -2430.657326337161, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "width": 116.33990478515625, "height": 25, "seed": 1233143386, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Instantiate", "textAlign": "center", "verticalAlign": "middle", "containerId": "LxcNVswbjijmK-xI9iX7H", "originalText": "Instantiate", "lineHeight": 1.25}, {"type": "arrow", "version": 592, "versionNonce": 233973793, "isDeleted": false, "id": "O8wgx3Yqjx9HRic1k-9-j", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8252.45281240538, "y": -2032.684670087161, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 684.0812859760099, "height": 148.64453125, "seed": 442376070, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"type": "text", "id": "X7trCiVj9nQXV0O_PwV0Q"}], "updated": 1712619379640, "link": null, "locked": false, "startBinding": {"elementId": "BI6ih7JNuXVyEDYt_30NK", "focus": 0.6208737818215856, "gap": 15.951171875}, "endBinding": {"elementId": "yDcGdIP_v7VKjhINbmz5X", "gap": 15.3984375, "focus": -0.6386223829681281}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [321.5937797941933, 131.37109375], [684.0812859760099, -17.2734375]]}, {"type": "text", "version": 40, "versionNonce": 532457071, "isDeleted": false, "id": "X7trCiVj9nQXV0O_PwV0Q", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8480.49668070055, "y": -1913.813576337161, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 187.09982299804688, "height": 25, "seed": 276565958, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Invoke its methods", "textAlign": "center", "verticalAlign": "middle", "containerId": "O8wgx3Yqjx9HRic1k-9-j", "originalText": "Invoke its methods", "lineHeight": 1.25}, {"type": "rectangle", "version": 98, "versionNonce": 1150974977, "isDeleted": false, "id": "xGhz8shKGiO9WUzntHspA", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8841.409873449573, "y": -2133.934670087161, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 216.125, "height": 47.3125, "seed": 427068378, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false}, {"type": "text", "version": 85, "versionNonce": 1163657359, "isDeleted": false, "id": "koXQc2E_yuUSrd62p9-pX", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8907.105185949573, "y": -2122.872170087161, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 80.75991821289062, "height": 25, "seed": 545065690, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Methods", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Methods", "lineHeight": 1.25}, {"type": "arrow", "version": 181, "versionNonce": 1322079201, "isDeleted": false, "id": "4BpXnpQFDrzMBNu_tjUFt", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8226.439144459258, "y": -2216.782326337161, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 0.21271239232373773, "height": 92.22265625, "seed": 1490236058, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [{"type": "text", "id": "qOHaNATqz3NDavV7xZEi4"}], "updated": 1712619379640, "link": null, "locked": false, "startBinding": {"elementId": "BI6ih7JNuXVyEDYt_30NK", "focus": -0.0004104928500754331, "gap": 11.947265625}, "endBinding": {"elementId": "GH5TJ2rJrZhgZAaaUSYAm", "focus": -0.0012204290463037498, "gap": 12.046875}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-0.21271239232373773, -92.22265625]]}, {"type": "text", "version": 22, "versionNonce": 1313543855, "isDeleted": false, "id": "qOHaNATqz3NDavV7xZEi4", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8197.12615915514, "y": -2301.571388837161, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 57.87992858886719, "height": 25, "seed": 715438534, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "import", "textAlign": "center", "verticalAlign": "middle", "containerId": "4BpXnpQFDrzMBNu_tjUFt", "originalText": "import", "lineHeight": 1.25}, {"type": "rectangle", "version": 111, "versionNonce": 1620717505, "isDeleted": false, "id": "KjCqi6a7BG6AFMag0Dfuo", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8669.277060949573, "y": -2476.493263837161, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 213.140625, "height": 59.73828125, "seed": 1331527878, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "tosE9D5YGxoJBFTyB48f6"}], "updated": 1712619379640, "link": null, "locked": false}, {"type": "text", "version": 23, "versionNonce": 825387215, "isDeleted": false, "id": "tosE9D5YGxoJBFTyB48f6", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 8735.467414343128, "y": -2459.124123212161, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 80.75991821289062, "height": 25, "seed": 1674950854, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Methods", "textAlign": "center", "verticalAlign": "middle", "containerId": "KjCqi6a7BG6AFMag0Dfuo", "originalText": "Methods", "lineHeight": 1.25}, {"type": "freedraw", "version": 20, "versionNonce": 2133921, "isDeleted": false, "id": "VSn1KuqvGOK9s6njO4-vX", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 9119.588458202026, "y": -1728.463742023605, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 0.10986328125, "height": 0, "seed": 1071495322, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379640, "link": null, "locked": false, "points": [[0, 0], [-0.10986328125, 0], [0, 0]], "lastCommittedPoint": null, "simulatePressure": true, "pressures": []}, {"id": "1hQfhQDwb7GejFJaYy_qh", "type": "line", "x": 18598.16291408616, "y": -1542.5300474979076, "width": 3.611646075583849, "height": 773.7492732558155, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 38849967, "version": 416, "versionNonce": 147954351, "isDeleted": false, "boundElements": null, "updated": 1712619379641, "link": null, "locked": false, "points": [[0, 0], [3.611646075583849, 773.7492732558155]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"type": "line", "version": 625, "versionNonce": 1521347521, "isDeleted": false, "id": "_SPD1G_cDErsRf9STvKLk", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 19760.987492281292, "y": -1551.3551338565328, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 3.611646075583849, "height": 773.7492732558155, "seed": 53411247, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [3.611646075583849, 773.7492732558155]]}, {"id": "V0ZDIrSlUF2H1Ii-xR2wL", "type": "text", "x": 18566.483331916304, "y": -1582.4169986689617, "width": 70.4652099609375, "height": 32.563403012386985, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 832851585, "version": 156, "versionNonce": 1615359183, "isDeleted": false, "boundElements": null, "updated": 1712619379641, "link": null, "locked": false, "text": "Client", "fontSize": 26.05072240990959, "fontFamily": 1, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Client", "lineHeight": 1.25}, {"type": "text", "version": 408, "versionNonce": 2019347361, "isDeleted": false, "id": "Ju_2Qxs0tGDaSjGq3XkFK", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 19658.051936534906, "y": -1593.6059487348184, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 209.65023803710938, "height": 32.563403012386985, "seed": 403003311, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "fontSize": 26.05072240990959, "fontFamily": 1, "text": "Blocking function", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Blocking function", "lineHeight": 1.25}, {"type": "line", "version": 1136, "versionNonce": 1633680111, "isDeleted": false, "id": "mof2XMEPE0PgcUJuAo7m5", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 19171.9695142931, "y": -1552.7913934367657, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 3.3026241563566145, "height": 511.2828414449093, "seed": 252889967, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [3.3026241563566145, 511.2828414449093]]}, {"type": "text", "version": 533, "versionNonce": 1625599873, "isDeleted": false, "id": "1nRn2vqNhczVNZcK_xqO9", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 19095.136773007373, "y": -1596.2554054794023, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 180.42218017578125, "height": 32.563403012386985, "seed": 279283087, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "fontSize": 26.05072240990959, "fontFamily": 1, "text": "Cloud function", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Cloud function", "lineHeight": 1.25}, {"id": "GAp4dC4iG7OE8kGWXSoF_", "type": "arrow", "x": 18599.42468861188, "y": -1500.5952092110535, "width": 569.9661319488587, "height": 90.05509139782885, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 1747559407, "version": 71, "versionNonce": 1518671713, "isDeleted": false, "boundElements": null, "updated": 1712619379641, "link": null, "locked": false, "points": [[0, 0], [569.9661319488587, 90.05509139782885]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "s14uCVXGSWajl175XA0Hd", "type": "text", "x": 18713.865806030255, "y": -1511.2812634794425, "width": 284.83978271484375, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 257514977, "version": 154, "versionNonce": 1300450095, "isDeleted": false, "boundElements": null, "updated": 1712619379641, "link": null, "locked": false, "text": "Check if the user needs MFA", "fontSize": 20, "fontFamily": 1, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Check if the user needs MFA", "lineHeight": 1.25}, {"id": "95vYkr44w_av7QFkdupa7", "type": "arrow", "x": 19171.44715160346, "y": -1334.4863899159263, "width": 565.4261802960973, "height": 61.35801884987427, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 474443055, "version": 109, "versionNonce": 932021057, "isDeleted": false, "boundElements": null, "updated": 1712619379641, "link": null, "locked": false, "points": [[0, 0], [-565.4261802960973, 61.35801884987427]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "text", "version": 374, "versionNonce": 1953500495, "isDeleted": false, "id": "d2H2JCklKhYsIdyagzzmg", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 18693.059392685485, "y": -1379.2429371630622, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 219.45982360839844, "height": 25, "seed": 1520823023, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "\"This user needs MFA\"", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "\"This user needs MFA\"", "lineHeight": 1.25}, {"type": "text", "version": 459, "versionNonce": 1415410465, "isDeleted": false, "id": "3cgejyMHDiQuAonRV3T4Q", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 18691.711180099264, "y": -1350.8625167051741, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 410.9996337890625, "height": 25, "seed": 236529057, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "sends verification code to the given email", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "sends verification code to the given email", "lineHeight": 1.25}, {"id": "ezPVzfIAUjxyati7wwJKs", "type": "arrow", "x": 18604.876445680617, "y": -1182.0012406645033, "width": 565.4223652106775, "height": 82.87128554728406, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 1070086479, "version": 170, "versionNonce": 1086294895, "isDeleted": false, "boundElements": null, "updated": 1712619379641, "link": null, "locked": false, "points": [[0, 0], [565.4223652106775, 82.87128554728406]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "text", "version": 347, "versionNonce": 1432643329, "isDeleted": false, "id": "iQpxQd6MS5a4HrHY-E-n0", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 18699.10466211772, "y": -1195.032175690272, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 384.21966552734375, "height": 25, "seed": 265385313, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Verify email with given verification code", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Verify email with given verification code", "lineHeight": 1.25}, {"id": "8UhmE4ExSYY-I1zZyjQHy", "type": "arrow", "x": 19170.859628448397, "y": -1054.0165699962988, "width": 568.3370904734584, "height": 80.52119292703162, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 1334764847, "version": 167, "versionNonce": 1857701263, "isDeleted": false, "boundElements": null, "updated": 1712619379641, "link": null, "locked": false, "points": [[0, 0], [-568.3370904734584, 80.52119292703162]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "text", "version": 552, "versionNonce": 622976737, "isDeleted": false, "id": "e3mpAB7kiES5YOc2R5XJR", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 18775.146316857386, "y": -1056.3684427724806, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 169.55984497070312, "height": 25, "seed": 464691439, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "\"You are verified\"", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "\"You are verified\"", "lineHeight": 1.25}, {"id": "_r1EUUD5MZ_-zlpRenvUl", "type": "arrow", "x": 18607.00144826094, "y": -871.4265816760255, "width": 1154.8454328142288, "height": 2.224194801310432, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 134446177, "version": 197, "versionNonce": 496548783, "isDeleted": false, "boundElements": null, "updated": 1712619379641, "link": null, "locked": false, "points": [[0, 0], [1154.8454328142288, 2.224194801310432]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "text", "version": 720, "versionNonce": 841308865, "isDeleted": false, "id": "RFw4gmgBjGWEIVpwSW1UL", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 19135.294195825685, "y": -909.4304277253584, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 82.04836334022745, "height": 30.56035604860372, "seed": 1920600463, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "fontSize": 24.448284838882977, "fontFamily": 1, "text": "Sign up", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Sign up", "lineHeight": 1.25}, {"type": "text", "version": 327, "versionNonce": 1150679631, "isDeleted": false, "id": "KZAqjij2BklPYmOh81s8q", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 18560.541658784598, "y": -1651.42256562041, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "width": 209.44544916872786, "height": 45.39082339756317, "seed": 1915852879, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619853462, "link": null, "locked": false, "fontSize": 36.31265871805055, "fontFamily": 1, "text": "Sign up flow", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Sign up flow", "lineHeight": 1.25}, {"type": "line", "version": 565, "versionNonce": 1829657249, "isDeleted": false, "id": "RKtWh4dVqQoA28ulUjCoq", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 18622.274900792694, "y": -580.7752321668033, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 3.611646075583849, "height": 773.7492732558155, "seed": 1955158177, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [3.611646075583849, 773.7492732558155]]}, {"type": "line", "version": 774, "versionNonce": 1972558831, "isDeleted": false, "id": "izqBfLXb8D6LfrRqJ6TsW", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 19785.09947898782, "y": -589.6003185254287, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 3.611646075583849, "height": 773.7492732558155, "seed": 569875585, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [3.611646075583849, 773.7492732558155]]}, {"type": "text", "version": 305, "versionNonce": 109862529, "isDeleted": false, "id": "dqK2Gu-Z_f6k5DX59l1-_", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 18590.595318622833, "y": -620.6621833378579, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 70.4652099609375, "height": 32.563403012386985, "seed": 884106337, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "fontSize": 26.05072240990959, "fontFamily": 1, "text": "Client", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Client", "lineHeight": 1.25}, {"type": "text", "version": 557, "versionNonce": 1037010447, "isDeleted": false, "id": "irAnYUWy7wY7Nc9ut1m83", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 19682.163923241435, "y": -631.8511334037141, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 209.65023803710938, "height": 32.563403012386985, "seed": 1485385793, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "fontSize": 26.05072240990959, "fontFamily": 1, "text": "Blocking function", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Blocking function", "lineHeight": 1.25}, {"type": "line", "version": 1624, "versionNonce": 2084062543, "isDeleted": false, "id": "85v8bm7sRCOs2M-ADY0Mu", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 19195.870563499633, "y": -591.0678281056619, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 1.6698116563566145, "height": 628.2008101949093, "seed": 324763681, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619394877, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [1.6698116563566145, 628.2008101949093]]}, {"type": "text", "version": 683, "versionNonce": 1228008495, "isDeleted": false, "id": "Yn3qDTaHLndSvwCwbLRS6", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 19119.2487597139, "y": -634.5005901482984, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 180.42218017578125, "height": 32.563403012386985, "seed": 51958785, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "fontSize": 26.05072240990959, "fontFamily": 1, "text": "Cloud function", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Cloud function", "lineHeight": 1.25}, {"type": "arrow", "version": 368, "versionNonce": 206926401, "isDeleted": false, "id": "bhb3CpWasGOmu1ivS90Ob", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 18623.36870656841, "y": -538.5278938799495, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 1152.6653506988587, "height": 58.965247647828846, "seed": 1539353569, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [1152.6653506988587, 58.965247647828846]]}, {"type": "text", "version": 498, "versionNonce": 597855823, "isDeleted": false, "id": "qoYzcftNnRno0lD65f_zV", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 19415.880136486787, "y": -527.9209793983384, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 134.7598876953125, "height": 25, "seed": 627214273, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Try to sign in", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Try to sign in", "lineHeight": 1.25}, {"type": "arrow", "version": 516, "versionNonce": 1213170209, "isDeleted": false, "id": "bOhE0qgCbqz5atBjA-1RU", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 19780.574763309993, "y": -418.72766833482206, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 1152.4066490460973, "height": 79.84630009987427, "seed": 975038369, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-1152.4066490460973, 79.84630009987427]]}, {"type": "text", "version": 810, "versionNonce": 1207561327, "isDeleted": false, "id": "TX1Kyz2Q8jv-7hFrsT3gS", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 19332.257316892017, "y": -428.9373405819581, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 219.45982360839844, "height": 25, "seed": 69356417, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "\"This user needs MFA\"", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "\"This user needs MFA\"", "lineHeight": 1.25}, {"type": "arrow", "version": 579, "versionNonce": 1107330703, "isDeleted": false, "id": "_SM3YgTJfW3OmJ_AO8JnS", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 18623.71108863715, "y": -263.39876908339943, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 563.0473652106775, "height": 22.93378554728406, "seed": 1695817537, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [563.0473652106775, 22.93378554728406]]}, {"type": "arrow", "version": 428, "versionNonce": 275700911, "isDeleted": false, "id": "CKAFA899FNArAeEnpzxx9", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 19194.573177654926, "y": -182.30081716519476, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 569.5636529734584, "height": 60.14228667703162, "seed": 1324614401, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-569.5636529734584, 60.14228667703162]]}, {"type": "text", "version": 490, "versionNonce": 328785615, "isDeleted": false, "id": "CVTRi54boVXmle4A0cUkZ", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 18585.476148018086, "y": -690.2898372726847, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 195.81976318359375, "height": 45.39082339756319, "seed": 493746817, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619858679, "link": null, "locked": false, "fontSize": 36.31265871805055, "fontFamily": 1, "text": "Sign in flow", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Sign in flow", "lineHeight": 1.25}, {"type": "text", "version": 947, "versionNonce": 1448414593, "isDeleted": false, "id": "n9TFFd-rSVQAHmelGjgJE", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 18763.980155505942, "y": -285.4818285420073, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 254.41976928710938, "height": 25, "seed": 1616995631, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Request verification email", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Request verification email", "lineHeight": 1.25}, {"type": "text", "version": 1301, "versionNonce": 795690767, "isDeleted": false, "id": "R6IRRd_wwn6EzBMPngoSU", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 18704.215026416587, "y": -197.9427660420073, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 372.5196533203125, "height": 25, "seed": 1020909249, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Sends the email with verification code", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Sends the email with verification code", "lineHeight": 1.25}, {"type": "arrow", "version": 674, "versionNonce": 1043969377, "isDeleted": false, "id": "SCMwCZrdbv4o2NOcinLJ_", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 18627.272322204797, "y": -66.34078533634539, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 563.0473652106775, "height": 22.93378554728406, "seed": 1952881679, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [563.0473652106775, 22.93378554728406]]}, {"type": "text", "version": 488, "versionNonce": 922640705, "isDeleted": false, "id": "AnwobTpPgcQNVZhRLmP1b", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 18713.90492204647, "y": -93.47010979200718, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 384.21966552734375, "height": 25, "seed": 706554433, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619379641, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Verify email with given verification code", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Verify email with given verification code", "lineHeight": 1.25}, {"type": "arrow", "version": 392, "versionNonce": 1893267457, "isDeleted": false, "id": "hhgw0dM2QwjuiheVstBA-", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 19192.93330004687, "y": 17.163980132567758, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 563.1613092234584, "height": 29.97041167703162, "seed": 971437249, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619398794, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-563.1613092234584, 29.97041167703162]]}, {"type": "text", "version": 753, "versionNonce": 1960075087, "isDeleted": false, "id": "no-F2UAGpKYwPkONuMnDA", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 18801.333269705858, "y": -2.6214863936140773, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 169.55984497070312, "height": 25, "seed": 2106373281, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619414844, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "\"You are verified\"", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "\"You are verified\"", "lineHeight": 1.25}, {"id": "wAs1ZvSj1Al61pIMTOWL7", "type": "arrow", "x": 18625.60850481014, "y": 143.4127027079926, "width": 1158.6796875, "height": 4.21484375, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 1709031151, "version": 113, "versionNonce": 2121636257, "isDeleted": false, "boundElements": null, "updated": 1712619413460, "link": null, "locked": false, "points": [[0, 0], [1158.6796875, 4.21484375]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "text", "version": 896, "versionNonce": 1345176161, "isDeleted": false, "id": "dNE8oiRHcCnEHEsg-iepg", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 19152.863738574793, "y": 102.2330152079926, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 80.48323059082033, "height": 33.74844807848379, "seed": 464656271, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619423724, "link": null, "locked": false, "fontSize": 26.998758462787034, "fontFamily": 1, "text": "Sign in", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Sign in", "lineHeight": 1.25}, {"type": "line", "version": 624, "versionNonce": 1911883521, "isDeleted": false, "id": "NpE6iNgMhZibI2AQTOAvD", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20180.80663308476, "y": -1546.2526243370696, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 3.697583575583849, "height": 478.5539607558155, "seed": 1179227695, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619519045, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [3.697583575583849, 478.5539607558155]]}, {"type": "line", "version": 851, "versionNonce": 526810991, "isDeleted": false, "id": "6usCT7aRhAC1s9h5FEc87", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 21343.959336279902, "y": -1555.1441169456946, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 3.244458575583849, "height": 479.78052325581575, "seed": 875886671, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619525277, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [3.244458575583849, 479.78052325581575]]}, {"type": "text", "version": 225, "versionNonce": **********, "isDeleted": false, "id": "HMY_OFF7WiNhs94VQ9C45", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20149.513769664914, "y": -1586.2333255081237, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 70.4652099609375, "height": 32.563403012386985, "seed": 783226479, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619455857, "link": null, "locked": false, "fontSize": 26.05072240990959, "fontFamily": 1, "text": "Client", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Client", "lineHeight": 1.25}, {"type": "text", "version": 477, "versionNonce": 115625327, "isDeleted": false, "id": "quDQ7yjmbwqqGat2W1gVi", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 21241.082374283516, "y": -1597.4222755739804, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 209.65023803710938, "height": 32.563403012386985, "seed": 746614927, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619455857, "link": null, "locked": false, "fontSize": 26.05072240990959, "fontFamily": 1, "text": "Blocking function", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Blocking function", "lineHeight": 1.25}, {"type": "line", "version": 1314, "versionNonce": 1025827137, "isDeleted": false, "id": "1NDvugiiHvGuu356_ogxx", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20754.097608291708, "y": -1556.8303765259277, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 3.5291866563566145, "height": 472.3570601949093, "seed": 929138351, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619533176, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [3.5291866563566145, 472.3570601949093]]}, {"type": "text", "version": 602, "versionNonce": 250936751, "isDeleted": false, "id": "LJHXQJrNGRXrTV9cTdyBK", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20678.167210755983, "y": -1600.0717323185643, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 180.42218017578125, "height": 32.563403012386985, "seed": 234543311, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619455857, "link": null, "locked": false, "fontSize": 26.05072240990959, "fontFamily": 1, "text": "Cloud function", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Cloud function", "lineHeight": 1.25}, {"type": "arrow", "version": 140, "versionNonce": 839348175, "isDeleted": false, "id": "wlqmy8FeM5nSbpILNBETZ", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20182.45512636049, "y": -1504.4115360502153, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 569.9661319488587, "height": 90.05509139782885, "seed": 98887407, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619455857, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [569.9661319488587, 90.05509139782885]]}, {"type": "text", "version": 223, "versionNonce": 1246141935, "isDeleted": false, "id": "SV_EoqBI4Sz4qaYqCo-rY", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20296.89624377886, "y": -1515.0975903186043, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 284.83978271484375, "height": 25, "seed": 765997327, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619455857, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Check if the user needs MFA", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Check if the user needs MFA", "lineHeight": 1.25}, {"type": "arrow", "version": 178, "versionNonce": 180029455, "isDeleted": false, "id": "c6LyYJ9UHPAtU1yPc2TYZ", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20754.477589352067, "y": -1338.3027167550883, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 565.4261802960973, "height": 61.35801884987427, "seed": 784152367, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619455857, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-565.4261802960973, 61.35801884987427]]}, {"type": "text", "version": 519, "versionNonce": 1777636879, "isDeleted": false, "id": "R4QBNaoDowyY5pZGaf_9j", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20304.87498668409, "y": -1353.356139002224, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 288.81976318359375, "height": 25, "seed": 1871477071, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619484494, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "\"This user doesn't need MFA\"", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "\"This user doesn't need MFA\"", "lineHeight": 1.25}, {"type": "arrow", "version": 442, "versionNonce": 759851247, "isDeleted": false, "id": "OQOQGqDoMkPTzMUU1Icxi", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20189.496727179223, "y": -1208.7160050036653, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 1155.1958027106775, "height": 50.52362929728406, "seed": 111915407, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619493628, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [1155.1958027106775, 50.52362929728406]]}, {"type": "text", "version": 634, "versionNonce": 905071759, "isDeleted": false, "id": "m8fJY3aDNNCAqa9tpA8XX", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20966.924162366322, "y": -1232.9149087794337, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 129.20587310790995, "height": 48.1249999999999, "seed": 209495983, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619505776, "link": null, "locked": false, "fontSize": 38.49999999999992, "fontFamily": 1, "text": "Sign up", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Sign up", "lineHeight": 1.25}, {"type": "text", "version": 399, "versionNonce": 501687535, "isDeleted": false, "id": "NogitVfnVBT-zvWepqFMS", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20143.94135289064, "y": -1665.663806823903, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "width": 389.0615234375, "height": 45.39082339756319, "seed": 714275407, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619850294, "link": null, "locked": false, "fontSize": 36.31265871805055, "fontFamily": 1, "text": "Sign up flow (No MFA)", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Sign up flow (No MFA)", "lineHeight": 1.25}, {"type": "line", "version": 897, "versionNonce": 16428399, "isDeleted": false, "id": "_dNOlrljHKKK8k6_95eaD", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20190.509180081124, "y": -589.8916799956447, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 1.5960210755838489, "height": 405.1750545058155, "seed": 2039446337, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619842062, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [1.5960210755838489, 405.1750545058155]]}, {"type": "line", "version": 1054, "versionNonce": 2134976399, "isDeleted": false, "id": "DIdpgQitiSUlMwjRyK3_u", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 21353.72828952625, "y": -598.8222351042701, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 1.8577398255838489, "height": 422.3430232558155, "seed": 1935698721, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619842062, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [1.8577398255838489, 422.3430232558155]]}, {"type": "text", "version": 464, "versionNonce": 2129878447, "isDeleted": false, "id": "MfrHW2PlSrXtXo_6Q69XC", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20159.313972911263, "y": -629.8723811666993, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 70.4652099609375, "height": 32.563403012386985, "seed": 155737857, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619842062, "link": null, "locked": false, "fontSize": 26.05072240990959, "fontFamily": 1, "text": "Client", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Client", "lineHeight": 1.25}, {"type": "text", "version": 716, "versionNonce": 1396297679, "isDeleted": false, "id": "AC4FoQoG-2Bb8sypKQqzQ", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 21250.882577529865, "y": -641.0613312325555, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 209.65023803710938, "height": 32.563403012386985, "seed": 700896993, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619842062, "link": null, "locked": false, "fontSize": 26.05072240990959, "fontFamily": 1, "text": "Blocking function", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Blocking function", "lineHeight": 1.25}, {"type": "line", "version": 1978, "versionNonce": 1445789167, "isDeleted": false, "id": "qQA0lPBbME-SjR_UYX30N", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 0, "opacity": 100, "angle": 0, "x": 20763.807967788063, "y": -600.1920884345033, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 0.7205929063566145, "height": 281.7672164449093, "seed": 1198215873, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619842062, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "points": [[0, 0], [0.7205929063566145, 281.7672164449093]]}, {"type": "text", "version": 842, "versionNonce": 181757967, "isDeleted": false, "id": "ybHq7axlpur5c7Ffp7cE9", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20687.96741400233, "y": -643.7107879771398, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 180.42218017578125, "height": 32.563403012386985, "seed": 1456820897, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619842062, "link": null, "locked": false, "fontSize": 26.05072240990959, "fontFamily": 1, "text": "Cloud function", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Cloud function", "lineHeight": 1.25}, {"type": "arrow", "version": 528, "versionNonce": 2133560879, "isDeleted": false, "id": "DMXzlQG5LGf4SquCSmr5i", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20192.08736085684, "y": -547.7068417087909, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 1152.6653506988587, "height": 58.965247647828846, "seed": 213145217, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712619842062, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [1152.6653506988587, 58.965247647828846]]}, {"type": "text", "version": 659, "versionNonce": 1847062607, "isDeleted": false, "id": "OzCAsGRULwoqIibVPw8VE", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20984.532384525217, "y": -537.1311772271798, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 134.7598876953125, "height": 25, "seed": 1539825249, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619842062, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Try to sign in", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Try to sign in", "lineHeight": 1.25}, {"type": "arrow", "version": 678, "versionNonce": 209203759, "isDeleted": false, "id": "qlGw8JUCjM5Cawl3TfL75", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 21348.550679922366, "y": -428.14693306507183, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 1152.4066490460973, "height": 79.84630009987427, "seed": 853502529, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1712706194118, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-1152.4066490460973, 79.84630009987427]]}, {"type": "text", "version": 1150, "versionNonce": 451098849, "isDeleted": false, "id": "4lC-nrA5Q1m9LCeqOuNyH", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20822.729107183968, "y": -463.47192221361627, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 335.031005859375, "height": 58.00506161971829, "seed": 2061138465, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712704394044, "link": null, "locked": false, "fontSize": 23.202024647887317, "fontFamily": 1, "text": "\"This user doesn't need MFA\"\nYou are signed in", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "\"This user doesn't need MFA\"\nYou are signed in", "lineHeight": 1.25}, {"type": "text", "version": 650, "versionNonce": 1320385569, "isDeleted": false, "id": "c9bHKgVGBqT49DJQZf3i0", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 20150.195721837063, "y": -709.7994791716073, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 375.4453125, "height": 45.39082339756319, "seed": 231457217, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1712619846146, "link": null, "locked": false, "fontSize": 36.31265871805055, "fontFamily": 1, "text": "Sign in flow (No MFA)", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Sign in flow (No MFA)", "lineHeight": 1.25}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}