# MFA Flow

MFA (Multi Factor Authentication) is a requirement for some users tied to specific company code. When they sign up to the app, the app rolls them into the MFA flow to verify their email.

## Terminology

- **Cloud function:** A serverless execution environment that contains a single API to execute. Used to send verification code via email and validate it.
- **Blocking function:** A custom function that gets executed when firebase auth API gets hit. Used to determine whether the user is required for MFA or not.

## Data store

The information of the MFA session is stored in firestore as `MFASessions` collection. The cloud / blocking function referees to it to look up its "session" that contains registered device information.

https://console.cloud.google.com/firestore/databases?hl=en&project=vivante-dev

## API (cloud function) references

All cloud functions consumed in MFA flow are defined in [mfa-auth-flow-functions](https://github.com/vivantehealth/mfa-auth-flow-functions) repo.

### /sendEmailVerificationCode

This function takes care of the followings;

1. Determine whether if the account requires MFA or not.
2. Sends verification email to the given email

It also changes its internal behavior to do the check of MFA requirement based on given body. When `registrationCode` is given, it does the check of MFA requirement based on given code and thus it is used for registration. On the other hand, when it's not given the function looks up the user in the function and determines the need for requirement.

For sign in, if the request contains `deviceToken`, it checks the firestore's `MFASession` collection to determine whether if the user + device can skip the MFA or not.

### /checkEmailVerificationCode

This function takes care of verification of the given code. When `isTrustedDevice` flag `true` in its body, its response contains `deviceToken` which is used for its trusted device manipulation.

`deviceToken` is stored in the local storage and is used upon `/sendEmailVerificationCode` request for sign in to skip MFA flow for the user with trusted device.

## Trusted device

Trusted device is a way for a user to bypass MFA flow upon sign in.

### 1. User logs into the app with `isTrustedDevice` checked

If the user checks on "trust this device" check box and proceed with MFA verification, the [/checkEmailVerificationCode](#checkemailverificationcode) returns a response that contains `deviceToken` and the app stores it in local storage. Then proceed with sign in.

### 2. User comes back and signs in

When [/sendEmailVerificationCode](#sendemailverificationcode) gets hit to check MFA requirement, it also contains `registrationCode` grabbed from local storage stored above and it does the check. If the given `registrationCode` is what's stored in `MFASession` collection in the firestore, it returns the response that indicates the user doesn't need MFA

## Sign up flow

### 1. Check if the user is required for MFA

When a user tires to sign up to the app, it first sends a request to cloud function to check if MFA is required for the user. If so, the function responses with a code `MFA_CODE_SENT` that indicates that MFA is required and the cloud function sent a verification email to the user.

When the app receive the code, the app navigates to email verification screen (`EmailVerificationScreen.tsx`) which let the user to verify their email with given verification code.

### 2. User validates their code given by the email

Once the user verifies their email, the app makes another request to a different cloud function to validate given verification number. If the code is correct, the app proceeds with regular sign up flow with firebase authentication which is the same API invoked by non-MFA sign up flow.

![](./signUpFlow.png)

## Sign up Flow (no MFA)

### 1. Check if the user is required for MFA

Same as the flow when the user is required for MFA, when they try to sign up the app makes a request to cloud function to check if MFA is required and the endpoint returns `MFA_NOT_REQUIRED` code if not required. The app skips the verification flow and directly hits firebase authentication API for sign up.

![](./signUpFlowNoMFA.png)

## Sign In Flow

### 1. Try to log into the app

When the user fills in their credentials and try to log in, the app hits firebase authentication API to sign them in. When it receives a request from the app, it executes blocking function which determines whether if the user feed MFA or not. When it requires, it returns an error which contains in its message `MFA_REQUIRED`.

### 2. Request verification email

When the app receives a response that suggests MFA is required for the user, it makes a request to cloud function to send verification email. When the user receives a successful response from it, the app navigates the user to email verification screen (`EmailVerificationScreen.tsx`).

### 3. User validates their code given by the email

Once the user verifies their email, the app makes another request to a different cloud function to validate given verification number. If the code is correct, the app proceeds with regular sign in flow with firebase authentication which is the same API invoked by non-MFA sign in flow.

![](./signInFlow.png)

## Sign In Flow (No MFA)

The user simply hits firebase authentication API and it lets the user sign into the app.

![](./signInFlowNoMFA.png)

## Recourse

Excalidraw file for the diagrams above. Open this in https://excalidraw.com so that you can expand the diagrams: [link](./MFAFlow.excalidraw)
