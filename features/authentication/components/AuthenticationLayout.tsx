import { Box } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { SPACING_80_PX } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";

type AuthenticationLayoutProps = Readonly<{
  children: React.ReactNode;
}>;

export function AuthenticationLayout({ children }: AuthenticationLayoutProps) {
  return (
    <Box sx={styles.body}>
      <AppIcon name="CompanyLogo" containerStyles={{ width: "170px", height: "42px" }} />
      {children}
    </Box>
  );
}

const styles = {
  body: {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    height: "100vh",
    paddingTop: SPACING_80_PX,
    backgroundColor: color.background.page,
  },
} as const satisfies Record<string, React.CSSProperties>;
