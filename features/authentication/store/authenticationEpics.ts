import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import type { Action, PayloadAction } from "@reduxjs/toolkit";
import { onAuthStateChanged } from "firebase/auth";
import { Epic, ofType } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { catchError, ignoreElements, map, mergeMap, switchMap } from "rxjs/operators";

import { createSimpleAnalyticsEpic } from "@Features/analytics/store/analyticsEpics";
import { loadMemberAccount } from "@Features/member/store/memberStateSlice";
import { loadMemberPreferences } from "@Features/memberPreferences/store/memberPreferencesStateSlice";
import { createNavigateToEpic } from "@Features/navigation/store/navigationEpics";
import { mapSSOCodeToAccessCode } from "@Features/sso/hooks/useOidcSSO";
import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { removeServerEventsSubscriptions, signOut, addServerEventsSubscriptions, resetAppState } from "@Store/actions";
import { store } from "@Store/store";
import { Routes } from "@Types";
import { getFirebaseAuth } from "@Utils/getFirebaseAuth";

import { authenticationStateSlice } from "./authenticationStateSlice";

export const {
  signInSuccess,
  signOutSuccess,
  signOutFailure,
  initializeFirebaseAuth,
  signInWithAuthToken,
  resetUserPassword,
  resetUserPasswordSuccess,
  resetUserPasswordFail,
  resetPasswordResetStatus,
} = authenticationStateSlice.actions;

const signOutEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(signOut.type),
    mergeMap(() =>
      from(vivanteCoreContainer.getAuthenticationUseCaseFactory().createSignOutUseCase().execute()).pipe(
        map(() => signOutSuccess()),
        catchError((error) => of(signOutFailure(error))),
      ),
    ),
  );
};

const resetAppEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(signOutSuccess.type),
    switchMap(() => [removeServerEventsSubscriptions(), resetAppState()]),
  );
};

const firebaseAuthEpic: Epic = (actions$: Observable<Action>) => {
  return actions$.pipe(
    ofType(initializeFirebaseAuth.type),
    switchMap(() => {
      return from(getFirebaseAuth()).pipe(
        map((firebase) => {
          const unsubscribe = onAuthStateChanged(firebase, async () => {
            const currentUser = firebase.currentUser;
            const [isAuthenticated, token] = await Promise.all([
              vivanteCoreContainer.getAuthenticationUseCaseFactory().createIsLoggedInUseCase().execute(),
              currentUser?.getIdTokenResult(),
            ]);
            const providerDetails = currentUser?.providerData[0];
            const ssoCode = mapSSOCodeToAccessCode(providerDetails?.providerId ?? "");
            /**
             * We determine if we should load a member by checking if the user is authenticated and either
             * logged in via username/password or SSO. As we have different SSO scenarios, we account for that by
             * checking the ssoCode. If the code is defined, that means it should also check if the user has a
             * cylinder_person_id claim in the token. This indicates that the account has been linked to a traditional
             * account and the user will have access to the member resources
             */
            const shouldLoadMember =
              isAuthenticated &&
              (providerDetails?.providerId === "password" ||
                providerDetails?.providerId === "custom" ||
                ssoCode === undefined ||
                (ssoCode && !!token?.claims?.cylinder_person_id));

            store.dispatch(signInWithAuthToken(isAuthenticated));

            if (shouldLoadMember) {
              store.dispatch(loadMemberAccount());
              store.dispatch(addServerEventsSubscriptions());
              store.dispatch(loadMemberPreferences());
            }

            unsubscribe && unsubscribe();
          });
        }),
      );
    }),
    ignoreElements(),
  );
};

const resetPasswordEpic: Epic = (actions$: Observable<PayloadAction<{ email: string }>>) => {
  return actions$.pipe(
    ofType(resetUserPassword.type),
    mergeMap((action: PayloadAction<{ email: string }>) =>
      from(
        vivanteCoreContainer
          .getAuthenticationUseCaseFactory()
          .createResetPasswordForEmailUseCase()
          .execute(action.payload.email),
      ).pipe(
        map(() => resetUserPasswordSuccess()),
        catchError((error) =>
          of(
            resetUserPasswordFail({
              type: error.type,
              message: error.message,
              discriminator: error.discriminator,
            }),
          ),
        ),
      ),
    ),
  );
};

const analyticsEpics: Epic[] = [
  createSimpleAnalyticsEpic(signInSuccess.type, ClickStreamActivityEventType.LOGIN_SUCCESS),
  createSimpleAnalyticsEpic(signOutSuccess.type, ClickStreamActivityEventType.LOGOUT),
];

const navigateToHomeOnLogin = createNavigateToEpic(signInSuccess.type, () => ({
  path: Routes.HOME,
  screenName: "Home",
}));

export const authenticationEpics = [
  firebaseAuthEpic,
  navigateToHomeOnLogin,
  signOutEpic,
  resetAppEpic,
  resetPasswordEpic,
  ...analyticsEpics,
];
