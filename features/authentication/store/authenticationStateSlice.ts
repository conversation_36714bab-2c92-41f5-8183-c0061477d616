// Expect this error as the action is actually used in epic and not in the reducer
/* eslint-disable @typescript-eslint/no-unused-vars */
import { AuthenticationError } from "@vivantehealth/vivante-core";
import { PayloadAction, createSlice } from "@reduxjs/toolkit";

import { LoadState, AuthError } from "@Types";
import { buildSliceStateSelector } from "@Utils/slice.util";
/// //////////////////////////////////////////////////////
/// state

export type AuthenticationState = Readonly<{
  /**
   * Credentials to hold while user is going through MFA verification.
   * This will be given to login API after MFA is complete.
   *  */
  mfaCredentials: {
    email: string;
    password: string;
    mobilePhone?: string;
  };
  isAuthenticated: boolean;
  passwordResetEmailSent: boolean;
  resetPasswordError: AuthenticationError | null;
  signOutError: AuthenticationError | null;
  firebaseLoaded: boolean;
  loadState: LoadState | "firebaseAuthLoading";
  isCurrentlyRegistering: boolean;
}>;

export const initialState: AuthenticationState = {
  mfaCredentials: {
    email: "",
    password: "",
  },
  isAuthenticated: false,
  passwordResetEmailSent: false,
  resetPasswordError: null,
  signOutError: null,
  firebaseLoaded: false,
  loadState: null,
  isCurrentlyRegistering: false,
};

type AuthCredentialsPayload = {
  email: string;
  password: string;
};

export const authenticationStateSlice = createSlice({
  name: "authenticationState",
  initialState: initialState as AuthenticationState,
  reducers: {
    storeMFACredentials: (state, action: PayloadAction<AuthCredentialsPayload>) => ({
      ...state,
      mfaCredentials: { ...state.mfaCredentials, ...action.payload },
    }),
    storeMFAMobilePhone: (state, action: PayloadAction<string>) => ({
      ...state,
      mfaCredentials: { ...state.mfaCredentials, mobilePhone: action.payload },
    }),
    signInSuccess: (state) => ({
      ...state,
      isAuthenticated: true,
      loadState: "loaded",
      signInError: null,
    }),
    signOutSuccess: (state) => ({
      ...state,
      isAuthenticated: false,
      loadState: "loaded",
      signOutError: null,
    }),
    signOutFailure: (state, action) => ({
      ...state,
      isAuthenticated: false,
      loadState: "loaded",
      signOutError: action.payload,
    }),
    signInWithAuthToken: (state, action) => ({
      ...state,
      isAuthenticated: action.payload,
      loadState: "loaded",
      signInError: null,
      firebaseLoaded: true,
    }),
    initializeFirebaseAuth: (state) => ({
      ...state,
      loadState: "firebaseAuthLoading",
    }),
    resetUserPassword: (state, _: PayloadAction<{ email: string }>) => ({
      ...state,
      loadState: "loading",
    }),
    resetUserPasswordSuccess: (state) => ({
      ...state,
      passwordResetEmailSent: true,
      loadState: "loaded",
      resetPasswordError: null,
    }),
    resetUserPasswordFail: (state, action: PayloadAction<AuthError>) => ({
      ...state,
      passwordResetEmailSent: false,
      loadState: "loaded",
      resetPasswordError: action.payload,
    }),
    resetPasswordResetStatus: (state) => ({
      ...state,
      passwordResetEmailSent: false,
      resetPasswordError: null,
    }),
    setCurrentlyRegistering: (state, action: PayloadAction<boolean>) => ({
      ...state,
      isCurrentlyRegistering: action.payload,
    }),
  },
});

/// //////////////////////////////////////////////////////
/// selectors

export const authenticationStateSelector = buildSliceStateSelector("authenticationState");

export const authenticationStateReducer = authenticationStateSlice.reducer;
