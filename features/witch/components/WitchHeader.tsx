import { Box, Paper, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

type WitchHeaderProps = Readonly<{
  showTitle: boolean;
  title: string;
  description: string;
  showDescription: boolean;
  isOutcome: boolean;
  isContextualEnumProperty: boolean | undefined;
  isInformationalNode?: boolean;
}>;

export const WitchHeader = ({
  showTitle,
  title,
  description,
  showDescription,
  isOutcome,
  isContextualEnumProperty,
  isInformationalNode,
}: WitchHeaderProps) => {
  /**
   * InformationalNodes only have a title. No description or actual form input. In this case,
   * we render a Paper component with the title and no form input.
   */
  return isInformationalNode ? (
    <Paper sx={{ px: 5 }}>
      <Typography variant="body">{title}</Typography>
    </Paper>
  ) : (
    <Box
      display="flex"
      flexDirection="column"
      alignItems={isOutcome || isContextualEnumProperty ? "center" : "initial"}
    >
      {showTitle ? (
        <Typography variant="h1Serif" color={color.text.default}>
          {title}
        </Typography>
      ) : null}

      {showDescription ? (
        <Typography variant="body" mt={6}>
          {description}
        </Typography>
      ) : null}
    </Box>
  );
};
