import { ChangeEvent, useEffect, useState } from "react";
import { Box } from "@mui/material";

import { EnumField, EnumFieldItem } from "./WitchEnumField";

export type EnumFieldGroupValue = string | number | string[] | number[];

type EnumFieldGroupProps = Readonly<{
  enumItems: EnumFieldItem[];
  value: EnumFieldGroupValue | undefined;
  customResponses?: Record<string | number, string>;
  setValue: (value: EnumFieldGroupValue) => void;
  setCustomResponses?: (value: Record<string | number, string> | undefined) => void;
  variant: "single_choice" | "multiple_choice";
  contextual?: boolean;
}>;

type EnumFieldItemWithState = EnumFieldItem &
  Readonly<{
    selected: boolean;
    customResponse?: string;
  }>;

export const EnumFieldGroup = (args: EnumFieldGroupProps) => {
  useEffect(() => {
    if (args.contextual === true) {
      args.setValue(args.enumItems[0].value);
    }
  }, [args]);

  if (args.contextual) {
    return null;
  }

  return <EnumFieldGroupImpl {...args} />;
};

const EnumFieldGroupImpl = ({
  enumItems,
  value,
  customResponses,
  setValue,
  setCustomResponses,
  variant,
}: EnumFieldGroupProps) => {
  const [localValue, setLocalValue] = useState<EnumFieldGroupValue | undefined>(value);
  const [localEnums, setLocalEnums] = useState<EnumFieldItemWithState[]>(
    enumItems.map((item) => {
      const isArray: (string | number)[] = Array.isArray(value) ? value : [];

      return {
        ...item,
        selected: item.value === value || isArray.includes(item.value),
        customResponse: customResponses?.[item.value],
      };
    }),
  );

  const handleCustomResponse = (
    enumItemKey: string | number,
    event?: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    setLocalEnums((prev) =>
      prev.map((item) => {
        if (item.value === enumItemKey) {
          return {
            ...item,
            customResponse: event?.target?.value,
          };
        }

        return item;
      }),
    );
  };

  const handleSingleChoiceAnswer = (incomingValue: string | number) => {
    setLocalEnums((prev) =>
      prev.map((item) => ({
        ...item,
        selected: item.value === incomingValue,
      })),
    );
  };

  const handleMultipleChoiceAnswer = (incomingValue: string | number) => {
    setLocalEnums((prev) => {
      const incomingIsExclusive = prev.filter((i) => i.value === incomingValue)[0].isExclusive === true;

      return prev.map((item) => {
        if (item.isExclusive === true || incomingIsExclusive) {
          return {
            ...item,
            selected: item.value === incomingValue,
          };
        }

        return {
          ...item,
          selected: item.value === incomingValue ? !item.selected : item.selected,
        };
      });
    });
  };

  const handleSetValue = variant === "multiple_choice" ? handleMultipleChoiceAnswer : handleSingleChoiceAnswer;

  useEffect(() => {
    const selectedEnums = localEnums.filter((item) => item.selected);

    if (selectedEnums.length === 0) {
      setLocalValue(undefined);
    } else if (selectedEnums.length === 1 && variant === "single_choice") {
      setLocalValue(selectedEnums[0].value);
    } else {
      setLocalValue(selectedEnums.map((item) => item.value.toString()));
    }

    const customResponses = selectedEnums.reduce<Record<string | number, string>>((acc, item) => {
      if (item.allowsCustomResponse && item.customResponse) {
        acc[item.value] = item.customResponse;
      }

      return acc;
    }, {});

    setCustomResponses?.(Object.keys(customResponses).length > 0 ? customResponses : undefined);
  }, [localEnums, setCustomResponses, variant]);

  useEffect(() => {
    if (localValue === undefined) return;

    setValue(localValue);
  }, [localValue, setValue]);

  return (
    <Box display="flex" flexDirection="column" gap={3}>
      {localEnums.map((item) => {
        return (
          <EnumField
            key={item.value}
            variant={variant === "multiple_choice" ? "checkbox" : "radio"}
            setValue={handleSetValue}
            customResponse={item.customResponse}
            setCustomResponse={(value) => handleCustomResponse(item.value, value)}
            enumItem={item}
            selected={item.selected}
          />
        );
      })}
    </Box>
  );
};
