import { OutcomeScreenAction } from "@vivantehealth/vivante-core";
import { Box, Button } from "@mui/material";

import { appStrings } from "@Assets/app_strings";

import { CLASSIC_SURVEY_TITLES } from "../../survey/assets/constants";
import { NextPageMeta } from "../utils/witchScreen.utils";
import { AnswerAction } from "../WitchScreenContainer";

type WitchPaginatorProps = Readonly<{
  nextPageMeta: ReadonlyArray<NextPageMeta>;
  isOutcome: boolean;
  isContextualEnumProperty: boolean;
  handleAnswer: AnswerAction;
  handleOutcomeAction?: (outcome: OutcomeScreenAction) => void;
  primaryAction?: OutcomeScreenAction;
  secondaryAction?: OutcomeScreenAction;
  contextualButtonText?: string;
  surveyId?: string;
  isInformationalNode?: boolean;
}>;

export const WitchPaginator = ({
  nextPageMeta,
  isOutcome,
  isContextualEnumProperty,
  handleAnswer,
  handleOutcomeAction,
  primaryAction,
  secondaryAction,
  contextualButtonText,
  surveyId,
  isInformationalNode,
}: WitchPaginatorProps) => {
  const isContextScreen = surveyId ? CLASSIC_SURVEY_TITLES.includes(surveyId) : false;

  if (isOutcome && !!handleOutcomeAction) {
    return (
      <Box display="flex" justifyContent="center" gap={3} mt={6}>
        {secondaryAction && (
          <Button
            variant={isContextScreen ? "intakeSecondary" : "secondary"}
            onClick={() => handleOutcomeAction(secondaryAction)}
            fullWidth
            sx={{ textTransform: "capitalize", maxWidth: "256px" }}
          >
            {secondaryAction.title}
          </Button>
        )}
        {primaryAction && (
          <Button
            variant={isContextScreen ? "intakePrimary" : "primary"}
            onClick={() => handleOutcomeAction(primaryAction)}
            fullWidth
            sx={{ textTransform: "capitalize", maxWidth: "256px" }}
          >
            {primaryAction.title}
          </Button>
        )}
      </Box>
    );
  }

  if (!isOutcome && !isContextualEnumProperty) {
    return (
      <Box
        display="flex"
        /**
         * If this is an informational node, that is, only text and no form input, and we have two buttons
         * we should space them out evenly. Otherwise, we should align them to the right
         */
        justifyContent={isInformationalNode && nextPageMeta.length === 2 ? "space-between" : "flex-end"}
        gap={2}
      >
        {nextPageMeta.map((next) => {
          const isSecondary = next.label === "back";

          return (
            <Button
              key={next.label}
              variant={isSecondary ? "secondary" : "primary"}
              disabled={!next.enabled}
              onClick={() => {
                next?.callback?.();
              }}
              fullWidth
              sx={{ textTransform: "capitalize", maxWidth: "256px" }}
            >
              {next.label}
            </Button>
          );
        })}
      </Box>
    );
  }

  if (isContextualEnumProperty) {
    return (
      <Box display="flex" justifyContent="center">
        <Button
          variant="intakePrimary"
          onClick={() => handleAnswer(() => undefined)}
          fullWidth
          sx={{ textTransform: "capitalize", maxWidth: "256px" }}
        >
          {contextualButtonText || appStrings.buttonText.continue}
        </Button>
      </Box>
    );
  }

  return null;
};
