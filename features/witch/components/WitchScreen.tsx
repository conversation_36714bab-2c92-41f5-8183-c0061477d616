import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import {
  EnumPropertyScreen,
  EnumPropertyScreenDisplay,
  ImperialHeight,
  ImperialWeight,
  IntegerPropertyScreen,
  OutcomeScreenAction,
  PropertyAnswer,
  StringPropertyScreen,
  StringPropertyScreenDisplay,
  SurveyAnswer,
  WizardScreen,
} from "@vivantehealth/vivante-core";
import { Box, TextField } from "@mui/material";

import { NumberStepper } from "@Components/form/NumberStepper";
import { memberStateSelector } from "@Features/member/store/memberStateSlice";
import { DateField } from "@Features/survey/components/DateField";
import { HeightInputForm } from "@Features/survey/components/HeightInputForm";
import { NumberInputForm } from "@Features/survey/components/NumberInputForm";
import { SurveyNavigationWrapper } from "@Features/survey/components/SurveyNavigationWrapper";
import { SurveyAnswerWithCustomValues } from "@Features/survey/SurveyContainer";

import { EnumFieldGroup } from "./WitchEnumFieldGroup";
import { WitchHeader } from "./WitchHeader";
import { WitchPaginator } from "./WitchPaginator";
import { CLASSIC_SURVEY_TITLES } from "../../survey/assets/constants";
import { parseImperial } from "../utils/witch.util";
import { SurveyPageFieldType, calculateFieldType, computeNextPageMeta } from "../utils/witchScreen.utils";
import { AnswerAction, PreviousPageAction } from "../WitchScreenContainer";

const isImperialWeight = (answer: SurveyAnswer): answer is ImperialWeight => {
  return typeof answer === "object" && "pounds" in answer;
};

type WitchScreenProps = Readonly<{
  loading: boolean;
  currentPage: WizardScreen | null;
  answer?: PropertyAnswer;
  handleUpdateAnswer: (value: PropertyAnswer | string) => {
    payload: PropertyAnswer;
    type: string;
  };
  handleNextPage?: () => void;
  handleAnswer: AnswerAction;
  handleSkip: () => void;
  handleOutcomeAction?: (outcome: OutcomeScreenAction) => void;
  handleAnswerAndClose: () => void;
  handleSkipAndClose: () => void;
  handlePreviousPage: PreviousPageAction;
  surveyId?: string;
}>;

export const WitchScreen = ({
  loading,
  currentPage,
  answer,
  handleUpdateAnswer,
  handleNextPage,
  handleAnswer,
  handleSkip,
  handleOutcomeAction,
  handleSkipAndClose,
  handlePreviousPage,
  surveyId,
}: WitchScreenProps) => {
  const member = useSelector(memberStateSelector("member"));

  const [isIntakeSurvey, setIsIntakeSurvey] = useState<boolean>();

  useEffect(() => {
    if (isIntakeSurvey === null) {
      setIsIntakeSurvey(member?.setting?.onboardingPending);
    }
  }, [isIntakeSurvey, member]);

  const isContextNode =
    (currentPage?.property as EnumPropertyScreen)?.display === EnumPropertyScreenDisplay.INFORMATIONAL ||
    (currentPage?.property as EnumPropertyScreen)?.display === EnumPropertyScreenDisplay.CONTEXTUAL;

  return (
    <SurveyNavigationWrapper
      isContextNode={isContextNode || !!currentPage?.property?.imageUrl || !!currentPage?.outcome?.imageUrl}
      showLoadingSpinner={loading || !currentPage}
      isIntakeSurvey={!!isIntakeSurvey}
      surveyId={surveyId}
    >
      {currentPage && (
        <WitchPage
          loading={loading}
          answer={answer}
          currentPage={currentPage}
          isContextNode={isContextNode}
          isIntakeSurvey={isIntakeSurvey || CLASSIC_SURVEY_TITLES.includes(surveyId ?? "")}
          handleUpdateAnswer={handleUpdateAnswer}
          handleNextPage={handleNextPage}
          handleAnswer={handleAnswer}
          handleSkip={handleSkip}
          handleOutcomeAction={handleOutcomeAction}
          handleAnswerAndClose={handleOutcomeAction}
          handleSkipAndClose={handleSkipAndClose}
          handlePreviousPage={handlePreviousPage}
          surveyId={surveyId}
        />
      )}
    </SurveyNavigationWrapper>
  );
};

type WitchPageProps = Readonly<{
  loading?: boolean;
  currentPage: WizardScreen;
  answer?: PropertyAnswer;
  isContextNode: boolean;
  isIntakeSurvey: boolean;
  handleUpdateAnswer: (value: PropertyAnswer) => {
    payload: PropertyAnswer;
    type: string;
  };
  handleNextPage?: () => void;
  handleAnswer: AnswerAction;
  handleSkip: () => void;
  handleOutcomeAction?: (outcome: OutcomeScreenAction) => void;
  handleAnswerAndClose: ((outcome: OutcomeScreenAction) => void) | undefined;
  handleSkipAndClose: () => void;
  handlePreviousPage: PreviousPageAction;
  surveyId?: string;
}>;

const WitchPage = ({
  loading,
  currentPage,
  answer,
  isContextNode,
  isIntakeSurvey,
  handleNextPage,
  handleUpdateAnswer,
  handleAnswer,
  handleSkip,
  handleAnswerAndClose,
  handleSkipAndClose,
  handleOutcomeAction,
  handlePreviousPage,
  surveyId,
}: WitchPageProps) => {
  const { nextEnabled, skipEnabled, answerEnabled, doneEnabled, outcome, property, backEnabled } = currentPage;

  // todo - add back validation handling when this is ready to be fleshed out
  const [validationPassed] = useState<boolean>(true);
  const canAnswer = ((answer !== undefined && answer !== null) || skipEnabled) && validationPassed;
  const isOutcome = !!outcome;
  const isContextualEnumProperty =
    !!property && property instanceof EnumPropertyScreen && property.display === EnumPropertyScreenDisplay.CONTEXTUAL;
  const isInformationalNode =
    !!property &&
    property instanceof EnumPropertyScreen &&
    property.display === EnumPropertyScreenDisplay.INFORMATIONAL;

  const nextPageMeta = computeNextPageMeta({
    isOutcome,
    handleNextPage,
    handleAnswer,
    handleSkip,
    handlePreviousPage,
    handleAnswerAndClose,
    handleSkipAndClose,
    nextEnabled,
    skipEnabled,
    canAnswer,
    answerEnabled,
    doneEnabled,
    backEnabled,
  });
  const isHeightQuestion = (property as StringPropertyScreen)?.display === StringPropertyScreenDisplay.IMPERIAL_HEIGHT;

  useEffect(() => {
    if (isHeightQuestion && !answer) {
      handleUpdateAnswer(`5'6"`);
    }
  }, [answer, handleUpdateAnswer, isHeightQuestion]);

  const actions = currentPage?.outcome?.actions;
  const primaryAction = actions ? actions[0] : undefined;
  const secondaryAction = actions && actions.length > 1 ? actions[1] : undefined;

  if (property) {
    const fieldType = calculateFieldType(property);

    const { title, showTitle, description, showDescription } = property;

    const intProperty = property as IntegerPropertyScreen;

    const feetMin = 3;
    const feetMax = 7;

    const inchesMin = 0;
    const inchesMax = 11;

    const handleHeightAnswer = (answer: SurveyAnswerWithCustomValues) => {
      const { feet, inches } = answer.answerValues as ImperialHeight;

      handleUpdateAnswer(`${feet}'${inches}"`);
    };

    const handleDateAnswer = (answer: SurveyAnswerWithCustomValues) => {
      if (answer.answerValues) {
        handleUpdateAnswer(answer?.answerValues.toString());
      }
    };

    const handleNumberAnswer = (answer: SurveyAnswerWithCustomValues) => {
      handleUpdateAnswer(Number(answer.answerValues));
    };

    const enumProperty = property as EnumPropertyScreen;

    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems={isContextNode && isIntakeSurvey ? "center" : undefined}
        height={isContextNode || isIntakeSurvey ? "100%" : undefined}
        key={currentPage.property?.title}
      >
        <Box display="flex" flexDirection="column" gap={6} width={"100%"}>
          <WitchHeader
            title={title}
            showTitle={showTitle}
            description={description}
            showDescription={showDescription}
            isOutcome={isOutcome}
            isContextualEnumProperty={isContextualEnumProperty}
            isInformationalNode={isInformationalNode}
          />

          {fieldType === SurveyPageFieldType.STRING_FIELD && (
            <TextField
              value={answer?.toString() ?? ""}
              onChange={(event) => handleUpdateAnswer(event.target.value)}
              fullWidth
            />
          )}

          {fieldType === SurveyPageFieldType.INTEGER_FIELD &&
            (title.toLowerCase().includes("weight") ? (
              <NumberInputForm
                setAnswer={(weight) => {
                  const pounds =
                    weight?.answerValues && isImperialWeight(weight.answerValues) ? weight.answerValues.pounds : 1;

                  handleUpdateAnswer(pounds);
                }}
                variant="weight"
                aria-label={property.title}
                value={answer ? Number(answer) : null}
                // If the minimum is 0, we add 1 to the minimum to avoid the stepper from starting at 0 as our up arrow will not work if at 0
                minimum={Number(intProperty.minimum) + (Number(intProperty.minimum) === 0 ? 1 : 0)}
                maximum={Number(intProperty.maximum)}
              />
            ) : (
              <NumberStepper
                value={answer ? Number(answer) : null}
                minimum={Number(intProperty.minimum)}
                maximum={Number(intProperty.maximum)}
                ariaLabel={property.title}
                onChange={(_e, val) => {
                  if (val != null) {
                    handleUpdateAnswer(val);
                  }
                }}
                onInputChange={(e) => handleUpdateAnswer(e.target.value)}
                autoFocus
              />
            ))}

          {fieldType === SurveyPageFieldType.DATE_FIELD && (
            <DateField answers={{ answerValues: new Date(String(answer)) }} setAnswer={handleDateAnswer} />
          )}

          {fieldType === SurveyPageFieldType.DATE_TIME_FIELD && (
            <DateField answers={{ answerValues: new Date(String(answer)) }} setAnswer={handleDateAnswer} />
          )}

          {fieldType === SurveyPageFieldType.IMPERIAL_HEIGHT_FIELD && (
            // We use an empty Box (div) to wrap the HeightInputForm component to avoid FlexBox applying a gap between the input fields
            <Box>
              <HeightInputForm
                answers={{
                  answerValues: {
                    feet: parseImperial(String(answer))?.feet || 5,
                    inches: parseImperial(String(answer))?.inches || 6,
                  },
                }}
                minimum={{ inches: inchesMin, feet: feetMin }}
                maximum={{ inches: inchesMax, feet: feetMax }}
                setAnswer={handleHeightAnswer}
              />
            </Box>
          )}

          {fieldType === SurveyPageFieldType.NUMBER_FIELD && (
            <NumberInputForm
              value={answer ? Number(answer) : null}
              minimum={Number(intProperty.minimum)}
              maximum={Number(intProperty.maximum)}
              aria-label={property.title}
              setAnswer={handleNumberAnswer}
            />
          )}

          {fieldType === SurveyPageFieldType.RADIO_FIELD && (
            <EnumFieldGroup
              value={answer}
              setValue={handleUpdateAnswer}
              enumItems={enumProperty.enumItems}
              variant="single_choice"
              contextual={isContextNode}
            />
          )}

          {fieldType === SurveyPageFieldType.CHECKBOX_FIELD && (
            <EnumFieldGroup
              value={answer}
              setValue={handleUpdateAnswer}
              enumItems={
                enumProperty.enumItems.map((enumItem) => ({
                  ...enumItem,
                  isExclusive: enumItem.value === "none",
                })) || []
              }
              variant="multiple_choice"
            />
          )}

          {!loading && (
            <WitchPaginator
              nextPageMeta={nextPageMeta}
              isOutcome={isOutcome}
              isContextualEnumProperty={isContextualEnumProperty}
              handleAnswer={handleAnswer}
              contextualButtonText={enumProperty?.enumItems ? enumProperty?.enumItems[0]?.title : ""}
              surveyId={surveyId}
              isInformationalNode={isInformationalNode}
            />
          )}
        </Box>
      </Box>
    );
  }

  if (outcome) {
    const { title, description } = outcome;

    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height={isContextNode || isIntakeSurvey ? "100%" : undefined}
        mt={6}
      >
        <Box>
          <WitchHeader
            showTitle
            title={title}
            showDescription
            description={description}
            isOutcome={isOutcome}
            isContextualEnumProperty={isContextualEnumProperty}
          />
          <WitchPaginator
            nextPageMeta={nextPageMeta}
            isOutcome={isOutcome}
            isContextualEnumProperty={isContextualEnumProperty}
            handleAnswer={handleAnswer}
            handleOutcomeAction={handleOutcomeAction}
            primaryAction={primaryAction}
            secondaryAction={secondaryAction}
            surveyId={surveyId}
          />
        </Box>
      </Box>
    );
  }

  return null;
};
