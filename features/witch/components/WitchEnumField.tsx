import { ChangeEventHandler } from "react";

import { SurveyFormField } from "@Features/survey/components/SurveyFormField";

export type EnumFieldVariant = "checkbox" | "radio";

export type EnumFieldItem = Readonly<{
  value: string | number;
  title: string;
  description?: string;
  allowsCustomResponse?: boolean;
  customResponsePlaceholder?: string;
  isExclusive?: boolean; // e.g. "None of the above"
}>;

export type EnumFieldProps = Readonly<{
  variant: EnumFieldVariant;
  enumItem: EnumFieldItem;
  setValue: (value: string | number) => void;
  selected?: boolean;
  customResponse?: string;
  setCustomResponse: ChangeEventHandler<HTMLInputElement | HTMLTextAreaElement>;
}>;

export const EnumField = ({
  variant,
  enumItem,
  setValue,
  selected = false,
  customResponse = "",
  setCustomResponse,
}: EnumFieldProps) => {
  const handleOnComponentPress = () => {
    setValue(enumItem.value);
  };

  const handleTextInputPress = () => {
    if (!selected) {
      setValue(enumItem.value);
    }
  };

  return (
    <SurveyFormField
      key={enumItem.value}
      variant={variant}
      selected={selected}
      title={enumItem.title}
      allowsCustomResponse={enumItem.allowsCustomResponse}
      textInputValue={customResponse}
      handleTextInputPress={handleTextInputPress}
      onClick={handleOnComponentPress}
      setTextInputValue={setCustomResponse}
    />
  );
};
