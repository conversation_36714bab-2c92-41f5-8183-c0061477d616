import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { OutcomeScreenAction, PropertyAnswer } from "@vivantehealth/vivante-core";
import { useRouter } from "next/router";

import { ToDosStateSlice, toDosSelectors as toDosSelector } from "@Features/toDos/store/toDosStateSlice";
import { WitchStateSlice, witchStateSelector } from "@Features/witch/store/witchStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { useOpenSymptomLoggingDrawer } from "@Hooks/useOpenSymptopLoggingDrawer";
import { logger } from "@Utils/logger";

import { WitchScreen } from "./components/WitchScreen";
import { handleOutcomeScreenAction } from "./utils/witch.util";

/** Type for `handlePreviousPage` to suggest the dispatched or given function invokes actions in redux in Witch feature */
export type PreviousPageAction = (value: () => void) => {
  payload: () => void;
  type: string;
};

/** Type for `handleAnswer` to suggest the dispatched or given function invokes actions in redux in Witch feature */
export type AnswerAction = (value: () => void) =>
  | {
      payload: () => void;
      type: string;
    }
  | {
      payload: undefined;
      type: string;
    };

type WitchScreenContainerProps = Readonly<{
  witchId?: string;
  todoId?: string;
}>;

export const WitchScreenContainer = ({ witchId, todoId }: WitchScreenContainerProps) => {
  const { sendEventAnalytics } = useAnalyticsHook();
  const dispatch = useDispatch();
  const router = useRouter();
  const triggerSymptomLoggingDrawer = useOpenSymptomLoggingDrawer(true);
  // selectors
  const currentPage = useSelector(witchStateSelector("currentPage"));
  const todo = useSelector(witchStateSelector("todo"));
  const loading = useSelector(witchStateSelector("loadState")) === "loading";
  const answer = useSelector(witchStateSelector("answer"));
  const todos = useSelector(toDosSelector("todos"));

  useEffect(() => {
    if (witchId) {
      dispatch(WitchStateSlice.actions.loadWitchById(witchId));
    }
  }, [witchId, dispatch]);

  useEffect(() => {
    if (todoId) {
      dispatch(ToDosStateSlice.actions.loadToDos());
    }
  }, [dispatch, todoId]);

  useEffect(() => {
    if (todoId && todos) {
      const todo = todos.find((n) => n.id === todoId);

      if (todo) {
        dispatch(WitchStateSlice.actions.loadWitchByTodo(todo));
      }
    }
  }, [dispatch, todoId, todos]);

  // handlers
  const handleUpdateAnswer = (value: PropertyAnswer) => dispatch(WitchStateSlice.actions.handleUpdateAnswer(value));
  const handleNextPage = () => logger.info("handleNextPage");
  const handleAnswer: AnswerAction = (value: () => void) => dispatch(WitchStateSlice.actions.handleAnswer(value));
  const handlePreviousPage: PreviousPageAction = (value: () => void) =>
    dispatch(WitchStateSlice.actions.handlePreviousPage(value));
  const handleSkip = () => logger.info("handleSkip");

  const handleOutcomeAction = (outcomeAction: OutcomeScreenAction) =>
    handleOutcomeScreenAction({
      outcomeAction,
      todo,
      router,
      sendEventAnalytics,
      actionDispatch: witchId ? triggerSymptomLoggingDrawer : undefined,
    });
  const handleAnswerAndClose = () => logger.info("handleAnswerAndClose");
  const handleSkipAndClose = () => logger.info("handleSkipAndClose");

  return (
    <WitchScreen
      loading={loading}
      answer={answer}
      currentPage={currentPage}
      handleUpdateAnswer={handleUpdateAnswer}
      handleNextPage={handleNextPage}
      handleAnswer={handleAnswer}
      handleSkip={handleSkip}
      handleOutcomeAction={handleOutcomeAction}
      handleAnswerAndClose={handleAnswerAndClose}
      handleSkipAndClose={handleSkipAndClose}
      handlePreviousPage={handlePreviousPage}
      surveyId={currentPage?.property?.title || currentPage?.outcome?.title}
    />
  );
};
