import {
  ImperialHeight,
  ClickStreamActivityEventType,
  OutcomeScreenAction,
  OutcomeScreenActionAction,
  ToDo,
} from "@vivantehealth/vivante-core";
import { NextRouter } from "next/router";

import { handleUriNavigation } from "@Features/navigation/utils/navigation.util";
import { SendEventAnalyticsAction } from "@Hooks/analyticsHook";
import { Routes } from "@Types";

type HandleOutcomeScreenAction = Readonly<{
  outcomeAction: OutcomeScreenAction;
  todo: ToDo | null;
  router: NextRouter;
  sendEventAnalytics: SendEventAnalyticsAction;
  actionDispatch?: () => void;
}>;

export const handleOutcomeScreenAction = ({
  outcomeAction,
  todo,
  router,
  sendEventAnalytics,
  actionDispatch,
}: HandleOutcomeScreenAction) => {
  if (outcomeAction.uri) {
    const path = handleUriNavigation(outcomeAction.uri);

    if (path) {
      router.push(path);
    } else {
      router.push(Routes.HOME);
    }
  } else {
    switch (outcomeAction.action) {
      case OutcomeScreenActionAction.CLOSE: {
        router.back();

        if (todo?.subtype === "HealthHistory") {
          // this.handleAnalyticsSendOnBoardingSessionScheduleLater() // see mobile
        }

        break;
      }
      case OutcomeScreenActionAction.SCHEDULE_SESSION: {
        router.push(Routes.CARE_TEAM);

        if (todo?.subtype === "HealthHistory") {
          sendEventAnalytics(ClickStreamActivityEventType.ONBOARDING_SCHEDULE_SESSION_NOW);
        }

        break;
      }
      case OutcomeScreenActionAction.LOG_SYMPTOM: {
        actionDispatch?.();

        router.push(Routes.PROGRESS);

        break;
      }
      default: {
        // todo - support
        alert(`Tried to open outcome action of unknown type: ${outcomeAction}`);
      }
    }
  }
};

export const parseImperial = (val?: string): ImperialHeight | null => {
  if (!val) {
    // empty
    return null;
  }

  const hasFeet = val.includes('"');
  const hasInches = val.includes("'");

  if (!hasInches && !hasFeet) {
    // invalid
    return null;
  }

  if (!hasFeet) {
    // feet only
    return {
      feet: Number(val.replace('"', "").replace("'", "")),
      inches: 0,
    };
  }
  if (!hasInches) {
    // inches only
    return {
      feet: 0,
      inches: Number(val.replace('"', "").replace("'", "")),
    };
  }

  // has both
  const [feetPart, inchesPart] = val.split("'");
  const feet = Number(feetPart);
  const inches = Number(inchesPart.split('"')[0]);

  return {
    feet,
    inches,
  };
};
/** Typeguard to determine if the payload contains an object for a witch with an id */
export const isWitchId = (payload: unknown): payload is { witch: { id: string } } => {
  return (
    payload != null &&
    typeof payload === "object" &&
    "witch" in payload &&
    payload.witch != null &&
    typeof payload.witch === "object" &&
    "id" in payload.witch
  );
};
