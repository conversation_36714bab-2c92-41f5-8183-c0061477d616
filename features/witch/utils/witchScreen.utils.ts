import {
  ArrayPropertyScreen,
  EnumPropertyScreen,
  IntegerPropertyScreen,
  NumberPropertyScreen,
  OutcomeScreenAction,
  PropertyScreen,
  StringPropertyScreen,
  StringPropertyScreenDisplay,
} from "@vivantehealth/vivante-core";

import { AnswerAction, PreviousPageAction } from "../WitchScreenContainer";

export enum SurveyPageFieldType {
  DATE_FIELD,
  DATE_TIME_FIELD,
  STRING_FIELD,
  INTEGER_FIELD,
  IMPERIAL_HEIGHT_FIELD,
  RADIO_FIELD,
  CHECKBOX_FIELD,
  NUMBER_FIELD,
}

export const calculateFieldType = (property: PropertyScreen): SurveyPageFieldType | null => {
  const isStringProperty = property instanceof StringPropertyScreen;

  const isDateField =
    isStringProperty && (property as StringPropertyScreen).display === StringPropertyScreenDisplay.DATE;

  if (isDateField) {
    return SurveyPageFieldType.DATE_FIELD;
  }

  const isDateTimeField =
    isStringProperty && (property as StringPropertyScreen).display === StringPropertyScreenDisplay.DATE_TIME;

  if (isDateTimeField) {
    return SurveyPageFieldType.DATE_TIME_FIELD;
  }

  const isStringField =
    isStringProperty &&
    (!(property as StringPropertyScreen).display ||
      (property as StringPropertyScreen).display === StringPropertyScreenDisplay.TEXTAREA);

  if (isStringField) {
    return SurveyPageFieldType.STRING_FIELD;
  }

  const isIntegerProperty = property instanceof IntegerPropertyScreen;

  if (isIntegerProperty) {
    return SurveyPageFieldType.INTEGER_FIELD;
  }

  const isImperialHeightField =
    isStringProperty && (property as StringPropertyScreen).display === StringPropertyScreenDisplay.IMPERIAL_HEIGHT;

  if (isImperialHeightField) {
    return SurveyPageFieldType.IMPERIAL_HEIGHT_FIELD;
  }

  if (property instanceof EnumPropertyScreen) {
    return SurveyPageFieldType.RADIO_FIELD;
  }

  if (property instanceof ArrayPropertyScreen) {
    return SurveyPageFieldType.CHECKBOX_FIELD;
  }

  if (property instanceof NumberPropertyScreen) {
    return SurveyPageFieldType.NUMBER_FIELD;
  }

  return null;
};

export type NextPageMeta = Readonly<{
  label: string;
  callback?: (done?: (() => void) | undefined) => void;
  enabled: boolean | undefined;
  arrowDisabled?: boolean;
}>;

export type ComputeNextPageMetaProps = Readonly<{
  isOutcome: boolean;
  handleNextPage: (() => void) | undefined;
  handleAnswer: AnswerAction;
  handleSkip: () => void;
  handleAnswerAndClose: ((outcome: OutcomeScreenAction) => void) | undefined;
  handleSkipAndClose: () => void;
  handlePreviousPage: PreviousPageAction;
  nextEnabled: boolean;
  skipEnabled: boolean;
  canAnswer: boolean;
  answerEnabled: boolean;
  doneEnabled: boolean | undefined;
  backEnabled: boolean | undefined;
}>;

export const computeNextPageMeta = ({
  isOutcome,
  handleNextPage,
  handleAnswer,
  handleSkip,
  handleAnswerAndClose,
  handleSkipAndClose,
  handlePreviousPage,
  nextEnabled,
  skipEnabled,
  canAnswer,
  answerEnabled,
  doneEnabled,
  backEnabled,
}: ComputeNextPageMetaProps): ReadonlyArray<NextPageMeta> => {
  if (isOutcome) {
    return [];
  }

  const next: NextPageMeta = {
    label: "next",
    callback: handleNextPage,
    enabled: nextEnabled,
    arrowDisabled: true,
  };

  const back: NextPageMeta = {
    label: "back",
    callback: handlePreviousPage as (done?: (() => void) | undefined) => void,
    enabled: backEnabled,
    arrowDisabled: true,
  };

  const skip: NextPageMeta = {
    label: "skip",
    callback: handleSkip,
    enabled: skipEnabled,
    arrowDisabled: true,
  };

  const answer: NextPageMeta = {
    label: "next",
    callback: handleAnswer as (done?: (() => void) | undefined) => void,
    enabled: canAnswer && answerEnabled,
    arrowDisabled: true,
  };

  const skipAndClose: NextPageMeta = {
    label: "skip and done",
    callback: handleSkipAndClose,
    enabled: skipEnabled && doneEnabled === true,
    arrowDisabled: true,
  };

  const answerAndClose: NextPageMeta = {
    label: "answer and done",
    callback: handleAnswerAndClose as () => void,
    enabled: canAnswer && answerEnabled && doneEnabled === true,
    arrowDisabled: true,
  };

  if (doneEnabled) {
    // terminal state
    const metas = [];

    if (answerEnabled) {
      metas.push(answerAndClose);
    }
    if (skipEnabled) {
      metas.push(skipAndClose);
    }

    return metas;
  }

  // non terminal state
  const metas = [];

  if (backEnabled) {
    metas.push(back);
  }
  if (skipEnabled) {
    metas.push(skip);
  }
  if (answerEnabled) {
    metas.push(answer);
  } else if (nextEnabled) {
    metas.push(next);
  }

  return metas;
};
