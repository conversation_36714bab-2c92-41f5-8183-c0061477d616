/* eslint-disable @typescript-eslint/no-unused-vars */
import { IWizard, PropertyAnswer, ToDo, VivanteException, WizardScreen } from "@vivantehealth/vivante-core";
import { PayloadAction, createSlice } from "@reduxjs/toolkit";

import { LoadState } from "@Types";
import { buildSliceStateSelector, processError } from "@Utils/slice.util";

/// //////////////////////////////////////////////////////
/// state

export interface WitchState {
  loadState: LoadState;
  currentWitch: IWizard | null;
  title: string | null;
  currentPage: WizardScreen | null;
  todo: ToDo | null;
  answer: PropertyAnswer | undefined;
}

export const initialState: WitchState = {
  loadState: null,
  currentWitch: null,
  title: null,
  currentPage: null,
  todo: null,
  answer: undefined,
};

/// //////////////////////////////////////////////////////
/// slice

export const WitchStateSlice = createSlice({
  name: "witchState",
  initialState,
  reducers: {
    loadWitchById: (state: WitchState, _: PayloadAction<string>) => {
      return {
        ...initialState, // clean state when starting new witch
        loadState: "loading" as LoadState,
      };
    },
    loadWitchByTodo: (state, _: PayloadAction<ToDo>) => ({ ...state, loadState: "loading" }),
    loadWitchFailure: (state, action: PayloadAction<Error | VivanteException>) => {
      processError({ error: action.payload, errorDisplayType: "modal" });

      return { ...state, loadState: "failure" };
    },
    loadWitchSuccess: (state, action: PayloadAction<IWizard>) => ({
      ...state,
      loadState: "loaded",
      currentWitch: action.payload,
    }),
    prepareScreen: (state: WitchState, action: PayloadAction<WizardScreen>) => {
      const screen = action.payload;

      return {
        ...state,
        currentPage: screen,
        answer: screen.property?.answer,
        loadState: "loaded" as LoadState,
      };
    },
    handleUpdateAnswer: (state, action: PayloadAction<PropertyAnswer>) => ({ ...state, answer: action.payload }),
    handleAnswer: (state, _: PayloadAction<() => void>) => ({ ...state, loadState: "loading" }),
    handlePreviousPage: (state, _: PayloadAction<() => void>) => state,
    handleSkipPage: (state, _: PayloadAction<() => void>) => state,
  },
});

/// //////////////////////////////////////////////////////
/// selectors

export const witchStateSelector = buildSliceStateSelector("witchState");

export const witchStateReducer = WitchStateSlice.reducer;
