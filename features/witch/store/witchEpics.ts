import { ClickStreamActivityEventType, IW<PERSON>rd, ToDo, WizardScreen } from "@vivantehealth/vivante-core";
import { PayloadAction } from "@reduxjs/toolkit";
import { Epic, ofType } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { switchMap, map, catchError, withLatestFrom, mergeMap } from "rxjs/operators";

import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { RootState } from "@Store/store";

import { WitchStateSlice } from "./witchStateSlice";
import { createSimpleAnalyticsEpic } from "../../analytics/store/analyticsEpics";
import { isWitchId } from "../utils/witch.util";

const {
  loadWitchById,
  loadWitchByTodo,
  loadWitchFailure,
  loadWitchSuccess,
  prepareScreen,
  handleAnswer,
  handlePreviousPage,
  handleSkipPage,
} = WitchStateSlice.actions;

const loadWitchByIdEpic: Epic = (actions$: Observable<PayloadAction<string>>) => {
  return actions$.pipe(
    ofType(loadWitchById.type),

    switchMap((action: PayloadAction<string>) =>
      from(vivanteCoreContainer.getWitchUseCaseFactory().createStartWitchByIdUseCase().execute(action.payload)).pipe(
        map((witch: IWizard) => loadWitchSuccess(witch)),
        catchError((error) => {
          return of(loadWitchFailure(error));
        }),
      ),
    ),
  );
};

const loadWitchByTodoEpic: Epic = (actions$: Observable<PayloadAction<ToDo>>) => {
  return actions$.pipe(
    ofType(loadWitchByTodo.type),

    switchMap((action: PayloadAction<ToDo>) =>
      from(
        vivanteCoreContainer.getWitchUseCaseFactory().createStartMemberTodoWitchUseCase().execute(action.payload),
      ).pipe(
        map((witch: IWizard) => loadWitchSuccess(witch)),
        catchError((error) => {
          return of(loadWitchFailure(error));
        }),
      ),
    ),
  );
};

const startWizardEpic: Epic = (actions$: Observable<PayloadAction<IWizard>>) => {
  return actions$.pipe(
    ofType(loadWitchSuccess.type),

    switchMap((action: PayloadAction<IWizard>) =>
      from(action.payload.peek()).pipe(
        map((screen: WizardScreen) => prepareScreen(screen)),
        catchError((error) => {
          return of(loadWitchFailure(error));
        }),
      ),
    ),
  );
};

const handleAnswerEpic: Epic = (
  actions$: Observable<PayloadAction<(() => void) | undefined>>,
  state$: Observable<RootState>,
) => {
  return actions$.pipe(
    ofType(handleAnswer.type),

    withLatestFrom(state$),

    mergeMap(([action, state]: [PayloadAction<(() => void) | undefined>, RootState]) => {
      if (!state.witchState.currentWitch) {
        return of(loadWitchFailure(new Error("Current witch is null")));
      }
      if (!state.witchState.answer) {
        return of(loadWitchFailure(new Error("Answer is null")));
      }

      return from(state.witchState.currentWitch.answer(state.witchState.answer)).pipe(
        map((screen: WizardScreen) => {
          const callback = action.payload;

          if (callback) {
            callback();
          }

          return prepareScreen(screen);
        }),
        catchError((error) => {
          return of(loadWitchFailure(error));
        }),
      );
    }),
  );
};

const handlePreviousPageEpic: Epic = (
  actions$: Observable<PayloadAction<(() => void) | undefined>>,
  state$: Observable<RootState>,
) => {
  return actions$.pipe(
    ofType(handlePreviousPage.type),

    withLatestFrom(state$),

    mergeMap(([action, state]: [PayloadAction<(() => void) | undefined>, RootState]) => {
      if (!state.witchState.currentWitch) {
        return of(loadWitchFailure(new Error("Current witch is null")));
      }

      return from(state.witchState.currentWitch.back()).pipe(
        map((screen: WizardScreen) => {
          const callback = action.payload;

          if (callback) {
            callback();
          }

          return prepareScreen(screen);
        }),
        catchError((error) => {
          return of(loadWitchFailure(error));
        }),
      );
    }),
  );
};

const handleSkipPageEpic: Epic = (
  actions$: Observable<PayloadAction<(() => void) | undefined>>,
  state$: Observable<RootState>,
) => {
  return actions$.pipe(
    ofType(handleSkipPage.type),

    withLatestFrom(state$),

    mergeMap(([action, state]: [PayloadAction<(() => void) | undefined>, RootState]) => {
      if (!state.witchState.currentWitch) {
        return of(loadWitchFailure(new Error("Current witch is null")));
      }

      return from(state.witchState.currentWitch.skip()).pipe(
        map((screen: WizardScreen) => {
          const callback = action.payload;

          if (callback) {
            callback();
          }

          return prepareScreen(screen);
        }),
        catchError((error) => {
          return of(loadWitchFailure(error));
        }),
      );
    }),
  );
};

const analyticsEpics: Epic[] = [
  createSimpleAnalyticsEpic<IWizard>(WitchStateSlice.actions.loadWitchSuccess.type, (payload) => ({
    eventType: ClickStreamActivityEventType.ENTER_SCREEN,
    currentScreen: "Survey",
    activityContextExtra: {
      surveyId: isWitchId(payload) ? payload?.witch?.id : "unknown",
    },
  })),
  createSimpleAnalyticsEpic(
    WitchStateSlice.actions.handlePreviousPage.type,
    ClickStreamActivityEventType.WIZARD_PREVIOUS_PAGE,
  ),
  createSimpleAnalyticsEpic(WitchStateSlice.actions.handleAnswer.type, ClickStreamActivityEventType.WIZARD_ANSWER),
  createSimpleAnalyticsEpic(WitchStateSlice.actions.handleSkipPage.type, ClickStreamActivityEventType.WIZARD_SKIP),
];

export const witchEpics = [
  handleAnswerEpic,
  handlePreviousPageEpic,
  loadWitchByIdEpic,
  loadWitchByTodoEpic,
  startWizardEpic,
  handleSkipPageEpic,
  ...analyticsEpics,
];
