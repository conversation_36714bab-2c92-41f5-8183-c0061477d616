import "@testing-library/jest-dom/vitest";
import { vi } from "vitest";

// Mock for buildSliceStateSelector globally
vi.mock("@Utils/slice.util", () => ({
  buildSliceStateSelector: <S_KEY extends string>(sliceKey: S_KEY) => {
    return <A_KEY extends string>(attribute: A_KEY) =>
      (state: Record<S_KEY, Record<A_KEY, unknown>>): unknown => {
        const slice = state[sliceKey];

        if (slice && Object.prototype.hasOwnProperty.call(slice, attribute)) {
          return slice[attribute];
        }

        return undefined;
      };
  },
}));

// Define mocks for Embla Carousel
Object.defineProperty(window, "IntersectionObserver", {
  writable: true,
  value: vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  })),
});

Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: vi.fn().mockImplementation((query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

Object.defineProperty(window, "ResizeObserver", {
  writable: true,
  value: vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  })),
});
