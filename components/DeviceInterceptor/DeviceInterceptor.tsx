import React, { useEffect } from "react";
import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { useRouter } from "next/router";

import { useAnalyticsHook } from "@Hooks/analyticsHook";
import { useFeatureFlag } from "@Hooks/useFeatureFlag";
import { Routes } from "@Types";
import { isMobileDevice } from "@Utils/isMobileDevice";
import { logger } from "@Utils/logger";

type DeviceInterceptorProps = Readonly<{
  children: React.ReactNode;
}>;

const APP_DOWNLOAD_SCREEN_LOCATION_FLAG = "app_download_screen_location";
const AFTER_INTAKE_FLAG_VALUE = "after-intake";

const MOBILE_ENABLED_PATHS: string[] = [
  Routes.ROOT,
  Routes.WELCOME,
  Routes.WELCOME_ACCOLADE,
  Routes.WELCOME_SOLERA,
  Routes.WELCOME_PERSONIFY_HEALTH,
  Routes.REGISTER,
  Routes.REGISTER_WITH_CODE,
  Routes.ELIGIBILITY,
  Routes.ELIGIBILITY_HELP,
  Routes.SURVEY,
  Routes.WELCOME_MOBILE_APP,
  Routes.ACCESS_CODE,
  Routes.SIGN_UP,
  Routes.HOME,
  Routes.ONBOARDING,
  Routes.LOGIN,
  Routes.SIGN_IN,
  Routes.MFA_VERIFICATION,
  Routes.MFA_VERIFICATION_LOGIN,
  Routes.MFA_VERIFICATION_REGISTRATION,
  Routes.NURSE_TRIAGE,
];

export const DeviceInterceptor = ({ children }: DeviceInterceptorProps) => {
  const router = useRouter();
  const { isReady, treatment } = useFeatureFlag(APP_DOWNLOAD_SCREEN_LOCATION_FLAG);
  const { sendEventAnalytics } = useAnalyticsHook();
  const intakePaths = MOBILE_ENABLED_PATHS;

  useEffect(() => {
    // if the flag is ready and the user is on a mobile device, move forard with the logic to determine what to render
    if (isReady && isMobileDevice() && router.asPath !== Routes.WELCOME_MOBILE_APP) {
      /*
      Check the current path and if the path is for the intake survey and the flag is set to after-intake,
      add the intake survey path to the list of allowed paths and send the appropriate analytics event
      If the path is not the intake survey, send the appropriate analytics event without adding intake to the allowed paths
      */
      if (router.asPath === Routes.DELPHI_INTAKE_SURVEY) {
        if (treatment === AFTER_INTAKE_FLAG_VALUE) {
          intakePaths.push(Routes.DELPHI_INTAKE_SURVEY);
          sendEventAnalytics(ClickStreamActivityEventType.MOBILE_ON_WEB_AFTER_INTAKE);
        } else {
          sendEventAnalytics(ClickStreamActivityEventType.MOBILE_ON_WEB_BEFORE_INTAKE);
        }
      }

      // if the current path is not in the allowed paths, redirect the user to the mobile app download screen
      if (!intakePaths.includes(router.asPath)) {
        logger.info(
          `Mobile path not included for mobile rendering: ${router.asPath}. Redirecting to mobile app download screen`,
        );
        router.replace(Routes.WELCOME_MOBILE_APP);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isReady, treatment, router.asPath, sendEventAnalytics]);

  return <>{children}</>;
};
