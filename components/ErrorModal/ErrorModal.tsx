import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { VivanteException } from "@vivantehealth/vivante-core";
import { Button, Typography } from "@mui/material";
import * as Sentry from "@sentry/nextjs";

import { appStrings } from "@Assets/app_strings";
import { BaseModal } from "@Components/BaseModal/BaseModal";
import { ErrorMeta } from "@Features/error/store/errorStateSlice";
import { RootState } from "@Store/store";

const isErrorMeta = (error: VivanteException | ErrorMeta | null): error is ErrorMeta => {
  return error !== null && !(error instanceof VivanteException) && error.message !== undefined;
};

type ErrorModalProps = Readonly<{
  errorSelector: (state: RootState) => VivanteException | ErrorMeta | null;
  onClose: () => void;
}>;

export const ErrorModal = ({ errorSelector, onClose }: ErrorModalProps) => {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const error = useSelector(errorSelector);
  const isErrorMetaError = isErrorMeta(error);
  const showHeader = Boolean(isErrorMetaError && error?.title);

  useEffect(() => {
    if (error) {
      setErrorMessage(error.message);

      Sentry.withScope((scope) => {
        scope.setLevel("log");
        scope.setTag("type", "triggeredErrorModal");
        Sentry.captureException(new Error(error.message));
      });

      setIsModalOpen(true);
    }
  }, [error]);

  const onCloseAction = () => {
    onClose();
    setIsModalOpen(false);
  };

  return (
    <BaseModal
      isModalOpen={isModalOpen}
      onClose={onCloseAction}
      title={showHeader ? error?.title : undefined}
      displayCloseButton={showHeader}
      bodyContent={<Typography variant="body">{errorMessage}</Typography>}
      actions={
        <Button
          variant="primary"
          onClick={() => {
            if (isErrorMetaError && error?.onButtonClick) {
              error.onButtonClick();
              return onCloseAction();
            }

            onCloseAction();
          }}
          fullWidth
        >
          {isErrorMetaError && error?.buttonText ? error.buttonText : appStrings.buttonText.gotIt}
        </Button>
      }
    />
  );
};
