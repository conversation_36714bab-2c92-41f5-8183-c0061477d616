import { Box, Collapse, Grid, Icon<PERSON><PERSON>on, Typo<PERSON>, Button } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";

const BUTTON_STRINGS = appStrings.buttonText;

type BannerDefaultProps = Readonly<{
  readMoreLink?: string;
  display: boolean;
  onDissmiss: () => void;
}>;

type TextConditional =
  | Readonly<{
      text: string;
      boldText?: string;
    }>
  | Readonly<{
      text?: string;
      boldText: string;
    }>;

type BannerProps = BannerDefaultProps & TextConditional;

export const Banner = ({ text, boldText, readMoreLink, display, onDissmiss }: BannerProps) => (
  <Collapse in={display}>
    <Box p={3} bgcolor={color.background.brand.default}>
      <Grid container direction="row" justifyContent="space-between" alignItems="center">
        <Grid item xs display="flex" justifyContent="center">
          <Box display="flex" alignItems="center" gap={2}>
            {boldText ? <Typography variant="actionDense">{boldText}</Typography> : null}
            {text ? <Typography variant="bodyDense">{text}</Typography> : null}
            {readMoreLink ? (
              <Button
                variant="tertiary"
                size="small"
                sx={{ borderColor: color.border.action.hover }}
                href={readMoreLink}
              >
                {BUTTON_STRINGS.readMore}
              </Button>
            ) : null}
          </Box>
        </Grid>
        <Grid item>
          <IconButton aria-label={BUTTON_STRINGS.close} size="small" onClick={onDissmiss}>
            <AppIcon size="sm" name="Close" color={color.icon.strong} />
          </IconButton>
        </Grid>
      </Grid>
    </Box>
  </Collapse>
);
