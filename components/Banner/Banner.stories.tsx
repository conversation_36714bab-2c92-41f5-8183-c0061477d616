import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";

import { <PERSON> } from "./Banner";
import { cylinderThemeDecorator } from "../stories/cylinderThemeDecorator";

const meta: Meta<typeof Banner> = {
  title: "@Components/Banner",
  component: Banner,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    ...cylinderThemeDecorator,
    (Story) => (
      <div style={{ width: "1000px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof Banner>;

export const BannerDefault: Story = {
  args: {
    boldText: "Important",
    text: "Announcement",
    display: true,
    onDissmiss: () => {
      alert("Dismissed");
    },
    readMoreLink: "https://test.com/",
  },
};
