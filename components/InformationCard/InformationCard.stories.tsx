import type { <PERSON>a, StoryObj } from "@storybook/nextjs";

import { InformationCard } from "./InformationCard";
import { cylinderThemeDecorator } from "../stories/cylinderThemeDecorator";

const meta: Meta<typeof InformationCard> = {
  title: "@Components/InformationCard",
  component: InformationCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: cylinderThemeDecorator,
};

export default meta;
type Story = StoryObj<typeof InformationCard>;

export const Primary: Story = {
  args: {
    header: "Header",
    subheaders: ["Sub Header 1", "Sub Header 2"],
    buttonText: "Button",
    onClick: () => alert("clicked"),
  },
};
