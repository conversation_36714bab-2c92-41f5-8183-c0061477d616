import { <PERSON>, <PERSON><PERSON>, <PERSON>, Typography } from "@mui/material";

import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";

type InformationCardProps = Readonly<{
  header: string;
  subheaders: string[];
  buttonText: string;
  onClick: () => void;
}>;

export const InformationCard = ({ header, subheaders, buttonText, onClick }: InformationCardProps) => {
  const { formWidth } = useResponsiveStylesHook();

  return (
    <Box display="flex" justifyContent="center">
      <Card sx={{ width: formWidth }}>
        <Box display="flex" flexDirection="column" gap={4}>
          <Typography variant="h1Serif" aria-label={`${header}.`}>
            {header}
          </Typography>
          {subheaders.map((subheader) => (
            <Typography key={subheader} variant="body" aria-label={`${subheader}.`}>
              {subheader}
            </Typography>
          ))}
          <Button variant="primary" onClick={onClick} fullWidth>
            {buttonText}
          </Button>
        </Box>
      </Card>
    </Box>
  );
};
