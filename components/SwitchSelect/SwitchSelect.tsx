import React, { useEffect, useRef, useState } from "react";
import { Box, Typography } from "@mui/material";
import { color, spacing, typography } from "@vivantehealth/design-tokens";
import { motion } from "framer-motion";

import { RADIUS_FULL_PX, SPACING_8_PX, SPACING_16_PX } from "@Assets/style_constants";

const GAP = spacing.space1;

const pillCss = {
  borderRadius: RADIUS_FULL_PX,
  lineHeight: typography.action.lineHeight,
  height: `calc(${typography.action.lineHeight} + ${SPACING_16_PX})`,
  padding: `${SPACING_8_PX} ${SPACING_16_PX}`,
} as const;

type SwitchSelectProps = Readonly<{
  options: string[];
  value: string;
  onChange: (currentValue: string) => void;
}>;

const getPillPosition = (currentIndex: number, pillWidth: number) => {
  return 1 + GAP + currentIndex * pillWidth + (currentIndex - 1) * GAP;
};

// calculate pill width as current width minus the spacing on the sides and the gap between the pills and the borders width
const calculatedPillWidth = (currentWidth: number, optionsLength: number) =>
  (currentWidth - 2 * GAP - (optionsLength - 1) * GAP - 2) / optionsLength;

export const SwitchSelect = ({ options, value, onChange }: SwitchSelectProps) => {
  const currentIndex = options.indexOf(value);
  const prevIndexRef = useRef(currentIndex);
  const [pillWidth, setPillWidth] = useState(0);

  // calculate the initial position of the pill using left border width, left spacing and number of gaps until the current pill
  const [initialPosition, setInitialPosition] = useState(getPillPosition(currentIndex, pillWidth));

  const ref = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const currentWidth = ref.current ? ref.current.offsetWidth : 0;

    setPillWidth(calculatedPillWidth(currentWidth, options.length));
  }, [options.length]);

  const selectOption = (selectedOption: string) => {
    onChange(selectedOption);
    // set pill position using left border width, left spacing and number of gaps until the current pill
    setInitialPosition(getPillPosition(currentIndex, pillWidth));
  };

  useEffect(() => {
    if (!ref.current) return;

    const resizeObserver = new ResizeObserver(() => {
      if (ref?.current) {
        const currentWidth = ref.current ? ref.current.offsetWidth : 0;

        setPillWidth(calculatedPillWidth(currentWidth, options.length));
      }
    });

    resizeObserver.observe(ref.current);
    return () => resizeObserver.disconnect(); // clean up
  }, [options.length]);

  // Update prevIndexRef for the next render
  prevIndexRef.current = currentIndex;

  return (
    <Box
      position="relative"
      display="flex"
      gap={1}
      p={1}
      bgcolor={color.background.surface.primary}
      border={`1px solid ${color.border.default}`}
      borderRadius={RADIUS_FULL_PX}
      width="100%"
      ref={ref}
    >
      <motion.div
        key={currentIndex}
        initial={{ x: initialPosition }}
        animate={{ x: getPillPosition(currentIndex, pillWidth) }}
        transition={{
          type: "spring",
          stiffness: 300,
          damping: 30,
        }}
        style={{
          position: "absolute",
          backgroundColor: color.background.action.default,
          zIndex: 1,
          width: pillWidth,
          ...pillCss,
        }}
      />

      {options.map((item) => (
        <motion.div
          style={{
            position: "relative",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            cursor: "pointer",
            userSelect: "none",
            backgroundColor: color.background.surface.primary,
            width: pillWidth,
            ...pillCss,
          }}
          whileHover={{ backgroundColor: color.background.surface.secondary }}
          transition={{
            duration: 0.3,
            ease: "easeInOut",
          }}
          key={item}
          tabIndex={0}
          onClick={() => selectOption(item)}
          onKeyDown={({ key }) => key === "Enter" && selectOption(item)}
          role="radio"
          aria-checked={item === value}
        >
          <Typography variant="action" color={color.text.strong} tabIndex={-1} zIndex={2}>
            {item}
          </Typography>
        </motion.div>
      ))}
    </Box>
  );
};
