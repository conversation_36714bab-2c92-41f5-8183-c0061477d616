import { Box } from "@mui/material";
import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import { useArgs } from "storybook/preview-api";

import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import { SwitchSelect } from "./SwitchSelect";

const meta: Meta<typeof SwitchSelect> = {
  title: "@Components/SwitchSelect",
  component: SwitchSelect,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
  decorators: [
    ...cylinderThemeDecorator,
    function Component(Story, ctx) {
      const [, setArgs] = useArgs<typeof ctx.args>();
      const onValueChange = (value: string) => {
        console.log("Setting cadence to: ", value);
        setArgs({ value });
      };

      return (
        <Box width={ctx.parameters.width}>
          <Story args={{ ...ctx.args, onChange: onValueChange }} />
        </Box>
      );
    },
  ],
};

export default meta;
type Story = StoryObj<typeof SwitchSelect>;

export const SwitchSelectDefault: Story = {
  args: {
    options: ["Daily", "Weekly"],
    value: "Daily",
  },
  parameters: {
    width: "350px",
    layout: "centered",
  },
};

export const SwitchSelectMultiple: Story = {
  args: {
    options: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"],
    value: "3",
  },
};
