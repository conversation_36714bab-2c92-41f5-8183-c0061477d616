import React from "react";
import { FormControlLabel, Radio, SxProps, Theme } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { RADIUS_16_PX } from "@Assets/style_constants";

type RadioButtonOptionProps = Readonly<{
  isSelected: boolean;
  isDisabled?: boolean;
  style?: SxProps<Theme>;
  onClick: () => void;
  children: string;
}>;

export function RadioButtonOption({ isSelected, isDisabled, style, onClick, children }: RadioButtonOptionProps) {
  return (
    <FormControlLabel
      value={children}
      control={
        <Radio
          size="small"
          sx={{ mr: 2 }}
          inputProps={{ "aria-label": children }}
          disableRipple
          disabled={isDisabled}
          checked={isSelected}
          value={children}
          onClick={onClick}
        />
      }
      label={children}
      componentsProps={{ typography: { tabIndex: -1 } }}
      sx={{
        px: 4,
        py: 3,
        m: 0,
        width: "100%",
        border: `1px solid ${color.border.default}`,
        borderRadius: RADIUS_16_PX,
        ":hover": { borderColor: color.border.action.brand },
        ...style,
      }}
    />
  );
}
