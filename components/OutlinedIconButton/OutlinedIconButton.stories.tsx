import type { Meta, StoryObj } from "@storybook/nextjs";

import { OutlinedIconButton } from "./OutlinedIconButton";
import { cylinderThemeDecorator } from "../stories/cylinderThemeDecorator";

const meta: Meta<typeof OutlinedIconButton> = {
  title: "@Components/OutlinedIconButton",
  component: OutlinedIconButton,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: cylinderThemeDecorator,
};

export default meta;
type Story = StoryObj<typeof OutlinedIconButton>;

export const Primary: Story = {
  args: {
    icon: "Close",
    onClick: () => alert("clicked"),
  },
};
