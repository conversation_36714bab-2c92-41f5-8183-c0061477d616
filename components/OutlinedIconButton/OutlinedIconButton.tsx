import { forwardRef, ForwardedRef } from "react";
import { IconButton, SxProps } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";

import { AppIcon, IconVariant } from "../AppIcon/AppIcon";

type OutlinedIconButtonProps = Readonly<{
  icon: IconVariant;
  ariaLabel?: string;
  onClick: () => void;
  sx?: SxProps;
  disabled?: boolean;
  tabIndex?: number;
}>;

// some parent MUI elements needs refs to be passed down
export const OutlinedIconButton = forwardRef(function CustomIconButton(
  props: OutlinedIconButtonProps,
  ref: ForwardedRef<HTMLButtonElement>,
) {
  const { sx, ariaLabel, onClick, icon, disabled, tabIndex, ...otherProps } = props;

  return (
    <IconButton
      {...otherProps}
      onClick={onClick}
      aria-label={ariaLabel ?? appStrings.a11y.closeModal}
      ref={ref}
      tabIndex={tabIndex}
      sx={{
        border: `1px solid ${color.border.action.default}`,
        p: 2,
        "&:hover": {
          backgroundColor: "transparent",
          border: `1px solid ${color.border.action.hover}`,
          color: color.text.action.default,
        },
        "&.Mui-disabled": {
          backgroundColor: color.background.action.disabled,
          border: `1px solid ${color.background.action.disabled}`,
          pointerEvents: "all",
        },
        ...sx,
      }}
      disabled={disabled}
    >
      <AppIcon
        name={icon}
        size="sm"
        color={color.icon.strong}
        svgStyles={{ outline: "none" }}
        includeContainer={false}
      />
    </IconButton>
  );
});
