import type { Meta, StoryObj } from "@storybook/nextjs";
import { useArgs } from "storybook/preview-api";

import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import { DaySelect } from "./DaySelect";

const meta: Meta<typeof DaySelect> = {
  title: "@Components/DaySelect",
  component: DaySelect,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: cylinderThemeDecorator,
};

export default meta;
type Story = StoryObj<typeof DaySelect>;

export const DaySelectDefault: Story = {
  args: {
    selectedDays: new Set(["FRIDAY", "THURSDAY", "MONDAY"]),
  },
  render: function Component(args) {
    const [{ selectedDays }, setArgs] = useArgs();

    const onValueChange = (value: Set<string>) => {
      console.log("Setting selected days to: ", value);
      setArgs({ selectedDays: value });
    };

    return <DaySelect {...args} selectedDays={selectedDays} setSelectedDays={onValueChange} />;
  },
};
