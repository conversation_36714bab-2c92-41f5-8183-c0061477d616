import { Box, Chip } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { iconSize } from "@Assets/style_constants";

const DAY_STRINGS = appStrings.features.goalSetting.days;
const DAYS = new Map(Object.entries(DAY_STRINGS).map(([name, day]) => [name.toUpperCase(), day]));

type DaySelectProps = Readonly<{
  selectedDays: Set<string>;
  setSelectedDays: (selectedDays: Set<string>) => void;
}>;

export const DaySelect = ({ selectedDays, setSelectedDays }: DaySelectProps) => (
  <Box display="flex" gap={3}>
    {Array.from(DAYS.entries()).map(([day, label]) => {
      const isSelected = selectedDays.has(day);

      return (
        <Chip
          sx={{
            p: 0,
            ...iconSize.xl,
          }}
          size="small"
          key={day}
          label={label}
          variant={isSelected ? "active" : "inactive"}
          onClick={() => {
            isSelected ? selectedDays.delete(day) : selectedDays.add(day);

            setSelectedDays(new Set([...selectedDays]));
          }}
        />
      );
    })}
  </Box>
);
