import { useRef, useState } from "react";
import { ClickStreamActivityContextExtra, ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import styled from "@emotion/styled";
import { Dispatch } from "@reduxjs/toolkit";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { RADIUS_16_PX } from "@Assets/style_constants";
import { sendAnalytics } from "@Features/analytics/store/analyticsEpics";

type AudioPlayerProps = Readonly<{
  url: string;
  activityContextExtra?: ClickStreamActivityContextExtra;
  dispatch: Dispatch;
  onComplete?: () => void;
  onStart?: () => void;
}>;

const StyledAudio = styled.audio`
  width: 100%;
  &::-webkit-media-controls-enclosure {
    border-radius: ${RADIUS_16_PX};
  };
  &::-webkit-media-controls-panel {
    background-color: ${color.background.page};
    border-radius: none;
    
  };
  &::-webkit-media-controls-play-button {
    background-color: ${color.background.action.default};
    border-radius: 50%;
    &:hover {
      background-color: ${color.background.action.hover};
    };
  };
}
`;

export const AudioPlayer = ({ url, dispatch, activityContextExtra = {}, onComplete, onStart }: AudioPlayerProps) => {
  const playerRef = useRef<HTMLAudioElement | null>(null);
  const [isStarted, setIsStarted] = useState(false);

  const dispatchAnalytics = (eventType: ClickStreamActivityEventType) => {
    dispatch(
      sendAnalytics({
        eventType,
        activityContextExtra: {
          ...activityContextExtra,
          audioDuration: playerRef?.current?.duration,
          audioCurrentTime: playerRef?.current?.currentTime,
        },
      }),
    );
  };

  return (
    <StyledAudio
      ref={playerRef}
      controls
      controlsList="nodownload noplaybackrate"
      onPlay={() => {
        dispatchAnalytics(ClickStreamActivityEventType.AUDIO_PLAYED);
        if (!isStarted) {
          setIsStarted(true);
          if (onStart) onStart();
        }
      }}
      onSeeked={() => {
        dispatchAnalytics(ClickStreamActivityEventType.AUDIO_SKIPPED);
      }}
      onPause={() => {
        dispatchAnalytics(ClickStreamActivityEventType.AUDIO_PAUSED);
      }}
      onEnded={() => {
        if (onComplete) onComplete();

        dispatchAnalytics(ClickStreamActivityEventType.AUDIO_ENDED);
      }}
    >
      <source src={url} type="audio/mpeg" />
      {appStrings.audioComponent.noSupportMessage}
    </StyledAudio>
  );
};
