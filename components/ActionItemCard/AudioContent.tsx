import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ActionPlanTargetState } from "@vivantehealth/vivante-core";
import { Box, Button, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { AudioPlayer } from "@Components/AudioPlayer/AudioPlayer";
import { actionPlansStateSelector } from "@Features/carePlan/store/actionPlansStateSlice";
import { markActionInProgressIfQualifying, setActionNewState } from "@Features/carePlan/utils/markAsDone.util";
import { useCheckAndUpdateParentActionState } from "@Hooks/useCheckAndUpdateParentActionState";

import { actionItemContentContainerStyles, ContentProps } from "./ActionItemCard";

export const AudioContent = ({ driver }: ContentProps) => {
  const { title, state, id, targetId, linkUri } = driver;

  const [isCompleted, setIsCompleted] = useState(false);
  const targetEntitiesState = useSelector(actionPlansStateSelector("targetEntities"));

  const dispatch = useDispatch();
  const checkAndUpdateParentActionState = useCheckAndUpdateParentActionState();
  const onComplete = () => {
    setIsCompleted(true);

    // mark parent/target item as completed if this is the last uncompleted driver
    if (targetId) {
      checkAndUpdateParentActionState(targetId, id, ActionPlanTargetState.COMPLETED);
    }
    // mark this action as completed
    if (state) {
      setActionNewState(ActionPlanTargetState.COMPLETED, state, id);
    }
  };

  const onStart = () => {
    // Mark parent as in progress
    if (targetId) {
      markActionInProgressIfQualifying(targetId, targetEntitiesState[targetId]?.state);
    }
    // Mark action as in progress
    if (driver?.state) {
      markActionInProgressIfQualifying(id, driver.state);
    }
  };

  return (
    <Box
      sx={{
        ...actionItemContentContainerStyles,
        p: 4,
        flexDirection: "column",
      }}
    >
      <Box
        width={"100%"}
        pl={2}
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
        flexGrow={1}
      >
        <Typography variant="body" tabIndex={-1} color={color.text.strong}>
          {title}
        </Typography>
        <>
          {state === ActionPlanTargetState.COMPLETED || isCompleted ? (
            <AppIcon name="Completed" />
          ) : (
            <Button variant="secondary" size="small" onClick={onComplete}>
              {appStrings.features.carePlan.markDone}
            </Button>
          )}
        </>
      </Box>
      <AudioPlayer url={linkUri} dispatch={dispatch} onComplete={onComplete} onStart={onStart} />
    </Box>
  );
};
