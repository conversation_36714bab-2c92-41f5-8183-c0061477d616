import React from "react";
import { ActionPlanDriver, ActionPlanTargetState } from "@vivantehealth/vivante-core";
import { Box, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";

import { actionItemContentContainerStyles, ContentProps } from "./ActionItemCard";

export type GenericContentProps = Readonly<{
  driver: ActionPlanDriver;
}>;
const CARE_PLAN_STRINGS = appStrings.features.carePlan;

export const GenericContent = ({ driver }: ContentProps) => {
  const { title, state } = driver;

  return (
    <Box sx={actionItemContentContainerStyles}>
      <Box width={"100%"} px={2} display="grid" alignItems="center">
        <Typography variant="body" tabIndex={-1} color={color.text.strong}>
          {title}
        </Typography>
      </Box>
      <Box display="flex" flexDirection="column" alignItems="center">
        {state === ActionPlanTargetState.COMPLETED ? (
          <AppIcon name="Completed" />
        ) : (
          <Box display="flex" flexDirection="row" alignItems="center">
            {state === ActionPlanTargetState.STARTED ? (
              <Typography variant="body" noWrap>
                {CARE_PLAN_STRINGS.inProgress}
              </Typography>
            ) : null}
            <AppIcon name="RightChevron" size="md" containerStyles={{ flexShrink: 0, color: color.icon.action }} />
          </Box>
        )}
      </Box>
    </Box>
  );
};
