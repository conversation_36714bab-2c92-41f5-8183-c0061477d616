import { ActionPlanDriver } from "@vivantehealth/vivante-core";
import Paper from "@mui/material/Paper";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { SPACING_12_PX, SPACING_16_PX } from "@Assets/style_constants";

import { AudioContent } from "./AudioContent";
import { GenericContent } from "./GenericContent";

export type ContentProps = Readonly<{
  driver: ActionPlanDriver;
}>;

export type ActionItemCardProps = Readonly<{
  onClick?: () => void;
  driver: ActionPlanDriver;
}>;

export const actionItemContentContainerStyles = {
  display: "flex",
  width: "100%",
  height: "fit-content",
  padding: `${SPACING_12_PX} ${SPACING_16_PX}`,
  justifyContent: "center",
  boxSizing: "border-box",
  gap: 4,
  border: "none",
};

export const ActionItemCard = ({ onClick, driver }: ActionItemCardProps) => {
  const ariaLabel = `${appStrings.a11y.title(driver.title)}`;
  const isAudio = driver?.subtype === "audio";

  return (
    <Paper
      sx={{
        display: "block",
        width: "100%",
        p: 0,
        textAlign: "left",
        backgroundColor: "transparent",
        ...(!isAudio
          ? {
              cursor: "pointer",
              "&:hover": {
                borderColor: color.border.action.hover,
              },
            }
          : null),
      }}
      aria-label={ariaLabel}
      onClick={onClick}
      component={!isAudio ? "button" : "div"}
    >
      {isAudio ? <AudioContent driver={driver} /> : <GenericContent driver={driver} />}
    </Paper>
  );
};
