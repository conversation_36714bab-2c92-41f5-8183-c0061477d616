import { useState, useCallback, useEffect, ReactNode } from "react";
import { Box, Skeleton } from "@mui/material";
import useEmblaCarousel from "embla-carousel-react";

import { appStrings } from "@Assets/app_strings";
import { OutlinedIconButton } from "@Components/OutlinedIconButton/OutlinedIconButton";

const ICON_BUTTON_WIDTH = 34;
/** The button itself is 34px wide and we add 8px for spacing */
const LEFT_CHEVRON_OFFSET = -(ICON_BUTTON_WIDTH + 8);

type CarouselProps = Readonly<{
  /** Number of Carousel items to scroll when Previous/Next button are clicked */
  numberOfItemsToScroll: number;
  /** The CSS top value to utilize for positioning of the left button */
  leftBtnOffset: number;
  /** The CSS top value to utilize for positioning of the right button */
  rightBtnOffset: number;
  /** If true, will show skeleton loaders for the buttons */
  isLoading: boolean;
  /** The Carousel contents to scroll through */
  children: ReactNode;
}>;
/**
 * Generic carousel component that wraps the Embla carousel library.
 * It provides a scrollable view of items with previous and next buttons.
 */
export const Carousel = ({
  numberOfItemsToScroll,
  leftBtnOffset,
  rightBtnOffset,
  isLoading,
  children,
}: CarouselProps) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({ slidesToScroll: numberOfItemsToScroll });
  const [disabledAction, setDisabledAction] = useState<"PREVIOUS" | "NEXT" | undefined>("PREVIOUS");

  const handleActionState = useCallback(() => {
    if (!emblaApi?.canScrollPrev()) return setDisabledAction("PREVIOUS");

    if (!emblaApi?.canScrollNext()) return setDisabledAction("NEXT");

    if (disabledAction !== undefined) return setDisabledAction(undefined);
  }, [emblaApi, disabledAction]);

  useEffect(() => {
    // Listen for slide events to update the disabled action state
    emblaApi?.on("slidesInView", handleActionState);

    return () => {
      emblaApi?.off("slidesInView", handleActionState);
    };
  }, [emblaApi, handleActionState]);

  const handlePrevious = useCallback(() => {
    if (emblaApi) {
      emblaApi.scrollPrev();
    }
  }, [emblaApi]);

  const handleNext = useCallback(() => {
    if (emblaApi) {
      emblaApi.scrollNext();
    }
  }, [emblaApi]);

  return (
    <>
      <Box
        position="relative"
        top={leftBtnOffset}
        left={LEFT_CHEVRON_OFFSET}
        width={ICON_BUTTON_WIDTH}
        mt={`-${ICON_BUTTON_WIDTH}px`}
      >
        {isLoading ? (
          <Skeleton variant="circular" height={ICON_BUTTON_WIDTH} />
        ) : (
          <OutlinedIconButton
            icon="LeftChevron"
            ariaLabel={appStrings.buttonText.previous}
            onClick={handlePrevious}
            disabled={disabledAction === "PREVIOUS"}
          />
        )}
      </Box>

      <Box ref={emblaRef} overflow="hidden">
        {children}
      </Box>

      <Box
        position="relative"
        top={rightBtnOffset}
        left={`calc(100% + 8px)`}
        width={ICON_BUTTON_WIDTH}
        mb={`-${ICON_BUTTON_WIDTH}px`}
      >
        {isLoading ? (
          <Skeleton variant="circular" height={ICON_BUTTON_WIDTH} />
        ) : (
          <OutlinedIconButton
            icon="RightChevron"
            ariaLabel={appStrings.buttonText.next}
            onClick={handleNext}
            disabled={disabledAction === "NEXT"}
          />
        )}
      </Box>
    </>
  );
};
