import { Grid } from "@mui/material";

import { AppIcon } from "@Components/AppIcon/AppIcon";

type AuthFormContainerProps = Readonly<{
  children: React.ReactNode;
}>;

export const AuthFormContainer = ({ children }: AuthFormContainerProps) => {
  return (
    <Grid
      item
      md={4}
      sm={6}
      xs={12}
      pt={8}
      pb={2}
      px={2}
      mt={5}
      display="flex"
      flexDirection="column"
      alignItems="center"
    >
      <Grid item mb={6}>
        <AppIcon name="CompanyLogo" size="logo" includeInTabIndex />
      </Grid>
      {children}
    </Grid>
  );
};
