import type { <PERSON>a, StoryObj } from "@storybook/nextjs";

import { AutocompleteDropdown } from "./AutocompleteDropdown";
import { cylinderThemeDecorator } from "../stories/cylinderThemeDecorator";

const meta: Meta<typeof AutocompleteDropdown> = {
  title: "@Components/AutcompleteDropdown",
  component: AutocompleteDropdown,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    ...cylinderThemeDecorator,
    (Story) => (
      <div style={{ width: "300px" }}>
        To note: this does not function as it would in a form due to a lack of form controller
        <Story />
      </div>
    ),
  ],
};

type Story = StoryObj<typeof AutocompleteDropdown>;

const options = [
  { id: "00520", label: "Blue Advantage/BCBS Arkansas" },
  { id: "AETNA", label: "Aetna" },
  { id: "UMR", label: "UMR/UnitedHealthcare" },
];

const mockField = {
  // eslint-disable-next-line no-alert
  onChange: (value: string) => alert(JSON.stringify(value)),
  onBlur: () => {},
  ref: (ref: unknown) => ref,
  name: "mockName",
  value: "mockValue",
};

const genericArgs = {
  options,
  label: "payer",
  required: true,
  field: mockField,
};

export const Primary: Story = {
  args: genericArgs,
};

export const WithPlaceholder: Story = {
  args: {
    ...genericArgs,
    placeholder: "placeholder",
  },
  decorators: [
    (Story) => (
      <div style={{ width: "200px" }}>
        <Story />
      </div>
    ),
  ],
};

export const Error: Story = {
  args: {
    ...genericArgs,
    error: "AHHHH!!! Something is definitely wrong!",
  },
};

export default meta;
