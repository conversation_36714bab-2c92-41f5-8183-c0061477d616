import React, { SyntheticEvent } from "react";
import { Autocomplete, InputLabel, TextField } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { FieldValues } from "react-hook-form";

import { AppIcon } from "@Components/AppIcon/AppIcon";
import { sentenceCaseLabel } from "@Utils/sentenceCaseLabel";

export type DropdownOptionType = Readonly<{
  id: string;
  label: string;
}>;
type AutocompleteDropdownProps = Readonly<{
  options: DropdownOptionType[];
  label: string;
  required: boolean;
  placeholder?: string;
  error: string;
  field: FieldValues;
  id: string;
}>;

export const AutocompleteDropdown = ({
  options,
  label,
  required,
  placeholder,
  error,
  field,
  id,
}: AutocompleteDropdownProps) => {
  const handleOnChange = (_: SyntheticEvent<Element, Event>, data: Readonly<{ id: string; label: string } | null>) => {
    // submit the id of the selected option if it exists, otherwise submit the entire object, or null if data is empty
    const dataValue = data?.id ?? data;

    field.onChange(dataValue);
  };

  const selectedOption = options.find((option) => [field.value, field.value?.id].includes(option.id)) || null;

  return (
    <>
      <InputLabel htmlFor={id} error={!!error} required={required}>
        {sentenceCaseLabel(label)}
      </InputLabel>
      <Autocomplete
        options={options}
        id={id}
        value={selectedOption}
        isOptionEqualToValue={(option, value) => option.id === value.id}
        getOptionLabel={(option: DropdownOptionType) => option.label || ""}
        onChange={handleOnChange}
        fullWidth
        popupIcon={<AppIcon name="Search" color={error ? color.text.input.error : color.icon.strong} />}
        renderInput={(params) => (
          <TextField
            {...params}
            required={required}
            error={!!error}
            helperText={error}
            placeholder={placeholder}
            inputProps={{
              ...params.inputProps,
              "aria-label": label,
            }}
          />
        )}
      />
    </>
  );
};
