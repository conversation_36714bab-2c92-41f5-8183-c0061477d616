import { Backdrop, Box, CircularProgress } from "@mui/material";

import { useResponsiveStylesHook } from "@Hooks/responsiveStylesHook";
import { useHeaderHook } from "@Hooks/useHeaderHook";

export const LoadingSpinner = ({
  open = false,
  overlay = true,
  customStyles,
  backdropCustomStyles,
  overlayDrawer = false,
  overlayHeader = false,
}: {
  open: boolean;
  overlay?: boolean;
  customStyles?: React.CSSProperties;
  backdropCustomStyles?: React.CSSProperties;
  overlayDrawer?: boolean;
  overlayHeader?: boolean;
}) => {
  const { drawerWidth } = useResponsiveStylesHook();
  const { topHeight } = useHeaderHook();

  return (
    <Box>
      {overlay ? (
        <Backdrop
          sx={{
            ...styles.backdrop,
            left: overlayDrawer ? 0 : drawerWidth,
            top: overlayHeader ? 0 : topHeight,
            ...backdropCustomStyles,
          }}
          open={open}
        >
          <SpinnerContent />
        </Backdrop>
      ) : (
        <Box sx={{ ...styles.container, ...customStyles }}>
          <SpinnerContent />
        </Box>
      )}
    </Box>
  );
};

const SpinnerContent = () => <CircularProgress thickness={2} size={80} sx={{ mb: 2 }} />;

const styles = {
  backdrop: {
    display: "flex",
    flexDirection: "column",
    background: "rgba(246, 247, 244, 0.9)",
    zIndex: 100,
  },
  container: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    position: "absolute",
    top: "45vh",
  },
} as const;
