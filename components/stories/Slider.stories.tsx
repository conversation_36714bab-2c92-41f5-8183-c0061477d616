import { Slider } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "./cylinderThemeDecorator";

const meta: Meta<typeof Slider> = {
  decorators: [
    ...cylinderThemeDecorator,
    (Story) => (
      <div style={{ width: "300px" }}>
        <Story />
      </div>
    ),
  ],
  title: "@Components/Slider",
  component: Slider,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {},
};

export default meta;
type Story = StoryObj<typeof Slider>;

const marks = [
  { value: 0, label: "0" },
  { value: 20, label: "20" },
  { value: 40, label: "40" },
  { value: 60, label: "60" },
  { value: 80, label: "80" },
  { value: 100, label: "100" },
];

export const Primary: Story = {
  args: { defaultValue: 0, marks, min: 0, max: 100, step: 20, title: "Slider" },
};
