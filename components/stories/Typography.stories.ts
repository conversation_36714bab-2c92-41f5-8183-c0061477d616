import { Typography } from "@mui/material";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "./cylinderThemeDecorator";

const meta: Meta<typeof Typography> = {
  decorators: cylinderThemeDecorator,
  title: "@Components/Typography",
  component: Typography,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: [
        "h1",
        "h1Serif",
        "h2",
        "h2Serif",
        "h3",
        "h3Serif",
        "h4",
        "body",
        "bodyDense",
        "link",
        "linkDense",
        "action",
        "actionDense",
        "caption",
      ],
      defaultValue: "h1",
    },
  },
};

export default meta;
type Story = StoryObj<typeof Typography>;

export const All: Story = {
  args: {
    children: "The quick brown fox jumps over the lazy dog",
    variant: "h1",
  },
};

export const Heading1: Story = {
  args: {
    children: "This is a heading 1",
    variant: "h1",
  },
  argTypes: {
    variant: { table: { disable: true } },
  },
};

export const Heading1Serif: Story = {
  args: {
    children: "This is a heading 1 Serif",
    variant: "h1Serif",
  },
  argTypes: {
    variant: { table: { disable: true } },
  },
};

export const Heading2: Story = {
  args: {
    children: "This is a heading 2",
    variant: "h2",
  },
  argTypes: {
    variant: { table: { disable: true } },
  },
};

export const Heading2Serif: Story = {
  args: {
    children: "This is a heading 2 Serif",
    variant: "h2Serif",
  },
  argTypes: {
    variant: { table: { disable: true } },
  },
};

export const Heading3: Story = {
  args: {
    children: "This is a heading 3",
    variant: "h3",
  },
  argTypes: {
    variant: { table: { disable: true } },
  },
};

export const Heading3Serif: Story = {
  args: {
    children: "This is a heading 3 Serif",
    variant: "h3Serif",
  },
  argTypes: {
    variant: { table: { disable: true } },
  },
};

export const Heading4: Story = {
  args: {
    children: "This is a heading 4",
    variant: "h4",
  },
  argTypes: {
    variant: { table: { disable: true } },
  },
};

export const Body: Story = {
  args: {
    children: "This is a body text",
    variant: "body",
  },
  argTypes: {
    variant: { table: { disable: true } },
  },
};

export const BodyDense: Story = {
  args: {
    children: "This is a body dense text",
    variant: "bodyDense",
  },
  argTypes: {
    variant: { table: { disable: true } },
  },
};

export const Link: Story = {
  args: {
    children: "This is a link text",
    variant: "link",
  },
  argTypes: {
    variant: { table: { disable: true } },
  },
};

export const LinkDense: Story = {
  args: {
    children: "This is a link dense text",
    variant: "linkDense",
  },
  argTypes: {
    variant: { table: { disable: true } },
  },
};

export const Action: Story = {
  args: {
    children: "This is an action text",
    variant: "action",
  },
  argTypes: {
    variant: { table: { disable: true } },
  },
};

export const ActionDense: Story = {
  args: {
    children: "This is an action dense text",
    variant: "actionDense",
  },
  argTypes: {
    variant: { table: { disable: true } },
  },
};

export const Caption: Story = {
  args: {
    children: "This is a caption",
    variant: "caption",
  },
  argTypes: {
    variant: { table: { disable: true } },
  },
};
