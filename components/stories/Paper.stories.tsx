import { Paper } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "./cylinderThemeDecorator";

const meta: Meta<typeof Paper> = {
  decorators: cylinderThemeDecorator,
  title: "@Components/Paper",
  component: Paper,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    elevation: { control: "number", defaultValue: 0 },
  },
};

export default meta;
type Story = StoryObj<typeof Paper>;

export const Primary: Story = {
  args: { elevation: 0, children: "This is a paper component. It has padding: '24px 16px' applied" },
};
