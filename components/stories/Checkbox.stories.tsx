import { Checkbox } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "./cylinderThemeDecorator";

const meta: Meta<typeof Checkbox> = {
  decorators: cylinderThemeDecorator,
  title: "@Components/Checkbox",
  component: Checkbox,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {},
};

export default meta;
type Story = StoryObj<typeof Checkbox>;

export const Primary: Story = {
  args: { size: "small" },
};
