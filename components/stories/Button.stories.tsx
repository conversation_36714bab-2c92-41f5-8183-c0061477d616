import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { CircularProgress } from "@mui/material";
import { Button } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "./cylinderThemeDecorator";

const meta: Meta<typeof Button> = {
  decorators: cylinderThemeDecorator,
  title: "@Components/Button",
  component: Button,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    size: { control: "select", options: ["small", "medium"], defaultValue: "medium" },
    variant: {
      control: "select",
      options: ["primary", "secondary", "tertiary", "ghost", "intakePrimary", "intakeSecondary"],
      defaultValue: "primary",
    },
  },
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Default: Story = {
  args: {
    children: "I am a button",
    variant: "primary",
    disabled: false,
    size: "medium",
  },
};

export const PrimaryProgress: Story = {
  args: {
    children: <CircularProgress thickness={4} size={16} color="inherit" />,
    disabled: true,
  },
  argTypes: {
    children: { table: { disable: true } },
  },
};

export const WithIcons: Story = {
  args: {
    variant: "primary",
    disabled: false,
    size: "small",
    children: "Button icon",
    startIcon: <ChevronLeftIcon />,
    endIcon: <ChevronRightIcon />,
  },
  argTypes: {
    startIcon: {
      control: "radio",
      options: ["None", "ChevronLeftIcon"],
      mapping: { None: undefined, ChevronLeftIcon: <ChevronLeftIcon /> },
      defaultValue: "ChevronLeftIcon",
    },
    endIcon: {
      control: "radio",
      options: ["None", "ChevronRightIcon"],
      mapping: { None: undefined, ChevronRightIcon: <ChevronRightIcon /> },
      defaultValue: "ChevronRightIcon",
    },
  },
};
