import { Chip } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "./cylinderThemeDecorator";
import { AppIcon } from "../AppIcon/AppIcon";

const meta: Meta<typeof Chip> = {
  decorators: cylinderThemeDecorator,
  title: "@Components/Chip",
  component: Chip,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {},
};

export default meta;
type Story = StoryObj<typeof Chip>;

export const Alternative: Story = {
  args: { label: "alternative", onClick: () => {} },
};

export const Active: Story = {
  args: { variant: "active", label: "active", onClick: () => {} },
};

export const Inactive: Story = {
  args: { variant: "inactive", label: "inactive", onClick: () => {} },
};

export const Disabled: Story = {
  args: { variant: "active", label: "disabled", disabled: true, onClick: () => {} },
};

export const ActiveSmall: Story = {
  args: { variant: "active", label: "active", size: "small", onClick: () => {} },
};

export const InactiveSmall: Story = {
  args: { variant: "inactive", label: "inactive", size: "small", onClick: () => {} },
};

export const DisabledSmall: Story = {
  args: { variant: "active", label: "disabled", size: "small", disabled: true, onClick: () => {} },
};

export const Enrollment: Story = {
  args: { label: "enrollment", icon: <AppIcon name="Completed" />, variant: "enrollment" },
};

export const Time: Story = {
  args: { variant: "time", label: "9:00 am", onClick: () => {} },
};

export const TimeDisabled: Story = {
  args: { variant: "time", label: "9:00 am", disabled: true, onClick: () => {} },
};
