import Tab from "@mui/material/Tab";
import Tabs from "@mui/material/Tabs";
import type { Meta, StoryObj } from "@storybook/nextjs";
import { useArgs } from "storybook/preview-api";

import { cylinderThemeDecorator } from "./cylinderThemeDecorator";

const meta: Meta<typeof Tabs> = {
  component: Tabs,
  title: "@Components/Tabs",
  decorators: cylinderThemeDecorator,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
  argTypes: {
    value: { control: "select", options: ["1", "2", "3"], defaultValue: "1" },
  },
};

export default meta;
type Story = StoryObj<typeof Tabs>;

export const Default: Story = {
  args: {
    value: "1",
  },
  render: function Render() {
    const [args, updateArgs] = useArgs();

    function onChange(event: React.SyntheticEvent, newValue: string) {
      updateArgs({ value: newValue });
    }

    return (
      <Tabs {...args} onChange={onChange}>
        <Tab key="value-1" value="1" label="Value One" />
        <Tab key="value-2" value="2" label="Value Two" />
        <Tab key="value-3" value="3" label="Value Three" />
      </Tabs>
    );
  },
};

export const DisabledExample: Story = {
  args: {
    children: [
      <Tab key="value-1" value="1" label="Active" />,
      <Tab key="value-2" value="2" label="Disabled" disabled />,
      <Tab key="value-3" value="3" label="Not Active" />,
    ],
    value: "1",
  },
};
