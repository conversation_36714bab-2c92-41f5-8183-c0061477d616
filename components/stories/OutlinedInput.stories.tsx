import { OutlinedInput } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/nextjs";

import { cylinderThemeDecorator } from "./cylinderThemeDecorator";

const meta: Meta<typeof OutlinedInput> = {
  decorators: cylinderThemeDecorator,
  title: "@Components/OutlinedInput",
  component: OutlinedInput,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    placeholder: { control: "text", defaultValue: "Placeholder text" },
    value: { control: "text", defaultValue: "" },
    disabled: { control: "boolean", defaultValue: false },
    error: { control: "boolean", defaultValue: false },
  },
};

export default meta;
type Story = StoryObj<typeof OutlinedInput>;

export const InputWithPlaceholder: Story = {
  args: { placeholder: "Placeholder text" },
};

export const InputWithText: Story = {
  args: { value: "Cylinder health" },
};

export const DisabledState: Story = {
  args: { value: "Disabled state", disabled: true },
};

export const ErrorState: Story = {
  args: { value: "Error state", error: true },
};
