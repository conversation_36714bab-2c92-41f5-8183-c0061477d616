import { useRouter } from "next/router";

import { useFirebaseAuth } from "@Hooks/firebaseAuthHook";
import { Routes } from "@Types";

import { LoadingSpinner } from "../LoadingSpinner/LoadingSpinner";

type ProtectedRouteProps = Readonly<{
  children: React.ReactNode;
  path?: Routes;
}>;

export const ProtectedRoute = ({ children, path }: ProtectedRouteProps) => {
  const router = useRouter();
  const { isFirebaseAuthLoading, isAuthenticated, checkAuthorization } = useFirebaseAuth();

  if (isFirebaseAuthLoading) {
    return <LoadingSpinner open overlayDrawer overlayHeader />;
  }

  if (!isAuthenticated) {
    router.push(Routes.WELCOME);
    return null;
  }

  const authorization = checkAuthorization(path);

  if (!authorization.isAuthorized) {
    router.push(authorization.redirect ?? Routes.WELCOME);
    return null;
  }

  return <div>{children}</div>;
};
