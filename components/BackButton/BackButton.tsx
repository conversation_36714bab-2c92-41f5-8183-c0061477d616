import { Button } from "@mui/material";

import { AppIcon } from "@Components/AppIcon/AppIcon";

type BackButtonProps = Readonly<{
  onClick: () => void;
  children: React.ReactNode;
  ariaLabel?: string;
}>;

export const BackButton = ({ children, ariaLabel, onClick }: BackButtonProps) => {
  return (
    <Button
      variant="secondary"
      onClick={onClick}
      aria-label={`${ariaLabel || String(children)}.`}
      startIcon={<AppIcon name="LeftChevron" />}
    >
      {children}
    </Button>
  );
};
