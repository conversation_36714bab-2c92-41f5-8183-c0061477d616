import { Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";

const APPOINTMENT_GET_READY_STRINGS = appStrings.features.scheduling.appointmentConfirmation;

type AppointmentGetReadyProps = Readonly<{
  headingSize: "h3" | "h4";
  excludeMedicalHistoryNote?: boolean;
  includePhoneNote?: boolean;
}>;

const getBulletPoints = (excludeMedicalHistoryNote: boolean, includePhoneNote: boolean) => {
  const phoneNoteEndIndex = APPOINTMENT_GET_READY_STRINGS.getReadyBulletPoints.length - 1;

  if (excludeMedicalHistoryNote && includePhoneNote) {
    return APPOINTMENT_GET_READY_STRINGS.getReadyBulletPoints.slice(2);
  }

  if (!excludeMedicalHistoryNote && !includePhoneNote) {
    return APPOINTMENT_GET_READY_STRINGS.getReadyBulletPoints.slice(0, phoneNoteEndIndex);
  }

  if (excludeMedicalHistoryNote) {
    return APPOINTMENT_GET_READY_STRINGS.getReadyBulletPoints.slice(1, phoneNoteEndIndex);
  }

  return APPOINTMENT_GET_READY_STRINGS.getReadyBulletPoints;
};

export const AppointmentGetReady = ({
  headingSize,
  excludeMedicalHistoryNote = false,
  includePhoneNote = false,
}: AppointmentGetReadyProps) => {
  const bulletPoints = getBulletPoints(excludeMedicalHistoryNote, includePhoneNote);

  return (
    <>
      <Typography variant={headingSize} color={color.text.strong}>
        {APPOINTMENT_GET_READY_STRINGS.getReady}
      </Typography>

      <ul>
        {bulletPoints.map((bulletPoint) => (
          <li key={bulletPoint}>
            <Typography variant="body">{bulletPoint}</Typography>
          </li>
        ))}
      </ul>
    </>
  );
};
