import { SxProps, Theme, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

type ErrorTextProps = Readonly<{
  errorMessage: string;
  errorTextRef?: React.RefObject<HTMLDivElement | null>;
  sx?: SxProps<Theme>;
}>;

export const ErrorText = ({ errorMessage, errorTextRef, sx }: ErrorTextProps) => (
  <Typography
    variant="bodyDense"
    sx={{ color: color.text.input.error, ...sx }}
    aria-label={`Error: ${errorMessage}.`}
    ref={errorTextRef}
  >
    {errorMessage}
  </Typography>
);
