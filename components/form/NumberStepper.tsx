import { Change<PERSON><PERSON>, <PERSON>er<PERSON><PERSON>, FocusEvent, KeyboardEvent, forwardRef } from "react";
import { Unstable_NumberInput as BaseNumberInput, NumberInputProps } from "@mui/base/Unstable_NumberInput";
import { styled } from "@mui/material";
import { color, typography } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { RADIUS_12_PX, SPACING_12_PX } from "@Assets/style_constants";

import { OutlinedIconButton } from "../OutlinedIconButton/OutlinedIconButton";

type StepperNumberFor = "Feet" | "Inches";

const NumberInput = forwardRef(function CustomNumberInput(
  props: NumberInputProps & { numberfor?: StepperNumberFor },
  ref: React.ForwardedRef<HTMLDivElement>,
) {
  return (
    <BaseNumberInput
      slots={{
        input: StyledInput,
        incrementButton: (btnProps) => (
          <OutlinedIconButton
            icon="Plus"
            {...btnProps}
            tabIndex={0}
            sx={{ order: 2 }}
            ariaLabel={appStrings.a11y.increaseValue(props.numberfor)}
          />
        ),
        decrementButton: (btnProps) => (
          <OutlinedIconButton
            icon="Minus"
            {...btnProps}
            tabIndex={0}
            ariaLabel={appStrings.a11y.decreaseValue(props.numberfor)}
          />
        ),
      }}
      {...props}
      ref={ref}
      slotProps={{
        input: {
          style: {
            ...typography.body,
            borderRadius: RADIUS_12_PX,
          },
        },
      }}
      style={{
        display: "flex",
        gap: SPACING_12_PX,
        alignItems: "center",
      }}
    />
  );
});

type NumberStepperProps = Readonly<{
  value: number | string | null;
  minimum: number;
  maximum: number;
  ariaLabel: React.ComponentProps<"button">["aria-label"];
  customStyles?: React.CSSProperties;
  autoFocus?: boolean;
  onChange: (
    event: FocusEvent<HTMLInputElement, Element> | PointerEvent<Element> | KeyboardEvent<Element>,
    val: number | null,
  ) => void;
  onInputChange: (event: ChangeEvent<HTMLInputElement>) => void;
  numberFor?: StepperNumberFor;
}>;

export const NumberStepper = ({
  value,
  minimum,
  maximum,
  ariaLabel,
  customStyles,
  autoFocus,
  onChange,
  onInputChange,
  numberFor,
}: NumberStepperProps) => {
  return (
    <NumberInput
      aria-label={ariaLabel}
      value={Number(value)}
      min={minimum}
      max={maximum}
      onChange={onChange}
      onInputChange={onInputChange}
      style={customStyles}
      autoFocus={autoFocus}
      numberfor={numberFor}
    />
  );
};

const StyledInput = styled("input")(
  `
  border: 1px solid ${color.border.input.default};
  backgroundColor: color.background.input.default;
  padding: ${SPACING_12_PX};
  width: 100%;

    &:hover {
      border-color: ${color.border.input.focused};
    }

    &:focus {
      border-color: ${color.border.input.focused};
    }

    &:focus-visible {
      outline: 0;
    }
`,
);
