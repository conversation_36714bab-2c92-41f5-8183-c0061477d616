import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";

import { CheckboxField } from "./CheckboxField";
import { cylinderThemeDecorator } from "../stories/cylinderThemeDecorator";

const meta: Meta<typeof CheckboxField> = {
  decorators: cylinderThemeDecorator,
  title: "@Components/form/CheckboxField",
  component: CheckboxField,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof CheckboxField>;

export const Primary: Story = {
  args: {
    name: "Checkbox",
    ariaLabel: "test",
    required: true,
  },
};
