import React, { CSSProperties, ChangeEvent } from "react";
import {
  SelectChangeEvent,
  Grid,
  FormControl,
  Select,
  MenuItem,
  TextField,
  FormHelperText,
  InputLabel,
  Box,
} from "@mui/material";
import { Dayjs } from "dayjs";
import { Controller, useFormContext } from "react-hook-form";

import { appStrings } from "@Assets/app_strings";
import { sentenceCaseLabel } from "@Utils/sentenceCaseLabel";

import { CheckboxField } from "./CheckboxField";
import { DateTimeField } from "./DateTimeField";
import { FormDaySelect } from "./fields/FormDaySelect";
import { FormSwitchSelect } from "./fields/FormSwitchSelect";
import { FormTextArea } from "./fields/FormTextArea";
import { FormFieldProperties } from "./FormFields";
import { BirthDateValue, birthDateInputValidation, shouldDisplayBirthdateErrorMessage } from "./utils/birthDate.utils";
import { AutocompleteDropdown, DropdownOptionType } from "../AutocompleteDropdown/AutocompleteDropdown";
import { InputMode, InputType, InputWithLabel } from "../InputWithLabel/InputWithLabel";

type WatchData = {
  inputName: string;
  message: string;
};

type Rule = ((...args: unknown[]) => void) | Record<string, string | RegExp | boolean | number>;

export type Rules = Record<string, Rule>;

type FormInput = Readonly<{
  label?: string;
  name: string;
  id?: string;
  required?: boolean;
  type: InputType;
  numberOfRows?: number;
  placeholder?: string;
  isOptional?: boolean;
  styles?: CSSProperties;
  onBlur?: (value: string) => void;
  onChange?: (value: string) => void;
  rules?: Rules & {
    watchData?: WatchData;
  };
  helperText?: string;
  inputMode?: InputMode;
  pattern?: string;
}>;

export const FormInput = ({
  name,
  id,
  label = "",
  required,
  type = "text",
  numberOfRows,
  rules,
  placeholder,
  isOptional,
  styles,
  onBlur,
  onChange,
  helperText,
  inputMode,
  pattern,
}: FormInput) => {
  const {
    register,
    formState: { errors },
    watch,
    setValue,
  } = useFormContext();

  const fullRules = {
    ...rules,
    ...(rules?.watchData && {
      validate: (value: string) => value === watch(rules.watchData?.inputName ?? "") || rules.watchData?.message,
    }),
    onChange: (event: ChangeEvent<HTMLInputElement>) => {
      if (onChange) {
        setValue(name, onChange(event.target.value));
      }
    },
    onBlur: (event: ChangeEvent<HTMLInputElement>) => {
      if (onBlur) {
        setValue(name, onBlur(event.target.value));
      }
    },
  };
  const errorMessage = errors[name]?.message as string;

  return (
    <InputWithLabel
      register={register}
      required={required}
      label={label}
      name={name}
      id={id ?? `${name}-input`}
      type={type}
      numberOfRows={numberOfRows}
      isMultiline={!!numberOfRows}
      rules={fullRules as Rules}
      error={!!errorMessage}
      helperText={errorMessage || helperText}
      placeholder={placeholder}
      isOptional={isOptional}
      fullWidth
      styles={styles}
      inputMode={inputMode}
      pattern={pattern}
    />
  );
};

type FormCheckboxProps = Readonly<{
  name: string;
  styles?: React.CSSProperties;
  ariaLabel: string;
  required?: boolean;
  rules?: Record<string, Record<string, string | RegExp | boolean>>;
  tabIndex?: number;
}>;

export const FormCheckbox = ({ name, ariaLabel, required, rules, styles }: FormCheckboxProps) => {
  const { register, control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field }) => {
        return (
          <CheckboxField
            name={name}
            isChecked={field.value}
            styles={styles}
            ariaLabel={ariaLabel}
            required={required}
            rules={rules}
            register={register}
          />
        );
      }}
    />
  );
};

const monthDropdownMenuItems = [
  { label: "January", value: "01" },
  { label: "February", value: "02" },
  { label: "March", value: "03" },
  { label: "April", value: "04" },
  { label: "May", value: "05" },
  { label: "June", value: "06" },
  { label: "July", value: "07" },
  { label: "August", value: "08" },
  { label: "September", value: "09" },
  { label: "October", value: "10" },
  { label: "November", value: "11" },
  { label: "December", value: "12" },
];

type BirthDateInputProps = Readonly<{
  formFieldProperties: FormFieldProperties;
  inputName: string;
}>;

/**
 * The BirthDateInput component is utilized to break a single expected input of birth_date/birthDate into
 * three separate inputs for month, day, and year. To accomplish this, we take advantage of React Hook Form's Controller
 * component.
 */
export const BirthDateInput = ({ formFieldProperties, inputName }: BirthDateInputProps) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const birthDateError = errors[inputName];
  const requiredError = birthDateError?.type === "required";

  return (
    <>
      <InputLabel htmlFor={inputName} required tabIndex={0}>
        {formFieldProperties.title}
      </InputLabel>
      <Controller
        name={inputName}
        control={control}
        defaultValue={""}
        rules={{
          required: true,
          validate: (value: BirthDateValue) =>
            birthDateInputValidation(value, formFieldProperties.formatMinimum, formFieldProperties.formatMaximum),
        }}
        render={(render) => {
          const { field } = render;
          const [year, month, day] = field.value.split("-");

          const handleNumberFieldOnChange = (
            event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
            fieldName: "day" | "year",
          ) => {
            // With MUI TextField, we can only set the maxLength if the type is text as opposed to number
            // Due to this, we need to manually strip out any non-numeric characters
            const numberOnlyValue = event.target.value.replace(/\D/, "");

            field.onChange(
              `${fieldName === "year" ? numberOnlyValue : year}-${month ?? ""}-${
                fieldName === "day" ? numberOnlyValue : day
              }`,
            );
          };

          const handleMonthOnChange = (event: SelectChangeEvent<string>) => {
            const { value } = event.target;

            field.onChange(`${year ?? ""}-${value}-${day ?? ""}`);
          };

          return (
            <Grid container spacing={2}>
              <Grid item xs={5}>
                <FormControl
                  fullWidth
                  variant="outlined"
                  error={shouldDisplayBirthdateErrorMessage(birthDateError, "month value") || requiredError}
                >
                  <Select
                    value={month ?? ""}
                    onChange={handleMonthOnChange}
                    displayEmpty
                    renderValue={month?.length ? undefined : () => "Month"}
                    aria-label={appStrings.a11y.birthdateMonth}
                  >
                    {monthDropdownMenuItems.map((menuItem) => (
                      <MenuItem key={menuItem.value} value={menuItem.value}>
                        {menuItem.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={3}>
                <TextField
                  fullWidth
                  error={shouldDisplayBirthdateErrorMessage(birthDateError, "day value") || requiredError}
                  placeholder={"Day (DD)"}
                  inputProps={{
                    maxLength: 2,
                  }}
                  value={day ?? ""}
                  onChange={(event) => handleNumberFieldOnChange(event, "day")}
                  onBlur={() => {
                    const dayValue = day?.padStart(2, "0") ?? "";

                    // If the day value is 00, we want to clear the day input
                    field.onChange(`${year ?? ""}-${month ?? ""}-${dayValue === "00" ? "" : dayValue}`);
                  }}
                  aria-label={appStrings.a11y.birthdateDay}
                />
              </Grid>
              <Grid item xs={4}>
                <TextField
                  fullWidth
                  error={shouldDisplayBirthdateErrorMessage(birthDateError, "year value") || requiredError}
                  placeholder={"Year (YYYY)"}
                  inputProps={{
                    maxLength: 4,
                  }}
                  value={year ?? ""}
                  onChange={(event) => {
                    handleNumberFieldOnChange(event, "year");
                  }}
                  aria-label={appStrings.a11y.birthdateYear}
                />
              </Grid>
            </Grid>
          );
        }}
      />
      {birthDateError && (
        <FormHelperText error tabIndex={0}>
          {requiredError ? "This field is required" : (birthDateError.message ?? "").toString()}
        </FormHelperText>
      )}
    </>
  );
};

type SearchableDropdownProps = Readonly<{
  options: DropdownOptionType[];
  label: string;
  name: string;
  required?: boolean;
  rules?: Rules;
}>;

export const SearchableDropdown = ({ options, label, name, required, rules }: SearchableDropdownProps) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field }) => (
        <AutocompleteDropdown
          options={options}
          label={label}
          required={Boolean(required)}
          error={errors[name]?.message as string}
          field={field}
          id={`${name}-autocomplete`}
        />
      )}
    />
  );
};

type DateTimeInputProps = Readonly<{
  label: string;
  name: string;
  required?: boolean;
  rules?: Rules;
  onAccept?: (value: Dayjs | string | null) => void;
  onOpen?: () => void;
  onBlur?: (value: string) => void;
  defaultValue?: Dayjs;
}>;

export const DateTimeInput = ({
  label,
  name,
  required,
  rules,
  onAccept,
  onOpen,
  onBlur,
  defaultValue,
}: DateTimeInputProps) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const errorMessageBase = errors[name]?.message;
  const errorMessage = errorMessageBase ? errorMessageBase.toString() : "";

  return (
    <>
      <Controller
        render={({ field }) => (
          <DateTimeField
            label={label}
            name={name}
            required={required}
            onAccept={onAccept}
            onOpen={onOpen}
            onBlur={onBlur}
            defaultValue={defaultValue}
            errorMessage={errorMessage}
            field={field}
            value={field.value}
          />
        )}
        name={name}
        control={control}
        rules={rules}
      />
    </>
  );
};

export type SelectOptions = Readonly<{
  label: string;
  value: string | number;
}>;

type FormDropdownProps = Readonly<{
  options: SelectOptions[];
  label: string;
  name: string;
  required?: boolean;
  rules?: Rules;
}>;

export const FormDropdown = ({ options, label, name, required, rules }: FormDropdownProps) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const errorMessageBase = errors[name]?.message;
  const errorMessage = errorMessageBase ? errorMessageBase.toString() : "";
  const id = `${name}-datetime-input`;
  const helperTextId = `${id}-helper-text`;

  return (
    <Box>
      <InputLabel htmlFor={id} required={required} error={!!errorMessage?.length}>
        {sentenceCaseLabel(label)}
      </InputLabel>

      <FormControl fullWidth>
        <Controller
          render={({ field }) => {
            return (
              <Select aria-describedby={helperTextId} {...field}>
                {options.map((menuItem) => (
                  <MenuItem key={menuItem.value} value={menuItem.value}>
                    {menuItem.label}
                  </MenuItem>
                ))}
              </Select>
            );
          }}
          name={name}
          control={control}
          rules={rules}
        />

        {!!errorMessage?.length && (
          <FormHelperText error id={helperTextId}>
            {errorMessage}
          </FormHelperText>
        )}
      </FormControl>
    </Box>
  );
};

export { FormTextArea, FormSwitchSelect, FormDaySelect };
