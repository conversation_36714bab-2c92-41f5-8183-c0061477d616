import { Box, Button, DialogActions, FormHelperText, InputLabel } from "@mui/material";
import { DateTimePicker } from "@mui/x-date-pickers";
import { color } from "@vivantehealth/design-tokens";
import { Dayjs } from "dayjs";
import { ControllerRenderProps, FieldValues } from "react-hook-form";

import { AppIcon } from "@Components/AppIcon/AppIcon";
import { sentenceCaseLabel } from "@Utils/sentenceCaseLabel";

type DateTimeFieldProps = Readonly<{
  label?: string;
  name: string;
  required?: boolean;
  onAccept?: (value: Dayjs | string | null) => void;
  onOpen?: () => void;
  onBlur?: (value: string) => void;
  defaultValue?: Dayjs;
  errorMessage?: string;
  field?: ControllerRenderProps<FieldValues, string>;
  value?: Dayjs;
  maxDateTime?: Dayjs;
}>;

export const DateTimeField = ({
  label,
  name,
  required,
  onAccept,
  onOpen,
  onBlur,
  defaultValue,
  errorMessage,
  field,
  value,
  maxDateTime,
}: DateTimeFieldProps) => {
  const id = `${name}-datetime-input`;
  const helperTextId = `${id}-helper-text`;

  return (
    <Box>
      {label ? (
        <InputLabel htmlFor={id} required={required} error={!!errorMessage?.length}>
          {sentenceCaseLabel(label)}
        </InputLabel>
      ) : null}

      <DateTimePicker
        {...field}
        defaultValue={defaultValue}
        onOpen={onOpen}
        onAccept={onAccept}
        value={value}
        maxDateTime={maxDateTime}
        slots={{
          openPickerIcon: () => <AppIcon name="Calendar" color={color.icon.strong} />,
          actionBar: ({ onAccept }) => (
            <DialogActions className="MuiPickersLayout-actionBar">
              <Button variant="primary" size="small" onClick={onAccept}>
                Ok
              </Button>
            </DialogActions>
          ),
        }}
        slotProps={{
          textField: {
            onBlur: (event) => onBlur?.(event.target.value),
            id,
            fullWidth: true,
            "aria-describedby": helperTextId,
            sx: { ".MuiOutlinedInput-input": { paddingLeft: 0 } },
          },
          inputAdornment: {
            position: "start",
          },
          day: {
            sx: {
              root: {
                backgroundColor: "transparent",
              },
              "&.Mui-selected": {
                backgroundColor: "transparent",
                border: `1px solid ${color.border.action.hover}`,
                ":hover": {
                  background: "transparent",
                },
                ":focus": {
                  background: "transparent",
                },
              },
              ":hover": {
                backgroundColor: "transparent",
              },
              "&.MuiPickersDay-today": { backgroundColor: color.background.surface.secondary, border: "none" },
            },
          },
        }}
      />

      {!!errorMessage?.length && (
        <FormHelperText error id={helperTextId}>
          {errorMessage}
        </FormHelperText>
      )}
    </Box>
  );
};
