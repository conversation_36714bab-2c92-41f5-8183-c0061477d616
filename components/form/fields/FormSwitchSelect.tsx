import { FormControl, InputLabel, Box } from "@mui/material";
import { Controller, useFormContext } from "react-hook-form";

import { SwitchSelect } from "@Components/SwitchSelect/SwitchSelect";
import { sentenceCaseLabel } from "@Utils/sentenceCaseLabel";

type FormSwitchSelectProps = Readonly<{
  options: string[];
  label: string;
  name: string;
  onChangeCallback: () => void;
}>;

export const FormSwitchSelect = ({ options, label, name, onChangeCallback }: FormSwitchSelectProps) => {
  const { control, setValue, getValues } = useFormContext();

  return (
    <Box display="flex" flexDirection="column" width="100%">
      <InputLabel htmlFor={name}>{sentenceCaseLabel(label)}</InputLabel>
      <FormControl fullWidth>
        <Controller
          name={name}
          control={control}
          render={() => (
            <SwitchSelect
              options={options}
              value={getValues(name)}
              onChange={(value) => {
                setValue(name, value);
                onChangeCallback();
              }}
            />
          )}
        />
      </FormControl>
    </Box>
  );
};
