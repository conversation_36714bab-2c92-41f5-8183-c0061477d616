import { FormControl } from "@mui/material";
import { Controller, useFormContext } from "react-hook-form";

import { DaySelect } from "@Components/DaySelect/DaySelect";

type FormDaySelectProps = Readonly<{
  name: string;
}>;

export const FormDaySelect = ({ name }: FormDaySelectProps) => {
  const { control, setValue, getValues } = useFormContext();

  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        render={() => <DaySelect selectedDays={getValues(name)} setSelectedDays={(value) => setValue(name, value)} />}
      />
    </FormControl>
  );
};
