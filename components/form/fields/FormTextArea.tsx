import React, { CSSProperties } from "react";
import { FormControl } from "@mui/base";
import { FormHelperText, InputLabel, OutlinedInput } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { useFormContext } from "react-hook-form";

import { Rules } from "@Components/form/Fields";
import { sentenceCaseLabel } from "@Utils/sentenceCaseLabel";

type FormTextAreaProps = Readonly<{
  label?: string;
  name: string;
  id?: string;
  required?: boolean;
  placeholder?: string;
  /** This field caps the text area to 5 rows maximum instead of indefinite expansion */
  isHybrid?: boolean;
  styles?: CSSProperties;
  /** Include maxLength to display character limit counter */
  rules?: Rules;
  helperText?: string;
}>;

export const FormTextArea = ({
  name,
  id,
  label = "",
  required,
  rules,
  placeholder,
  styles,
  helperText,
  isHybrid,
}: FormTextAreaProps) => {
  const {
    register,
    formState: { errors },
    getValues,
  } = useFormContext();

  const CHARACTER_LIMIT = rules?.maxLength && "value" in rules.maxLength ? rules?.maxLength?.["value"] : undefined;
  const errorMessage = errors[name]?.message?.toString() ?? "";
  const helperTextId = helperText ? `${id}-helper-text` : undefined;
  const textOrError = errorMessage || helperText;
  const fieldId = id ?? `${name}-input`;

  return (
    <FormControl>
      <InputLabel htmlFor={fieldId} error={!!errorMessage} required={required}>
        {sentenceCaseLabel(label)}
      </InputLabel>
      <OutlinedInput
        id={fieldId}
        error={!!errorMessage}
        placeholder={placeholder}
        aria-describedby={helperTextId}
        name={name}
        inputProps={{
          maxLength: CHARACTER_LIMIT,
          ...(register && register(name, rules)),
        }}
        required={required}
        sx={styles}
        type="text"
        minRows={3}
        maxRows={isHybrid ? 5 : undefined}
        multiline
        fullWidth
      />
      {textOrError || CHARACTER_LIMIT ? (
        <FormHelperText
          sx={{
            display: "flex",
            justifyContent: textOrError ? "space-between" : "flex-end",
            mr: 0,
            color: color.text.strong,
          }}
          error={!!errorMessage}
          id={helperTextId}
        >
          {textOrError && <span>{textOrError}</span>}
          {CHARACTER_LIMIT && (
            <span style={{ color: errorMessage ? undefined : color.text.subtle }}>
              {getValues(name).length} / {CHARACTER_LIMIT}
            </span>
          )}
        </FormHelperText>
      ) : null}
    </FormControl>
  );
};
