import { Checkbox } from "@mui/material";
import { FieldValues, UseFormRegister } from "react-hook-form";

type CheckboxFieldProps = Readonly<{
  name: string;
  isChecked: boolean;
  styles?: React.CSSProperties;
  ariaLabel?: string;
  required?: boolean;
  rules?: Record<string, Record<string, string | RegExp | boolean>>;
  register?: UseFormRegister<FieldValues>;
}>;

export const CheckboxField = ({
  name,
  styles,
  isChecked,
  ariaLabel,
  required,
  rules,
  register,
}: CheckboxFieldProps) => (
  <Checkbox
    checked={isChecked}
    name={name}
    required={required}
    sx={{
      ...styles,
    }}
    inputProps={{
      "aria-label": ariaLabel,
      ...(register && register(name, rules)),
    }}
  />
);
