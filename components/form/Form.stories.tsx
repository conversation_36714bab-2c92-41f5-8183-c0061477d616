import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/nextjs";

import { FormInput } from "./Fields";
import { Form } from "./Form";

const meta: Meta<typeof Form> = {
  title: "@Components/form/Form",
  component: Form,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof Form>;

export const Primary: Story = {
  args: {
    // eslint-disable-next-line no-alert
    onSubmit: (data) => alert(JSON.stringify(data)),
    children: (
      <div>
        <FormInput label="name" name="email" type="email" />
        <FormInput label="password" name="password" type="password" />
      </div>
    ),
    formHeader: "Log in to your account",
    formSubheader: "Please log in with email and password",
  },
};
