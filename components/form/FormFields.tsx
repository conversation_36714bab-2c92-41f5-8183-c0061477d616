import { FormField, FormFieldOption, FormFieldValueType } from "@vivantehealth/vivante-core";
import { Grid } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { SPACING_16_PX } from "@Assets/style_constants";
import { formatPhoneNumber } from "@Utils/formatPhoneNumber";

import { FormInput, BirthDateInput, SearchableDropdown } from "./Fields";
import { buildInputRules } from "./utils/buildInputRules";
import { DropdownOptionType } from "../AutocompleteDropdown/AutocompleteDropdown";

export type FormFieldProperties = Readonly<
  {
    default?: string;
    meta?: Record<string, Record<string, string | boolean | RegExp>>;
    formatMaximum: string;
    formatMinimum: string;
    enum?: string[];
  } & FormField
>;

export type FormSubmission = Record<string, FormFieldValueType | FormFieldOption>;

type FormFieldsProps = Readonly<{
  inputProperties: [string, FormFieldProperties][];
  requiredFields: string[];
}>;

export const FormFields = ({ inputProperties, requiredFields }: FormFieldsProps) =>
  inputProperties?.map(([inputName, properties]) => {
    if (properties?.meta?.hidden) {
      return <input key={inputName} type="hidden" name={inputName} value={properties.default} />;
    }

    const isRequired = requiredFields?.includes(inputName);
    const label: string =
      typeof properties?.meta?.placeholder === "string" ? properties?.meta?.placeholder : properties.title;
    const rules = buildInputRules(properties, isRequired);

    if (properties?.enum && properties.meta) {
      const options = Object.entries(properties.meta["enum-titles"]).reduce((acc: DropdownOptionType[], option) => {
        acc.push({
          id: option[0],
          label: option[1] as string,
        });
        return acc;
      }, []);

      return (
        <Grid item mb={SPACING_16_PX} key={inputName}>
          <SearchableDropdown options={options} label={label} name={inputName} required={isRequired} rules={rules} />
        </Grid>
      );
    }

    if (["birth_date", "birthDate", "date_of_birth"].includes(inputName)) {
      return (
        <Grid item mb={SPACING_16_PX} key={inputName}>
          <BirthDateInput formFieldProperties={properties} inputName={inputName} />
        </Grid>
      );
    }

    return (
      <Grid item mb={SPACING_16_PX} key={inputName}>
        <FormInput
          label={label}
          name={inputName}
          type={properties.format || "text"}
          required={isRequired}
          // Overwrite the pattern rule for phone_mobile input as we don't control the pattern recevied from the server
          rules={
            inputName === "phone_mobile"
              ? {
                  ...rules,
                  pattern: {
                    value: /^\d{3}-\d{3}-\d{4}$/,
                    message: appStrings.sharedFormText.invalidMobilePhone,
                  },
                }
              : rules
          }
          onChange={inputName === "phone_mobile" ? formatPhoneNumber : undefined}
        />
      </Grid>
    );
  });
