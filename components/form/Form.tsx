import React, { useEffect } from "react";
import { Grid, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { useForm, FormProvider } from "react-hook-form";

type FormProps = Readonly<{
  // Record<string, any> results in type incompatibility with any shape of object so staying with any
  // we are not supposed to decouple useForm from its usage as useForm needs to be aware of form's shape as type...
  onSubmit: (data: unknown, event?: React.BaseSyntheticEvent) => void;
  children: React.ReactNode;
  defaultValues?: Record<string, unknown>;
  formHeader?: string;
  formSubheader?: string;
  sx?: React.CSSProperties;
  serverValidationErrors?: Record<string, string>;
}>;

export const Form = ({
  onSubmit,
  children,
  defaultValues,
  formHeader,
  formSubheader,
  sx,
  serverValidationErrors,
}: FormProps) => {
  const methods = useForm({
    defaultValues,
  });
  const { handleSubmit, setError } = methods;

  useEffect(() => {
    if (serverValidationErrors && Object.keys(serverValidationErrors).length !== 0) {
      Object.entries(serverValidationErrors).forEach(([inputName, errorMessage]) => {
        setError(inputName, { type: "server", message: errorMessage });
      });
    }
  }, [serverValidationErrors, setError]);

  return (
    <Grid container sx={sx}>
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)} style={{ width: "100%" }}>
          {formHeader && (
            <Grid item mb={2}>
              <Typography variant="h3" aria-label={`${formHeader}.`} color={color.text.strong}>
                {formHeader}
              </Typography>
            </Grid>
          )}
          {formSubheader && (
            <Grid item>
              <Typography variant="body" aria-label={`${formSubheader}.`} whiteSpace="pre-wrap">
                {formSubheader}
              </Typography>
            </Grid>
          )}
          {children}
        </form>
      </FormProvider>
    </Grid>
  );
};
