import { Box, Button } from "@mui/material";

const MAX_BUTTON_WIDTH = "202px";

type FormActionsProps = Readonly<{
  primaryText: string;
  secondaryText: string;
  secondaryCallback: () => void;
  primaryCallback?: () => void;
}>;

export const FormActions = ({ primaryText, secondaryText, secondaryCallback, primaryCallback }: FormActionsProps) => {
  return (
    <Box display="flex" justifyContent="space-between" gap={3}>
      <Button variant="secondary" onClick={secondaryCallback} sx={{ flex: `0 1 ${MAX_BUTTON_WIDTH}` }}>
        {secondaryText}
      </Button>
      <Button
        variant="primary"
        type={primaryCallback ? "button" : "submit"}
        onClick={primaryCallback && primaryCallback}
        sx={{ flex: `0 1 ${MAX_BUTTON_WIDTH}` }}
      >
        {primaryText}
      </Button>
    </Box>
  );
};
