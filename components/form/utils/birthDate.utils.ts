import { FieldError, Merge, FieldErrorsImpl, FieldValues } from "react-hook-form";

export type BirthDateValue = `${string}-${string}-${string}`;

// Validation function for birthday Controller input
// As we are breaking apart the incoming birth_date/birthDate field into 3 separate fields we need to validate each one
export const birthDateInputValidation = (birthDate: BirthDateValue, formatMinimum: string, formatMaximum: string) => {
  const [year, month, day] = birthDate.split("-");

  if (!month) return "Birthday is missing month value";

  if (!day) return "Birthday is missing day value";
  if (Number(day) <= 0 || Number(day) > 31) return "Birthday day value must be between 1 and 31";

  const minimumYear = Number(formatMinimum.slice(0, 4));
  const maximumYear = Number(formatMaximum.slice(0, 4));

  if (!year) return "Birthday is missing year value";
  if (Number(year) < minimumYear || Number(year) > maximumYear) {
    return `Birthday year value must be between ${minimumYear} and ${maximumYear}`;
  }

  return true;
};

export const shouldDisplayBirthdateErrorMessage = (
  error: FieldError | Merge<FieldError, FieldErrorsImpl<FieldValues>> | undefined,
  valueToCheck: string,
) => {
  if (!!error && typeof error.message === "string") {
    return error.message.includes(valueToCheck);
  }

  return !!error;
};
