import { appStrings } from "@Assets/app_strings";

import { Rules } from "../Fields";
import { FormFieldProperties } from "../FormFields";

export const buildInputRules = (properties: FormFieldProperties, isRequired: boolean) => {
  const rules: Rules = {};

  if (isRequired) {
    rules.required = {
      value: true,
      message: properties?.meta?.errors?.required ?? appStrings.sharedFormText.requiredMessage,
    };
  }
  if (properties.pattern) {
    rules.pattern = {
      value: new RegExp(properties.pattern),
      message: properties?.meta?.errors?.pattern ?? appStrings.sharedFormText.patternMessage,
    };
  }
  if (properties.minLength) {
    rules.minLength = {
      value: properties.minLength,
      message: properties?.meta?.errors?.minLength ?? appStrings.sharedFormText.minLengthMessage,
    };
  }
  if (properties.maxLength) {
    rules.maxLength = {
      value: properties.maxLength,
      message: properties?.meta?.errors?.maxLength ?? appStrings.sharedFormText.maxLengthMessage,
    };
  }
  if (properties.formatMaximum) {
    rules.max = {
      value: properties.formatMaximum,
      message: properties?.meta?.errors?.formatMaximum ?? appStrings.sharedFormText.formatMaximumMessage,
    };
  }
  if (properties.formatMinimum) {
    rules.min = {
      value: properties.formatMinimum,
      message: properties?.meta?.errors?.formatMinimum ?? appStrings.sharedFormText.formatMinimumMessage,
    };
  }

  return rules;
};
