import { JsonSchemaForm, JsonSchemaProperty } from "@vivantehealth/vivante-core";
import dayjs from "dayjs";

import { DropdownOptionType } from "@Components/AutocompleteDropdown/AutocompleteDropdown";
import { formatPhoneNumber } from "@Utils/formatPhoneNumber";

import { FormFieldProperties } from "../FormFields";

const isNumberArray = (array: unknown[]): array is number[] => {
  return array?.every((item) => typeof item === "number");
};
// Normalizing JsonSchemaProperty into FormFieldProperties
// While the types are nearly the same, there are a few properties that need to be checked/changed
const normalizeFormData = (
  formData: Record<string, JsonSchemaProperty> | [string, JsonSchemaProperty][],
  requiredFields: string[],
): Record<string, FormFieldProperties> => {
  return Object.entries(formData).reduce<Record<string, FormFieldProperties>>((acc, [key, value]) => {
    return {
      ...acc,
      [key]: {
        ...value,
        title: value.title ?? "",
        order: value.order || 0,
        required: requiredFields.includes(value.title),
        hidden: value.meta?.hidden || false,
        default: typeof value.default === "number" ? value.default.toString() : value.default,
        meta: value.meta as Record<string, Record<string, string | boolean>>,
        enum: isNumberArray(value.enum) ? value.enum.map((item: number) => item.toString()) : value.enum,
        readOnly: value.readOnly || false,
      },
    };
  }, {});
};

type FormFieldsSpec = Readonly<{
  requiredFields: string[];
  inputProperties: [string, FormFieldProperties][];
  defaultValues: Record<string, string | null | DropdownOptionType>;
}>;

export const formatInputData = (
  formData?: JsonSchemaForm,
  previouslyInputData?: Record<string, string>,
): FormFieldsSpec => {
  const requiredFields = formData?.required ?? [];
  const normalizedFormData = normalizeFormData(formData?.properties ?? {}, requiredFields);
  const inputProperties: [string, FormFieldProperties][] = Object.entries(normalizedFormData).sort(
    (a, b) => (a[1].order || 0) - (b[1].order || 0),
  );

  const defaultValues = inputProperties.reduce<Record<string, string | null | DropdownOptionType>>(
    (acc, [inputName, properties]) => {
      let previousData: string | null | DropdownOptionType = null;

      if (previouslyInputData) {
        previousData =
          inputName === "phone_mobile"
            ? formatPhoneNumber(previouslyInputData[inputName])
            : previouslyInputData[inputName];

        if (properties.format === "date") {
          if (previouslyInputData[inputName]) {
            previousData = dayjs(previousData).format("YYYY-MM-DD");
          }
        }
      }

      acc[inputName] = previousData || properties.default || "";
      return acc;
    },
    {},
  );

  return {
    inputProperties,
    defaultValues,
    requiredFields,
  };
};
