import { Box, SxProps, Theme } from "@mui/material";

export type GradientBorderCardProps = Readonly<{
  gradient: string;
  borderRadius: number;
  borderWidth: number;
  containerSx?: SxProps<Theme>;
  contentSx?: SxProps<Theme>;
  children: React.ReactNode;
}>;

export const CARD_HORIZONTAL_PADDING = `88px`;
export const BLUE_TO_PINK_GRADIENT = "linear-gradient(106.82deg, #495BFF 10.02%, #FFB8F0 88.45%)";

export const GradientBorderCard = ({
  gradient,
  borderRadius,
  borderWidth,
  containerSx,
  contentSx,
  children,
}: GradientBorderCardProps) => {
  return (
    <Box
      sx={{
        ...{
          position: "relative",
          background: `${gradient} border-box`,
          padding: `${borderWidth}px`,
          borderRadius: `${borderRadius}px`,
          width: "100%",
        },
        ...containerSx,
      }}
    >
      <Box
        display="flex"
        flexDirection="column"
        alignItems="flex-start"
        flexGrow={1}
        justifyContent="space-between"
        height="100%"
        sx={{ ...contentSx, borderRadius: `${borderRadius - borderWidth}px` }}
      >
        {children}
      </Box>
    </Box>
  );
};
