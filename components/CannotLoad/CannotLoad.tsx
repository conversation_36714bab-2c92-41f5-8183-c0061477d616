import { Box, Card, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { SPACING_16_PX } from "@Assets/style_constants";
import { BackButton } from "@Components/BackButton/BackButton";

export type CannotLoadProps = Readonly<{
  backText?: string;
  message?: string;
  backHandler: () => void;
}>;

export const CannotLoad = ({
  backText = appStrings.buttonText.back,
  message = "This content could not be loaded.",
  backHandler,
}: CannotLoadProps) => (
  <>
    <Box mb={SPACING_16_PX}>
      <BackButton onClick={backHandler}>{backText}</BackButton>
    </Box>
    <Card>
      <Typography variant="body">{message}</Typography>
    </Card>
  </>
);
