import type { <PERSON>a, StoryObj } from "@storybook/nextjs";

import { CannotLoad } from "./CannotLoad";
import { cylinderThemeDecorator } from "../stories/cylinderThemeDecorator";

const meta: Meta<typeof CannotLoad> = {
  title: "@Components/CannotLoad",
  component: CannotLoad,
  tags: ["autodocs"],
  decorators: cylinderThemeDecorator,
};

export default meta;
type Story = StoryObj<typeof CannotLoad>;

export const Default: Story = {
  args: {
    backText: "Back",
    message: "This content could not be loaded.",
    backHandler: () => alert("Back clicked"),
  },
};
