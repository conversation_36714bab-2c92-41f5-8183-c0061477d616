import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>u, MenuItem, Typography } from "@mui/material";

import { SPACING_40_PX } from "@Assets/style_constants";
import { AppIcon, IconVariant } from "@Components/AppIcon/AppIcon";

import { NestedMenuItems } from "../NestedMenuItems/NestedMenuItems";

type MenuItemsCommonProps = Readonly<{
  text: string;
  onClick: () => void;
  onMouseOver?: () => void;
  onMouseOut?: () => void;
  leftIcon?: IconVariant;
  iconStyles?: React.CSSProperties;
  isDisabled?: boolean;
}>;

type MenuItems =
  | ({ type?: "topLevel" } & MenuItemsCommonProps)
  | { type: "nested"; text: string; menuItems: MenuItemsCommonProps[] };

type BasicMenuProps = Readonly<{
  id: string;
  children: React.ReactNode;
  ariaLabel: React.ComponentProps<"button">["aria-label"];
  disableRipple?: boolean;
  menuItems: MenuItems[];
  menuIconRight?: IconVariant;
  menuIconLeft?: IconVariant;
  buttonStyles?: React.CSSProperties;
  buttonVariant?: "primary" | "secondary" | "ghost";
  buttonSize?: "small" | "medium";
  onMenuClick?: () => void;
}>;

export const BasicMenu = ({
  id,
  children,
  disableRipple,
  menuItems,
  menuIconRight,
  menuIconLeft,
  ariaLabel,
  buttonStyles,
  buttonVariant = "ghost",
  buttonSize = "medium",
  onMenuClick,
}: BasicMenuProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);

    if (onMenuClick) {
      onMenuClick();
    }
  };

  const handleClose = (callBack: () => void) => {
    callBack();
    setAnchorEl(null);
  };

  return (
    <>
      <Button
        variant={buttonVariant}
        id={`${id}-button`}
        aria-controls={open ? `${id}-menu` : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        onClick={handleClick}
        aria-label={ariaLabel}
        disableRipple={disableRipple}
        endIcon={menuIconRight ? <AppIcon name={menuIconRight} /> : null}
        startIcon={menuIconLeft ? <AppIcon name={menuIconLeft} /> : null}
        sx={buttonStyles}
        size={buttonSize}
      >
        {children}
      </Button>
      <Menu
        id={`${id}-menu`}
        anchorEl={anchorEl}
        open={open}
        onClose={() => setAnchorEl(null)}
        MenuListProps={{
          "aria-labelledby": `${id}-button`,
        }}
      >
        {menuItems.map((menuItem) => {
          return menuItem.type === "nested" ? (
            <NestedMenuItems key={menuItem.text} label={menuItem.text} isSubMenuOpen={Boolean(anchorEl)}>
              {menuItem.menuItems.map((nestedMenuItem) => (
                <MenuItem
                  key={nestedMenuItem.text}
                  aria-label={nestedMenuItem.text}
                  onClick={() => handleClose(nestedMenuItem.onClick)}
                  disabled={nestedMenuItem.isDisabled}
                >
                  {nestedMenuItem.leftIcon && (
                    <AppIcon
                      name={nestedMenuItem.leftIcon}
                      svgStyles={{ verticalAlign: "middle" }}
                      containerStyles={{ ...(nestedMenuItem.iconStyles ? nestedMenuItem.iconStyles : {}) }}
                    />
                  )}
                  <Typography variant="body" sx={{ mr: SPACING_40_PX }}>
                    {nestedMenuItem.text}
                  </Typography>
                </MenuItem>
              ))}
            </NestedMenuItems>
          ) : (
            <MenuItem
              key={menuItem.text}
              aria-label={menuItem.text}
              onClick={() => handleClose(menuItem.onClick)}
              onMouseOver={() => {
                if (menuItem.onMouseOver) {
                  menuItem.onMouseOver();
                }
              }}
              onMouseLeave={() => {
                if (menuItem.onMouseOut) {
                  menuItem.onMouseOut();
                }
              }}
              disabled={menuItem.isDisabled}
            >
              {menuItem.leftIcon && (
                <AppIcon
                  name={menuItem.leftIcon}
                  svgStyles={{ verticalAlign: "middle" }}
                  containerStyles={{ ...(menuItem.iconStyles ? menuItem.iconStyles : {}) }}
                />
              )}
              <Typography variant="body" sx={{ mr: SPACING_40_PX }}>
                {menuItem.text}
              </Typography>
            </MenuItem>
          );
        })}
      </Menu>
    </>
  );
};
