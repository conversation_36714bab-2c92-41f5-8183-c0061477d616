import type { <PERSON>a, StoryObj } from "@storybook/nextjs";

import { BasicMenu } from "./BasicMenu";
import { AppIcon } from "../AppIcon/AppIcon";
import { cylinderThemeDecorator } from "../stories/cylinderThemeDecorator";

const meta: Meta<typeof BasicMenu> = {
  title: "@Components/BasicMenu",
  component: BasicMenu,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: cylinderThemeDecorator,
};

export default meta;
type Story = StoryObj<typeof BasicMenu>;

export const Default: Story = {
  args: {
    id: "default",
    children: "Menu 1",
    menuIconRight: "DownChevron",
    menuItems: [
      { text: "Logout", onClick: () => alert("logging out") },
      {
        text: "Progress",
        onClick: () => alert("progressing"),
        leftIcon: "Progress",
        iconStyles: { marginRight: "8px" },
      },
    ],
  },
};

export const BasicMenuWLeftIcon: Story = {
  args: {
    id: "left-icon",
    children: "Menu 2",
    menuIconLeft: "DownChevron",
    menuItems: [{ text: "Logout", onClick: () => alert("logging out") }],
  },
};

export const BasicMenuWOText: Story = {
  args: {
    id: "no-text",
    children: <AppIcon name="Ellipse" />,
    menuItems: [{ text: "Reschedule me", onClick: () => alert("rescheduling") }],
  },
};
