import { useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Box, Grid } from "@mui/material";
import { useRouter } from "next/router";

import { styleConstants } from "@Assets/style_constants";
import { HeaderContainer } from "@Components/Header/HeaderContainer";
import { LoadingSpinner } from "@Components/LoadingSpinner/LoadingSpinner";
import { NavigationDrawer } from "@Components/Navigation/NavigationDrawer";
import { ProtectedRoute } from "@Components/ProtectedRoute/ProtectedRoute";
import { useGetUserStatusQuery } from "@Features/authentication/api/registrationApi";
import { authenticationStateSelector } from "@Features/authentication/store/authenticationStateSlice";
import { bootstrapStateSelector, BootstrapStateSlice } from "@Features/bootstrap/store/bootstrapStateSlice";
import { memberStateSelector } from "@Features/member/store/memberStateSlice";
import { MemberConvertedToVirtualClinicModal } from "@Features/memberConvertedToVirtualClinic/MemberConvertedToVirtualClinicModal";
import { navigationStateSelector, setNavDrawerOpen } from "@Features/navigation/store/navigationStateSlice";
import { updatePageTitle } from "@Features/navigation/utils/updatePageTitle";
import { useHeaderHook } from "@Hooks/useHeaderHook";
import { Routes } from "@Types";
import { isMobileDevice } from "@Utils/isMobileDevice";

const BOOTSTRAP_TIMEOUT = 1000;
/** Frequency to check user status endpoint for the currently logged in user */
const USER_STATUS_CHECK_INTERVAL = 1000 * 60 * 60;

type PostAuthContainerProps = Readonly<{
  showLoader?: boolean;
  children: React.ReactNode;
}>;

export const PostAuthContainer = ({ children, showLoader = false }: PostAuthContainerProps) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const isAuthenticated: boolean = useSelector(authenticationStateSelector("isAuthenticated"));
  const bootstrapped = useSelector(bootstrapStateSelector("bootstrapped"));
  const isMemberLoaded = useSelector(memberStateSelector("loadState")) === "loaded";
  const member = useSelector(memberStateSelector("member"));
  const { contentHeight } = useHeaderHook();
  const { data } = useGetUserStatusQuery(undefined, {
    skip: !isAuthenticated,
    pollingInterval: USER_STATUS_CHECK_INTERVAL,
  });
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const shouldDisplayMobileDownloadPage =
    isAuthenticated &&
    isMemberLoaded &&
    !member?.setting?.onboardingPending &&
    typeof window !== "undefined" &&
    router.pathname === Routes.HOME &&
    isMobileDevice();

  useEffect(() => {
    if (!bootstrapped && isAuthenticated) {
      // Resets flag which is used in useBootstrapCompleted hook
      sessionStorage.setItem("isBootstrapped", "false");

      timerRef.current = setTimeout(() => {
        dispatch(BootstrapStateSlice.actions.bootstrap());
      }, BOOTSTRAP_TIMEOUT);
    }

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }

      dispatch(BootstrapStateSlice.actions.unmount());
    };
  }, [bootstrapped, dispatch, isAuthenticated]);

  const activeNavOption = useSelector(navigationStateSelector("activeNavOption"));
  const navDrawerOpen = useSelector(navigationStateSelector("navDrawerOpen"));
  const navDrawerOpenCallback = (open: boolean) => {
    dispatch(setNavDrawerOpen(open));
  };

  /**
   * We need to allow the user to get to the Home page for redirect purposes if they are logged out
   * However, if they are logged in and on mobile, we want to show the download mobile app page
   */
  if (shouldDisplayMobileDownloadPage) {
    router.replace(Routes.WELCOME_MOBILE_APP);
  }
  /**
   * If the user is inactive, we need to redirect them to the deactivated page
   */
  if (data?.data?.attributes?.account_status === "inactive") {
    router.push(Routes.MEMBER_DEACTIVATED);
  }

  return (
    <ProtectedRoute>
      <Box overflow="hidden">
        <HeaderContainer />
        <NavigationDrawer
          activeNavOption={activeNavOption}
          navDrawerOpenCallback={navDrawerOpenCallback}
          navDrawerOpen={navDrawerOpen}
          contentHeight={contentHeight}
          updatePageTitle={updatePageTitle}
        >
          <Grid container justifyContent="center" height="100%">
            <Grid item md={12} px={{ xs: 5, lg: 0 }} pb={8} maxWidth={styleConstants.contentMaxWidth}>
              <MemberConvertedToVirtualClinicModal />
              {children}
            </Grid>
          </Grid>
        </NavigationDrawer>
        <LoadingSpinner open={showLoader || shouldDisplayMobileDownloadPage} />
      </Box>
    </ProtectedRoute>
  );
};
