import { ReactNode } from "react";
import { Box } from "@mui/material";

type TabsPanelProps = Readonly<{
  children?: ReactNode;
  tabName: string;
  currentSelectedTabName: string;
  sx?: React.CSSProperties;
}>;
/**
 * TabsPanel is a component that renders the content of a tab panel which is hidden or shown based on the selected tab within a Tabs component.
 * When creating the Tab (using MUI Tab component), the tabName prop should be passed as the value prop and we should use the helper function
 * tabsA11yProps to get the a11y props for the tab component. The tabName prop should be the same as the tabName prop passed to the TabsPanel component.
 * By using the tabsA11yProps helper function, the tab component will be associated with the tab panel component and will be accessible to screen readers.
 *
 * Base setup of the component from Material UI Documentation (https://mui.com/material-ui/react-tabs/#introduction)
 *
 * Example usage
 *
 *    <Tabs value={activeTab} onChange={(_, targetTab) => setActiveTab(targetTab)}>
 *       <Tab
 *         label={"Tab 1"}
 *         value={"tab1"}
 *         {...tabsA11yProps("tab1")}
 *       />
 *       <Tab
 *         label={"Tab 2"}
 *         value={"tab2"}
 *         {...tabsA11yProps("tab2")}
 *       />
 *     </Tabs>
 *     <TabsPanel tabName={"tab1"} currentSelectedTabName={activeTab}>
 *       <Tab1Component />
 *     </TabsPanel>
 *     <TabsPanel tabName={"tab2"} currentSelectedTabName={activeTab}>
 *       <Tab2Component />
 *     </TabsPanel>
 *
 */
export const TabsPanel = ({ children, tabName, currentSelectedTabName, sx = { marginTop: 5 } }: TabsPanelProps) => {
  return (
    <Box
      role="tabpanel"
      hidden={tabName !== currentSelectedTabName}
      id={`tabs-panel-${tabName}`}
      aria-labelledby={`tab-${tabName}`}
      sx={sx}
    >
      {children}
    </Box>
  );
};
