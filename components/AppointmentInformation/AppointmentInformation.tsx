import { AppointmentCommunicationMethod, SessionCommunicationMethod } from "@vivantehealth/vivante-core";
import { Box, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import dayjs from "dayjs";

import { appStrings } from "@Assets/app_strings";
import { iconSize } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";

const CARE_TEAM_STRINGS = appStrings.features.careTeam;

type AppointmentInformationProps = Readonly<{
  startTime: number | Date;
  communicationMethod?: AppointmentCommunicationMethod;
}>;

export const AppointmentInformation = ({ startTime, communicationMethod }: AppointmentInformationProps) => {
  const isPhoneAppointment = communicationMethod === SessionCommunicationMethod.PHONE;
  const visitString = isPhoneAppointment ? CARE_TEAM_STRINGS.phoneCallVisit : CARE_TEAM_STRINGS.videoVisit;

  return (
    <>
      <Box display="flex" alignItems="center" gap={2} mb={2}>
        <AppIcon name="Calendar" containerStyles={{ ...iconSize.md, color: color.icon.subtle }} />

        <Typography variant="body">{dayjs(startTime).format("dddd, MMMM D, YYYY [at] h:mmA")}</Typography>
      </Box>

      <Box display="flex" alignItems="start" gap={2}>
        <AppIcon
          name={isPhoneAppointment ? "Phone" : "VideoCall"}
          containerStyles={{ ...iconSize.md, color: color.icon.subtle }}
        />

        <Typography variant="body" aria-label={appStrings.a11y.appointmentType(visitString)}>
          {visitString}
        </Typography>
      </Box>
    </>
  );
};
