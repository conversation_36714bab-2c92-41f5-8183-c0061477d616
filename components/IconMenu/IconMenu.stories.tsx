import type { Meta, StoryObj } from "@storybook/nextjs";

import { IconMenu } from "./IconMenu";
import { AppIcon } from "../AppIcon/AppIcon";
import { cylinderThemeDecorator } from "../stories/cylinderThemeDecorator";

const meta: Meta<typeof IconMenu> = {
  title: "@Components/IconMenu",
  component: IconMenu,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: cylinderThemeDecorator,
};

export default meta;
type Story = StoryObj<typeof IconMenu>;

export const Default: Story = {
  args: {
    id: "default",
    children: <AppIcon name="More" size="sm" containerStyles={{ display: "flex" }} />,
    iconButtonStyles: { border: `1px solid black`, borderRadius: "50%" },
    menuItems: [
      { text: "Logout", onClick: () => alert("logging out") },
      {
        text: "Progress",
        onClick: () => alert("progressing"),
        leftIcon: "Progress",
        iconStyles: { marginRight: "8px" },
      },
    ],
  },
};
