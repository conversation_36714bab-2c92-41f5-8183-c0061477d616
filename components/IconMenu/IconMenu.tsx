import { useState } from "react";
import { Icon<PERSON>utton, Menu, MenuItem, Typography } from "@mui/material";

import { AppIcon, IconVariant } from "../AppIcon/AppIcon";

type MenuItems = Readonly<{
  text: string;
  onClick: () => void;
  leftIcon?: IconVariant;
  iconStyles?: React.CSSProperties;
  isDisabled?: boolean;
}>;

type IconMenuProps = Readonly<{
  id: string;
  iconButtonStyles?: React.CSSProperties | Record<string, React.CSSProperties>;
  children: React.ReactNode;
  ariaLabel: React.ComponentProps<"button">["aria-label"];
  disableRipple?: boolean;
  menuItems: MenuItems[];
}>;

export const IconMenu = ({ id, children, iconButtonStyles, ariaLabel, disableRipple, menuItems }: IconMenuProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  return (
    <>
      <IconButton
        id={`${id}-button`}
        aria-controls={open ? `${id}-menu` : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        onClick={handleClick}
        aria-label={ariaLabel}
        disableRipple={disableRipple}
        sx={iconButtonStyles}
      >
        {children}
      </IconButton>
      <Menu
        id={`${id}-menu`}
        anchorEl={anchorEl}
        open={open}
        onClose={() => setAnchorEl(null)}
        MenuListProps={{
          "aria-labelledby": `${id}-button`,
        }}
      >
        {menuItems.map((menuItem) => {
          return (
            <MenuItem
              key={menuItem.text}
              aria-label={menuItem.text}
              onClick={() => {
                menuItem.onClick();
                setAnchorEl(null);
              }}
            >
              {menuItem.leftIcon && (
                <AppIcon
                  name={menuItem.leftIcon}
                  svgStyles={{ verticalAlign: "middle" }}
                  containerStyles={{ ...(menuItem?.iconStyles ? menuItem.iconStyles : {}) }}
                />
              )}
              <Typography variant="body" sx={{ mr: 7 }}>
                {menuItem.text}
              </Typography>
            </MenuItem>
          );
        })}
      </Menu>
    </>
  );
};
