import { Practitioner } from "@vivantehealth/vivante-core";
import { Box, Typography } from "@mui/material";

import { appStrings } from "@Assets/app_strings";
import { getPractitionerDisplayableRole } from "@Utils/getPractitionerDisplayableRole";

import { CareTeamAvatar } from "../CareTeamAvatar/CareTeamAvatar";

type PractitionerInformationProps = Readonly<{
  practitioner: Practitioner;
}>;

const getPractitionerCredential = (role: string) => {
  if (role === "MD" || role === "PA") {
    return `, ${role}`;
  }

  return "";
};

export const PractitionerInformation = ({ practitioner }: PractitionerInformationProps) => {
  const isValidAvatarImage = practitioner?.avatarLink && !practitioner.avatarLink.includes("avatar1.png");

  return (
    <Box display="flex">
      {isValidAvatarImage && (
        <CareTeamAvatar
          avatarLink={practitioner?.avatarLink}
          altText={appStrings.features.careTeam.profileImageAltText}
        />
      )}

      <Box ml={isValidAvatarImage ? 4 : 0}>
        <Typography variant="h3">{`${practitioner.firstName} ${practitioner.lastName}${getPractitionerCredential(
          practitioner.role,
        )}`}</Typography>

        <Typography variant="bodyDense">{getPractitionerDisplayableRole(practitioner)}</Typography>
      </Box>
    </Box>
  );
};
