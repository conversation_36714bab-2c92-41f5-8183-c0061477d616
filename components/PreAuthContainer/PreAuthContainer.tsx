import { NextRouter, useRouter } from "next/router";

import { useFirebaseAuth } from "@Hooks/firebaseAuthHook";
import { Routes } from "@Types";

import { LoadingSpinner } from "../LoadingSpinner/LoadingSpinner";

type PreAuthContainerProps = Readonly<{
  readonly children: React.ReactNode;
}>;

export const PreAuthContainer = ({ children }: PreAuthContainerProps) => {
  const router = useRouter();
  const { isAuthenticated, isFirebaseAuthLoading } = useFirebaseAuth();

  if (isFirebaseAuthLoading) {
    return <LoadingSpinner open overlayDrawer overlayHeader />;
  }

  if (isAuthenticated) {
    const returnUrl = preAuthRouter(router);

    router.push(returnUrl);
    return null;
  }

  return <div>{children}</div>;
};

const preAuthRouter = (router: NextRouter) => {
  if (typeof router.query.returnUrl === "string") {
    return router.query.returnUrl;
  }

  switch (router.pathname) {
    case Routes.SIGN_UP:
      return Routes.ELIGIBILITY;
    default:
      return Routes.HOME;
  }
};
