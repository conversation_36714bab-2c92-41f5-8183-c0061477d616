import { MutableRefObject, ReactNode } from "react";
import { Box, Drawer, SxProps } from "@mui/material";

import { styleConstants } from "@Assets/style_constants";

import { BaseDrawerHeader } from "./BaseDrawerHeader";

type BaseDrawerProps = Readonly<{
  isDrawerOpen: boolean;
  onClose: () => void;
  header: string;
  /** children is the body content of the drawer */
  children: ReactNode;
  /** actions are the action button(s) for the drawer. For example you may have a "cancel"
   * and "save" button in a container. If ommited, add them to children object */
  actions?: ReactNode;
  anchor?: "left" | "right" | "top" | "bottom";
  variant?: "temporary" | "persistent" | "permanent";
  focusRef?: MutableRefObject<HTMLDivElement | null>;
  paperSx?: SxProps;
  backBtnAriaLabel?: string;
  closeBtnAriaLabel?: string;
  onBack?: () => void;
  showBackBtn?: boolean;
}>;

export const BaseDrawer = ({
  isDrawerOpen,
  onClose,
  anchor = "right",
  variant = "temporary",
  children,
  actions,
  header,
  focusRef,
  paperSx,
  backBtnAriaLabel,
  closeBtnAriaLabel,
  onBack,
  showBackBtn,
}: BaseDrawerProps) => {
  return (
    <Drawer
      open={isDrawerOpen}
      onClose={onClose}
      anchor={anchor}
      variant={variant}
      PaperProps={{
        sx: paperSx ? paperSx : {},
      }}
    >
      <BaseDrawerHeader
        header={header}
        onClose={onClose}
        backBtnAriaLabel={backBtnAriaLabel}
        closeBtnAriaLabel={closeBtnAriaLabel}
        onBack={onBack}
        showBackBtn={showBackBtn}
      />
      <Box height="100%" overflow="hidden" ref={focusRef}>
        <Box sx={{ height: `calc(100% - ${actions ? styleConstants.headerHeight : "0px"})`, overflow: "auto" }}>
          {children}
        </Box>
        {actions ? <Box mt={3}>{actions}</Box> : null}
      </Box>
    </Drawer>
  );
};
