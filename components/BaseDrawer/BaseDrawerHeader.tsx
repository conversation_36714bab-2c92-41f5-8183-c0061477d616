import { useEffect, useRef } from "react";
import { Box, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";

import { OutlinedIconButton } from "../OutlinedIconButton/OutlinedIconButton";

const ARIA_STRINGS = appStrings.a11y;

type BaseDrawerHeaderProps = Readonly<{
  header: string;
  onClose: () => void;
  showBackBtn?: boolean;
  onBack?: () => void;
  backBtnAriaLabel?: string;
  closeBtnAriaLabel?: string;
}>;

export const BaseDrawerHeader = ({
  header,
  onClose,
  onBack,
  showBackBtn,
  backBtnAriaLabel = ARIA_STRINGS.goBack,
  closeBtnAriaLabel = ARIA_STRINGS.closeDrawer,
}: BaseDrawerHeaderProps) => {
  const textRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (textRef?.current) {
      textRef.current?.focus();
    }
  }, [header]);

  return (
    <Box display="flex" justifyContent="space-between" alignItems="center" mb={5}>
      <Box display="flex" alignItems="center">
        {showBackBtn ? (
          <OutlinedIconButton icon="LeftChevron" onClick={() => onBack?.()} ariaLabel={backBtnAriaLabel} />
        ) : null}
        <Typography variant="h3" color={color.text.strong} sx={showBackBtn ? { py: 0, px: 4 } : {}} ref={textRef}>
          {header}
        </Typography>
      </Box>
      <OutlinedIconButton icon="Close" onClick={onClose} ariaLabel={closeBtnAriaLabel} />
    </Box>
  );
};
