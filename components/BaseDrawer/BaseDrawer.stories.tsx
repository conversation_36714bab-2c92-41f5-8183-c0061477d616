import { useState } from "react";
import { Box, Button, Typography } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/nextjs";

import { BaseDrawer } from "./BaseDrawer";
import { cylinderThemeDecorator } from "../stories/cylinderThemeDecorator";

const meta: Meta<typeof BaseDrawer> = {
  decorators: cylinderThemeDecorator,
  title: "@Components/BaseDrawer",
  component: BaseDrawer,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof BaseDrawer>;

const StateHandledDrawer = () => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  return (
    <>
      <Button variant="primary" onClick={() => setIsDrawerOpen(true)}>
        Open drawer
      </Button>
      <BaseDrawer
        isDrawerOpen={isDrawerOpen}
        onClose={() => setIsDrawerOpen(false)}
        header={"Base drawer"}
        actions={
          <Box display="flex" gap={2}>
            <Button onClick={() => alert("Clicked close")} variant="secondary" fullWidth>
              {"Close"}
            </Button>
            <Button onClick={() => alert("Clicked saved")} variant="primary" fullWidth>
              {"Save"}
            </Button>
          </Box>
        }
      >
        <Box display="flex" flexDirection="column" gap={4}>
          <Typography
            variant="h4"
            mb={"1200px"}
          >{`This section is scrollable while keeping the actions container always in view. Make the drawer shorter to see!`}</Typography>

          <Typography variant="bodyDense">
            {`This may have been off screen, but can be scrolled to while the buttons stayed in their own container`}
          </Typography>
        </Box>
      </BaseDrawer>
    </>
  );
};

export const SingleCTA: Story = {
  render: () => <StateHandledDrawer />,
};
