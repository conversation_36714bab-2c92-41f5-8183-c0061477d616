import { CSSProperties, ReactNode } from "react";
import { Dialog, DialogTitle, Box, DialogContent, DialogActions, Breakpoint } from "@mui/material";

import { SPACING_24_PX, SPACING_80_PX, SPACING_8_PX } from "@Assets/style_constants";

import { OutlinedIconButton } from "../OutlinedIconButton/OutlinedIconButton";

type BaseModalCommonProps = Readonly<{
  isModalOpen: boolean;
  displayCloseButton?: boolean;
  bodyContent: ReactNode;
  actions?: ReactNode;
  hideBackdrop?: boolean;
  onClose: () => void;
  closeBtnAriaLabel?: string;
  alignTitleSection?: CSSProperties["alignItems"];
  fullWidth?: boolean;
  maxWidth?: Breakpoint;
  isVideoModal?: boolean;
}>;
/**
 * We use the following type to ensure that we only pass either a title or a titleIcon, but not both.
 * While also allowing for neither to be passed.
 */
type BaseModalTitleOrIconProps =
  | Readonly<{ title?: string; titleIcon?: never }>
  | Readonly<{ title?: never; titleIcon?: ReactNode }>;

export type BaseModalProps = BaseModalTitleOrIconProps & BaseModalCommonProps;

export const BaseModal = ({
  isModalOpen,
  title,
  titleIcon,
  displayCloseButton = true,
  bodyContent,
  actions,
  hideBackdrop,
  onClose,
  closeBtnAriaLabel,
  alignTitleSection = "center",
  maxWidth = "xs",
  fullWidth = true,
  isVideoModal = false,
}: BaseModalProps) => {
  const includesTitle = title !== undefined && title.length > 0;
  const displayDialogTitle = includesTitle || displayCloseButton || titleIcon;
  const modalContentStyle = (displayDialogTitle: boolean | ReactNode, isVideoModal: boolean) => {
    if (!displayDialogTitle) return { mt: `-${SPACING_24_PX}` };
    if (isVideoModal) return { p: `${SPACING_24_PX} 0 ${SPACING_8_PX} 0 !important` };

    return {};
  };

  return (
    <Dialog open={isModalOpen} onClose={onClose} hideBackdrop={hideBackdrop} maxWidth={maxWidth} fullWidth={fullWidth}>
      {displayDialogTitle && (
        <DialogTitle>
          <Box
            display="flex"
            flexDirection="row"
            justifyContent={includesTitle || titleIcon ? "space-between" : "flex-end"}
            alignItems={alignTitleSection}
          >
            {title ?? ""}
            {titleIcon}
            {displayCloseButton ? (
              <OutlinedIconButton icon="Close" onClick={onClose} ariaLabel={closeBtnAriaLabel} />
            ) : null}
          </Box>
        </DialogTitle>
      )}
      {/** As we apply global padding to DialogContent in the theme, in the situation where we do not have a title or close button we need to remove the top
       * padding. CSS padding does not support negative values, so we need to use margin instead.
       *
       * Also, we need to add specific padding to the video modal content, so we need to check if it is a video modal and apply the padding accordingly.
       */}
      {actions && isVideoModal ? (
        <Box sx={{ position: "absolute", right: SPACING_80_PX }}>
          <DialogActions sx={{ pt: 0 }}>{actions}</DialogActions>
        </Box>
      ) : null}
      <DialogContent sx={modalContentStyle(displayDialogTitle, isVideoModal)}>{bodyContent}</DialogContent>
      {actions && !isVideoModal ? <DialogActions>{actions}</DialogActions> : null}
    </Dialog>
  );
};
