import { useState } from "react";
import { Box, Button, Typography } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/nextjs";

import { BaseModal, BaseModalProps } from "./BaseModal";
import { cylinderThemeDecorator } from "../stories/cylinderThemeDecorator";

const meta: Meta<typeof BaseModal> = {
  decorators: cylinderThemeDecorator,
  title: "@Components/BaseModal",
  component: BaseModal,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof BaseModal>;

const StateHandledModal = ({
  title,
  displayCloseButton,
  bodyContent,
  numberOfActionButtons,
}: Pick<BaseModalProps, "title" | "displayCloseButton" | "bodyContent"> & {
  numberOfActionButtons: 1 | 2;
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const actions =
    numberOfActionButtons === 1 ? (
      <Button variant="primary" fullWidth onClick={() => setIsModalOpen(false)}>
        Okay
      </Button>
    ) : (
      <>
        <Button variant="secondary" fullWidth onClick={() => setIsModalOpen(false)}>
          Cancel
        </Button>
        <Button variant="primary" fullWidth onClick={() => setIsModalOpen(false)}>
          Okay
        </Button>
      </>
    );

  return (
    <>
      <BaseModal
        isModalOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={title}
        displayCloseButton={displayCloseButton}
        bodyContent={bodyContent}
        actions={actions}
      />
      <Box display="flex" justifyContent="center" alignItems="center">
        <Button
          variant="primary"
          onClick={() => {
            setIsModalOpen(true);
          }}
        >
          Open modal
        </Button>
      </Box>
    </>
  );
};

export const SingleCTA: Story = {
  render: () => (
    <StateHandledModal
      title={"Base modal"}
      bodyContent={<Typography variant="body">Body content</Typography>}
      numberOfActionButtons={1}
    />
  ),
};

export const MultipleCTA: Story = {
  render: () => (
    <StateHandledModal
      title={"Base modal"}
      bodyContent={<Typography variant="body">Body content</Typography>}
      numberOfActionButtons={2}
    />
  ),
};

export const WithNoTitle: Story = {
  render: () => (
    <StateHandledModal
      bodyContent={<Typography variant="body">Body content with no title</Typography>}
      numberOfActionButtons={1}
    />
  ),
};

export const WithNoTitleOrCloseButton: Story = {
  render: () => (
    <StateHandledModal
      displayCloseButton={false}
      bodyContent={<Typography variant="body">Body content with no title or close button</Typography>}
      numberOfActionButtons={1}
    />
  ),
};
