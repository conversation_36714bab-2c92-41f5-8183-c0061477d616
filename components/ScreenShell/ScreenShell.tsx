import React, { useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ClickStreamActivityEventType } from "@vivantehealth/vivante-core";
import { Box } from "@mui/material";
import Head from "next/head";
import { useRouter } from "next/router";

import { ErrorModal } from "@Components/ErrorModal/ErrorModal";
import { ErrorStateSlice, selectError } from "@Features/error/store/errorStateSlice";
import { memberStateSelector } from "@Features/member/store/memberStateSlice";
import { NavigationStateSlice, navigationStateSelector } from "@Features/navigation/store/navigationStateSlice";
import { BaseSnackbar } from "@Features/snackbar/components/BaseSnackbar";
import { snackbarSelector, SnackbarStateSlice } from "@Features/snackbar/store/snackbarStateSlice";
import { useAnalyticsHook } from "@Hooks/analyticsHook";

type ScreenShellProps = Readonly<{
  children: React.ReactNode;
}>;

export const ScreenShell = ({ children }: ScreenShellProps) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const pageTitle = useSelector(navigationStateSelector("pageTitle"));
  const snackbarState = useSelector(snackbarSelector);
  const navDrawerOpen = useSelector(navigationStateSelector("navDrawerOpen"));
  const member = useSelector(memberStateSelector("member"));

  const [currentRoute, setCurrentRoute] = useState<string | undefined>();
  const { sendEventAnalytics } = useAnalyticsHook();

  const routeChangeComplete = useCallback(
    (updatedRoute: string) => {
      if (updatedRoute !== currentRoute && currentRoute) {
        dispatch(NavigationStateSlice.actions.pageTitleChanged(window.location.host + updatedRoute));
        setCurrentRoute(updatedRoute);
      }
    },
    [currentRoute, dispatch],
  );

  useEffect(() => {
    // unsubscribe on component destroy in useEffect return function
    router.events.on("routeChangeComplete", routeChangeComplete);
    return () => {
      router.events.off("routeChangeComplete", routeChangeComplete);
    };
  }, [currentRoute, routeChangeComplete, router.events]);

  useEffect(() => {
    sendEventAnalytics(ClickStreamActivityEventType.INITIAL_PAGE_LOAD, {
      pathname: window.location.pathname,
    });
    // We only want this effect to run once on page load, so we pass an empty array as the second argument.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <Head key="screenShellHead">
        <title>{pageTitle}</title>
      </Head>
      {snackbarState.isOpen && (
        <BaseSnackbar
          snackbarProperties={{
            isOpen: snackbarState.isOpen,
            message: snackbarState.message,
            duration: snackbarState.duration,
          }}
          onClose={() => dispatch(SnackbarStateSlice.actions.toggleSnackbar({ isOpen: false }))}
          variant={snackbarState?.variant}
          anchorOrigin={snackbarState?.anchorOrigin}
          sx={snackbarState?.sx}
          navDrawerOpen={navDrawerOpen}
          member={member}
        />
      )}
      <ErrorModal errorSelector={selectError} onClose={() => dispatch(ErrorStateSlice.actions.clearError())} />
      <Box padding={0} margin={0}>
        {children}
      </Box>
    </>
  );
};
