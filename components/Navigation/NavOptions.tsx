import { IconVariant } from "@Components/AppIcon/AppIcon";
import { ScreenName } from "@Components/Navigation/ScreenName";
import { Routes } from "@Types";

export enum NavOptions {
  HOME = "Home",
  CARE_PLAN = "Care Plan",
  COURSES = "Courses",
  ARTICLES = "Articles",
  PROGRESS = "Progress",
  GUT_CHECK = "GutCheck",
  CARE_TEAM = "Care Team",
}

type NavOption = {
  name: NavOptions;
  url: string;
  icon: IconVariant;
  screenName: ScreenName;
  showIfFeatureFlag?: string;
  showIfFeatureFlagValue?: boolean;
};

export const navOptions: NavOption[] = [
  {
    name: NavOptions.HOME,
    url: Routes.HOME,
    icon: "Home",
    screenName: "Home",
  },
  {
    name: NavOptions.CARE_PLAN,
    url: Routes.CARE_PLAN,
    icon: "CarePlan",
    screenName: "ActionPlan",
  },
  {
    name: NavOptions.COURSES,
    url: Routes.COURSES,
    icon: "VideoCourse",
    screenName: "CourseLanding",
  },
  {
    name: NavOptions.ARTICLES,
    url: Routes.ARTICLES,
    icon: "Document",
    screenName: "Articles",
  },
  {
    name: NavOptions.PROGRESS,
    url: Routes.PROGRESS,
    icon: "Goal",
    screenName: "HistoryDayView",
  },
  {
    name: NavOptions.GUT_CHECK,
    url: Routes.GUT_CHECK,
    icon: "GutCheck",
    screenName: "GutCheckLanding",
    showIfFeatureFlag: "newGutCheckExperienceFE",
    showIfFeatureFlagValue: false,
  },
  {
    name: NavOptions.GUT_CHECK,
    url: Routes.GUT_CHECK_NEW,
    icon: "GutCheck",
    screenName: "GutCheckNew",
    showIfFeatureFlag: "newGutCheckExperienceFE",
    showIfFeatureFlagValue: true,
  },
  {
    name: NavOptions.CARE_TEAM,
    url: Routes.CARE_TEAM,
    icon: "CareTeam",
    screenName: "CareTeam",
  },
];
