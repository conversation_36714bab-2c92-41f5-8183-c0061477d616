import * as React from "react";
import { useSelector } from "react-redux";
import { Box, IconButton, List, ListItem, ListItemButton, ListItemIcon } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import Link from "next/link";

import { appStrings } from "@Assets/app_strings";
import { styleConstants, RADIUS_FULL_PX } from "@Assets/style_constants";
import { MotionDrawer } from "@Components/animation/MotionDrawer";
import { MotionTypography } from "@Components/animation/MotionTypography";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { NEW_GUTCHECK_EXPERIENCE_FRONTEND_FF } from "@Features/gutCheck/assets/constants";
import { getDateDay } from "@Features/gutCheck/utils/GutCheck.utils";
import { memberStateSelector } from "@Features/member/store/memberStateSlice";
import { useFeatureFlagWithKey } from "@Hooks/useFeatureFlag";

import { navOptions, NavOptions } from "./NavOptions";
import { ScreenName } from "./ScreenName";

type FeatureFlagsForNav = Record<string, boolean>;

type DrawerProps = Readonly<{
  activeNavOption: NavOptions;
  navDrawerOpen: boolean;
  navDrawerOpenCallback: (open: boolean) => void;
  children?: React.ReactNode;
  hide?: boolean;
  updatePageTitle: (screenName: ScreenName) => void;
  contentHeight: string;
}>;

const variants = {
  open: { width: styleConstants.navDrawerWidth },
  closed: { width: styleConstants.navDrawerClosedWidth },
};

const typographyVariant = {
  closed: { width: 0 },
  open: { width: "auto" },
};

const SPRING_TRANSITION = { type: "spring", stiffness: 100, damping: 15, mass: 1, duration: 0.8 };

export const NavigationDrawer = ({
  children,
  activeNavOption,
  navDrawerOpen,
  hide,
  navDrawerOpenCallback,
  updatePageTitle,
  contentHeight,
}: DrawerProps) => {
  const member = useSelector(memberStateSelector("member"));

  const memberDayOfDOB = getDateDay(member?.birthDate ?? "");

  const { isReady, treatment } = useFeatureFlagWithKey(NEW_GUTCHECK_EXPERIENCE_FRONTEND_FF, memberDayOfDOB);
  const isNewGutCheckExperienceFE = isReady && treatment === "on";

  const featureFlagsForNav: FeatureFlagsForNav = {
    newGutCheckExperienceFE: isNewGutCheckExperienceFE,
  };

  return (
    <Box
      display={"flex"}
      sx={{
        height: hide ? styleConstants.contentHeightIntakeSurvey : contentHeight,
      }}
    >
      <MotionDrawer
        variant="permanent"
        open={navDrawerOpen}
        animate={navDrawerOpen ? "open" : "closed"}
        variants={variants}
        transition={SPRING_TRANSITION}
        initial={false}
        sx={{
          "& .MuiPaper-root": {
            zIndex: 1,
            pt: 7,
            background: color.background.surface.primary,
            position: "relative",
            px: navDrawerOpen ? 5 : "20px",
            width: "100%",
            border: "none",
          },
          display: hide ? "none" : "initial",
          flexShrink: 0,
          whiteSpace: "nowrap",
          boxSizing: "border-box",
          overflowX: "hidden",
          border: "none",
        }}
        role="navigation"
      >
        <List style={{ minWidth: "40px", width: "100%", padding: 0 }}>
          {navOptions.map((navItem) => {
            // skip this menu item if the item is under a freature flag, the feature flag is not ready
            // or the value is not the expected one
            if (
              navItem.showIfFeatureFlag &&
              navItem.showIfFeatureFlag in featureFlagsForNav &&
              (navItem.showIfFeatureFlagValue !== featureFlagsForNav[navItem.showIfFeatureFlag] || !isReady)
            ) {
              return null;
            }

            const isLinkActive = activeNavOption === navItem.name;
            const linkAriaLabel = appStrings.a11y.linkLabel(isLinkActive, navItem.name);

            return (
              <ListItem
                key={navItem.name}
                disablePadding
                sx={{
                  display: "block",
                  width: "100%",
                  borderRadius: RADIUS_FULL_PX,
                  background: isLinkActive ? color.background.surface.secondary : "transparent",
                  "&:hover": {
                    borderRadius: RADIUS_FULL_PX,
                    background: color.background.surface.secondary,
                  },
                  "& .MuiButtonBase-root:hover": { background: "transparent" },
                  ".Mui-focusVisible": {
                    borderRadius: RADIUS_FULL_PX,
                    background: color.background.surface.secondary,
                  },
                }}
              >
                <ListItemButton
                  sx={{
                    mb: 4,
                    height: "40px",
                    px: 4,
                    py: 2,
                    justifyContent: "flex-start",
                  }}
                  disableRipple
                  onClick={() => {
                    updatePageTitle(navItem.screenName);
                  }}
                  LinkComponent={Link}
                  href={navItem.url}
                  aria-label={linkAriaLabel}
                >
                  <ListItemIcon
                    aria-hidden
                    sx={{
                      mr: 2,
                      ml: 0,
                      minWidth: 0,
                    }}
                  >
                    <AppIcon name={navItem.icon} color={isLinkActive ? color.icon.brand : color.icon.subtle} />
                  </ListItemIcon>
                  <MotionTypography
                    variant="h4"
                    tabIndex={-1}
                    animate={navDrawerOpen ? "open" : "closed"}
                    variants={typographyVariant}
                    transition={SPRING_TRANSITION}
                    initial={false}
                    sx={{
                      overflow: "hidden",
                      opacity: navDrawerOpen ? "1" : "0",
                      color: isLinkActive ? color.text.strong : color.text.subtle,
                    }}
                  >
                    {navItem.name}
                  </MotionTypography>
                </ListItemButton>
              </ListItem>
            );
          })}
        </List>
        <IconButton
          sx={{
            height: "40px",
            width: "40px",
            padding: 0,
            borderRadius: RADIUS_FULL_PX,
            marginTop: 5,
            border: `1px solid ${color.border.action.default}`,
          }}
          onClick={() => navDrawerOpenCallback(!navDrawerOpen)}
        >
          {navDrawerOpen ? (
            <AppIcon
              name="LeftChevron"
              containerStyles={{
                color: color.icon.strong,
              }}
            />
          ) : (
            <AppIcon
              name="RightChevron"
              containerStyles={{
                color: color.icon.strong,
              }}
            />
          )}
        </IconButton>
      </MotionDrawer>
      <Box
        sx={{ flexGrow: 1, pt: 7, overflow: "auto", background: color.background.page, position: "relative" }}
        role="main"
        id="content-body"
      >
        {children}
      </Box>
    </Box>
  );
};
