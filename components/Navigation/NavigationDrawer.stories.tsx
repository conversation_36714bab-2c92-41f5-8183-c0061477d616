import Paper from "@mui/material/Paper";
import type { Meta, StoryObj } from "@storybook/nextjs";
import { useArgs } from "storybook/preview-api";

import { styleConstants } from "@Assets/style_constants";
import { NavigationDrawer } from "@Components/Navigation/NavigationDrawer";
import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

import { navOptions } from "./NavOptions";

const meta: Meta<typeof NavigationDrawer> = {
  component: NavigationDrawer,
  title: "@Components/Navigation",
  decorators: cylinderThemeDecorator,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
};

export default meta;

type Story = StoryObj<typeof NavigationDrawer>;

export const Default: Story = {
  args: {
    activeNavOption: navOptions[0].name,
    navDrawerOpen: true,
  },
  render: function Render() {
    const [args, updateArgs] = useArgs();

    return (
      <Paper>
        <NavigationDrawer
          activeNavOption={args.activeNavOption}
          navDrawerOpen={args.navDrawerOpen}
          navDrawerOpenCallback={(isOpen: boolean) => {
            updateArgs({ navDrawerOpen: isOpen });
          }}
          contentHeight={styleConstants.contentHeightIntakeSurvey}
          updatePageTitle={() => {}}
        />
      </Paper>
    );
  },
};
