import { <PERSON>, Button, Chip, Typography } from "@mui/material";
import { color, radius } from "@vivantehealth/design-tokens";
import { useRouter } from "next/router";

import { appStrings } from "@Assets/app_strings";
import carePlanBgImage from "@Assets/images/care_plan_card_bg_v2.webp";
import CarePlanFilledIcon from "@Assets/images/care_plan_filled.svg";
import WarningDiamondIcon from "@Assets/images/warning_diamond.svg";
import { BLUE_TO_PINK_GRADIENT, GradientBorderCard } from "@Components/GradientBorderCard/GradientBorderCard";
import { Routes } from "@Types";

const CARE_PLAN_STRINGS = appStrings.features.home.carePlanSummary;

export type CarePlanHomeCardProps = Readonly<{
  title: string;
  subtitle?: string;
  buttonText?: string;
  href?: string;
  cardType?: "carePlan" | "intervention";
}>;

export const CARD_HORIZONTAL_PADDING = `88px`;

export const CarePlanHomeCard = ({ title, subtitle, buttonText, href, cardType }: CarePlanHomeCardProps) => {
  const router = useRouter();
  const isIntervention = cardType === "intervention";

  const cardBackground = isIntervention ? color.background.information : `url(${carePlanBgImage.src})`;

  const chipIcon = isIntervention ? (
    <WarningDiamondIcon color={color.icon.information} />
  ) : (
    <CarePlanFilledIcon color={color.icon.default} />
  );

  return (
    <GradientBorderCard
      gradient={isIntervention ? BLUE_TO_PINK_GRADIENT : color.border.default}
      borderRadius={radius.radius2}
      borderWidth={1}
      contentSx={{
        background: `${cardBackground} right bottom / auto no-repeat`,
        backgroundSize: "cover",
        p: 4,
        pr: 5,
      }}
    >
      <Box display="flex" alignItems="center" flexGrow={1} justifyContent="space-between" width="100%">
        <Box display="grid" whiteSpace="normal">
          <Chip
            icon={chipIcon}
            size="small"
            label={isIntervention ? CARE_PLAN_STRINGS.actionNeeded : CARE_PLAN_STRINGS.carePlan}
            tabIndex={-1}
            sx={{
              width: "fit-content",
              color: color.text.action.default,
              "& .MuiChip-icon": {
                m: 0,
                mr: 1,
              },
              border: `1px solid ${color.border.action.default}`,
              backgroundColor: color.background.surface.primary,
              ...(isIntervention
                ? {
                    backgroundColor: "rgba(73, 91, 255, 0.12)",
                    borderColor: "transparent",
                  }
                : null),
            }}
          />
          <Typography variant="h4" tabIndex={-1} mt={3}>
            {title}
          </Typography>
          {subtitle ? (
            <Typography variant="bodyDense" color={color.text.strong} tabIndex={-1}>
              {subtitle}
            </Typography>
          ) : null}
        </Box>
        <Button
          variant="cardDark"
          size="small"
          onClick={() => router.push(href ?? Routes.HOME)}
          aria-label={cardType ? appStrings.a11y.carouselAction[cardType](title, subtitle ?? "") : ""}
        >
          {buttonText}
        </Button>
      </Box>
    </GradientBorderCard>
  );
};
