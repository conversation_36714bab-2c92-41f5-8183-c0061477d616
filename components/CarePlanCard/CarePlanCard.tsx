import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Chip from "@mui/material/Chip";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";
import { color } from "@vivantehealth/design-tokens";
import Link from "next/link";

import { appStrings } from "@Assets/app_strings";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { CarePlanSkeleton } from "@Components/CarePlanCard/CarePlanSkeleton";

type CarePlanCardButtonOrLinkProps = { onClick: () => void; href?: never } | { onClick?: never; href: string };

export type CarePlanCardProps = Readonly<{
  title: string;
  subtitle?: string;
  dateString?: string;
  imageSrc?: string;
  imageAltText?: string;
  chipText?: string;
  ctaText?: string;
  isContentCompleted?: boolean;
  isInterventionCard?: boolean;
  isSkeletonLoading?: boolean;
}> &
  CarePlanCardButtonOrLinkProps;

export const CarePlanCard = ({
  title,
  subtitle,
  dateString,
  imageSrc,
  imageAltText,
  chipText,
  ctaText,
  href,
  onClick,
  isContentCompleted,
  isInterventionCard,
  isSkeletonLoading,
}: CarePlanCardProps) => {
  if (isSkeletonLoading) {
    return <CarePlanSkeleton />;
  }

  const statusAriaText = !isInterventionCard && isContentCompleted ? appStrings.buttonText.complete : chipText;

  const ariaLabel = `${
    isInterventionCard ? appStrings.a11y.interventionCard : appStrings.a11y.carePlanCard
  }. ${appStrings.a11y.title(title)}, ${
    subtitle ? `${appStrings.a11y.subtitle(subtitle)},` : ""
  } ${appStrings.a11y.status(statusAriaText)}, ${appStrings.a11y.callToAction(ctaText ?? "")}.`;

  const chip = isContentCompleted ? <AppIcon name="Completed" /> : <Chip size="small" label={chipText} tabIndex={-1} />;
  const content = (
    <Box
      sx={{
        display: "flex",
        width: "100%",
        height: "fit-content",
        padding: 4,
        justifyContent: "center",
        boxSizing: "border-box",
        gap: 4,
        border: "none",
      }}
    >
      <Box display="flex" alignItems="center">
        {imageSrc ? (
          <img
            style={{
              width: "40px",
              height: "40px",
            }}
            src={imageSrc}
            alt={imageAltText}
          />
        ) : (
          <AppIcon
            name="IsometricOrangeBubble"
            iconAriaLabel={imageAltText}
            size="lg"
            containerStyles={{ flexShrink: 0 }}
          />
        )}
      </Box>

      <Box width={"100%"} px={2} display="grid" alignItems="center">
        <Typography variant="h3" tabIndex={-1}>
          {title}
        </Typography>
        {subtitle && (
          <Typography variant="body" sx={{ mt: 1, color: color.text.subtle }} tabIndex={-1}>
            {subtitle}
          </Typography>
        )}
        {dateString && (
          <Typography variant="body" sx={{ mt: 1, color: color.text.subtle }}>
            {dateString}
          </Typography>
        )}
      </Box>
      <Box display="flex" flexDirection="column" alignItems="center">
        {isInterventionCard ? (
          <Button variant="secondary">{ctaText}</Button>
        ) : (
          <>
            {chip}
            <Typography
              variant="actionDense"
              sx={{
                display: "block",
                textAlign: "center",
                mt: !isInterventionCard ? 1 : 0,
                borderBottom: ctaText && `1px solid ${color.border.default}`,
              }}
              tabIndex={-1}
            >
              {ctaText}
            </Typography>
          </>
        )}
      </Box>
    </Box>
  );

  if (onClick) {
    return (
      <Paper
        sx={{
          display: "block",
          width: "100%",
          p: 0,
          textAlign: "left",
        }}
        aria-label={ariaLabel}
        onClick={onClick}
        component="button"
      >
        {content}
      </Paper>
    );
  }

  return (
    <Link style={{ textDecoration: "none", display: "block", width: "100%" }} aria-label={ariaLabel} href={href}>
      <Paper sx={{ p: 0 }}>{content}</Paper>
    </Link>
  );
};
