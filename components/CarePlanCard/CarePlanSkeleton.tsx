import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Skeleton from "@mui/material/Skeleton";
import { color } from "@vivantehealth/design-tokens";

export const CarePlanSkeleton = () => {
  return (
    <Paper
      sx={{
        display: "flex",
        width: "100%",
        height: "fit-content",
        padding: 4,
        justifyContent: "center",
        alignItems: "center",
        boxSizing: "border-box",
        borderColor: color.border.default,
        gap: 5,
      }}
    >
      <Skeleton variant="circular" width={40} height={40} sx={{ flexShrink: 0 }} />
      <Box sx={{ width: "100%" }}>
        <Skeleton variant="text" width={"50%"} height={33} />
        <Skeleton variant="text" width={"60%"} height={20} />
      </Box>

      <Box display="flex" flexDirection="column" alignItems="center" sx={{ flexShrink: 0 }}>
        <Skeleton variant="rounded" width={42} height={33} sx={{ mb: 1 }} />
        <Skeleton variant="text" width={41} height={20} />
      </Box>
    </Paper>
  );
};
