import type { Meta, StoryObj } from "@storybook/nextjs";

import { CarePlanCard } from "@Components/CarePlanCard/CarePlanCard";
import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

const meta: Meta<typeof CarePlanCard> = {
  title: "@Components/CarePlanCard",
  component: CarePlanCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    ...cylinderThemeDecorator,
    (Story) => (
      <div style={{ width: "643px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof CarePlanCard>;

const defaultProps = {
  href: "careplanpage",
  imageSrc:
    "https://storage.googleapis.com/vh-media-assets-6ae3/githrive/action-plan/module-images/Education%20General.png",
  imageAltText: "placeholder image",
};

export const SingleLineNotStartedCarePlanCard: Story = {
  args: {
    ...defaultProps,
    title: "Learn about digestive health",
    subtitle: "Learn about what may be causing your symptoms",
    chipText: "0/2",
    ctaText: "Start",
    imageSrc: "",
  },
};

export const LinkCarePlanCard: Story = {
  args: {
    ...defaultProps,
    title: "Learn about digestive health",
    subtitle: "Learn about what may be causing your symptoms",
    chipText: "0/2",
    ctaText: "Start",
    imageSrc: "",
  },
};

export const SkeletonCard: Story = {
  args: {
    ...defaultProps,
    title: "Learn about digestive health",
    subtitle: "Learn about what may be causing your symptoms",
    chipText: "1/2",
    ctaText: "Resume",
    isSkeletonLoading: true,
  },
};

export const SingleLineIncompleteCarePlanCard: Story = {
  args: {
    ...defaultProps,
    title: "Learn about digestive health",
    subtitle: "Learn about what may be causing your symptoms",
    chipText: "1/2",
    ctaText: "Resume",
  },
};

export const NoSubtitlePlanCard: Story = {
  args: {
    ...defaultProps,
    title: "Learn about digestive health",
    chipText: "1/2",
    ctaText: "Resume",
  },
};

export const MultiLineCareIncompletePlanCard: Story = {
  args: {
    ...defaultProps,
    title: "I am the title, and I am so amazing that I need to be extra long.",
    subtitle: "I am the subtitle, and I am more amazing than the title, so I need to be extra extra extra long.",
    chipText: "1/2",
    ctaText: "Resume",
  },
};

export const SingleLineCompleteCarePlanCard: Story = {
  args: {
    ...defaultProps,
    title: "I am the title",
    subtitle: "I am the subtitle",
    chipText: "2/2",
    isContentCompleted: true,
  },
};

export const HistoricalCompleteCarePlanCard: Story = {
  args: {
    ...defaultProps,
    title: "Learn about digestive health",
    chipText: "2/2",
    isContentCompleted: true,
    dateString: "Completed on May 19",
    imageSrc: "",
  },
};

export const MultiLineCareCompletePlanCard: Story = {
  args: {
    ...defaultProps,
    title: "I am the title, and I am so amazing that I need to be extra long.",
    subtitle: "I am the subtitle, and I am more amazing than the title, so I need to be extra extra extra long.",
    chipText: "2/2",
    isContentCompleted: true,
  },
};
export const InterventionCard: Story = {
  args: {
    ...defaultProps,
    title: "I am the title",
    subtitle: "I am the subtitle",
    ctaText: "Start",
    isInterventionCard: true,
  },
};
