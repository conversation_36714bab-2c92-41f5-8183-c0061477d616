import type { Meta, StoryObj } from "@storybook/nextjs";

import avatarTestImage from "@Assets/images/SquareAvatarTestImage.jpg";

import { CareTeamAvatar } from "./CareTeamAvatar";

const meta: Meta<typeof CareTeamAvatar> = {
  title: "@Components/CareTeamAvatar",
  component: CareTeamAvatar,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof CareTeamAvatar>;

export const Primary: Story = {
  args: {
    avatarLink: avatarTestImage.src,
  },
};
