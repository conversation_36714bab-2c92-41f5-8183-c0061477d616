import { Box } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { RADIUS_FULL_PX, iconSize } from "@Assets/style_constants";

type CareTeamAvatarProps = Readonly<{
  avatarLink: string;
  altText: string;
  includeInTabIndex?: boolean;
  width?: string;
  hasUnreadMessages?: boolean;
}>;

export const CareTeamAvatar = ({
  avatarLink,
  altText,
  includeInTabIndex,
  width = "48px",
  hasUnreadMessages = false,
}: CareTeamAvatarProps) => {
  return (
    // it's always a square so height is the same as width
    <Box height={width}>
      {hasUnreadMessages && (
        <Box
          position="absolute"
          bgcolor={color.background.brand.default}
          width={iconSize.xs.width}
          height={iconSize.xs.width}
          border={`1px solid ${color.icon.onColorFill}`}
          borderRadius={RADIUS_FULL_PX}
        />
      )}
      <img
        src={avatarLink}
        style={{ borderRadius: RADIUS_FULL_PX, width }}
        alt={altText}
        tabIndex={includeInTabIndex ? 0 : -1}
      />
    </Box>
  );
};
