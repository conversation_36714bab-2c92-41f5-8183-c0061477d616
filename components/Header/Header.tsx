import { CSSProperties } from "react";
import { Member } from "@vivantehealth/vivante-core";
import { Grid, useMediaQuery, useTheme, Button, Box } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import Link from "next/link";

import { appStrings } from "@Assets/app_strings";
import { styleConstants } from "@Assets/style_constants";
import { AppIcon } from "@Components/AppIcon/AppIcon";
import { Banner } from "@Components/Banner/Banner";
import { BasicMenu } from "@Components/BasicMenu/BasicMenu";
import { ScreenName } from "@Features/navigation/types/screenName";
import { Routes } from "@Types";

const ARIA_STRINGS = appStrings.a11y;
const BUTTON_STRINGS = appStrings.buttonText;
const ANNOUNCEMENT_STRINGS = appStrings.announcements;

type HeaderProps = Readonly<{
  memberInformation?: Member;
  settingsCallback: () => void;
  logoutCallback: () => void;
  reportIssuesCallback: () => void;
  dismissBannerCallback: () => void;
  isBannerVisible: boolean;
  isBootstrapped: boolean;
  hasUnreadChat: boolean;
  isIntakeSurvey?: boolean | null;
  updatePageTitle: (screenName: ScreenName) => void;
}>;
/**
 * Determine the name to display within the header dropdown.
 * We can encounter the case where the memberInformation object is not fully populated
 * So as to not show null we should display the email address as a fallback followed by an empty string if all else fails
 */
const getDisplayName = (memberInformation: Member, isXsScreen: boolean) => {
  if (isXsScreen) {
    return memberInformation?.firstName ?? memberInformation?.email ?? "";
  }

  return memberInformation?.firstName
    ? `${memberInformation.firstName} ${memberInformation?.lastName ?? ""}`
    : memberInformation?.email ?? "";
};

export const Header = ({
  memberInformation,
  isBannerVisible,
  dismissBannerCallback,
  logoutCallback,
  settingsCallback,
  reportIssuesCallback,
  isBootstrapped,
  hasUnreadChat,
  isIntakeSurvey,
  updatePageTitle,
}: HeaderProps) => {
  const theme = useTheme();
  const isXsScreen = useMediaQuery(theme.breakpoints.only("xs"));
  const menuItems = [
    {
      text: BUTTON_STRINGS.settings,
      onClick: settingsCallback,
    },
    {
      text: BUTTON_STRINGS.reportIssue,
      onClick: reportIssuesCallback,
    },
    {
      text: BUTTON_STRINGS.logOut,
      onClick: logoutCallback,
      isDisabled: !isBootstrapped,
    },
  ];

  return (
    <header>
      {!isIntakeSurvey ? (
        <Banner
          display={isBannerVisible}
          onDissmiss={dismissBannerCallback}
          text={ANNOUNCEMENT_STRINGS.cylinderReleaseBold}
          readMoreLink={ANNOUNCEMENT_STRINGS.cylinderReleaseLink}
        />
      ) : null}
      <Grid
        container
        px={5}
        height={styleConstants.headerHeight}
        bgcolor={color.background.surface.primary}
        alignItems="center"
        display="flex"
        role="banner"
        borderBottom={`1px solid ${color.border.default}`}
      >
        <Grid item xs={9} sm={5} lg={3}>
          <Grid container direction="row" alignItems="center">
            <Grid item>
              <AppIcon name="CompanyLogo" size="headerLogo" includeInTabIndex />
            </Grid>
            <Grid item px={4}>
              <Button
                variant="tertiary"
                size="small"
                sx={styles.skipToContentBtn}
                href="#content-body"
                disableFocusRipple
              >
                {ARIA_STRINGS.skipToContent}
              </Button>
            </Grid>
          </Grid>
        </Grid>
        {memberInformation && (
          <Grid item xs={3} sm={7} lg={9} justifyContent="flex-end" display="flex">
            <Box display="flex" alignItems="center" gap={3}>
              {!isIntakeSurvey && (
                <Button
                  variant="ghost"
                  aria-label={
                    hasUnreadChat ? ARIA_STRINGS.chat.headerIconNewMessages : ARIA_STRINGS.chat.headerIconNoMessages
                  }
                  onClick={() => updatePageTitle("Chat")}
                  href={Routes.CHAT}
                  component={Link}
                  endIcon={<AppIcon name={hasUnreadChat ? "ChatBubbleNotification" : "ChatBubble"} />}
                >
                  Chat
                </Button>
              )}
              <BasicMenu
                id="top-bar"
                ariaLabel="user menu dropdown"
                menuItems={isIntakeSurvey ? [menuItems[2]] : menuItems}
                menuIconRight="DownChevron"
              >
                {getDisplayName(memberInformation, isXsScreen)}
              </BasicMenu>
            </Box>
          </Grid>
        )}
      </Grid>
    </header>
  );
};

const styles = {
  skipToContentBtn: {
    transform: `translateX(-300%)`, // hide offscreen
    zIndex: 1000, // ensure link is above all other elements
    "&:focus": {
      transform: `translateX(0)`, // show onscreen
    },
  },
} as const satisfies Record<string, CSSProperties | Record<string, CSSProperties>>;
