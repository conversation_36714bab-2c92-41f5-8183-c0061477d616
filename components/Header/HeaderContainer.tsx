import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { Ticket } from "@vivantehealth/vivante-core";

import { Header } from "@Components/Header/Header";
import { chatStateSelector } from "@Features/chat/store/chatStateSlice";
import { loadChat } from "@Features/chat/store/chatThunks";
import { hasUnreadMessages } from "@Features/chat/utils/chat.utils";
import { memberStateSelector } from "@Features/member/store/memberStateSlice";
import { setNotificationSettings } from "@Features/memberPreferences/store/memberPreferencesStateSlice";
import { NavigationStateSlice } from "@Features/navigation/store/navigationStateSlice";
import { updatePageTitle } from "@Features/navigation/utils/updatePageTitle";
import { ReportIssuesModal, OnIssueModalSendArgs } from "@Features/reportTicket/ReportIssuesModal";
import { submitReportTicket } from "@Features/reportTicket/store/reportTicketSlice";
import { useSignOutHook } from "@Hooks/signOutHook";
import { useBootstrapCompleted } from "@Hooks/useBootstrapCompleted";
import { useHeaderHook } from "@Hooks/useHeaderHook";
import { useAppDispatch } from "@Store/hooks";
import { Routes } from "@Types";

type HeaderContainerProps = Readonly<{
  isIntakeSurvey?: boolean | null;
}>;

export const HeaderContainer = ({ isIntakeSurvey }: HeaderContainerProps) => {
  const isBootstrapped = useBootstrapCompleted();
  const { bannerName, isBannerVisible } = useHeaderHook();
  const dispatch = useAppDispatch();
  const { signOutOfApp } = useSignOutHook();

  const [isIssuesModalOpen, setIsIssuesModalOpen] = useState<boolean>(false);
  const [modalKey, setModalKey] = useState(0);
  const member = useSelector(memberStateSelector("member"));
  const loadState = useSelector(chatStateSelector("loadState"));
  const healthCoachChat = useSelector(chatStateSelector("healthCoach"));
  const dietitianChat = useSelector(chatStateSelector("dietitian"));
  const hasUnreadChat = hasUnreadMessages(healthCoachChat, dietitianChat);

  useEffect(() => {
    if (!loadState) dispatch(loadChat({ displayError: false, batchSize: 1 }));
  }, [loadState, dispatch]);

  const onLogout = () => {
    signOutOfApp();
  };

  const onSettingsClick = () => {
    dispatch(
      NavigationStateSlice.actions.navigateTo({
        path: Routes.SETTINGS,
        screenName: "Settings",
      }),
    );
  };

  const onIssueModalClose = () => {
    setModalKey((prev) => prev + 1);
    setIsIssuesModalOpen(false);
  };

  const onIssuesModalSend = ({ message }: OnIssueModalSendArgs) => {
    const ticket: Ticket = {
      subject: `Web Ticket`,
      message: `
          Message: ${message}
          MemberId: ${member?.id}
          Browser/Os: ${navigator.userAgent}`,
    };

    dispatch(submitReportTicket({ ticket }));
    onIssueModalClose();
  };

  const onDismissBanner = () => {
    dispatch(
      setNotificationSettings({
        preferenceId: bannerName,
        value: !isBannerVisible,
      }),
    );
  };

  return (
    <>
      <ReportIssuesModal
        key={modalKey}
        isOpen={isIssuesModalOpen}
        onExitClick={onIssueModalClose}
        onSend={onIssuesModalSend}
      />
      <Header
        memberInformation={member}
        logoutCallback={onLogout}
        settingsCallback={onSettingsClick}
        reportIssuesCallback={() => setIsIssuesModalOpen(true)}
        dismissBannerCallback={onDismissBanner}
        isBannerVisible={isBannerVisible}
        isBootstrapped={!!isBootstrapped}
        hasUnreadChat={hasUnreadChat}
        isIntakeSurvey={isIntakeSurvey}
        updatePageTitle={updatePageTitle}
      />
    </>
  );
};
