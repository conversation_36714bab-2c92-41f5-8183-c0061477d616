import type { <PERSON>a, StoryObj } from "@storybook/nextjs";

import { Head<PERSON> } from "./Header";
import { cylinderThemeDecorator } from "../stories/cylinderThemeDecorator";

const meta: Meta<typeof Header> = {
  title: "@Components/Header",
  component: Header,
  parameters: {
    backgrounds: {
      default: "grey",
      values: [{ name: "grey", value: "#F6F7F4" }],
    },
  },
  tags: ["autodocs"],
  decorators: cylinderThemeDecorator,
};

export default meta;
type Story = StoryObj<typeof Header>;

export const Default: Story = {
  args: {
    memberInformation: {
      id: "",
      firstName: "John",
      lastName: "Doe",
      avatarLink: "",
      email: "",
      birthDate: "",
      phoneMobile: "",
      setting: {
        email: false,
        timezone: "",
        memberNutritionReady: false,
        memberShowIntroductionScreen: false,
        sessionNotificationOffsetBeforeStart: "",
        featureNutritionEnabled: false,
        featureMicrobiomeEnabled: false,
        featureFoodLogEnabled: false,
        featureSymptomLogEnabled: false,
        onboardingPending: false,
      },
      hasGroup: false,
      condition: [],
    },
    hasUnreadChat: false,
    isIntakeSurvey: false,
    isBannerVisible: false,
    isBootstrapped: true,
    dismissBannerCallback: () => alert("banner dismissed"),
    logoutCallback: () => alert("logging out"),
    settingsCallback: () => alert("settings clicked"),
    reportIssuesCallback: () => alert("reporting issues"),
    updatePageTitle: () => {},
  },
};
