import type { Meta, StoryObj } from "@storybook/nextjs";

import { InputWithLabel } from "./InputWithLabel";
import { cylinderThemeDecorator } from "../stories/cylinderThemeDecorator";

const meta: Meta<typeof InputWithLabel> = {
  decorators: cylinderThemeDecorator,
  title: "@Components/InputWithLabel",
  component: InputWithLabel,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof InputWithLabel>;

export const InputWithPlaceholder: Story = {
  args: {
    label: "Input with placeholder",
    id: "inputWithPlaceholder",
    placeholder: "Placeholder text",
  },
};

export const InputWithText: Story = {
  args: {
    label: "Input with text",
    id: "inputWithText",
    value: "Cylinder health",
  },
};

export const InputRequired: Story = {
  args: {
    label: "Input required",
    id: "inputRequired",
    value: "Cylinder health",
    required: true,
  },
};

export const InputWithHelperText: Story = {
  args: {
    label: "Input with helper text",
    id: "inputWithHelperText",
    value: "Cylinder health",
    helperText: "Helper text",
  },
};

export const DisabledState: Story = {
  args: { label: "Disabled state", id: "disabledState", value: "Disabled state", disabled: true },
};

export const ErrorState: Story = {
  args: { label: "Error state", id: "errorState", value: "Error state", error: true, helperText: "Error message" },
};
