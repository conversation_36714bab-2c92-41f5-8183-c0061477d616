import { CSSProperties } from "react";
import { FormControl } from "@mui/base";
import { FormHelperText, InputLabel, OutlinedInput } from "@mui/material";
import { FieldValues, UseFormRegister } from "react-hook-form";

import { Rules } from "@Components/form/Fields";
import { sentenceCaseLabel } from "@Utils/sentenceCaseLabel";

export type InputType = "email" | "password" | "number" | "text" | "date" | "telephone";
export type InputMode = "email" | "text" | "search" | "none" | "tel" | "url" | "numeric" | "decimal";

type InputWithLabelProps = {
  label: string;
  name: string;
  id: string;
  type?: InputType;
  value?: string;
  placeholder?: string;
  isOptional?: boolean;
  isMultiline?: boolean;
  numberOfRows?: number;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  required?: boolean;
  fullWidth?: boolean;
  rules?: Rules;
  register?: UseFormRegister<FieldValues>;
  onChange?: (value: string) => void;
  styles?: CSSProperties;
  inputMode?: InputMode;
  pattern?: string;
};

export const InputWithLabel = ({
  label,
  name,
  id,
  type = "text",
  value,
  isMultiline,
  numberOfRows,
  placeholder,
  isOptional,
  error,
  helperText,
  disabled,
  required,
  fullWidth,
  rules,
  register,
  onChange,
  styles,
  inputMode = "text",
  pattern,
}: InputWithLabelProps) => {
  const helperTextId = helperText ? `${id}-helper-text` : undefined;

  return (
    <FormControl>
      <InputLabel htmlFor={id} error={error} required={required}>
        {isOptional ? `${sentenceCaseLabel(label)} - Optional` : sentenceCaseLabel(label)}
      </InputLabel>

      <OutlinedInput
        id={id}
        value={value}
        onChange={(event) => {
          if (onChange) {
            onChange(event.target.value);
          }
        }}
        error={error}
        disabled={disabled}
        placeholder={placeholder}
        aria-describedby={helperTextId}
        name={name}
        fullWidth={fullWidth}
        inputProps={{ ...(register && register(name, rules)), pattern }}
        required={required}
        type={type}
        multiline={isMultiline}
        rows={numberOfRows}
        sx={styles}
        inputMode={inputMode}
      />

      {helperText && (
        <FormHelperText error={error} id={helperTextId}>
          {helperText}
        </FormHelperText>
      )}
    </FormControl>
  );
};
