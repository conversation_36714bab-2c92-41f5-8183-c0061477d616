import { Box, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";

const HAVING_TROUBLE_STRINGS = appStrings.havingTrouble;

export const HavingTrouble = () => {
  return (
    <Box display="flex" gap={2}>
      <Typography variant="body" tabIndex={-1}>
        {HAVING_TROUBLE_STRINGS.helpText}
      </Typography>

      <Typography
        component="a"
        variant="link"
        href={`mailto:${appStrings.cylinderLinks.contactUsEmail}`}
        color={color.text.strong}
      >
        {HAVING_TROUBLE_STRINGS.helpTextLink}
      </Typography>
    </Box>
  );
};
