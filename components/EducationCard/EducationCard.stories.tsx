import type { Meta, StoryObj } from "@storybook/nextjs";

import { EducationCard } from "./EducationCard";
import { cylinderThemeDecorator } from "../stories/cylinderThemeDecorator";

const MOCK_ARTICLE = {
  articleCategory: {
    title: "Medicine",
    articleCategoryOrder: 2,
    id: "0ee4db8a-a924-4062-a71b-1f38110f2fae",
  },
  articleOrder: 70,
  body: "There’s so much coronavirus information out there that it’s hard to know what to believe. For accurate, reliable details, we recommend visiting the Center for Disease Control (CDC) website: https://www.cdc.gov/coronavirus/2019-ncov/index.html\r\n\r\nAlso, consider enrolling in these GIThrive Courses for help managing stress and anxiety:\r\n\r\nBetter Breathing\r\nDealing with Anxiety\t\r\nReducing Stress 1\r\nReducing Stress 2\r\nBetter Sleep\r\nDigestive Habits\r\n\r\nTo explore these and other self-paced GIThrive Courses, tap on the “Learn” tab and select “View All Courses.” \r\n\r\nFinally, remember that your Care Team is here to help. Chat in the app or call 1.800.827.SHERPA (1.800.827.7437) to connect.\r\n\r\nhttps://article-cms.dev3.vivante.dev/content/?action=edit&entity=Article&sortField=myOrder&sortDirection=ASC&menuIndex=&submenuIndex=&query=Stressed%20About%20Coronavirus?&page=1&referer=%252Fcontent%252F%253Faction%253Dsearch%2526entity%253DArticle%2526sortField%253DmyOrder%2526sortDirection%253DASC%2526menuIndex%253D%2526submenuIndex%253D%2526query%253DStressed%252520About%252520Coronavirus%253F%2526page%253D1&id=28991da5-e80c-41f9-8edf-4850647541a1",
  by: "Cylinder Team",
  id: "28991da5-e80c-41f9-8edf-4850647541a1",
  imageLink:
    "https://imagedelivery.dev.vivantehealth.com/absolute?image=https%3A%2F%2Fstorage.googleapis.com/cylinderhealth-media-assets-ea9d%2Farticles-images%2FStressed-About-Coronavirus.jpg&client_info=os%3Aweb%2CassetSize%3A1x",
  myRating: null,
  time: "1 min",
  title: "Stressed About Coronavirus?",
  totalDislikes: 0,
  totalLikes: 0,
};

const meta: Meta<typeof EducationCard> = {
  title: "@Components/EducationCard",
  component: EducationCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    ...cylinderThemeDecorator,
    (Story) => (
      <div style={{ width: "300px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof EducationCard>;

export const Primary: Story = {
  args: {
    imageSrc: MOCK_ARTICLE.imageLink,
    title: MOCK_ARTICLE.title,
    category: MOCK_ARTICLE.articleCategory.title,
    buttonText: "Read more",
    chipText: "",
    icon: "Clock",
    iconText: MOCK_ARTICLE.time,
    isSkeletonLoading: false,
    onClick: () => alert("clicked"),
  },
};
