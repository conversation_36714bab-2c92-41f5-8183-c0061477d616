import { fireEvent, render, screen } from "@testing-library/react";
import { describe, test, expect, vi } from "vitest";

import { EducationCard, EducationCardProps } from "./EducationCard";

describe("EducationCard", () => {
  const defaultProps: EducationCardProps = {
    imageSrc: "test-image.jpg",
    ariaLabel: "Test Education Card",
    title: "Test Title",
    body: "Test Body",
    buttonText: "Learn More",
    chipText: "Completed",
    category: "Test Category",
    icon: "Completed",
    iconText: "Icon Text",
    onClick: vi.fn(),
    isSkeletonLoading: false,
  };

  test("renders skeleton loading state when isSkeletonLoading is true", () => {
    const { container } = render(<EducationCard {...defaultProps} isSkeletonLoading />);
    // eslint-disable-next-line testing-library/no-container, testing-library/no-node-access
    const skeletons = container.querySelectorAll(".MuiSkeleton-root");

    // Ensure the skeletons are rendered correctly (image, category, time, title, body, and button)
    expect(skeletons.length).toBe(7);
    expect(screen.queryByText("Test Title")).not.toBeInTheDocument();
    expect(screen.queryByText("Test Body")).not.toBeInTheDocument();
    expect(screen.queryByText("Learn More")).not.toBeInTheDocument();
  });

  test("renders the EducationCard with all props", () => {
    render(<EducationCard {...defaultProps} />);

    expect(screen.getByLabelText("Test Education Card")).toBeInTheDocument();
    expect(screen.getByText("Test Title")).toBeInTheDocument();
    expect(screen.getByText("Test Body")).toBeInTheDocument();
    expect(screen.getByText("Learn More")).toBeInTheDocument();
    expect(screen.getByText("Completed")).toBeInTheDocument();
    expect(screen.getByText("Icon Text")).toBeInTheDocument();
  });

  test("renders the chip when chipText is provided", () => {
    render(<EducationCard {...defaultProps} chipText="Completed" />);
    expect(screen.getByText("Completed")).toBeInTheDocument();
  });

  test("does not render the chip when chipText is not provided", () => {
    render(<EducationCard {...defaultProps} chipText={undefined} />);
    expect(screen.queryByText("Completed")).not.toBeInTheDocument();
  });

  test("calls onClick when the card is clicked", () => {
    render(<EducationCard {...defaultProps} />);
    const card = screen.getByLabelText("Test Education Card");

    fireEvent.click(card);

    expect(defaultProps.onClick).toHaveBeenCalled();
  });
});
