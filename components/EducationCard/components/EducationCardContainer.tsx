import { ReactNode } from "react";
import { Box, Grid } from "@mui/material";
// Size based on card width in Figma (https://www.figma.com/design/ydrDgk5wf6jdpO3I3tcCEK/Learning-Center-Article-Updates?node-id=4054-7013&m=dev)
const CAROUSEL_CARD_WIDTH = "310px";

type EducationCardContainerProps = Readonly<{
  isContainedInCarousel: boolean;
  children: ReactNode;
}>;
/** For EducationCards contained within a carousel, we want a fixed width while else where should be responsive  */
export const EducationCardContainer = ({ isContainedInCarousel, children }: EducationCardContainerProps) => {
  return isContainedInCarousel ? (
    <Box minWidth={CAROUSEL_CARD_WIDTH} width={CAROUSEL_CARD_WIDTH}>
      {children}
    </Box>
  ) : (
    <Grid item md={6} sm={6} xs={12} position="relative">
      {children}
    </Grid>
  );
};
