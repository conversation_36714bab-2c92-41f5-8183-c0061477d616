import { Box, Button, Typography, Skeleton } from "@mui/material";
import { typography } from "@vivantehealth/design-tokens";

import { EducationCardProps } from "../EducationCard";

type EducationCardTitleProps = Pick<EducationCardProps, "title" | "body" | "buttonText" | "isSkeletonLoading">;

export const EducationCardTitle = ({ title, body, buttonText, isSkeletonLoading }: EducationCardTitleProps) => {
  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="flex-start"
      flexGrow={1}
      gap={2}
      justifyContent="space-between"
    >
      {isSkeletonLoading ? (
        <>
          <Skeleton variant="text" width="40%" height={typography.heading3Serif.lineHeight} />
          <Skeleton variant="text" width="100%" height={typography.bodyDense.lineHeight} />
          <Skeleton variant="text" width="100%" height={typography.bodyDense.lineHeight} />
          <Skeleton variant="text" width="78px" />
        </>
      ) : (
        <>
          <Typography variant="h3Serif" tabIndex={-1} textAlign="left">
            {title}
          </Typography>

          <Box display="flex" flexDirection="column" alignItems="flex-start" gap={2} width={"100%"}>
            {body ? (
              <Typography
                variant="bodyDense"
                tabIndex={-1}
                sx={{
                  WebkitLineClamp: 3,
                  display: "-webkit-box",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  WebkitBoxOrient: "vertical",
                  textAlign: "left",
                  width: "100%",
                }}
              >
                {body}
              </Typography>
            ) : null}

            <Button variant="tertiary" tabIndex={-1} size="small" component="span">
              {buttonText}
            </Button>
          </Box>
        </>
      )}
    </Box>
  );
};
