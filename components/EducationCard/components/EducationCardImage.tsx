import { CSSProperties } from "react";
import { Box, CardMedia, Skeleton } from "@mui/material";

import { RADIUS_16_PX } from "@Assets/style_constants";

import { EducationCardProps } from "../EducationCard";

type EducationCardImageProps = Pick<EducationCardProps, "imageSrc" | "title" | "isSkeletonLoading">;

export const EducationCardImage = ({ imageSrc, title, isSkeletonLoading }: EducationCardImageProps) => {
  return (
    <Box width="100%">
      {isSkeletonLoading ? (
        <Skeleton variant="rectangular" sx={styles.imageContainer} />
      ) : (
        <CardMedia sx={styles.imageContainer} image={imageSrc} title={title} tabIndex={-1} />
      )}
    </Box>
  );
};

const styles = {
  imageContainer: {
    height: "176px",
    borderRadius: RADIUS_16_PX,
  },
} as const satisfies Record<string, CSSProperties | Record<string, CSSProperties>>;
