import { Box, Typography, Skeleton } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { AppIcon } from "@Components/AppIcon/AppIcon";

import { EducationCardProps } from "../EducationCard";

type EducationCardCategoryProps = Pick<EducationCardProps, "category" | "icon" | "iconText" | "isSkeletonLoading">;

export const EducationCardCategory = ({ category, icon, iconText, isSkeletonLoading }: EducationCardCategoryProps) => (
  <Box display="flex" justifyContent="space-between" pt={4} pb={2} width="100%">
    {isSkeletonLoading ? (
      <>
        <Skeleton variant="text" width="20%" />
        <Box display="flex" alignItems="center" tabIndex={-1}>
          <Skeleton variant="text" width="45px" height={"16px"} />
        </Box>
      </>
    ) : (
      <>
        {category ? (
          <Typography variant="caption" color={color.text.subtle} tabIndex={-1}>
            {category}
          </Typography>
        ) : null}
        {icon ? (
          <Box display="flex" alignItems="center" gap={1} tabIndex={-1}>
            <AppIcon name={icon} size="sm" color={color.text.subtle} />
            <Typography variant="caption" color={color.text.subtle} tabIndex={-1}>
              {iconText}
            </Typography>
          </Box>
        ) : null}
      </>
    )}
  </Box>
);
