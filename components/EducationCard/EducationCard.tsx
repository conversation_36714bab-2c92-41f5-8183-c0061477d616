import { CSSProperties } from "react";
import { Chip, Paper } from "@mui/material";

import { SPACING_16_PX, SPACING_32_PX, SPACING_48_PX } from "@Assets/style_constants";
import { IconVariant } from "@Components/AppIcon/AppIcon";
import { AppIcon } from "@Components/AppIcon/AppIcon";

import { EducationCardCategory } from "./components/EducationCardCategory";
import { EducationCardContainer } from "./components/EducationCardContainer";
import { EducationCardImage } from "./components/EducationCardImage";
import { EducationCardTitle } from "./components/EducationCardTitle";

export type EducationCardProps = Readonly<{
  imageSrc: string;
  ariaLabel: string;
  title: string;
  body?: string;
  category?: string;
  buttonText: string;
  chipText?: string;
  icon?: IconVariant;
  iconText?: string;
  onClick?: () => void;
  isSkeletonLoading?: boolean;
  isContainedInCarousel?: boolean;
}>;

export const EducationCard = ({
  imageSrc,
  ariaLabel,
  title,
  body,
  category,
  buttonText,
  chipText,
  icon,
  iconText,
  isSkeletonLoading,
  onClick,
  isContainedInCarousel = false,
}: EducationCardProps) => (
  <EducationCardContainer isContainedInCarousel={isContainedInCarousel}>
    <Paper sx={styles.baseCard} onClick={onClick} aria-label={ariaLabel} component="button">
      {chipText && (
        <Chip
          label={chipText}
          variant="enrollment"
          icon={<AppIcon name="Completed" />}
          sx={styles.badge}
          tabIndex={-1}
        />
      )}

      <EducationCardImage imageSrc={imageSrc} title={title} isSkeletonLoading={isSkeletonLoading} />

      <EducationCardCategory
        category={category}
        icon={icon}
        iconText={iconText}
        isSkeletonLoading={isSkeletonLoading}
      />

      <EducationCardTitle title={title} body={body} buttonText={buttonText} isSkeletonLoading={isSkeletonLoading} />
    </Paper>
  </EducationCardContainer>
);

const styles = {
  baseCard: {
    cursor: "pointer",
    width: "100%",
    height: "100%",
    display: "flex",
    flexDirection: "column",
    padding: SPACING_16_PX,
  },
  badge: {
    position: "absolute",
    top: SPACING_48_PX,
    right: SPACING_32_PX,
    zIndex: 1,
  },
} as const satisfies Record<string, CSSProperties | Record<string, CSSProperties>>;
