import type { <PERSON>a, StoryObj } from "@storybook/nextjs";

import { TileButton } from "./TileButton";
import { cylinderThemeDecorator } from "../stories/cylinderThemeDecorator";

const meta: Meta<typeof TileButton> = {
  title: "@Components/TileButton",
  component: TileButton,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: cylinderThemeDecorator,
};

export default meta;
type Story = StoryObj<typeof TileButton>;

export const Primary: Story = {
  args: {
    icon: "Diet",
    onClick: () => alert("clicked"),
    children: "Button text",
    disabled: false,
  },
};
