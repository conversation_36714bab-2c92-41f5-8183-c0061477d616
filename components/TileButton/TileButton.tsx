import { Button, ButtonProps } from "@mui/material";

import { RADIUS_16_PX } from "@Assets/style_constants";

import { AppIcon, IconVariant } from "../AppIcon/AppIcon";

interface TileButtonProps extends ButtonProps {
  icon: IconVariant;
}

export const TileButton = (props: TileButtonProps) => {
  const { children, icon, sx, ...restProps } = props;

  return (
    <Button
      variant="secondary"
      startIcon={<AppIcon name={icon} size="smd" />}
      sx={{
        ".MuiButton-startIcon": {
          m: 0,
        },
        flexDirection: "column",
        borderRadius: RADIUS_16_PX,
        gap: 1,
        py: 4,
        px: 6,
        ...sx,
      }}
      {...restProps}
    >
      {children}
    </Button>
  );
};
