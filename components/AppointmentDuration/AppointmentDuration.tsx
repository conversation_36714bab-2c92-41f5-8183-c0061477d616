import { Box, Typography } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { appStrings } from "@Assets/app_strings";
import { iconSize } from "@Assets/style_constants";
import { SPACING_4_PX } from "@Assets/style_constants";

import { AppIcon } from "../AppIcon/AppIcon";

type AppointmentDurationProps = Readonly<{
  duration: number;
}>;

export const AppointmentDuration = ({ duration }: AppointmentDurationProps) => {
  return (
    <Box
      display="flex"
      justifyContent="flex-end"
      alignItems="center"
      gap={SPACING_4_PX}
      tabIndex={0}
      aria-label={appStrings.a11y.appointmentDuration(duration)}
    >
      <AppIcon
        name="Clock"
        containerStyles={{ ...iconSize.sm, color: color.icon.subtle, marginBottom: SPACING_4_PX }}
      />
      <Typography variant="caption" sx={{ color: color.text.subtle }} tabIndex={-1}>{`${duration}min`}</Typography>
    </Box>
  );
};
