import { Box, SxProps, Theme } from "@mui/material";

import AlertIcon from "@Assets/images/alert.svg";
import AppStoreLogo from "@Assets/images/appStore.svg";
import BarcodeIcon from "@Assets/images/barcode.svg";
import BookIcon from "@Assets/images/book.svg";
import BrokersIcon from "@Assets/images/brokers.svg";
import CalendarIcon from "@Assets/images/calendar.svg";
import CameraIcon from "@Assets/images/camera.svg";
import CarePlanIcon from "@Assets/images/care_plan.svg";
import CareTeamIcon from "@Assets/images/care_team.svg";
import CellphoneIcon from "@Assets/images/cellphone.svg";
import ChatBubbleIcon from "@Assets/images/chat_bubble.svg";
import ChatBubbleIconNotification from "@Assets/images/chat_bubble_notification.svg";
import CheckIcon from "@Assets/images/check.svg";
import ClockIcon from "@Assets/images/clock.svg";
import CloseIcon from "@Assets/images/close.svg";
import CompanyLogo from "@Assets/images/company_logo.svg";
import CompletedIcon from "@Assets/images/completed.svg";
import DevicesIcon from "@Assets/images/devices.svg";
import DietIcon from "@Assets/images/diet.svg";
import DoctorIcon from "@Assets/images/doctor.svg";
import DocumentIcon from "@Assets/images/documents.svg";
import DownArrowIcon from "@Assets/images/down_arrow.svg";
import DownChevronIcon from "@Assets/images/down_chevron.svg";
import DownloadIcon from "@Assets/images/download.svg";
import EditIcon from "@Assets/images/edit.svg";
import EllipseIcon from "@Assets/images/ellipse.svg";
import EventRepeatIcon from "@Assets/images/event_repeat.svg";
import FastFoodIcon from "@Assets/images/fast_food.svg";
import FilterIcon from "@Assets/images/filter.svg";
import FireIcon from "@Assets/images/fire.svg";
import FoodIcon from "@Assets/images/food.svg";
import FoodHoverIcon from "@Assets/images/food_hover.svg";
import GIMateIcon from "@Assets/images/gi_mate.svg";
import GoalIcon from "@Assets/images/goal.svg";
import GooglePlayLogo from "@Assets/images/googlePlay.svg";
import GutCheckIcon from "@Assets/images/gut_check.svg";
import HelpIcon from "@Assets/images/help.svg";
import HomeIcon from "@Assets/images/home.svg";
import InProgressIcon from "@Assets/images/in_progress.svg";
import InFlammatoryIcon from "@Assets/images/inflammatory.svg";
import InfoIcon from "@Assets/images/info.svg";
import IsometricOrangeBubbleIcon from "@Assets/images/isometric/orange_bubble.svg";
import LaptopIcon from "@Assets/images/laptop.svg";
import LeftArrowIcon from "@Assets/images/left_arrow.svg";
import LeftChevronIcon from "@Assets/images/left_chevron.svg";
import LockIcon from "@Assets/images/lock.svg";
import MailIcon from "@Assets/images/mail.svg";
import MedicationIcon from "@Assets/images/medication.svg";
import MenuIcon from "@Assets/images/menu.svg";
import MenuBookIcon from "@Assets/images/menu_book.svg";
import MinusIcon from "@Assets/images/minus.svg";
import MoodIcon from "@Assets/images/mood.svg";
import MoreIcon from "@Assets/images/more.svg";
import PhoneIcon from "@Assets/images/phone.svg";
import PlanIcon from "@Assets/images/plan.svg";
import PlayIcon from "@Assets/images/play_video.svg";
import PlusIcon from "@Assets/images/plus.svg";
import PrinterIcon from "@Assets/images/printer.svg";
import ProgressIcon from "@Assets/images/progress.svg";
import ProgressCircleGroupIcon from "@Assets/images/progress_circle_group.svg";
import RepeatIcon from "@Assets/images/repeat.svg";
import RightArrowIcon from "@Assets/images/right_arrow.svg";
import RightChevronIcon from "@Assets/images/right_chevron.svg";
import SearchIcon from "@Assets/images/search.svg";
import SemiColonIcon from "@Assets/images/semi_colon.svg";
import SendIcon from "@Assets/images/send.svg";
import SendMsgIcon from "@Assets/images/send_msg.svg";
import ShareIcon from "@Assets/images/share.svg";
import ShieldIcon from "@Assets/images/shield.svg";
import SortingIcon from "@Assets/images/sorting.svg";
import StoolIcon from "@Assets/images/stool.svg";
import StoolWithBackgroundIcon from "@Assets/images/stool_with_background.svg";
import StreakIcon from "@Assets/images/streak.svg";
import SymptomIcon from "@Assets/images/symptoms.svg";
import SymptomHoverIcon from "@Assets/images/symptoms_hover.svg";
import TodoIcon from "@Assets/images/todo.svg";
import TrashIcon from "@Assets/images/trash.svg";
import UpArrowIcon from "@Assets/images/up_arrow.svg";
import UpChevronIcon from "@Assets/images/up_chevron.svg";
import UserIcon from "@Assets/images/user.svg";
import VideoCallIcon from "@Assets/images/video_call.svg";
import VideoCourseIcon from "@Assets/images/video_course.svg";
import WarningIcon from "@Assets/images/warning.svg";
import { iconSize } from "@Assets/style_constants";

export const appIcons = {
  Alert: { icon: AlertIcon, ariaLabel: "alert icon" },
  Barcode: { icon: BarcodeIcon, ariaLabel: "barcode icon" },
  AppStoreLogo: { icon: AppStoreLogo, ariaLabel: "app store icon" },
  Book: { icon: BookIcon, ariaLabel: "book icon" },
  Brokers: { icon: BrokersIcon, ariaLabel: "brokers icon" },
  Calendar: { icon: CalendarIcon, ariaLabel: "calendar icon" },
  Camera: { icon: CameraIcon, ariaLabel: "camera icon" },
  CarePlan: { icon: CarePlanIcon, ariaLabel: "care plan icon" },
  CareTeam: { icon: CareTeamIcon, ariaLabel: "care team icon" },
  Cellphone: { icon: CellphoneIcon, ariaLabel: "cellphone icon" },
  GooglePlayLogo: { icon: GooglePlayLogo, ariaLabel: "google play icon" },
  ChatBubble: { icon: ChatBubbleIcon, ariaLabel: "chat bubble icon" },
  ChatBubbleNotification: { icon: ChatBubbleIconNotification, ariaLabel: "chat bubble icon with notification" },
  Check: { icon: CheckIcon, ariaLabel: "check icon" },
  Clock: { icon: ClockIcon, ariaLabel: "clock icon" },
  Close: { icon: CloseIcon, ariaLabel: "close icon" },
  CompanyLogo: { icon: CompanyLogo, ariaLabel: "logo icon" },
  Completed: { icon: CompletedIcon, ariaLabel: "completed icon" },
  Devices: { icon: DevicesIcon, ariaLabel: "devices icon" },
  Diet: { icon: DietIcon, ariaLabel: "diet icon" },
  Doctor: { icon: DoctorIcon, ariaLabel: "doctor icon" },
  Document: { icon: DocumentIcon, ariaLabel: "document icon" },
  DownArrow: { icon: DownArrowIcon, ariaLabel: "down arrow icon" },
  DownChevron: { icon: DownChevronIcon, ariaLabel: "down chevron icon" },
  Download: { icon: DownloadIcon, ariaLabel: "download icon" },
  Edit: { icon: EditIcon, ariaLabel: "edit icon" },
  Ellipse: { icon: EllipseIcon, ariaLabel: "ellipse icon" },
  EventRepeat: { icon: EventRepeatIcon, ariaLabel: "event repeat icon" },
  FastFood: { icon: FastFoodIcon, ariaLabel: "fast food icon" },
  Fire: { icon: FireIcon, ariaLabel: "fire icon" },
  Food: { icon: FoodIcon, ariaLabel: "food icon" },
  FoodHover: { icon: FoodHoverIcon, ariaLabel: "food hover icon" },
  Filter: { icon: FilterIcon, ariaLabel: "filter icon" },
  GIMate: { icon: GIMateIcon, ariaLabel: "gimate icon" },
  Goal: { icon: GoalIcon, ariaLabel: "goal icon" },
  GutCheck: { icon: GutCheckIcon, ariaLabel: "gut check icon" },
  Help: { icon: HelpIcon, ariaLabel: "help icon" },
  Home: { icon: HomeIcon, ariaLabel: "home icon" },
  Info: { icon: InfoIcon, ariaLabel: "info icon" },
  InProgress: { icon: InProgressIcon, ariaLabel: "in progress icon" },
  InFlammatory: { icon: InFlammatoryIcon, ariaLabel: "inflammatory icon" },
  Laptop: { icon: LaptopIcon, ariaLabel: "laptop icon" },
  LeftArrow: { icon: LeftArrowIcon, ariaLabel: "left arrow icon" },
  LeftChevron: { icon: LeftChevronIcon, ariaLabel: "left chevron icon" },
  Lock: { icon: LockIcon, ariaLabel: "lock icon" },
  Mail: { icon: MailIcon, ariaLabel: "mail icon" },
  Medication: { icon: MedicationIcon, ariaLabel: "medication icon" },
  Menu: { icon: MenuIcon, ariaLabel: "menu icon" },
  MenuBook: { icon: MenuBookIcon, ariaLabel: "menu book icon" },
  Minus: { icon: MinusIcon, ariaLabel: "minus icon" },
  Mood: { icon: MoodIcon, ariaLabel: "mood icon" },
  More: { icon: MoreIcon, ariaLabel: "more icon" },
  Phone: { icon: PhoneIcon, ariaLabel: "phone icon" },
  Plan: { icon: PlanIcon, ariaLabel: "plan icon" },
  PlayVideo: { icon: PlayIcon, ariaLabel: "play video icon" },
  Plus: { icon: PlusIcon, ariaLabel: "plus icon" },
  Printer: { icon: PrinterIcon, ariaLabel: "printer icon" },
  Progress: { icon: ProgressIcon, ariaLabel: "progress icon" },
  ProgressCircleGroup: { icon: ProgressCircleGroupIcon, ariaLabel: "progress group icon" },
  Repeat: { icon: RepeatIcon, ariaLabel: "repeat icon" },
  RightArrow: { icon: RightArrowIcon, ariaLabel: "right arrow icon" },
  RightChevron: { icon: RightChevronIcon, ariaLabel: "right chevron icon" },
  Search: { icon: SearchIcon, ariaLabel: "search icon" },
  SemiColon: { icon: SemiColonIcon, ariaLabel: "semi colon icon" },
  Send: { icon: SendIcon, ariaLabel: "send icon" },
  SendMsg: { icon: SendMsgIcon, ariaLabel: "send message icon" },
  Share: { icon: ShareIcon, ariaLabel: "share icon" },
  Shield: { icon: ShieldIcon, ariaLabel: "shield icon" },
  Sorting: { icon: SortingIcon, ariaLabel: "sorting icon" },
  Stool: { icon: StoolIcon, ariaLabel: "stool icon" },
  StoolWithBackground: { icon: StoolWithBackgroundIcon, ariaLabel: "stool with background icon" },
  Streak: { icon: StreakIcon, ariaLabel: "streak icon" },
  Symptoms: { icon: SymptomIcon, ariaLabel: "symptom icon" },
  SymptomsHover: { icon: SymptomHoverIcon, ariaLabel: "symptom hover icon" },
  Todo: { icon: TodoIcon, ariaLabel: "todo icon" },
  Trash: { icon: TrashIcon, ariaLabel: "trash icon" },
  UpArrow: { icon: UpArrowIcon, ariaLabel: "up arrow icon" },
  UpChevron: { icon: UpChevronIcon, ariaLabel: "up chevron icon" },
  User: { icon: UserIcon, ariaLabel: "user icon" },
  VideoCall: { icon: VideoCallIcon, ariaLabel: "video call icon" },
  VideoCourse: { icon: VideoCourseIcon, ariaLabel: "video course icon" },
  Warning: { icon: WarningIcon, ariaLabel: "warning icon" },
  IsometricOrangeBubble: { icon: IsometricOrangeBubbleIcon, ariaLabel: "orange bubble icon" },
};

export type IconVariant = keyof typeof appIcons;

export type AppIconProps = Readonly<{
  name: IconVariant;
  color?: string;
  size?: keyof typeof iconSize;
  containerStyles?: SxProps<Theme>;
  iconAriaLabel?: string;
  svgStyles?: React.CSSProperties;
  includeInTabIndex?: boolean;
  includeContainer?: boolean;
}>;

export const AppIcon = ({
  name,
  color,
  size = "md",
  containerStyles,
  iconAriaLabel,
  svgStyles,
  includeInTabIndex,
  includeContainer = true,
}: AppIconProps) => {
  const { icon: IconComponent, ariaLabel } = appIcons[name];

  return includeContainer ? (
    <Box sx={{ ...iconSize[size], ...containerStyles }} tabIndex={includeInTabIndex ? 0 : -1}>
      <IconComponent title={name} aria-label={iconAriaLabel ?? ariaLabel} style={{ color, ...svgStyles }} />
    </Box>
  ) : (
    <IconComponent
      tabIndex={includeInTabIndex ? 0 : -1}
      title={name}
      aria-label={iconAriaLabel ?? ariaLabel}
      style={{ color, ...iconSize[size], ...svgStyles }}
    />
  );
};
