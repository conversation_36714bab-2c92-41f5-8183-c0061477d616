import { Grid } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/nextjs";

import { iconSize } from "@Assets/style_constants";
import { AppIcon, appIcons } from "@Components/AppIcon/AppIcon";
import { cylinderThemeDecorator } from "@Components/stories/cylinderThemeDecorator";

const meta: Meta<typeof AppIcon> = {
  component: AppIcon,
  title: "@Components/AppIcon",
  decorators: cylinderThemeDecorator,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
  argTypes: {
    name: { control: "select", options: Object.keys(appIcons), defaultValue: "Alert" },
  },
};

export default meta;
type Story = StoryObj<typeof AppIcon>;

export const Default: Story = {
  args: {
    name: "Alert",
  },
};

export const IsometricComponents: Story = {
  render: function Render() {
    return (
      <Grid container columns={6} spacing={4}>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="IsometricOrangeBubble" containerStyles={iconSize.lg} />
        </Grid>
      </Grid>
    );
  },
};

export const IconComponents: Story = {
  render: function Render() {
    return (
      <Grid container columns={10} spacing={4}>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="User" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Brokers" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Calendar" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Clock" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="CarePlan" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="CareTeam" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Warning" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Lock" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Diet" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="DownArrow" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="UpArrow" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="LeftArrow" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="RightArrow" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Doctor" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Document" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Check" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Goal" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="InFlammatory" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Edit" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="GutCheck" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Plan" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Home" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="InProgress" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Progress" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Medication" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Mood" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Shield" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Todo" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="EventRepeat" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="VideoCall" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Streak" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="LeftChevron" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="RightChevron" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="UpChevron" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="DownChevron" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Book" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="FastFood" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="MenuBook" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Send" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="VideoCourse" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Plus" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Filter" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Sorting" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Barcode" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Camera" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Repeat" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="ChatBubble" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Help" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Menu" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Search" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Trash" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Stool" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Phone" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Share" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Cellphone" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Send" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Laptop" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Close" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Completed" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Alert" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Minus" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="More" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Ellipse" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Food" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="FoodHover" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Symptoms" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="SymptomsHover" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="ProgressCircleGroup" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="StoolWithBackground" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="GIMate" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="SemiColon" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Info" />
        </Grid>
        <Grid xs={1} display="flex" justifyContent="center" alignItems="center">
          <AppIcon name="Fire" />
        </Grid>
      </Grid>
    );
  },
};
