import React, { KeyboardEventHandler, useRef, useState, useCallback, useId } from "react";
import { MenuItemProps, MenuItem, Menu, MenuProps } from "@mui/material";

import { SPACING_16_PX } from "@Assets/style_constants";

import { AppIcon } from "../AppIcon/AppIcon";

type NestedMenuItemsProps = Readonly<
  {
    button?: true;
    label: string;
    isSubMenuOpen: boolean;
    MenuProps?: Omit<MenuProps, "open" | "onClose" | "anchorEl" | "onKeyDown">;
  } & Omit<MenuItemProps, "onKeyDown" | "onMouseEnter" | "onMouseLeave">
>;

export const NestedMenuItems = ({ label, children, id, MenuProps, isSubMenuOpen, ...other }: NestedMenuItemsProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const open = useCallback(() => setIsOpen(true), []);
  const close = useCallback(() => setIsOpen(false), []);

  const menuItemRef = useRef<HTMLLIElement>(null);
  const menuItemId = useId();
  const normMenuItemId = id ?? menuItemId;

  const handleItemKeyDown: KeyboardEventHandler<HTMLLIElement> = (ev) => {
    if ((ev.key !== "ArrowRight" && ev.key !== "Enter") || ev.ctrlKey || ev.shiftKey || ev.altKey || ev.metaKey) {
      return;
    }

    ev.preventDefault();
    ev.stopPropagation();
    setIsOpen(true);
  };

  const handleMenuKeyDown: KeyboardEventHandler<HTMLDivElement> = (ev) => {
    ev.stopPropagation();
    if ((ev.key !== "ArrowLeft" && ev.key !== "Escape") || ev.ctrlKey || ev.shiftKey || ev.altKey || ev.metaKey) {
      return;
    }

    ev.preventDefault();
    setIsOpen(false);
  };

  return (
    <MenuItem
      {...other}
      onKeyDown={handleItemKeyDown}
      ref={menuItemRef}
      onMouseEnter={open}
      onMouseLeave={close}
      id={normMenuItemId}
      sx={{ justifyContent: "space-between" }}
    >
      {label}
      <AppIcon name="RightChevron" />
      <Menu
        TransitionProps={{ onExited: () => menuItemRef.current?.focus() }}
        disableRestoreFocus
        onKeyDown={handleMenuKeyDown}
        sx={styles.menu}
        MenuListProps={{
          ...MenuProps?.MenuListProps,
          "aria-labelledby": normMenuItemId,
        }}
        anchorEl={menuItemRef.current}
        open={isOpen && isSubMenuOpen}
        onClose={close}
        anchorOrigin={MenuProps?.anchorOrigin ?? { vertical: "top", horizontal: "right" }}
        transformOrigin={
          MenuProps?.transformOrigin ?? {
            vertical: "top",
            horizontal: "left",
          }
        }
      >
        {children}
      </Menu>
    </MenuItem>
  );
};

const styles = {
  menu: {
    pointerEvents: "none",
    marginLeft: "2px",
    marginTop: `-${SPACING_16_PX}`,
    "& .MuiList-root": {
      pointerEvents: "auto",
    },
  },
};
