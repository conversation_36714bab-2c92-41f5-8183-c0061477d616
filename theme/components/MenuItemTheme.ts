import { Components } from "@mui/material";
import { color, typography } from "@vivantehealth/design-tokens";

import { SPACING_8_PX, SPACING_16_PX } from "@Assets/style_constants";

export const MenuItemTheme: Components["MuiMenuItem"] = {
  styleOverrides: {
    root: {
      ...typography.body,
      padding: `${SPACING_8_PX} ${SPACING_16_PX}`,
      "&.Mui-selected": {
        backgroundColor: color.background.surface.secondary,
        ":hover": {
          backgroundColor: color.background.surface.secondary,
        },
        ":focusVisible": {
          backgroundColor: color.background.surface.secondary,
        },
      },
      "&.Mui-focusVisible": {
        backgroundColor: color.background.surface.secondary,
        "&.Mui-selected": {
          backgroundColor: color.background.surface.secondary,
          ":hover": {
            backgroundColor: color.background.surface.secondary,
          },
          ":focusVisible": {
            backgroundColor: color.background.surface.secondary,
          },
        },
      },
    },
  },
};
