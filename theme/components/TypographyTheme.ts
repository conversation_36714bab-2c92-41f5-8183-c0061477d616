import React from "react";
import { typography } from "@vivantehealth/design-tokens";

/**
 * https://mui.com/material-ui/customization/theme-components/#creating-new-component-variants
 * We can define our own variants to each MUI component as we see fit. This allows us to define our own styles for each variant
 * within the MUI theme. When we define a variant, we need to add it to the global namespace of the component.
 */
declare module "@mui/material/styles" {
  interface TypographyVariants {
    h1Serif: React.CSSProperties;
    h2Serif: React.CSSProperties;
    h3Serif: React.CSSProperties;
    body: React.CSSProperties;
    bodyDense: React.CSSProperties;
    linkDense: React.CSSProperties;
    action: React.CSSProperties;
    actionDense: React.CSSProperties;
    caption: React.CSSProperties;
    link: React.CSSProperties;
  }

  // allow configuration using `createTheme`
  interface TypographyVariantsOptions {
    h1Serif?: React.CSSProperties;
    h2Serif?: React.CSSProperties;
    h3Serif?: React.CSSProperties;
    body?: React.CSSProperties;
    bodyDense?: React.CSSProperties;
    linkDense?: React.CSSProperties;
    action?: React.CSSProperties;
    actionDense?: React.CSSProperties;
    caption?: React.CSSProperties;
    link?: React.CSSProperties;
  }
}

// Update the Typography's variant prop options
declare module "@mui/material/Typography" {
  interface TypographyPropsVariantOverrides {
    h3Serif: true;
    h1Serif: true;
    h2Serif: true;
    body: true;
    bodyDense: true;
    linkDense: true;
    action: true;
    actionDense: true;
    caption: true;
    link: true;
  }
}

export const typographyBase = {
  ...typography.bodyDefaults,
  h1Serif: {
    ...typography.heading1Serif,
  },
  h1: {
    ...typography.heading1,
  },
  h2Serif: {
    ...typography.heading2Serif,
  },
  h2: {
    ...typography.heading2,
  },
  h3Serif: {
    ...typography.heading3Serif,
  },
  h3: {
    ...typography.heading3,
  },
  h4: {
    ...typography.heading4,
  },
  body: {
    ...typography.body,
  },
  bodyDense: {
    ...typography.bodyDense,
  },
  linkDense: {
    ...typography.linkDense,
  },
  action: {
    ...typography.action,
  },
  actionDense: {
    ...typography.actionDense,
  },
  caption: {
    ...typography.caption,
  },
  link: { ...typography.link },
};

export const TypographyTheme = {
  defaultProps: {
    variantMapping: {
      h1: "h1",
      h1Serif: "h1",
      h2: "h2",
      h2Serif: "h2",
      h3Serif: "h3",
      h3: "h3",
      h4: "h4",
      body: "p",
      bodyDense: "p",
      linkDense: "span",
      action: "span",
      actionDense: "span",
      caption: "span",
      link: "span",
    },
    /** For a11y purposes, we should default to including Typography in the tab index */
    tabIndex: 0,
  },
};
