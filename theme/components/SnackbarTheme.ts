import { Components } from "@mui/material";
import { typography } from "@vivantehealth/design-tokens";

import { RADIUS_16_PX, SPACING_0_PX, SPACING_12_PX, SPACING_16_PX, SPACING_24_PX } from "@Assets/style_constants";

const SNACKBAR_WIDTH = "684px";
/**
 * This number is calculated based on the Snackbar width and padding. We use a width of 684px which utilizes 16px of padding on either side
 * Thus we have an actual width of 650px. Our action button is 20px wide (with 4px padding on the button) with 24px of padding (on the container)
 * on the left side. This leaves us with 598px of space for the message
 */
const SNACKBAR_MESSAGE_MAX_WIDTH = "598px";

export const SnackbarContentTheme: Components["MuiSnackbarContent"] = {
  styleOverrides: {
    root: {
      ...typography.actionDense,
      padding: `${SPACING_12_PX} ${SPACING_16_PX}`,
      borderRadius: RADIUS_16_PX,
      boxShadow: "none",
      width: SNACKBAR_WIDTH,
    },
    message: {
      padding: SPACING_0_PX,
      width: SNACKBAR_MESSAGE_MAX_WIDTH,
    },
    action: {
      paddingLeft: SPACING_24_PX,
      marginLeft: 0,
    },
  },
};
