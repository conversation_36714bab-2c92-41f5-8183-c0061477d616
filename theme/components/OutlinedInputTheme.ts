import { Components, outlinedInputClasses } from "@mui/material";
import { typography, color } from "@vivantehealth/design-tokens";

import { RADIUS_12_PX, SPACING_0_PX, SPACING_12_PX, SPACING_8_PX } from "@Assets/style_constants";

export const InputAdornmentTheme: Components["MuiInputAdornment"] = {
  styleOverrides: {
    positionEnd: {
      margin: SPACING_0_PX,
    },
    positionStart: {
      margin: SPACING_0_PX,
    },
  },
};

export const OutlinedInputTheme: Components["MuiOutlinedInput"] = {
  styleOverrides: {
    root: {
      ...typography.body,
      borderRadius: RADIUS_12_PX,
      borderColor: color.border.input.default,
      backgroundColor: color.background.input.default,
      [`&.Mui-focused .${outlinedInputClasses.notchedOutline}`]: {
        border: `1px solid ${color.border.input.focused}`,
      },
      [`&.Mui-error .${outlinedInputClasses.notchedOutline}`]: {
        borderColor: color.border.input.error,
      },
      [`&.Mui-error .${outlinedInputClasses.input}`]: {
        color: color.text.input.error,
      },

      [`&.Mui-disabled`]: {
        color: color.text.input.placeholder,
        borderColor: color.border.input.default,
        backgroundColor: color.background.input.disabled,
      },
    },
    input: {
      padding: SPACING_12_PX,
      color: color.text.strong,
      "&::placeholder": {
        color: color.text.input.placeholder,
      },
    },
    multiline: {
      padding: SPACING_0_PX,
    },
    inputAdornedStart: {
      paddingLeft: SPACING_8_PX,
    },
    inputAdornedEnd: {
      paddingRight: SPACING_8_PX,
    },
    adornedStart: {
      paddingLeft: SPACING_12_PX,
      paddingRight: SPACING_8_PX,
    },
    adornedEnd: {
      paddingRight: SPACING_12_PX,
    },
  },
};
