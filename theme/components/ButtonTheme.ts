import { Components } from "@mui/material";
import { color, typography } from "@vivantehealth/design-tokens";

import {
  RADIUS_FULL_PX,
  RADIUS_0_PX,
  SPACING_0_PX,
  SPACING_8_PX,
  SPACING_12_PX,
  SPACING_16_PX,
  SPACING_24_PX,
} from "@Assets/style_constants";
/**
 * https://mui.com/material-ui/customization/theme-components/#creating-new-component-variants
 * We can define our own variants to each MUI component as we see fit. This allows us to define our own styles for each variant
 * within the MUI theme. When we define a variant, we need to add it to the global namespace of the component.
 */
declare module "@mui/material/Button" {
  interface ButtonPropsVariantOverrides {
    primary: true;
    secondary: true;
    tertiary: true;
    ghost: true;
    intakePrimary: true;
    intakeSecondary: true;
    cardDark: true;
  }
}

export const ButtonTheme: Components["MuiButton"] = {
  styleOverrides: {
    root: {
      ...typography.action,
      padding: `${SPACING_12_PX} ${SPACING_24_PX}`,
      borderRadius: RADIUS_FULL_PX,
      textTransform: "none",
      "&.Mui-disabled": {
        backgroundColor: color.background.action.disabled,
        color: color.text.action.disabledOnFill,
      },
    },
    sizeSmall: {
      ...typography.actionDense,
      padding: `${SPACING_8_PX} ${SPACING_16_PX}`,
    },
  },
  variants: [
    {
      props: { variant: "primary" },
      style: {
        backgroundColor: color.background.action.default,
        border: "1px solid transparent", // This is to ensure the size of buttons are consistent
        color: color.text.action.default,
        "&:hover": {
          backgroundColor: color.background.action.hover,
          color: color.text.action.hover,
        },
      },
    },
    {
      props: { variant: "secondary" },
      style: {
        backgroundColor: "transparent",
        border: `1px solid ${color.border.action.default}`,
        color: color.text.action.default,
        "&:hover": {
          backgroundColor: "transparent",
          border: `1px solid ${color.border.action.hover}`,
          color: color.text.action.default,
        },
      },
    },
    {
      props: { variant: "tertiary" },
      style: {
        border: "none",
        borderRadius: RADIUS_0_PX,
        borderBottom: `1px solid ${color.border.action.default}`,
        backgroundColor: "transparent",
        color: color.text.action.default,
        padding: SPACING_0_PX,
        minWidth: "unset",
        "&:hover": {
          borderBottomColor: color.border.action.hover,
          backgroundColor: "transparent",
        },
        "&.Mui-disabled": {
          backgroundColor: "transparent",
          color: color.text.action.disabled,
          borderBottomColor: color.border.action.disabled,
        },
      },
    },
    {
      props: { variant: "ghost" },
      style: {
        backgroundColor: "transparent",
        "&:hover": {
          backgroundColor: color.background.surface.secondary,
        },
      },
    },
    {
      props: { variant: "intakePrimary" },
      style: {
        backgroundColor: color.background.surface.primary,
        border: "1px solid transparent", // This is to ensure the size of buttons are consistent
        color: color.text.action.default,
        "&:hover": {
          backgroundColor: color.background.surface.secondary,
          color: color.text.action.hover,
        },
      },
    },
    {
      props: { variant: "intakeSecondary" },
      style: {
        backgroundColor: "rgba(0,0,0,0.2)",
        border: "1px solid transparent", // This is to ensure the size of buttons are consistent
        color: color.text.action.onFill,
        "&:hover": {
          backgroundColor: "rgba(0,0,0,0.4)",
          color: color.text.action.onFill,
        },
      },
    },
    {
      props: { variant: "cardDark" },
      style: {
        backgroundColor: color.background.surface.dark,
        border: "1px solid transparent", // This is to ensure the size of buttons are consistent
        color: color.text.action.disabledOnFill,
        width: "fit-content",
        "&:hover": {
          backgroundColor: "#363636",
          color: color.text.action.onFill,
        },
      },
    },
  ],
};
