import { Components } from "@mui/material";
import { color, typography } from "@vivantehealth/design-tokens";

import { RADIUS_FULL_PX, SPACING_12_PX, SPACING_4_PX } from "@Assets/style_constants";

export const SliderTheme: Components["MuiSlider"] = {
  styleOverrides: {
    root: {
      transform: "translateX(-4px)",
      width: "96%",
      marginBottom: SPACING_12_PX,
      "& .MuiSlider-markLabel, MuiSlider-markActive": {
        '&[style="left: 100%;"]': {
          transform: "translateX(-95%)",
        },
      },
    },
    thumb: {
      height: "16px",
      width: "16px",
      marginLeft: SPACING_4_PX,
      "&:focus, &:hover, &.Mui-active": {
        boxShadow: "none",
      },
      ":before": {
        boxShadow: "none",
      },
    },
    markLabel: {
      ...typography.caption,
      color: color.text.action.default,
      "&[data-index='0']": {
        transform: "translateX(0%)",
      },
    },
    rail: {
      backgroundColor: color.background.surface.secondary,
      opacity: 1,
      height: "4px",
    },
    mark: {
      backgroundColor: color.background.surface.secondary,
      height: "12px",
      width: "12px",
      borderRadius: RADIUS_FULL_PX,
      "&.MuiSlider-markActive": {
        opacity: 1,
        backgroundColor: "currentColor",
      },
    },
  },
};
