import { Components, alertClasses } from "@mui/material";
import { color, typography } from "@vivantehealth/design-tokens";

import { RADIUS_16_PX, SPACING_4_PX, SPACING_16_PX } from "@Assets/style_constants";

export const AlertTheme: Components["MuiAlert"] = {
  styleOverrides: {
    root: {
      ...typography.actionDense,
      borderRadius: RADIUS_16_PX,
      /** Only apply padding 4px to top/bottom as message has padding of 8px top/bottom */
      padding: `${SPACING_4_PX} ${SPACING_16_PX}`,

      [`&.${alertClasses.standard}.${alertClasses.colorWarning}`]: {
        backgroundColor: color.background.warning,
        borderColor: color.border.warning,
        color: color.text.warning,
      },
    },
  },
};
