import { Components } from "@mui/material";
import { color, typography } from "@vivantehealth/design-tokens";

import { SPACING_0_PX, SPACING_24_PX, SPACING_32_PX, SPACING_8_PX } from "@Assets/style_constants";

export const DialogTheme: Components["MuiDialog"] = {
  styleOverrides: {
    paper: {
      padding: `${SPACING_32_PX} ${SPACING_24_PX}`,
      minWidth: "448px",
    },
  },
  defaultProps: {
    maxWidth: "xs",
  },
};

export const DialogTitleTheme: Components["MuiDialogTitle"] = {
  styleOverrides: {
    root: {
      ...typography.heading3,
      padding: SPACING_0_PX,
      color: color.text.strong,
    },
  },
};

export const DialogContentTheme: Components["MuiDialogContent"] = {
  styleOverrides: {
    root: { padding: `${SPACING_24_PX} ${SPACING_0_PX} ${SPACING_0_PX} ${SPACING_0_PX} !important` },
  },
};

export const DialogActionsTheme: Components["MuiDialogActions"] = {
  styleOverrides: {
    root: {
      padding: `${SPACING_24_PX} ${SPACING_0_PX} ${SPACING_0_PX} ${SPACING_0_PX}`,
      display: "flex",
      justifyContent: "flex-start",
      gap: SPACING_8_PX,
    },
  },
};
