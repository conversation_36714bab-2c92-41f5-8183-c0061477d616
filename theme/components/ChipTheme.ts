import { Components } from "@mui/material";
import { color, typography } from "@vivantehealth/design-tokens";

import {
  RADIUS_4_PX,
  RADIUS_FULL_PX,
  SPACING_4_PX,
  SPACING_8_PX,
  SPACING_16_PX,
  SPACING_12_PX,
} from "@Assets/style_constants";

declare module "@mui/material/Chip" {
  interface ChipPropsVariantOverrides {
    active: true;
    inactive: true;
    enrollment: true;
    time: true;
  }
}

export const ChipTheme: Components["MuiChip"] = {
  styleOverrides: {
    root: {
      ...typography.actionDense,
      borderRadius: RADIUS_FULL_PX,
    },
    label: {
      padding: `${SPACING_8_PX} ${SPACING_16_PX}`,
    },
    deleteIcon: { margin: `0 ${SPACING_16_PX} 0 -${SPACING_12_PX}` },
    sizeSmall: {
      ...typography.actionDense,
      padding: `${SPACING_4_PX} ${SPACING_12_PX}`,
      "& .MuiChip-label": {
        padding: 0,
      },
    },
  },
  variants: [
    {
      props: { variant: "time" },
      style: {
        backgroundColor: color.background.action.default,
        color: color.text.action.onFill,
        borderRadius: RADIUS_4_PX,
        padding: SPACING_8_PX,
        "&:hover": {
          color: color.text.action.onFill,
          backgroundColor: color.background.action.hover,
        },
        "&:disabled": {
          backgroundColor: color.background.action.disabled,
          color: color.text.action.disabledOnFill,
        },
        "& .MuiChip-label": {
          padding: 0,
        },
      },
    },
    {
      props: { variant: "active" },
      style: {
        backgroundColor: color.background.surface.dark,
        color: color.text.action.onFill,
        border: "1px solid transparent",
        "&:hover": {
          backgroundColor: color.background.surface.dark,
        },
        "&:disabled": {
          backgroundColor: color.background.surface.disabled,
        },
      },
    },
    {
      props: { variant: "inactive" },
      style: {
        backgroundColor: color.background.surface.primary,
        color: color.text.action.default,
        border: `1px solid ${color.border.action.default}`,
        "&:hover": {
          backgroundColor: color.background.surface.primary,
          border: `1px solid ${color.border.action.hover}`,
        },
        "&:disabled": {
          backgroundColor: color.background.surface.disabled,
          border: `1px solid ${color.background.surface.disabled}`,
        },
      },
    },
    {
      props: { variant: "enrollment" },
      style: {
        paddingLeft: SPACING_4_PX, // needed due to the icon that should be used with this variant
        backgroundColor: color.background.surface.primary,
        color: color.text.action.default,
        "&:hover": {
          backgroundColor: color.background.surface.primary,
        },
        "& .MuiChip-label": {
          paddingLeft: SPACING_4_PX,
          paddingRight: SPACING_8_PX,
        },
      },
    },
  ],
  defaultProps: {
    tabIndex: 0,
  },
};
