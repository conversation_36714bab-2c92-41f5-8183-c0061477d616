import { Components } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { RADIUS_16_PX, SPACING_24_PX, SPACING_16_PX } from "@Assets/style_constants";

export const PaperTheme: Components["MuiPaper"] = {
  styleOverrides: {
    root: {
      padding: `${SPACING_24_PX} ${SPACING_16_PX}`,
    },
    rounded: {
      borderColor: color.border.default,
      borderRadius: RADIUS_16_PX,
    },
    elevation0: {
      border: `1px solid ${color.border.default}`,
    },
  },
  defaultProps: {
    elevation: 0,
  },
};
