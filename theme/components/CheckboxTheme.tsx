import { Components } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { SPACING_16_PX } from "@Assets/style_constants";

const checkboxUncheckedIcon = (
  <svg xmlns="http://www.w3.org/2000/svg" width={SPACING_16_PX} height={SPACING_16_PX} viewBox="0 0 16 16" fill="none">
    <path
      d="M0.5 4C0.5 2.067 2.067 0.5 4 0.5H12C13.933 0.5 15.5 2.067 15.5 4V12C15.5 13.933 13.933 15.5 12 15.5H4C2.067 15.5 0.5 13.933 0.5 12V4Z"
      fill={color.palette.neutral[0]}
    />
    <path
      d="M0.5 4C0.5 2.067 2.067 0.5 4 0.5H12C13.933 0.5 15.5 2.067 15.5 4V12C15.5 13.933 13.933 15.5 12 15.5H4C2.067 15.5 0.5 13.933 0.5 12V4Z"
      stroke={color.border.action.default}
    />
  </svg>
);

const checkboxCheckedIcon = (
  <svg width={SPACING_16_PX} height={SPACING_16_PX} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M0.5 4C0.5 2.067 2.067 0.5 4 0.5H12C13.933 0.5 15.5 2.067 15.5 4V12C15.5 13.933 13.933 15.5 12 15.5H4C2.067 15.5 0.5 13.933 0.5 12V4Z"
      fill={color.background.action.default}
    />
    <path
      d="M0.5 4C0.5 2.067 2.067 0.5 4 0.5H12C13.933 0.5 15.5 2.067 15.5 4V12C15.5 13.933 13.933 15.5 12 15.5H4C2.067 15.5 0.5 13.933 0.5 12V4Z"
      stroke={color.border.action.brand}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.3035 4.10269C12.523 4.27032 12.565 4.5841 12.3973 4.80355L7.27359 11.511C6.92672 11.9651 6.2593 12.0096 5.85527 11.6056L3.64645 9.39678C3.45119 9.20152 3.45118 8.88494 3.64644 8.68967C3.84171 8.49441 4.15829 8.49441 4.35355 8.68967L6.51746 10.8535L11.6027 4.19651C11.7703 3.97706 12.0841 3.93506 12.3035 4.10269Z"
      fill={color.palette.neutral[0]}
    />
  </svg>
);

export const CheckboxTheme: Components["MuiCheckbox"] = {
  defaultProps: {
    size: "small",
    icon: checkboxUncheckedIcon,
    checkedIcon: checkboxCheckedIcon,
  },
};
