import { Components } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";

import { iconSize } from "@Assets/style_constants";

const radioUncheckedIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={iconSize.sm.width}
    height={iconSize.sm.height}
    viewBox={`0 0 16 16`}
    fill="none"
  >
    <rect x="0.5" y="0.5" width="15" height="15" rx="7.5" fill={color.palette.neutral[0]} />
    <rect x="0.5" y="0.5" width="15" height="15" rx="7.5" stroke={color.palette.neutral[300]} />
  </svg>
);

const radioCheckIcon = (
  <svg
    width={iconSize.sm.width}
    height={iconSize.sm.height}
    viewBox={`0 0 16 16`}
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect x="0.5" y="0.5" width="15" height="15" rx="7.5" fill={color.palette.neutral[0]} />
    <rect x="0.5" y="0.5" width="15" height="15" rx="7.5" stroke={color.background.action.default} />
    <rect x="4" y="4" width="8" height="8" rx="4" fill={color.background.action.default} />
  </svg>
);

export const RadioTheme: Components["MuiRadio"] = {
  defaultProps: {
    size: "small",
    style: {
      padding: "0",
    },
    icon: radioUncheckedIcon,
    checkedIcon: radioCheckIcon,
  },
};
