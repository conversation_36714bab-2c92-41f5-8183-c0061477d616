import { Components } from "@mui/material";
import { color, typography } from "@vivantehealth/design-tokens";

import { SPACING_0_PX, SPACING_8_PX, SPACING_16_PX } from "@Assets/style_constants";

export const AutocompleteTheme: Components["MuiAutocomplete"] = {
  styleOverrides: {
    root: {
      ...typography.body,
    },
    /** Due to the structure of Autocomplete utilizing an input inside itself, we have some non-standard padding values to get to our standard padding total */
    inputRoot: {
      padding: `5px 4px 5px 7px`,
    },
    paper: {
      padding: `${SPACING_8_PX} ${SPACING_0_PX}`,
      marginTop: SPACING_8_PX,
    },
    popupIndicator: {
      transform: "none",
    },
    option: {
      padding: `${SPACING_8_PX} ${SPACING_16_PX}`,
      backgroundColor: color.background.surface.primary,
      "&.Mui-focused": {
        backgroundColor: color.background.surface.secondary,
      },
      "&[aria-selected=true]": {
        backgroundColor: `${color.background.surface.secondary} !important`,
      },
    },
  },
};
