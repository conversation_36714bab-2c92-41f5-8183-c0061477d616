import { Components } from "@mui/material";
import { color, typography } from "@vivantehealth/design-tokens";

import { SPACING_0_PX, SPACING_16_PX } from "@Assets/style_constants";

export const TabsTheme: Components["MuiTabs"] = {
  styleOverrides: {
    indicator: {
      backgroundColor: color.background.surface.dark,
    },
    root: {
      borderBottom: `1px solid ${color.background.surface.secondary}`,
    },
  },
};

export const TabTheme: Components["MuiTab"] = {
  defaultProps: {
    disableRipple: true,
  },
  styleOverrides: {
    root: {
      ...typography.action,
      textTransform: "none",
      color: color.text.subtle,
      paddingBottom: SPACING_16_PX,
      paddingTop: SPACING_0_PX,
      "&.Mui-selected": {
        color: color.text.action.default,
      },
      "&.Mui-focusVisible": {
        color: color.background.brand.default,
      },
    },
  },
};
