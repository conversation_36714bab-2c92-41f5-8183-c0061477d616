import { createTheme } from "@mui/material";
import { color } from "@vivantehealth/design-tokens";
import { spacing } from "@vivantehealth/design-tokens";

import { AlertTheme } from "./components/AlertTheme";
import { AutocompleteTheme } from "./components/AutocompleteTheme";
import { ButtonTheme } from "./components/ButtonTheme";
import { CheckboxTheme } from "./components/CheckboxTheme";
import { ChipTheme } from "./components/ChipTheme";
import { DialogActionsTheme, DialogContentTheme, DialogTheme, DialogTitleTheme } from "./components/DialogTheme";
import { DividerTheme } from "./components/DividerTheme";
import { DrawerTheme } from "./components/DrawerTheme";
import { FormHelperTextTheme } from "./components/FormHelperTextTheme";
import { InputLabelTheme } from "./components/InputLabelTheme";
import { LinearProgressTheme } from "./components/LinearProgressTheme";
import { LinkTheme } from "./components/LinkTheme";
import { MenuItemTheme } from "./components/MenuItemTheme";
import { MenuTheme } from "./components/MenuTheme";
import { InputAdornmentTheme, OutlinedInputTheme } from "./components/OutlinedInputTheme";
import { PaperTheme } from "./components/PaperTheme";
import { RadioTheme } from "./components/RadioTheme";
import { SelectTheme } from "./components/SelectTheme";
import { SliderTheme } from "./components/SliderTheme";
import { SnackbarContentTheme } from "./components/SnackbarTheme";
import { TabsTheme, TabTheme } from "./components/TabsTheme";
import { typographyBase, TypographyTheme } from "./components/TypographyTheme";

/**
 * https://mui.com/material-ui/customization/theming/
 * Modifying the MUI theme per component can be a bit tedious as the documentation does not always fully flesh out the exact
 * MUI classes that we need to override which CSS property. Each component on its respective API page in the documentation will list the classes
 * that can be overriden.
 */
export const theme = createTheme({
  spacing: [
    spacing.space0,
    spacing.space1,
    spacing.space2,
    spacing.space3,
    spacing.space4,
    spacing.space5,
    spacing.space6,
    spacing.space7,
    spacing.space8,
    spacing.space9,
    spacing.space10,
    spacing.space11,
  ],
  palette: {
    primary: {
      main: color.palette.orange[200],
    },
    secondary: {
      main: color.palette.neutral[300],
    },
    background: {
      default: color.background.page,
    },
  },
  typography: typographyBase,
  components: {
    MuiAlert: AlertTheme,
    MuiAutocomplete: AutocompleteTheme,
    MuiTypography: TypographyTheme,
    MuiInputLabel: InputLabelTheme,
    MuiOutlinedInput: OutlinedInputTheme,
    MuiInputAdornment: InputAdornmentTheme,
    MuiFormHelperText: FormHelperTextTheme,
    MuiButton: ButtonTheme,
    MuiPaper: PaperTheme,
    MuiCheckbox: CheckboxTheme,
    MuiLink: LinkTheme,
    MuiLinearProgress: LinearProgressTheme,
    MuiMenu: MenuTheme,
    MuiMenuItem: MenuItemTheme,
    MuiSelect: SelectTheme,
    MuiTab: TabTheme,
    MuiTabs: TabsTheme,
    MuiDialog: DialogTheme,
    MuiDialogTitle: DialogTitleTheme,
    MuiDialogContent: DialogContentTheme,
    MuiDialogActions: DialogActionsTheme,
    MuiChip: ChipTheme,
    MuiSlider: SliderTheme,
    MuiDrawer: DrawerTheme,
    MuiDivider: DividerTheme,
    MuiSnackbarContent: SnackbarContentTheme,
    MuiRadio: RadioTheme,
  },
});
