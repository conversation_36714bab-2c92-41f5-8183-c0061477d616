import { createApi, fetchBaseQuery, retry } from "@reduxjs/toolkit/query/react";

import { vivanteCoreContainer } from "@Lib/vivanteCore";
import { getBaseApiUrl } from "@Utils/getBaseApiUrl";
/** Endpoints that use JsonAPI and thus need to use content-type: application/vnd.api+json */
const JSON_API_ENDPOINTS = ["activateUser"];
/** Endpoints that use Form url encoded content and thus need to use content-type: application/x-www-form-urlencoded */
const FORM_URL_ENCODED_ENDPOINTS: string[] = [];

const getContentType = (endpoint: string) => {
  if (JSON_API_ENDPOINTS.includes(endpoint)) return "application/vnd.api+json";

  if (FORM_URL_ENCODED_ENDPOINTS.includes(endpoint)) return "application/x-www-form-urlencoded";

  return "application/json";
};

/** BFF Empty API slice. Use .injectEndpoints to create feature slices. */
export const bffApi = createApi({
  reducerPath: "bffApi",
  tagTypes: ["Goal", "CareGuide", "Appointment", "Gutcheck"],
  baseQuery: retry(
    async (args, api, extraOptions) => {
      const result = await fetchBaseQuery({
        baseUrl: getBaseApiUrl("BFF_URL"),
        prepareHeaders: async (headers, { endpoint }) => {
          const firebaseToken = await vivanteCoreContainer.authClient.getValidAccessToken();

          headers.set("content-type", getContentType(endpoint));

          // We only include the firebaseToken if it exists this prevents the server from returning a 401
          if (firebaseToken) {
            headers.set("Authorization", `Bearer ${firebaseToken}`);
          }

          return headers;
        },
      })(args, api, extraOptions);

      /**
       * Bail out of retries immediately if the error is a 401 as we know successive retries will fail
       */
      if (result.error?.status === 401) {
        retry.fail(result.error, result.meta);
      }

      return result;
    },
    { maxRetries: 3 },
  ),
  endpoints: () => ({}),
});
