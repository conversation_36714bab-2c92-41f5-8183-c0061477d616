import { combineEpics } from "redux-observable";

import { analyticsEpics } from "@Features/analytics/store/analyticsEpics";
import { appointmentsEpics } from "@Features/appointments/store/appointmentsEpics";
import { authenticationEpics } from "@Features/authentication/store/authenticationEpics";
import { bootstrapEpics } from "@Features/bootstrap/store/bootstrapEpics";
import { actionPlanEpics } from "@Features/carePlan/store/actionPlanEpics";
import { careTeamEpics } from "@Features/careTeam/store/careTeamEpics";
import { courseEpics } from "@Features/courses/store/courseEpics";
import { courseItemsEpics } from "@Features/courses/store/courseItemsEpics";
import { eligibilityProcessEpics } from "@Features/eligibility/store/eligibilityProcessEpics";
import { foodLogEpics } from "@Features/foodTracking/store/foodLogEpics";
import { onboardingEpics } from "@Features/landing/store/onboardingEpics";
import { loadMemberEpics } from "@Features/member/store/memberEpics";
import { memberConvertedToVirtualClinicEpics } from "@Features/memberConvertedToVirtualClinic/store/memberConvertedToVirtualClinicEpics";
import { loadMemberPreferencesEpics } from "@Features/memberPreferences/store/memberPreferencesEpics";
import { microbiomeEpics } from "@Features/microbiome/store/microbiomeEpics";
import { navigationEpics } from "@Features/navigation/store/navigationEpics";
import { orderEpics } from "@Features/order/store/orderEpics";
import { progressEpics } from "@Features/progress/store/progressEpics";
import { reportTicketEpics } from "@Features/reportTicket/store/reportTicketEpics";
import { serverEventEpics } from "@Features/serverSentEvents/store/serverEventEpics";
import { shippingFormEpics } from "@Features/shippingForm/store/shippingFormEpics";
import { slideshowEpics } from "@Features/slideshow/store/slideshowEpics";
import { surveyEpics } from "@Features/survey/store/surveyEpics";
import { symptomLoggingEpics } from "@Features/symptomLogging/store/symptomLoggingEpics";
import { todoEpics } from "@Features/toDos/store/toDoEpics";
import { witchEpics } from "@Features/witch/store/witchEpics";

export const rootEpic = combineEpics(
  ...actionPlanEpics,
  ...analyticsEpics,
  ...appointmentsEpics,
  ...bootstrapEpics,
  ...authenticationEpics,
  ...careTeamEpics,
  ...courseEpics,
  ...courseItemsEpics,
  ...eligibilityProcessEpics,
  ...foodLogEpics,
  ...loadMemberEpics,
  ...loadMemberPreferencesEpics,
  ...memberConvertedToVirtualClinicEpics,
  ...microbiomeEpics,
  ...navigationEpics,
  ...onboardingEpics,
  ...orderEpics,
  ...progressEpics,
  ...reportTicketEpics,
  ...shippingFormEpics,
  ...slideshowEpics,
  ...serverEventEpics,
  ...surveyEpics,
  ...symptomLoggingEpics,
  ...todoEpics,
  ...witchEpics,
);
