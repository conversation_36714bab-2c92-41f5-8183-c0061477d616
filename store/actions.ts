import { createAction } from "@reduxjs/toolkit";

export const loadHomeScreen = createAction("loadHomeScreen");
export const signOut = createAction("signOut");
export const resetAppState = createAction("resetAppState");
export const removeServerEventsSubscriptions = createAction("removeServerEventsSubscriptions");
export const addServerEventsSubscriptions = createAction("addServerEventsSubscriptions");
