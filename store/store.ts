import { AnyAction, combineReducers, configureStore } from "@reduxjs/toolkit";
import { createEpicMiddleware } from "redux-observable";

import { bffApi } from "@Api/bffApi";
import { appointmentsStateReducer } from "@Features/appointments/store/appointmentsStateSlice";
import { articlesApi } from "@Features/articles/api/articlesApi";
import { authenticationStateReducer } from "@Features/authentication/store/authenticationStateSlice";
import { bootstrapStateReducer } from "@Features/bootstrap/store/bootstrapStateSlice";
import { actionPlansStateReducer } from "@Features/carePlan/store/actionPlansStateSlice";
import { careTeamStateReducer } from "@Features/careTeam/store/careTeamStateSlice";
import { chatStateReducer } from "@Features/chat/store/chatStateSlice";
import { courseItemsStateReducer } from "@Features/courses/store/courseItemsStateSlice";
import { coursesStateReducer } from "@Features/courses/store/coursesStateSlice";
import { eligibilityProcessStateReducer } from "@Features/eligibility/store/eligibilityProcessStateSlice";
import { errorStateReducer } from "@Features/error/store/errorStateSlice";
import { foodLogApi } from "@Features/foodTracking/api/foodLogApi";
import { foodLogStateReducer } from "@Features/foodTracking/store/foodLogStateSlice";
import { memberStateReducer } from "@Features/member/store/memberStateSlice";
import { memberConvertedToVirtualClinicStateReducer } from "@Features/memberConvertedToVirtualClinic/store/memberConvertedToVirtualClinicStateSlice";
import { memberPreferencesStateReducer } from "@Features/memberPreferences/store/memberPreferencesStateSlice";
import { microbiomeStateReducer } from "@Features/microbiome/store/microbiomeStateSlice";
import { navigationStateReducer } from "@Features/navigation/store/navigationStateSlice";
import { orderStateReducer } from "@Features/order/store/orderStateSlice";
import { partnerPreVerifyApi } from "@Features/partnerPreVerify/api/partnerPreVerifyApi";
import { progressStateReducer } from "@Features/progress/store/progressStateSlice";
import { reportTicketStateReducer } from "@Features/reportTicket/store/reportTicketSlice";
import { shippingFormStateReducer } from "@Features/shippingForm/store/shippingFormStateSlice";
import { signUpApi } from "@Features/signUp/api/signUpApi";
import { slideshowStateReducer } from "@Features/slideshow/store/slideshowStateSlice";
import { snackbarStateReducer } from "@Features/snackbar/store/snackbarStateSlice";
import { surveyStateReducer } from "@Features/survey/store/surveyStateSlice";
import { symptomLoggingStateReducer } from "@Features/symptomLogging/store/symptomLoggingStateSlice";
import { toDosStateReducer } from "@Features/toDos/store/toDosStateSlice";
import { witchStateReducer } from "@Features/witch/store/witchStateSlice";

import { rootEpic } from "./rootEpic";

const epicMiddleware = createEpicMiddleware();

const appReducer = combineReducers({
  bffApi: bffApi.reducer,
  bootstrapState: bootstrapStateReducer,
  appointmentsState: appointmentsStateReducer,
  actionPlansState: actionPlansStateReducer,
  articlesApi: articlesApi.reducer,
  authenticationState: authenticationStateReducer,
  careTeamState: careTeamStateReducer,
  chatState: chatStateReducer,
  courseItemsState: courseItemsStateReducer,
  coursesState: coursesStateReducer,
  eligibilityState: eligibilityProcessStateReducer,
  errorState: errorStateReducer,
  foodLogState: foodLogStateReducer,
  foodLogApi: foodLogApi.reducer,
  memberConvertedToVirtualClinicState: memberConvertedToVirtualClinicStateReducer,
  memberState: memberStateReducer,
  memberPreferencesState: memberPreferencesStateReducer,
  microbiomeState: microbiomeStateReducer,
  navigationState: navigationStateReducer,
  orderState: orderStateReducer,
  partnerPreVerifyApi: partnerPreVerifyApi.reducer,
  progressState: progressStateReducer,
  reportTicketState: reportTicketStateReducer,
  snackbarState: snackbarStateReducer,
  surveyState: surveyStateReducer,
  toDosState: toDosStateReducer,
  shippingFormState: shippingFormStateReducer,
  signUpApi: signUpApi.reducer,
  slideshowState: slideshowStateReducer,
  symptomLoggingState: symptomLoggingStateReducer,
  witchState: witchStateReducer,
});

const rootReducer = (state: RootState, action: AnyAction) => {
  if (action.type === "resetAppState") {
    return appReducer({} as RootState, action);
  }

  return appReducer(state, action);
};

export const store = configureStore({
  reducer: (state, action) => rootReducer(state, action),
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }).concat(
      foodLogApi.middleware,
      bffApi.middleware,
      articlesApi.middleware,
      signUpApi.middleware,
      partnerPreVerifyApi.middleware,
      epicMiddleware,
    ),
});

export type AppDispatch = typeof store.dispatch;

export type RootState = ReturnType<typeof appReducer>;

epicMiddleware.run(rootEpic);
