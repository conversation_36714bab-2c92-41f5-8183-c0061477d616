import { VivanteApiError } from "@vivantehealth/vivante-core";
import { PayloadAction } from "@reduxjs/toolkit";
import { Epic, StateObservable, ofType } from "redux-observable";
import { Observable, from, of } from "rxjs";
import { catchError, map, mergeMap, switchMap, withLatestFrom } from "rxjs/operators";

import { logger } from "@Utils/logger";

import { RootState } from "./store";

type P<ACTION_PAYLOAD, PROMISE_RESPONSE_TYPE, SLICE> = (
  args?: ACTION_PAYLOAD,
  slice?: SLICE,
) => Promise<PROMISE_RESPONSE_TYPE>;

type O<ACTION_PAYLOAD, PROMISE_RESPONSE_TYPE, SLICE> = (
  args?: ACTION_PAYLOAD,
  slice?: SLICE,
) => Observable<PROMISE_RESPONSE_TYPE>;

/**
 * Syntactic sugar which creates a simple epic that:
 * 1. listens to a singular dispatched action type
 * 2. executes a promise function with the action's payload
 * 3. if successful, dispatches a success action with the promise's result
 * 4. if failed, dispatches a failure action
 *
 * For example, given this usage:
 *
 *  const loadActivityEpic = buildSimpleActionEpic<string, Activity>(
 *    loadActivity.type,
 *    vivanteCoreContainer
 *     .getActivityUseCaseFactory()
 *     .createGetActivityByIdUseCase().execute,
 *    loadActivitySuccess,
 *    loadActivityFailure
 *  )
 *
 * ... this yields an epic that looks like this:
 *
 *  const loadActivityEpic: Epic = (actions$: Observable<Action>) => {
 *    return actions$.pipe(
 *      ofType(loadActivity),
 *
 *    switchMap((action: PayloadAction<string>) => {
 *      const activityId = action?.payload
 *      return from(
 *        vivanteCoreContainer
 *          .getActivityUseCaseFactory()
 *          .createGetActivityByIdUseCase()
 *          .execute(activityId)
 *      ).pipe(
 *        map((activity: Activity) => loadActivitySuccess(activity)),
 *        catchError(() => {
 *          return of(loadActivityFailure())
 *        })
 *      )
 *    })
 *    )
 *  }
 *
 * Consider using this function if your usecase conforms to the above.
 *
 * @param triggeringAction An action which has a payload.
 * @param promise A promise bearing function that can yield its result eiher as a promise, or an observable.
 * @param successAction An action which has a payload
 * @param failureAction An action with doesn't have a payload
 * @returns
 */
export const buildSimpleActionEpic = <ACTION_PAYLOAD = void, PROMISE_RESPONSE_TYPE = unknown>(
  triggeringAction: string,
  promise: P<ACTION_PAYLOAD, PROMISE_RESPONSE_TYPE, RootState> | O<ACTION_PAYLOAD, PROMISE_RESPONSE_TYPE, RootState>,
  successAction: (payload: PROMISE_RESPONSE_TYPE) => PayloadAction<PROMISE_RESPONSE_TYPE>,
  failureAction: (payload: Error | VivanteApiError | undefined) => PayloadAction<Error | VivanteApiError | undefined>,
  options?: {
    mapType: "switchMap" | "mergeMap";
  },
): Epic => {
  const operationToUse =
    options?.mapType === "switchMap" ? switchMap : options?.mapType === "mergeMap" ? mergeMap : switchMap;

  if (!operationToUse) {
    throw new Error(`Unrecognized operation ${options?.mapType}`);
  }

  return (actions$: Observable<PayloadAction<ACTION_PAYLOAD>>, state$: StateObservable<RootState>) =>
    actions$.pipe(
      ofType(triggeringAction),
      withLatestFrom(state$),
      operationToUse(([action, state]) => {
        return from(promise(action.payload, state)).pipe(
          map((resp: PROMISE_RESPONSE_TYPE) => {
            return successAction(resp);
          }),
          catchError((error) => {
            logger.error(error);
            return of(failureAction(error));
          }),
        );
      }),
    );
};
