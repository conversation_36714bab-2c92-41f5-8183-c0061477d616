import { AuthenticationErrorType } from "@vivantehealth/vivante-core/dist/domain/entities/errors/authentication-error";

export enum Routes {
  ACTIVITY = "/activity",
  APPOINTMENTS = "/appointments",
  ARTICLE_CATEGORY = "/learn/articles/category",
  ARTICLES = "/learn/articles",
  CHAT = "/chat",
  CARE_GUIDE = "/care-guide",
  CARE_PLAN = "/care-plan",
  CARE_TEAM = "/team",
  CARE_TEAM_SCHEDULING = "/team?showScheduling=true",
  CHANGE_PASSWORD = "/settings/change-password",
  ACCESS_CODE = "/welcome/access-code",
  COURSES = "/learn/courses",
  DELPHI_INTAKE_SURVEY = "/new_survey?code=survey_intake_001",
  ELIGIBILITY = "/eligibility",
  ELIGIBILITY_HELP = "/eligibility/help",
  GUT_CHECK = "/microbiome",
  HOME = "/home",
  LOGIN = "/login",
  NEW_SURVEY = "/new_survey",
  NURSE_TRIAGE = "/nurse-triage",
  MEMBER_DEACTIVATED = "/member-deactivated",
  ONBOARDING = "/onboarding",
  ORDER = "/order",
  PROGRESS = "/progress",
  REGISTER = "/register",
  REGISTER_WITH_CODE = "/register/[code]",
  RESET_PASSWORD = "/reset-password",
  ROOT = "/",
  SETTINGS = "/settings",
  SIGN_UP = "/sign-up",
  SIGN_IN = "/sign-in",
  SLIDESHOW = "/slideshow",
  SURVEY = "/survey",
  SURVEY_LOADING = "/survey-loading",
  TYPE_OF_EMPLOYEE = `/welcome/access-code/type-of-employee`,
  TODO = "/todo",
  WELCOME = "/welcome",
  WELCOME_MOBILE_APP = "/welcome/mobile-app",
  WELCOME_ACCOLADE = "/welcome/accolade",
  WELCOME_SOLERA = "/welcome/solera",
  WELCOME_PERSONIFY_HEALTH = "/welcome/personify-health",
  Food_Tracking = "progress/food-tracking",
  GOAL_SETTING_WELCOME_SPECIFIC = "/progress/goal-setting/welcome/specificGoals",
  GOAL_SETTING_WELCOME_ACHIEVABLE = "/progress/goal-setting/welcome/achievableGoals",
  GOAL_SETTING_WELCOME_START = "/progress/goal-setting/welcome/getStarted",
  MFA_VERIFICATION = "/mfa-verification",
  MFA_VERIFICATION_LOGIN = "/mfa-verification?variant=login",
  MFA_VERIFICATION_REGISTRATION = "/mfa-verification?variant=registration",
  UPDATE_VERIFICATION_METHOD = "/update-verification-method?updateType=method",
  UPDATE_VERIFICATION_PHONE = "/update-verification-method?updateType=phone",
  GUT_CHECK_NEW = "/gutcheck",
  GUT_CHECK_INTRO = "/gutcheck/intro",
  GUT_CHECK_ORDER = "/gutcheck/order",
  GUT_CHECK_RESULTS = "/gutcheck/results",
}

export type LoadState = "loading" | "loaded" | "failure" | null;

export type AuthError = Readonly<{
  type: AuthenticationErrorType;
  message: string;
  discriminator: string;
}>;
/**
 *  Adds `null` to all values of given object type
 *
 * @example
 * // Make all values nullable
 * type TestType = NullableObject<{
 *   a: string;
 *   b: number;
 * }>
 *
 * // this is equivalent to:
 * type TestType = {
 *  a: string | null;
 *  b: number | null;
 * }
 *
 * // Make all values nullable besides "a"
 * type TestType = NullableObject<{
 *   a: string;
 *   b: number;
 * }, "a">
 *
 * // this is equivalent to:
 * type TestType = {
 *  a: string;
 *  b: number | null;
 * }
 *
 */
export type NullableObject<BaseObject extends object, ExcludedKey extends keyof BaseObject = keyof BaseObject> = {
  [Key in keyof BaseObject]: ExcludedKey extends Key ? BaseObject[Key] : BaseObject[Key] | null;
};
