vivante_core_environment = "dev4"
myvh_api_server          = "https://main-api.dev.vivantehealth.com"
myvh_helpdesk_url        = "https://support.vivantehealth.com"
myvh_helpdesk_path       = "/assets/form/form.js"
sentry                   = "https://<EMAIL>/6047131"
sentry_env               = "dev"
is_staging_env           = "true"
matomo_legacy_id         = "1"
matomo_cp_id             = "10"
matomo_target            = "//analytics.vivantehealth.com/"
redirect_uri             = "https://githrive-reactweb.dev.vivante.dev/"
welcome_page_url         = "https://welcome.dev4.vivante.dev"

# Web app domains
member_portal_domain                = "my.dev.vivante.dev."
redirect_domains                    = ["my.dev.vivante.dev", "redirect.dev4.vivante.dev", "another-redirect.dev4.vivante.dev", "my.dev4.vivante.dev"]
additional_domains_for_certificates = ["my.dev4.vivante.dev", "redirect.dev4.vivante.dev", "another-redirect.dev4.vivante.dev"]

# Web app domains used for testing while the old app is still live
test_member_portal_domain                = "githrive-reactweb.dev.vivante.dev."
test_redirect_domains                    = ["githrive-reactweb-redirect.dev4.vivante.dev"]
test_additional_domains_for_certificates = ["githrive-reactweb-redirect.dev4.vivante.dev"]

redirect_domain_for_githrive = "app.dev.cylinderhealth.com"
# redirect_domain_for_githrive = "my.dev.vivante.dev"

firebase_application_domain = "vivante-dev"
