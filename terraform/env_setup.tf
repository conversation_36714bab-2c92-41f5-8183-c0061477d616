# resource "null_resource" "env_setup" {

#   triggers = {
#     always_run = "${timestamp()}"
#   }

#   provisioner "local-exec" {
#     command = <<EOF
#       echo "Configuring for environment: ${var.vivante_core_environment}"
#       sed -i -e \
#         's#VIVANTE_CORE_ENVIRONMENT#'${var.vivante_core_environment}'#g' ../dist/*.js;
#       sed -i -e \
#         's#VIVANTE_API_SERVER#'${var.myvh_api_server}'#g' ../dist/*.js;
#       sed -i -e \
#         's#VIVANTE_HELPDESK_URL#'${var.myvh_helpdesk_url}'#g' ../dist/*.js;
#       sed -i -e \
#         's#VIVANTE_HELPDESK_PATH#'${var.myvh_helpdesk_path}'#g' ../dist/*.js;
#       sed -i -e \
#         's#VIVANTE_SENTRY_TARGET#'${var.sentry}'#g' ../dist/*.js;
#       sed -i -e \
#         's#VIVANTE_SENTRY_ENV#'${var.sentry_env}'#g' ../dist/*.js;
#       sed -i -e \
#         's#IS_STAGING_ENV#'${var.is_staging_env}'#g' ../dist/*.js;
#       sed -i -e \
#         's#VH_MATOMO_LEGACY_ID#'${var.matomo_legacy_id}'#g' ../dist/*.js;
#       sed -i -e \
#         's#VH_MATOMO_CP_ID#'${var.matomo_cp_id}'#g' ../dist/*.js;
#       sed -i -e \
#         's#//analytics.gke-stg.vivantehealth.net/#'${var.matomo_target}'#g' ../dist/index.html
#       sed -i -e \
#         's#VH_REDIRECT_URI#'${var.redirect_uri}'#g' ../dist/*.js;
#       sed -i -e \
#         's#VH_WELCOME_PAGE_URL#'${var.welcome_page_url}'#g' ../dist/*.js;
#       sed -i -e \
#         's#CURRENT_CI_BUILD#'${var.ci_build}'#g' ../dist/*.js;
#       sed -i -e \
#         's#VH_ENV_ID#'${local.env_config.env_id}'#g' ../dist/*.js;
#     EOF
#   }
# }
