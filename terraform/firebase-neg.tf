# Set up the network endpoint group
resource "google_compute_global_network_endpoint_group" "neg" {
  name                  = "firebase-domain"
  default_port          = 443
  project               = var.domain_project_id
  network_endpoint_type = "INTERNET_FQDN_PORT"
}

# create global network endpoint for the firebase application domain
resource "google_compute_global_network_endpoint" "firebase_domain" {
  global_network_endpoint_group = google_compute_global_network_endpoint_group.neg.self_link
  project                       = var.domain_project_id
  fqdn                          = "${var.firebase_application_domain}.firebaseapp.com"
  port                          = 443
}

resource "google_compute_backend_service" "firebase_app_backend_service" {
  name                            = "firebase-app-backend-service"
  protocol                        = "HTTPS"
  enable_cdn                      = true
  timeout_sec                     = 10
  connection_draining_timeout_sec = 10

  backend {
    group = google_compute_global_network_endpoint_group.neg.id
  }
}
