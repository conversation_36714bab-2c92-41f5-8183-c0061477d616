resource "random_id" "cylinder_certificate" {
  byte_length = 4
  prefix      = "${var.stack_name}-cylinder-cert-"

  keepers = {
    domains = join(",", local.cylinder_certificate_domains)
  }
}

resource "google_compute_managed_ssl_certificate" "cylinder" {
  // https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_managed_ssl_certificate#example-usage---managed-ssl-certificate-recreation
  name = random_id.cylinder_certificate.hex

  managed {
    domains = local.cylinder_certificate_domains
  }

  lifecycle {
    create_before_destroy = true
  }
}
