# get files into bucket
resource "null_resource" "upload_objects" {

  triggers = {
    always_run = "${timestamp()}"
  }

  provisioner "local-exec" {
    command = <<EOF
      # rm ../dist/*.map
      gsutil -m rsync -d -r ../build-flavors/out-${local.env_config.env_id} gs://${google_storage_bucket.githrive_web.name}
      # Set attributes for index.html to disable caching
      gsutil -m setmeta -h "Cache-Control:no-cache, no-store, max-age=0" \
        gs://${google_storage_bucket.githrive_web.name}/index.html \
        gs://${google_storage_bucket.githrive_web.name}/commit-sha.txt
    EOF
  }

  # depends_on = []
}
