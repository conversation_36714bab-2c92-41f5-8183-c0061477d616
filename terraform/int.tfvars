vivante_core_environment = "staging"
myvh_api_server          = "https://main-api.int.vivantehealth.com"
myvh_helpdesk_url        = "https://support.vivantehealth.com"
myvh_helpdesk_path       = "/assets/form/form.js"
sentry                   = "https://<EMAIL>/6047131"
sentry_env               = "int"
is_staging_env           = "true"
matomo_legacy_id         = "1"
matomo_cp_id             = "10"
matomo_target            = "//analytics.vivantehealth.com/"
redirect_uri             = "https://my.gke-stg.vivantehealth.com/"
welcome_page_url         = "https://welcome.gke-stg.vivantehealth.com"

# Web app domains
member_portal_domain                = "my.int.vivante.dev."
redirect_domains                    = ["my.int.vivante.dev", "mygithrive.gke-stg.vivantehealth.com", "my.gke-stg.vivantehealth.com"]
additional_domains_for_certificates = ["mygithrive.gke-stg.vivantehealth.com", "my.gke-stg.vivantehealth.com"]

# Web app domains used for testing while the old app is still live
test_member_portal_domain                = "githrive-reactweb.int.vivante.dev."
test_redirect_domains                    = ["githrive-reactweb-redirect.gke-stg.vivantehealth.com", "new-my.gke-stg.vivantehealth.com"]
test_additional_domains_for_certificates = ["githrive-reactweb-redirect.gke-stg.vivantehealth.com", "new-my.gke-stg.vivantehealth.com"]

redirect_domain_for_githrive = "app.int.cylinderhealth.com"
# redirect_domain_for_githrive = "my.int.vivante.dev"

firebase_application_domain = "vivante-platform-core-staging"
