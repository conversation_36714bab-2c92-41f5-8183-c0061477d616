locals {
  cylinder_host_name           = trimsuffix("app.${data.google_dns_managed_zone.cylinder_dns_zone.dns_name}", ".")
  cylinder_certificate_domains = ["app.${data.google_dns_managed_zone.cylinder_dns_zone.dns_name}"]
}

# Reserve a public IP address for the chapp-web
resource "google_compute_global_address" "cylinder_website" {
  name = "${var.stack_name}-lb-ip-cylinder-${local.env_config.env_id}"
}

# Get the managed DNS zone for cylinder
data "google_dns_managed_zone" "cylinder_dns_zone" {
  name    = local.env_config.cylinder_dns_zone
  project = local.env_config.cylinder_dns_project
}

# Add the IP to the Cylinder DNS - Live domain
resource "google_dns_record_set" "cylinder_website_record" {
  project      = local.env_config.cylinder_dns_project
  name         = "app.${data.google_dns_managed_zone.cylinder_dns_zone.dns_name}"
  type         = "A"
  ttl          = "300"
  rrdatas      = [google_compute_global_address.cylinder_website.address]
  managed_zone = data.google_dns_managed_zone.cylinder_dns_zone.name
}

# GCP URL Map that handles domain redirects
resource "google_compute_url_map" "cylinder_website" {
  name            = "${var.stack_name}-cylinder-url-map"
  default_service = google_compute_backend_bucket.website.self_link

  host_rule {
    hosts        = [local.cylinder_host_name]
    path_matcher = "allpaths"
  }

  path_matcher {
    name            = "allpaths"
    default_service = google_compute_backend_bucket.website.self_link

    path_rule {
      paths   = ["/__/auth/*"]
      service = google_compute_backend_service.firebase_app_backend_service.self_link
    }
  }
}

resource "google_compute_target_https_proxy" "cylinder_website" {
  name             = "${var.stack_name}-cylinder-target-proxy"
  url_map          = google_compute_url_map.cylinder_website.self_link
  ssl_certificates = [google_compute_managed_ssl_certificate.cylinder.self_link]
  ssl_policy       = google_compute_ssl_policy.ssl_policy.name
}

resource "google_compute_global_forwarding_rule" "cylinder_default" {
  name                  = "${var.stack_name}-cylinder-forwarding-rule"
  load_balancing_scheme = "EXTERNAL"
  ip_address            = google_compute_global_address.cylinder_website.address
  ip_protocol           = "TCP"
  port_range            = "443"
  target                = google_compute_target_https_proxy.cylinder_website.self_link
}

# HTTP to HTTPS redirect
resource "google_compute_url_map" "cylinder_https_redirect" {
  name = "${var.stack_name}-cylinder-https-redirect"
  default_url_redirect {
    https_redirect         = true
    redirect_response_code = "MOVED_PERMANENTLY_DEFAULT"
    strip_query            = false
  }
}

resource "google_compute_target_http_proxy" "cylinder_http_proxy" {
  name    = "${var.stack_name}-cylinder-http-proxy"
  url_map = join("", google_compute_url_map.cylinder_https_redirect.*.self_link)
}

resource "google_compute_global_forwarding_rule" "cylinder_http" {
  name                  = "${var.stack_name}-cylinder-http-forwarding-rule"
  target                = google_compute_target_http_proxy.cylinder_http_proxy.self_link
  ip_address            = google_compute_global_address.cylinder_website.address
  port_range            = "80"
  load_balancing_scheme = "EXTERNAL"
}
