variable "domain_project_id" {
  description = "Domain's GCP project id"
  type        = string
}

variable "docker_registry" {
  type        = string
  description = "Docker registry to pull images from"
}

variable "terraform_project_id" {
  description = "Folder's Terraform state GCP project id"
  type        = string
}

variable "stack_name" {
  type = string
}

variable "vivante_core_environment" {
  type = string
}

variable "myvh_api_server" {
  type = string
}

variable "myvh_helpdesk_url" {
  type = string
}

variable "myvh_helpdesk_path" {
  type = string
}

variable "sentry" {
  type = string
}

variable "sentry_env" {
  type = string
}

variable "is_staging_env" {
  type = string
}

variable "matomo_legacy_id" {
  type = string
}

variable "matomo_cp_id" {
  type = string
}

variable "matomo_target" {
  type = string
}

variable "redirect_uri" {
  type = string
}

variable "welcome_page_url" {
  type = string
}

variable "ci_build" {
  type = string
}

variable "additional_domains_for_certificates" {
  type        = list(string)
  default     = []
  description = "List of additional domains to add to the managed certificates"
}

variable "member_portal_domain" {
  type        = string
  description = "The main domain of the member portal. All other domains will perform a 301 to this domain"
}

variable "redirect_domains" {
  type        = list(string)
  default     = []
  description = "Domains that redirect to the member portal domain."
}

variable "test_additional_domains_for_certificates" {
  type        = list(string)
  default     = []
  description = "List of additional domains to add to the managed certificates"
}

variable "test_member_portal_domain" {
  type        = string
  description = "The main domain of the member portal. All other domains will perform a 301 to this domain"
}

variable "test_redirect_domains" {
  type        = list(string)
  default     = []
  description = "Domains that redirect to the member portal domain."
}

variable "redirect_domain_for_githrive" {
  type        = string
  description = "The domain to which the old domain will redirect."
}

variable "firebase_application_domain" {
  type        = string
  description = "The domain of the firebase application"
}
