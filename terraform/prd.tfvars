vivante_core_environment = "production"
myvh_api_server          = "https://main-api.prd.vivantehealth.com"
myvh_helpdesk_url        = "https://support.vivantehealth.com"
myvh_helpdesk_path       = "/assets/form/form.js"
sentry                   = "https://<EMAIL>/6047131"
sentry_env               = "prd"
is_staging_env           = "false"
matomo_legacy_id         = "1"
matomo_cp_id             = "13"
matomo_target            = "//analytics.vivantehealth.com/"
redirect_uri             = "https://my.vivantehealth.com/"
welcome_page_url         = "https://welcome.vivantehealth.com"

# Web app domains
member_portal_domain                = "mygithrive.com."
redirect_domains                    = ["mygithrive.com", "my.vivantehealth.com", "member-portal.vivantehealth.com", "www.mygithrive.com"]
additional_domains_for_certificates = ["my.vivantehealth.com", "member-portal.vivantehealth.com", "www.mygithrive.com"]

# Web app domains used for testing while the old app is still live
test_member_portal_domain                = "githrive-reactweb.mygithrive.com."
test_redirect_domains                    = ["githrive-reactweb-redirect.vivantehealth.com", "new-my.vivantehealth.com"]
test_additional_domains_for_certificates = ["githrive-reactweb-redirect.vivantehealth.com", "new-my.vivantehealth.com"]

redirect_domain_for_githrive = "app.cylinderhealth.com"
# redirect_domain_for_githrive = "mygithrive.com"

firebase_application_domain = "production-204821"
