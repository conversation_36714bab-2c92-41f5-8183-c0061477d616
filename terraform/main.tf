data "google_storage_bucket_object_content" "env_config" {
  name   = "config.v2.json"
  bucket = "${var.terraform_project_id}-env-config"
}

locals {
  env_config = jsondecode(data.google_storage_bucket_object_content.env_config.content)
}

# tfsec:ignore:google-storage-bucket-encryption-customer-key Encryption keys are irrelevant in this use case
resource "google_storage_bucket" "githrive_web" {
  name     = "${var.stack_name}-site-contents-${local.env_config.env_id}-vh"
  project  = var.domain_project_id
  location = local.env_config.env_region

  uniform_bucket_level_access = true

  cors {
    // @TODO: Update this to only allow the domains that are needed
    origin          = ["*"]
    method          = ["GET"]
    response_header = ["*"]
    max_age_seconds = 86400
  }

  website {
    main_page_suffix = "index.html"
    not_found_page   = "index.html"
  }
  // add module for labels
  labels = {
    vanta-owner              = "vivantehealth-eng"
    vanta-non-prod           = local.env_config.env_id != "prd" ? "true" : "false"
    vanta-description        = "public-githrive-reactweb-assets"
    vanta-contains-user-data = "false"
    vanta-user-data-stored   = "false"
    vanta-contains-ephi      = "false"
  }
}

#tfsec:ignore:google-storage-no-public-access The purpose of this bucket is to serve public content
resource "google_storage_bucket_iam_member" "public_access" {
  bucket = google_storage_bucket.githrive_web.name
  role   = "roles/storage.objectViewer"
  member = "allUsers"
}

# Reserve a public IP address for the githrive-web
resource "google_compute_global_address" "website" {
  name = "${var.stack_name}-lb-ip-${local.env_config.env_id}"
}

# Get the managed DNS zone
data "google_dns_managed_zone" "website_zone" {
  name    = local.env_config.mygithrive_dns_zone
  project = local.env_config.mygithrive_dns_project
}

# Add the IP to the DNS - Live domain
resource "google_dns_record_set" "website_record" {
  project      = local.env_config.mygithrive_dns_project
  name         = var.member_portal_domain
  type         = "A"
  ttl          = "300"
  rrdatas      = [google_compute_global_address.website.address]
  managed_zone = data.google_dns_managed_zone.website_zone.name
}

# Add the IP to the DNS - Test domain
resource "google_dns_record_set" "test_website_record" {
  project      = local.env_config.mygithrive_dns_project
  name         = var.test_member_portal_domain
  type         = "A"
  ttl          = "300"
  rrdatas      = [google_compute_global_address.website.address]
  managed_zone = data.google_dns_managed_zone.website_zone.name
}

# Add the bucket as a CDN backend
resource "google_compute_backend_bucket" "website" {
  name                    = "${var.stack_name}-website-backend-${local.env_config.env_id}"
  description             = "Website backend bucket"
  bucket_name             = google_storage_bucket.githrive_web.name
  enable_cdn              = true
  compression_mode        = "AUTOMATIC"
  custom_response_headers = ["X-Frame-Options: SAMEORIGIN", "Strict-Transport-Security: max-age=63072000; includeSubDomains; preload", "X-Content-Type-Options: nosniff", "Referrer-Policy: no-referrer, strict-origin-when-cross-origin", "Content-Security-Policy: default-src https: 'unsafe-eval' 'unsafe-inline'; object-src 'none'; img-src https: data:; frame-src 'self' blob:;"]

  cdn_policy {
    negative_caching = true

    negative_caching_policy {
      code = "404"
      ttl  = 0
    }
  }

}

# Create HTTPS certificate
locals {
  managed_certificate_domains      = concat([var.member_portal_domain], var.additional_domains_for_certificates)
  test_managed_certificate_domains = concat([var.test_member_portal_domain], var.test_additional_domains_for_certificates)
}

resource "random_id" "certificate" {
  byte_length = 4
  prefix      = "managed-cert-"

  keepers = {
    domains = join(",", local.managed_certificate_domains)
  }
}

resource "random_id" "test_certificate" {
  byte_length = 4
  prefix      = "${var.stack_name}-test-managed-cert-"

  keepers = {
    domains = join(",", local.test_managed_certificate_domains)
  }
}

resource "google_compute_managed_ssl_certificate" "website" {
  name = random_id.certificate.hex

  managed {
    domains = local.managed_certificate_domains
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "google_compute_managed_ssl_certificate" "test_website" {
  name = random_id.test_certificate.hex

  managed {
    domains = local.test_managed_certificate_domains
  }

  lifecycle {
    create_before_destroy = true
  }
}

# GCP URL Map that handles domain redirects
resource "google_compute_url_map" "website" {
  name            = "${var.stack_name}-url-map"
  default_service = google_compute_backend_bucket.website.self_link

  host_rule {
    hosts        = var.redirect_domains
    path_matcher = "allpaths"
  }

  path_matcher {
    name            = "allpaths"
    default_service = google_compute_backend_bucket.website.self_link
    path_rule {
      paths = ["/*"]
      url_redirect {
        # Test domain
        # host_redirect = var.test_member_portal_domain
        # Live domain
        host_redirect          = var.redirect_domain_for_githrive
        redirect_response_code = "MOVED_PERMANENTLY_DEFAULT"
        strip_query            = false
      }
    }
  }
}

# GCP target proxy
resource "google_compute_target_https_proxy" "website" {
  name             = "${var.stack_name}-target-proxy"
  url_map          = google_compute_url_map.website.self_link
  ssl_certificates = [google_compute_managed_ssl_certificate.website.self_link]
  ssl_policy       = google_compute_ssl_policy.ssl_policy.name
}

# GCP forwarding rule
resource "google_compute_global_forwarding_rule" "default" {
  name                  = "${var.stack_name}-forwarding-rule"
  load_balancing_scheme = "EXTERNAL"
  ip_address            = google_compute_global_address.website.address
  ip_protocol           = "TCP"
  port_range            = "443"
  target                = google_compute_target_https_proxy.website.self_link
}

# Http to https redirect
resource "google_compute_url_map" "https_redirect" {
  name = "${var.stack_name}-https-redirect"
  default_url_redirect {
    https_redirect         = true
    redirect_response_code = "MOVED_PERMANENTLY_DEFAULT"
    strip_query            = false
  }
}

resource "google_compute_target_http_proxy" "default" {
  name    = "${var.stack_name}-http-proxy"
  url_map = join("", google_compute_url_map.https_redirect.*.self_link)
}

resource "google_compute_global_forwarding_rule" "http" {
  name                  = "${var.stack_name}-http-forwarding-rule"
  target                = google_compute_target_http_proxy.default.self_link
  ip_address            = google_compute_global_address.website.address
  port_range            = "80"
  load_balancing_scheme = "EXTERNAL"
}
