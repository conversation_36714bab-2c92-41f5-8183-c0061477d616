{"extends": "./tsconfig.build.json", "compilerOptions": {"baseUrl": ".", "paths": {"@Api/*": ["./api/*"], "@Assets/*": ["./assets/*"], "@Components/*": ["./components/*"], "@Config/*": ["./config/*"], "@Features/*": ["./features/*"], "@Hooks/*": ["./hooks/*"], "@Lib/*": ["./lib/*"], "@Store/*": ["./store/*"], "@TestUtils/*": ["./testUtils/*"], "@Theme/*": ["./theme/*"], "@Types": ["./types/types.ts"], "@Utils/*": ["./utils/*"], "@PublicImages/*": ["./public/images/*"], "rxjs": ["node_modules/rxjs"], "rxjs/*": ["node_modules/rxjs/*"]}, "target": "es6", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "skipLibCheck": true, "incremental": true}, "exclude": ["node_modules"], "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "vitest.setup.ts"]}