import { test, expect } from "@playwright/test";
import "dotenv/config";

test("Should login as an existing user and click through several pages", async ({ page }) => {
  await page.goto("/");

  await page.getByRole("button", { name: "Already a member? Log in now" }).click();

  await expect(page).toHaveURL(/\/login/);

  await page.getByLabel("Email *").fill(process.env.TEST_USER_EMAIL ?? "");
  await page.getByLabel("Password *").fill(process.env.TEST_USER_PASSWORD ?? "");
  await page.getByLabel("Continue").click();

  await expect(page).toHaveURL(/\/home/<USER>

  await page.getByRole("link", { name: "Care Team" }).click();
  await expect(page).toHaveURL(/\/team/);

  await page.getByRole("link", { name: "Articles" }).click();
  await expect(page).toHaveURL(/\/learn\/articles/);
});
