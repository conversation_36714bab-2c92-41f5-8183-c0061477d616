import { test, expect } from "@playwright/test";

import { appStrings } from "@Assets/app_strings";

const ACCESS_CODE_STRINGS = appStrings.features.accessCode;

test("Should click through from the welcome screen to access code page including no code error", async ({ page }) => {
  await page.goto("/");
  await expect(page.getByText("Welcome to Better Digestive Health", { exact: true })).toBeVisible();

  await page.getByRole("button", { name: "Register now" }).click();

  await expect(page).toHaveURL(/\/welcome\/access-code/);

  await page.getByLabel("Continue").click();

  await page.getByLabel("Access code *").fill("   ");
  await page.getByLabel("Continue").click();

  await expect(page.getByText(ACCESS_CODE_STRINGS.errorMissingAccessCode)).toBeVisible();

  await page.getByLabel("Access code *").fill("delphi");

  await page.getByLabel("Continue").click();

  await expect(page).toHaveURL(/\/sign-up/);
});

test("Should go to the register route and display error with invalid code", async ({ page }) => {
  await page.goto("/register/TestCode");

  await expect(page.getByLabel("Access code *")).toHaveValue("TestCode");

  await page.getByLabel("Continue").click();

  await expect(page.getByText(ACCESS_CODE_STRINGS.errorWrongAccessCode)).toBeVisible();
});
