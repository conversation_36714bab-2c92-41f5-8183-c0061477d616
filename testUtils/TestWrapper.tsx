import { ReactNode } from "react";
import { Provider } from "react-redux";
import { ThemeProvider } from "@emotion/react";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";

import { store } from "@Store/store";
import { theme } from "@Theme/cylinderTheme";
/**
 * Use this wrapper to wrap components in tests that require the store and theme provider.
 * Additional Provider's can be added here as needed.
 * Example usage:
 * ```tsx
 * render(<CareGuide />, { wrapper: TestWrapper });
 * ```
 */
export const TestWrapper = ({ children }: { children: ReactNode }) => {
  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <LocalizationProvider dateAdapter={AdapterDayjs}>{children}</LocalizationProvider>
      </ThemeProvider>
    </Provider>
  );
};
