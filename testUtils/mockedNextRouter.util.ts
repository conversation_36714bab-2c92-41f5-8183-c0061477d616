import { vi } from "vitest";

const createMockedNextRouter = () => {
  const mockedRouter = vi.hoisted(() => {
    const mockedRouterReplace = vi.fn();
    const mockedRouterPush = vi.fn();
    const mockedRouterQuery = vi.fn();
    const mockedRouterBack = vi.fn();

    return {
      useRouter: () => ({
        replace: mockedRouterReplace,
        push: mockedRouterPush,
        asPath: "/path?query=string",
        pathname: "/path",
        query: mockedRouterQuery,
        back: mockedRouterBack,
      }),
      mockedRouterBack,
      mockedRouterQuery,
      mockedRouterReplace,
      mockedRouterPush,
    };
  });

  vi.mock("next/router", async () => {
    const actual = await vi.importActual("next/router");

    return {
      ...actual,
      useRouter: mockedRouter.useRouter,
    };
  });

  return mockedRouter;
};

/**
 * Usage: Import mockedNextRouter from this file and use it in your test files. You can spy on the methods of
 * mockedRouter to check if they were called or to set their return values.
 *
 * Example:
 *```tsx
 * const {  mockedRouterReplace } = mockedNextRouter;
 * fireEvent.click(screen.getByRole("button", { name: appStrings.buttonText.continue }));
 * expect(mockedRouterReplace).toHaveBeenCalledWith(Routes.HOME);
 * ```
 */
export const mockedNextRouter = createMockedNextRouter();
