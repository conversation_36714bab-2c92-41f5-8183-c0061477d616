{"name": "cylinder-web", "private": true, "version": "1.0.0", "scripts": {"build": "next build", "serve-static": "next build && node serve.js", "dev": "concurrently -n NEXT,TS -c magenta,cyan \"next dev --turbopack\" \"yarn ts --watch\"", "dev:linked": "concurrently -n NEXT,TS -c magenta,cyan \"next dev\" \"yarn ts --watch\"", "start": "next start", "storybook": "storybook dev -p 6006", "test": "TZ=UTC vitest", "test:coverage": "TZ=UTC vitest --coverage", "test:e2e": "playwright test --ui", "test:e2e:headless": "playwright test", "ts": "tsc --noEmit --incremental --preserveWatchOutput --pretty", "docs": "doctoc --title '**Table of content**' README.md", "lint": "eslint --ext ts,tsx .", "format": "prettier --write .", "pre-commit": "yarn lint-staged && yarn tsc", "prepare": "husky", "build-storybook": "storybook build"}, "dependencies": {"@chatscope/chat-ui-kit-react": "2.1.1", "@chatscope/chat-ui-kit-styles": "^1.4.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@jonkoops/matomo-tracker": "0.7.0", "@mui/icons-material": "5.15.20", "@mui/material": "^5.15.20", "@mui/x-date-pickers": "^7.28.3", "@reduxjs/toolkit": "2.8.2", "@sentry/nextjs": "9.27.0", "@splitsoftware/splitio-react": "^2.3.1", "@storybook/addon-themes": "^9.0.5", "@vivantehealth/design-tokens": "^0.5.4", "@vivantehealth/vivante-core": "35.8.13", "axios": "^1.9.0", "date-fns": "4.1.0", "dayjs": "1.11.13", "embla-carousel-react": "^8.6.0", "file-saver": "2.0.5", "firebase": "^11.9.0", "framer-motion": "^12.16.0", "html-react-parser": "5.2.5", "js-cookie": "^3.0.5", "next": "^15.3.3", "normalizr": "3.6.2", "qs": "^6.14.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "7.57.0", "react-pdf": "^9.2.1", "react-player": "2.16.0", "react-redux": "9.2.0", "redux-observable": "2.0.0", "rxjs": "6.6.6", "sse.js": "^2.6.0", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@babel/core": "7.27.4", "@next/eslint-plugin-next": "^14.2.4", "@playwright/test": "^1.52.0", "@storybook/addon-docs": "^9.0.5", "@storybook/addon-links": "^9.0.5", "@storybook/nextjs": "^9.0.5", "@stylistic/eslint-plugin": "^1.7.0", "@svgr/webpack": "8.1.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/file-saver": "2.0.7", "@types/js-cookie": "^3.0.6", "@types/node": "^22.15.30", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@types/react-places-autocomplete": "^7.2.14", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@vitejs/plugin-react": "^4.5.1", "@vitest/coverage-v8": "3.2.2", "@vitest/eslint-plugin": "^1.2.1", "babel-loader": "10.0.0", "concurrently": "9.1.2", "doctoc": "2.2.1", "eslint": "^8.57.0", "eslint-config-next": "^15.3.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-storybook": "^9.0.5", "eslint-plugin-testing-library": "^7.4.0", "express": "5.1.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "15.5.1", "prettier": "^3.5.3", "react-markdown": "10.1.0", "storybook": "^9.0.5", "ts-node": "^10.9.2", "tsconfig-paths-webpack-plugin": "4.2.0", "typescript": "5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.2"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "eslint --fix", "**/*.{js,jsx,ts,tsx,css,md,json}": "prettier --write"}, "resolutions": {"@babel/runtime": "^7.27.4", "tar-fs": "^2.1.3", "@types/express": "4.17.3", "eslint-plugin-react-hooks": "4.3.0", "loader-utils": "2.0.4", "file-type": "^4.0.0", "trim-newlines": "^3.0.1", "terser": "^5.14.2", "node-fetch": "^2.6.7", "trim": "^0.0.3", "nth-check": "^2.0.1", "glob-parent": "^6.0.2", "hosted-git-info": "^6.1.1"}, "packageManager": "yarn@1.22.22"}