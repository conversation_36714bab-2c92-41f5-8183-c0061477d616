import { Environment } from "@vivantehealth/vivante-core";

export type EnvironmentVariableKeys =
  | "PASSIO_PROXY_URL"
  | "SPLIT_IO_API_KEY"
  | "BFF_URL"
  | "ARTICLE_API_URL"
  | "MAIN_API_URL"
  | "PARTNER_PREVERIFY_URL";

export const environmentVariables = {
  dev4: {
    PASSIO_PROXY_URL: "https://passio-proxy.dev.vivantehealth.com/",
    SPLIT_IO_API_KEY: "7nm1r7pt8k23ol9pnaamllqdubevrsu77l8n",
    BFF_URL: "https://chapp-bff.dev.vivantehealth.com/",
    ARTICLE_API_URL: "https://article.dev.vivantehealth.com/member",
    MAIN_API_URL: "https://main-api.dev.vivantehealth.com",
    PARTNER_PREVERIFY_URL: "https://pre-signup-eligibility-retriever.dev.vivantehealth.com/v1/integration/",
  },
  staging: {
    PASSIO_PROXY_URL: "https://passio-proxy.int.vivantehealth.com/",
    SPLIT_IO_API_KEY: "utdn42ollgisa8e37vl66m8it9ilea9pdnae",
    BFF_URL: "https://chapp-bff.int.vivantehealth.com/",
    ARTICLE_API_URL: "https://article.int.vivantehealth.com/member",
    MAIN_API_URL: "https://main-api.int.vivantehealth.com",
    PARTNER_PREVERIFY_URL: "https://pre-signup-eligibility-retriever.int.vivantehealth.com/v1/integration/",
  },
  production: {
    PASSIO_PROXY_URL: "https://passio-proxy.prd.vivantehealth.com/",
    SPLIT_IO_API_KEY: "e375l21m4tlhf3ssjebfisp9okusrj4mtjqa",
    BFF_URL: "https://chapp-bff.prd.vivantehealth.com/",
    ARTICLE_API_URL: "https://article.prd.vivantehealth.com/member",
    MAIN_API_URL: "https://main-api.prd.vivantehealth.com",
    PARTNER_PREVERIFY_URL: "https://pre-signup-eligibility-retriever.prd.vivantehealth.com/v1/integration/",
  },
} as const satisfies Record<Environment, Record<EnvironmentVariableKeys, string>>;
