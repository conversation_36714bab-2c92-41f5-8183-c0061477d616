import { Environment } from "@vivantehealth/vivante-core";

type BuildFlavor = "development" | "production" | "test" | "staging";

export const getVivanteEnvironment = (): Environment => {
  const buildFlavor = (process.env.NEXT_PUBLIC_BUILD_FLAVOR || "development") as BuildFlavor;

  return mapBuildFlavorToVivanteEnvironment(buildFlavor);
};

const mapBuildFlavorToVivanteEnvironment = (buildFlavor: BuildFlavor): Environment => {
  switch (buildFlavor) {
    case "production":
      return "production";
    case "staging":
      return "staging";
    case "development":
      return "dev4";
    default:
      throw Error("Unknown buildFlavor configuration");
  }
};
