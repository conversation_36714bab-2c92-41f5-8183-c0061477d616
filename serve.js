/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-require-imports */
/*
  Incredibly simple Node.js and Express application server for serving static assets.
  
  DON'T USE THIS IN PRODUCTION!
    It is meant for learning purposes only. This server is not optimized for performance, 
    and is missing key features such as compression and caching.
*/

const express = require("express");
const path = require("path");
const port = process.env.PORT || 8080;
const app = express();

// serve static assets normally
app.use(express.static(`${__dirname}/out`));

// handle every other route with 404.html, which will bootstrap the app
// and either route to the correct place or show a 404 message
// With express v5, we can no longer use the wildcard * by itself, but instead need to
// include a name https://expressjs.com/en/guide/migrating-5.html#path-syntax
app.get("/*splat", (_request, response) => {
  response.sendFile(path.resolve(__dirname, "out", "index.html"));
});

app.listen(port);
console.log(`server started on port ${port}`);
