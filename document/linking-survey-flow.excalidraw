{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"type": "text", "version": 430, "versionNonce": 955075758, "isDeleted": false, "id": "iqGZ6Z0D1q2K1979yTUo0", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3011.0756483195155, "y": 716.6208254546498, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 167.22289507460778, "height": 31.28705544719844, "seed": 1425087858, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1705481428601, "link": null, "locked": false, "fontSize": 25.029644357758745, "fontFamily": 1, "text": "Survey screen", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Survey screen", "lineHeight": 1.25, "baseline": 22.00000000000001}, {"type": "rectangle", "version": 883, "versionNonce": 1959277746, "isDeleted": false, "id": "cgNvH9d6UyySzuDdLc5YF", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 2998.2201234632016, "y": 757.2869923997627, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 895.5650325186058, "height": 222.05671290029704, "seed": 1160196206, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"id": "Rps7nF49HbNN3HvrK1Fh1", "type": "arrow"}, {"id": "a-5o0QtF1bY4THkgqKRhR", "type": "arrow"}], "updated": 1705481413812, "link": null, "locked": false}, {"type": "rectangle", "version": 292, "versionNonce": 1885048430, "isDeleted": false, "id": "iQALjViX3D2I1ef1ew-2S", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3484.0101421412664, "y": 830.2233851447096, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 133.9283905193456, "height": 85, "seed": 243372206, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "vfpJvc_4fJ-N8RQFgNEBo"}, {"id": "ec12K7us-QqS0PyPkmuq9", "type": "arrow"}, {"id": "3Wl33pOW7JR-VKxN0Ub2n", "type": "arrow"}], "updated": 1705481385020, "link": null, "locked": false}, {"type": "text", "version": 245, "versionNonce": 2143115122, "isDeleted": false, "id": "vfpJvc_4fJ-N8RQFgNEBo", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3489.8444011826778, "y": 847.7233851447096, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 122.25987243652344, "height": 50, "seed": 1302809326, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1705481385020, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Fetch \ninterventions", "textAlign": "center", "verticalAlign": "middle", "containerId": "iQALjViX3D2I1ef1ew-2S", "originalText": "Fetch interventions", "lineHeight": 1.25, "baseline": 43}, {"type": "rectangle", "version": 670, "versionNonce": 1727762606, "isDeleted": false, "id": "DPONekTet1lcrJiUtxmby", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3691.54442941931, "y": 776.7970965257265, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 182.12056571954005, "height": 185, "seed": 1726798766, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "2aBkMZCKzjMkdPQnnHd3I"}, {"id": "ec12K7us-QqS0PyPkmuq9", "type": "arrow"}], "updated": 1705481385020, "link": null, "locked": false}, {"type": "text", "version": 754, "versionNonce": 1153800498, "isDeleted": false, "id": "2aBkMZCKzjMkdPQnnHd3I", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3698.0347888782007, "y": 781.7970965257265, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 169.1398468017578, "height": 175, "seed": 812725742, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1705481385020, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "<PERSON><PERSON> receives and\nfinds an \nintervention with \nan id in the \nquery param in \nthe \"terminal \nnode\"", "textAlign": "center", "verticalAlign": "middle", "containerId": "DPONekTet1lcrJiUtxmby", "originalText": "<PERSON><PERSON> receives and finds an intervention with an id in the query param in the \"terminal node\"", "lineHeight": 1.25, "baseline": 168}, {"type": "arrow", "version": 656, "versionNonce": 115428078, "isDeleted": false, "id": "ec12K7us-QqS0PyPkmuq9", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3625.9064493379506, "y": 872.0343518652089, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 55.62605492802413, "height": 0.06615364778281219, "seed": 319831410, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1705481385020, "link": null, "locked": false, "startBinding": {"elementId": "iQALjViX3D2I1ef1ew-2S", "focus": -0.02115834964540242, "gap": 7.96791667733828}, "endBinding": {"elementId": "DPONekTet1lcrJiUtxmby", "focus": -0.03156963205965434, "gap": 10.011925153334914}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [55.62605492802413, 0.06615364778281219]]}, {"type": "text", "version": 263, "versionNonce": 1320503278, "isDeleted": false, "id": "H3taTiGxB2trDA3KxuRau", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3975.477786086407, "y": 815.7225690815009, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 158.2798614501953, "height": 25, "seed": 1663101422, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1705481420250, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Follow up screen", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Follow up screen", "lineHeight": 1.25, "baseline": 18}, {"type": "rectangle", "version": 306, "versionNonce": 556007090, "isDeleted": false, "id": "G-nRJ9L8672qmws9-gWkD", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3985.1770579243735, "y": 844.862192271733, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 133.9283905193456, "height": 85, "seed": 529103918, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "aYe8GyyeMYWQIE2WrRXr6"}, {"id": "a-5o0QtF1bY4THkgqKRhR", "type": "arrow"}], "updated": 1705481420488, "link": null, "locked": false}, {"type": "text", "version": 274, "versionNonce": 440050798, "isDeleted": false, "id": "aYe8GyyeMYWQIE2WrRXr6", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3991.9213129984996, "y": 849.862192271733, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 120.43988037109375, "height": 75, "seed": 1571145326, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1705481420250, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Whatever \nthe following\nup item", "textAlign": "center", "verticalAlign": "middle", "containerId": "G-nRJ9L8672qmws9-gWkD", "originalText": "Whatever the following up item", "lineHeight": 1.25, "baseline": 68}, {"type": "text", "version": 379, "versionNonce": 1887954606, "isDeleted": false, "id": "5wIcHSw_09ZwVod24aydR", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3923.625971218882, "y": 935.4491483068584, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 279.07977294921875, "height": 25, "seed": **********, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1705481420250, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "(survey, MD scheduling, etc.)", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "(survey, MD scheduling, etc.)", "lineHeight": 1.25, "baseline": 18}, {"id": "bQl4YBNz5DVs-nv4FnHSf", "type": "text", "x": 3009.9439609042884, "y": 839.7287462149685, "width": 129.57078552246094, "height": 47.984375000000014, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": **********, "version": 312, "versionNonce": **********, "isDeleted": false, "boundElements": [{"id": "BX3bnlj3nX1-9JYeF3fsL", "type": "arrow"}], "updated": 1705481385020, "link": null, "locked": false, "text": "User finishes \nSurvey", "fontSize": 19.193750000000005, "fontFamily": 1, "textAlign": "left", "verticalAlign": "top", "baseline": 41, "containerId": null, "originalText": "User finishes \nSurvey", "lineHeight": 1.25}, {"id": "BX3bnlj3nX1-9JYeF3fsL", "type": "arrow", "x": 3143.9634921542884, "y": 865.9710571148521, "width": 41.8203125, "height": 0.4192033657270713, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 1885622427, "version": 518, "versionNonce": 84239342, "isDeleted": false, "boundElements": null, "updated": 1705481385020, "link": null, "locked": false, "points": [[0, 0], [41.8203125, 0.4192033657270713]], "lastCommittedPoint": null, "startBinding": {"elementId": "w_iNle9NXkzBsbXeNI_La", "focus": 0.024807211946090865, "gap": 8.30078125}, "endBinding": {"elementId": "MJDlxp8OkaJMcCyImxSz2", "focus": 0.04880368866178852, "gap": 3.12890625}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "text", "version": 527, "versionNonce": 1130732018, "isDeleted": false, "id": "rWyOcNfAsvwigsfCFw5Dq", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3200.826536893058, "y": 806.6232774649685, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 211.01303100585938, "height": 119.96093750000003, "seed": 8911477, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"id": "BX3bnlj3nX1-9JYeF3fsL", "type": "arrow"}], "updated": 1705481385020, "link": null, "locked": false, "fontSize": 19.193750000000005, "fontFamily": 1, "text": "App receives \na \"terminal node\" \nwith url that\ncontains all possibly\nlinked interventions ids", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "App receives \na \"terminal node\" \nwith url that\ncontains all possibly\nlinked interventions ids", "lineHeight": 1.25, "baseline": 113}, {"id": "MJDlxp8OkaJMcCyImxSz2", "type": "rectangle", "x": 3188.9127109042884, "y": 793.3185899649685, "width": 225.4375, "height": 156.19921875, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "seed": 1942708987, "version": 129, "versionNonce": 1120843310, "isDeleted": false, "boundElements": [{"id": "BX3bnlj3nX1-9JYeF3fsL", "type": "arrow"}, {"id": "3Wl33pOW7JR-VKxN0Ub2n", "type": "arrow"}], "updated": 1705481385020, "link": null, "locked": false}, {"id": "QFSzPoEOKUI0It-Xj1zdz", "type": "rectangle", "x": 3063.594717981199, "y": 634.888681692248, "width": 53.02734375, "height": 48.3671875, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "#ffec99", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "seed": 1814087445, "version": 116, "versionNonce": 1269020146, "isDeleted": false, "boundElements": null, "updated": 1705481432500, "link": null, "locked": false}, {"type": "text", "version": 726, "versionNonce": 828123058, "isDeleted": false, "id": "LRtkDQgP27DAHJ3DAOJIh", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3130.5413274782695, "y": 646.826181692248, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 193.1088409423828, "height": 23.992187500000007, "seed": 255503099, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1705481432500, "link": null, "locked": false, "fontSize": 19.193750000000005, "fontFamily": 1, "text": "= Background action", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "= Background action", "lineHeight": 1.25, "baseline": 17}, {"type": "rectangle", "version": 180, "versionNonce": 849371506, "isDeleted": false, "id": "P_WyJMDMoH-xGBf6ux5pZ", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3062.678702356199, "y": 570.021494192248, "strokeColor": "#1e1e1e", "backgroundColor": "#1e1e1e", "width": 53.02734375, "height": 48.3671875, "seed": 1120118811, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1705481432500, "link": null, "locked": false}, {"type": "text", "version": 834, "versionNonce": 2058838834, "isDeleted": false, "id": "7eFFuYqzWD8q5_UlGDpDr", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3133.880141260008, "y": 584.404306692248, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 183.32192993164062, "height": 23.992187500000007, "seed": 125961717, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1705481432500, "link": null, "locked": false, "fontSize": 19.193750000000005, "fontFamily": 1, "text": "= User interaction ", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "= User interaction ", "lineHeight": 1.25, "baseline": 17}, {"id": "w_iNle9NXkzBsbXeNI_La", "type": "rectangle", "x": 3005.2447421542884, "y": 820.4787462149685, "width": 130.41796875, "height": 87.3125, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "seed": 1775305525, "version": 121, "versionNonce": 831768370, "isDeleted": false, "boundElements": [{"id": "BX3bnlj3nX1-9JYeF3fsL", "type": "arrow"}], "updated": 1705481385020, "link": null, "locked": false}, {"type": "arrow", "version": 496, "versionNonce": 936705262, "isDeleted": false, "id": "3Wl33pOW7JR-VKxN0Ub2n", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3427.369894957891, "y": 869.815307148346, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 45.45097033778211, "height": 0.1498848902321015, "seed": 1039097006, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1705481385020, "link": null, "locked": false, "startBinding": {"elementId": "MJDlxp8OkaJMcCyImxSz2", "focus": -0.02571057917736513, "gap": 13.01968405360276}, "endBinding": {"elementId": "iQALjViX3D2I1ef1ew-2S", "focus": 0.05853033088785929, "gap": 11.18927684559344}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [45.45097033778211, 0.1498848902321015]]}, {"type": "arrow", "version": 737, "versionNonce": 358247666, "isDeleted": false, "id": "a-5o0QtF1bY4THkgqKRhR", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 3914.906372703569, "y": 885.2001493017472, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 62.616444364138715, "height": 0.8687337508500832, "seed": 522953518, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1705481420488, "link": null, "locked": false, "startBinding": {"elementId": "cgNvH9d6UyySzuDdLc5YF", "focus": 0.088529566583069, "gap": 21.121216721761584}, "endBinding": {"elementId": "G-nRJ9L8672qmws9-gWkD", "focus": 0.00594213122671671, "gap": 7.65424085666541}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [62.616444364138715, 0.8687337508500832]]}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}