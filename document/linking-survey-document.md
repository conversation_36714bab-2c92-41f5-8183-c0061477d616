# Linking Survey

Survey is our core functionality of this app. The survey consist of `contextNode -> questionNode -> terminalNode` and eventually ends when terminalNode is executed. In a regular flow, when the user finishes the app brings the user to the specified screen under `terminalNode`. However, as the app has grown and added complexity, we now support opening another intervention from the `terminalNode` so the survey "links" to following up actionable item such as another survey, MD scheduling, etc. With this flow, instead of landing on a screen and hoping for the user to press on an intervention for a follow up action, the app now can directly navigate to the place where user can start the follow up action.

Context: https://github.com/vivantehealth/zi/issues/4002

## How it works

Terminal node has its field called `actions` which represents the CTA(s) of the survey screen. There's a specific url that suggests the survey has linked intervention to be opened right after finishing the terminal node. The structure of the url is as follows; `"survey_loading?linkedIntervention=referral_summary_crc,schedule_gi,referral_summary_crc"`.

The query parameter of the url lists out ALL the possible interventions to be linked to the current survey. Hence, when the CTA of the terminal node gets clicked, the app parses the query parameter `linkedIntervention` and pass it onto `linkingInterventionTriggered()` which takes care of;

1. Retrieves the lates actionPlan data
2. Finds the linked intervention which matches the given interventionId
3. Sends a request to update the intervention above's state to `OPENED_WITH_LINK`
   - that indicates the intervention has been opened by the linked one so that it skips the check when another intervention that contains same intervention id in `linkedIntervention` query param
4. Opens the linked intervention above which replaces the current url path with the one in linked intervention

Also, note that while logic above is on going, the global state on actionPlan `loadState` is set to be `loading` so it displays loading spinner on the screen.

The following is an example of terminal node with `"survey_loading"`

```json
{
  "actions": [
    {
      "title": "Next",
      "uri": "survey_loading?linkedIntervention=referral_summary_crc,schedule_gi"
    }
  ],
  "body": "Thanks for your responses. Your care plan will be updated for your next steps to help you manage your IBS.",
  "callToAction": "",
  "extraInfo": "",
  "id": "t.survey_poorly_controlled_ibs_completed",
  "imageUrl": "",
  "title": "Your care plan is updated"
}
```

## Resource

**Flow of the linking survey**
![](./linking-survey-flow.png)

[excalidraw file of the image above](./linking-survey-flow.excalidraw)
